#!/usr/bin/env python3
"""
Minimal test to reproduce the configuration issue
"""

import os
from typing import List
from pydantic import BaseModel, field_validator
from pydantic_settings import BaseSettings

class MinimalSettings(BaseSettings):
    ALLOWED_HOSTS: List[str] = ["*"]
    
    @field_validator("ALLOWED_HOSTS", mode="before")
    @classmethod
    def parse_hosts(cls, v):
        print(f"Input: {repr(v)} (type: {type(v)})")
        if isinstance(v, str):
            if "," in v:
                return [h.strip() for h in v.split(",") if h.strip()]
            return [v.strip()]
        return v

    class Config:
        env_file = ".env"

if __name__ == "__main__":
    print("Testing minimal configuration...")
    try:
        settings = MinimalSettings()
        print(f"Success! ALLOWED_HOSTS = {settings.ALLOWED_HOSTS}")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()