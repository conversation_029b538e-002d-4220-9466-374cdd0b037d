#!/usr/bin/env python3
"""
API Documentation Validation Script
Tests the examples provided in the API documentation to ensure they are accurate.
"""

import requests
import json
import sys
from typing import Dict, Any

BASE_URL = "http://localhost:8010"

class APIDocTester:
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.auth_token = None
        
    def test_health_check(self) -> bool:
        """Test the health check endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            print(f"✅ Health Check: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Status: {data.get('status', 'unknown')}")
                return True
            return False
        except Exception as e:
            print(f"❌ Health Check Failed: {e}")
            return False
    
    def test_openapi_spec(self) -> bool:
        """Test OpenAPI specification endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/openapi.json")
            print(f"✅ OpenAPI Spec: {response.status_code}")
            if response.status_code == 200:
                spec = response.json()
                print(f"   API Title: {spec.get('info', {}).get('title', 'Unknown')}")
                print(f"   Version: {spec.get('info', {}).get('version', 'Unknown')}")
                print(f"   Paths: {len(spec.get('paths', {}))}")
                return True
            return False
        except Exception as e:
            print(f"❌ OpenAPI Spec Failed: {e}")
            return False
    
    def test_docs_endpoint(self) -> bool:
        """Test documentation endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/docs")
            print(f"✅ Docs Endpoint: {response.status_code}")
            return response.status_code == 200
        except Exception as e:
            print(f"❌ Docs Endpoint Failed: {e}")
            return False
    
    def register_test_user(self) -> bool:
        """Register a test user for API testing"""
        try:
            user_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "first_name": "API",
                "last_name": "Tester"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/auth/register",
                json=user_data
            )
            
            print(f"✅ User Registration: {response.status_code}")
            
            # If user already exists, that's fine for testing
            if response.status_code in [200, 201, 400]:
                return True
            return False
            
        except Exception as e:
            print(f"❌ User Registration Failed: {e}")
            return False
    
    def login_test_user(self) -> bool:
        """Login test user and get auth token"""
        try:
            login_data = {
                "username": "<EMAIL>",
                "password": "TestPassword123!"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/auth/login",
                data=login_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            print(f"✅ User Login: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get("access_token")
                print(f"   Token received: {bool(self.auth_token)}")
                return bool(self.auth_token)
            return False
            
        except Exception as e:
            print(f"❌ User Login Failed: {e}")
            return False
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authorization headers"""
        if not self.auth_token:
            return {}
        return {"Authorization": f"Bearer {self.auth_token}"}
    
    def test_person_creation(self) -> bool:
        """Test person creation endpoint"""
        try:
            person_data = {
                "first_name": "Jane",
                "last_name": "Smith",
                "contact_info": {
                    "email": "<EMAIL>",
                    "phone": "******-0123"
                },
                "professional_info": {
                    "company": "Tech Corp",
                    "title": "Software Engineer",
                    "industry": "Technology"
                },
                "personal_details": {
                    "tags": ["colleague", "friend"],
                    "notes": "Met at tech conference"
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/persons",
                json=person_data,
                headers=self.get_auth_headers()
            )
            
            print(f"✅ Person Creation: {response.status_code}")
            
            if response.status_code in [200, 201]:
                data = response.json()
                print(f"   Person ID: {data.get('person_id', 'unknown')}")
                print(f"   Name: {data.get('first_name')} {data.get('last_name')}")
                return True
            return False
            
        except Exception as e:
            print(f"❌ Person Creation Failed: {e}")
            return False
    
    def test_search_functionality(self) -> bool:
        """Test search functionality"""
        try:
            response = self.session.get(
                f"{self.base_url}/api/v1/search/persons?q=Jane&limit=10",
                headers=self.get_auth_headers()
            )
            
            print(f"✅ Search Functionality: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   Response structure valid: {all(key in data for key in ['results', 'pagination', 'metadata'])}")
                print(f"   Results count: {len(data.get('results', []))}")
                return True
            return False
            
        except Exception as e:
            print(f"❌ Search Functionality Failed: {e}")
            return False
    
    def test_user_profile(self) -> bool:
        """Test user profile endpoint"""
        try:
            response = self.session.get(
                f"{self.base_url}/api/v1/users/me",
                headers=self.get_auth_headers()
            )
            
            print(f"✅ User Profile: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   User email: {data.get('email', 'unknown')}")
                return True
            return False
            
        except Exception as e:
            print(f"❌ User Profile Failed: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all API documentation tests"""
        print("🔍 Testing API Documentation Examples...")
        print("=" * 60)
        
        results = {}
        
        # Basic connectivity tests
        results["health_check"] = self.test_health_check()
        results["openapi_spec"] = self.test_openapi_spec()
        results["docs_endpoint"] = self.test_docs_endpoint()
        
        print("\n" + "=" * 60)
        
        # Authentication tests
        results["user_registration"] = self.register_test_user()
        results["user_login"] = self.login_test_user()
        
        if results["user_login"]:
            print("\n" + "=" * 60)
            
            # Authenticated endpoint tests
            results["person_creation"] = self.test_person_creation()
            results["search_functionality"] = self.test_search_functionality()
            results["user_profile"] = self.test_user_profile()
        else:
            print("\n⚠️  Skipping authenticated tests due to login failure")
        
        return results

def main():
    """Main test runner"""
    print("📚 API Documentation Validation")
    print("Testing examples from API_DOCUMENTATION.md")
    print("=" * 60)
    
    tester = APIDocTester()
    results = tester.run_all_tests()
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY:")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {test_name.replace('_', ' ').title()}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All API documentation examples are working correctly!")
        return 0
    else:
        print("⚠️  Some API examples may need updating in the documentation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
