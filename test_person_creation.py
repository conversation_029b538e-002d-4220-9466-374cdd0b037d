#!/usr/bin/env python3
"""
Test person creation functionality
"""

import requests
import json

BASE_URL = "http://localhost:8010"

def test_person_creation():
    print("Testing person creation functionality...")

    # 1. Register user first
    register_data = {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "first_name": "Test",
        "last_name": "User"
    }

    register_response = requests.post(f"{BASE_URL}/api/v1/auth/register-local", json=register_data)
    print(f"Register: {register_response.status_code}")

    if register_response.status_code not in [200, 409]:  # 409 means already exists
        print(f"Register failed: {register_response.text}")
        return

    # 2. Login to get token
    login_data = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }

    login_response = requests.post(f"{BASE_URL}/api/v1/auth/login-local", json=login_data)
    print(f"Login: {login_response.status_code}")

    if login_response.status_code != 200:
        print(f"Login failed: {login_response.text}")
        return
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    # 3. Clean up any existing data
    cleanup_response = requests.post(f"{BASE_URL}/api/v1/admin/reset-test-data", headers=headers)
    print(f"Cleanup: {cleanup_response.status_code}")

    # 4. Test person creation
    person_data = {
        "first_name": "John",
        "last_name": "Doe",
        "contact_info": {
            "email": "<EMAIL>",
            "phone": "******-0123"
        },
        "professional_info": {
            "company": "Tech Corp",
            "title": "Software Engineer"
        },
        "personal_details": {
            "interests": ["technology", "music"]
        }
    }
    
    create_response = requests.post(f"{BASE_URL}/api/v1/persons/", json=person_data, headers=headers)
    print(f"Person creation: {create_response.status_code}")
    
    if create_response.status_code == 200:
        created_person = create_response.json()
        print(f"Created person: {created_person['first_name']} {created_person['last_name']}")
        print(f"Person ID: {created_person['person_id']}")
        
        # 5. Test duplicate detection
        duplicate_response = requests.post(f"{BASE_URL}/api/v1/persons/", json=person_data, headers=headers)
        print(f"Duplicate creation attempt: {duplicate_response.status_code}")

        if duplicate_response.status_code == 409:
            print("✅ Duplicate detection working correctly!")
        else:
            print("❌ Duplicate detection failed")

        # 6. Test getting the person
        person_id = created_person['person_id']
        get_response = requests.get(f"{BASE_URL}/api/v1/persons/{person_id}", headers=headers)
        print(f"Get person: {get_response.status_code}")

        if get_response.status_code == 200:
            retrieved_person = get_response.json()
            print(f"Retrieved person: {retrieved_person['first_name']} {retrieved_person['last_name']}")
            print("✅ Person retrieval working correctly!")
        else:
            print("❌ Person retrieval failed")

        # 7. Test listing persons
        list_response = requests.get(f"{BASE_URL}/api/v1/persons/", headers=headers)
        print(f"List persons: {list_response.status_code}")

        if list_response.status_code == 200:
            persons_list = list_response.json()
            print(f"Found {len(persons_list)} persons")
            print("✅ Person listing working correctly!")
        else:
            print("❌ Person listing failed")
            
    else:
        print(f"Person creation failed: {create_response.text}")

if __name__ == "__main__":
    test_person_creation()
