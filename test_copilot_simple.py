#!/usr/bin/env python3
"""
Simple Copilot Test - Direct API testing
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8010"

def main():
    print("🤖 Simple Copilot Test")
    print("=" * 40)
    
    session = requests.Session()
    
    # 1. Register and login
    print("1. Setting up authentication...")
    user_data = {
        "email": f"simpletest.{int(time.time())}@example.com",
        "password": "TestPassword123!",
        "first_name": "Simple",
        "last_name": "Test"
    }
    
    # Register
    response = session.post(f"{BASE_URL}/api/v1/auth/register-local", json=user_data)
    if response.status_code not in [200, 201]:
        print(f"❌ Registration failed: {response.text}")
        return
    
    # Login
    login_data = {"email": user_data["email"], "password": user_data["password"]}
    response = session.post(f"{BASE_URL}/api/v1/auth/login-local", json=login_data)
    if response.status_code != 200:
        print(f"❌ Login failed: {response.text}")
        return
    
    token = response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Authentication successful")
    
    # 2. Test basic conversation
    print("\n2. Testing basic conversation...")
    message_data = {"message": "Hello! Can you help me?"}
    
    try:
        response = session.post(
            f"{BASE_URL}/api/v1/copilot/converse",
            json=message_data,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Basic conversation successful")
            print(f"   Response: {data.get('response', '')[:100]}...")
            print(f"   Conversation ID: {data.get('conversation_id')}")
            print(f"   Tool calls: {len(data.get('tool_calls', []))}")
            
            conversation_id = data.get('conversation_id')
        else:
            print(f"❌ Conversation failed: {response.status_code} - {response.text}")
            return
            
    except Exception as e:
        print(f"❌ Conversation error: {e}")
        return
    
    # 3. Test function calling
    print("\n3. Testing function calling...")
    message_data = {
        "message": "Can you search my network for connections?",
        "conversation_id": conversation_id
    }
    
    try:
        response = session.post(
            f"{BASE_URL}/api/v1/copilot/converse",
            json=message_data,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            tool_calls = data.get('tool_calls', [])
            print("✅ Function calling test successful")
            print(f"   Tool calls made: {len(tool_calls)}")
            
            for tc in tool_calls:
                print(f"   - {tc.get('name')}: {tc.get('status')}")
        else:
            print(f"❌ Function calling failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Function calling error: {e}")
    
    # 4. Test intent analysis
    print("\n4. Testing intent analysis...")
    message_data = {"message": "I want to create a task"}
    
    try:
        response = session.post(
            f"{BASE_URL}/api/v1/copilot/analyze",
            json=message_data,
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Intent analysis successful")
            print(f"   Intent: {data.get('intent')}")
            print(f"   Confidence: {data.get('confidence')}")
        else:
            print(f"❌ Intent analysis failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Intent analysis error: {e}")
    
    # 5. Test suggestions
    print("\n5. Testing suggestions...")
    
    try:
        response = session.get(
            f"{BASE_URL}/api/v1/copilot/suggestions?limit=3",
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            suggestions = data.get('suggestions', [])
            print("✅ Suggestions successful")
            print(f"   Suggestions count: {len(suggestions)}")
            print(f"   Has LLM recommendations: {'llm_recommendations' in data}")
        else:
            print(f"❌ Suggestions failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Suggestions error: {e}")
    
    # 6. Save test report
    print("\n6. Saving test report...")
    
    test_report = {
        "timestamp": datetime.now().isoformat(),
        "test_type": "simple_copilot_test",
        "base_url": BASE_URL,
        "user_email": user_data["email"],
        "conversation_id": conversation_id,
        "status": "completed"
    }
    
    filename = f"copilot_simple_test_report_{int(time.time())}.json"
    with open(filename, 'w') as f:
        json.dump(test_report, f, indent=2)
    
    print(f"✅ Test report saved: {filename}")
    print("\n🎉 Simple copilot test completed!")

if __name__ == "__main__":
    main()
