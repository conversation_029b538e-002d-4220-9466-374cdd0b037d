"""
Test assertions and validation utilities for interface testing.
"""
import re
import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from .client import TestResponse


class ValidationError(Exception):
    """Custom exception for validation errors."""
    pass


class ResponseAssertions:
    """Assertion utilities for API response validation."""
    
    @staticmethod
    def assert_status_code(response: TestResponse, expected_code: int) -> None:
        """Assert response has expected status code."""
        if response.status_code != expected_code:
            raise AssertionError(
                f"Expected status code {expected_code}, got {response.status_code}. "
                f"Response: {response.text()}"
            )
    
    @staticmethod
    def assert_success_response(response: TestResponse) -> None:
        """Assert response indicates success (2xx status)."""
        if not response.is_success:
            raise AssertionError(
                f"Expected successful response (2xx), got {response.status_code}. "
                f"Response: {response.text()}"
            )
    
    @staticmethod
    def assert_error_response(response: TestResponse, expected_code: Optional[int] = None) -> None:
        """Assert response indicates an error (4xx or 5xx status)."""
        if not response.is_error:
            raise AssertionError(
                f"Expected error response (4xx/5xx), got {response.status_code}. "
                f"Response: {response.text()}"
            )
        
        if expected_code and response.status_code != expected_code:
            raise AssertionError(
                f"Expected error code {expected_code}, got {response.status_code}. "
                f"Response: {response.text()}"
            )
    
    @staticmethod
    def assert_json_response(response: TestResponse) -> Dict[str, Any]:
        """Assert response is valid JSON and return parsed data."""
        try:
            return response.json()
        except ValueError as e:
            raise AssertionError(f"Response is not valid JSON: {e}")
    
    @staticmethod
    def assert_response_time(response: TestResponse, max_time: float = 2.0) -> None:
        """Assert response time is within acceptable limits."""
        if response.elapsed > max_time:
            raise AssertionError(
                f"Response time {response.elapsed:.3f}s exceeds maximum {max_time}s"
            )
    
    @staticmethod
    def assert_has_header(response: TestResponse, header_name: str, 
                         expected_value: Optional[str] = None) -> None:
        """Assert response has specific header."""
        if header_name not in response.headers:
            raise AssertionError(f"Response missing header: {header_name}")
        
        if expected_value and response.headers[header_name] != expected_value:
            raise AssertionError(
                f"Header {header_name} has value '{response.headers[header_name]}', "
                f"expected '{expected_value}'"
            )


class DataValidation:
    """Data validation utilities."""
    
    UUID_PATTERN = re.compile(
        r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
        re.IGNORECASE
    )
    
    ISO_DATE_PATTERN = re.compile(
        r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z?$'
    )
    
    EMAIL_PATTERN = re.compile(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    )
    
    @staticmethod
    def is_valid_uuid(value: str) -> bool:
        """Check if value is a valid UUID."""
        return bool(DataValidation.UUID_PATTERN.match(str(value)))
    
    @staticmethod
    def is_valid_iso_date(value: str) -> bool:
        """Check if value is a valid ISO date string."""
        if not DataValidation.ISO_DATE_PATTERN.match(str(value)):
            return False
        try:
            datetime.fromisoformat(str(value).replace('Z', '+00:00'))
            return True
        except ValueError:
            return False
    
    @staticmethod
    def is_valid_email(value: str) -> bool:
        """Check if value is a valid email address."""
        return bool(DataValidation.EMAIL_PATTERN.match(str(value)))
    
    @staticmethod
    def assert_valid_uuid(value: Any, field_name: str = "field") -> None:
        """Assert value is a valid UUID."""
        if not DataValidation.is_valid_uuid(value):
            raise ValidationError(f"{field_name} must be a valid UUID, got: {value}")
    
    @staticmethod
    def assert_valid_iso_date(value: Any, field_name: str = "field") -> None:
        """Assert value is a valid ISO date string."""
        if not DataValidation.is_valid_iso_date(value):
            raise ValidationError(f"{field_name} must be a valid ISO date, got: {value}")
    
    @staticmethod
    def assert_valid_email(value: Any, field_name: str = "field") -> None:
        """Assert value is a valid email address."""
        if not DataValidation.is_valid_email(value):
            raise ValidationError(f"{field_name} must be a valid email, got: {value}")


class StructureValidator:
    """Validation utilities for complex data structures."""
    
    @staticmethod
    def validate_response_structure(
        data: Dict[str, Any],
        required_fields: List[str],
        optional_fields: Optional[List[str]] = None,
        strict: bool = False
    ) -> None:
        """Validate response has required structure."""
        optional_fields = optional_fields or []
        
        # Check required fields
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValidationError(f"Missing required fields: {missing_fields}")
        
        # In strict mode, check for unexpected fields
        if strict:
            allowed_fields = set(required_fields + optional_fields)
            unexpected_fields = [field for field in data.keys() if field not in allowed_fields]
            if unexpected_fields:
                raise ValidationError(f"Unexpected fields: {unexpected_fields}")
    
    @staticmethod
    def validate_user_response(data: Dict[str, Any]) -> None:
        """Validate user response structure."""
        required_fields = ["user_id", "email", "first_name", "last_name", "is_active", "created_at"]
        optional_fields = ["last_login", "settings"]
        
        StructureValidator.validate_response_structure(data, required_fields, optional_fields)
        
        # Validate specific field types
        DataValidation.assert_valid_uuid(data["user_id"], "user_id")
        DataValidation.assert_valid_email(data["email"], "email")
        DataValidation.assert_valid_iso_date(data["created_at"], "created_at")
        
        if not isinstance(data["is_active"], bool):
            raise ValidationError(f"is_active must be boolean, got: {type(data['is_active'])}")
    
    @staticmethod
    def validate_person_response(data: Dict[str, Any]) -> None:
        """Validate person response structure."""
        required_fields = ["person_id", "user_id", "first_name", "last_name", "created_at", "updated_at"]
        optional_fields = ["contact_info", "professional_info", "social_profiles", "personal_details"]
        
        StructureValidator.validate_response_structure(data, required_fields, optional_fields)
        
        # Validate specific field types
        DataValidation.assert_valid_uuid(data["person_id"], "person_id")
        DataValidation.assert_valid_uuid(data["user_id"], "user_id")
        DataValidation.assert_valid_iso_date(data["created_at"], "created_at")
        DataValidation.assert_valid_iso_date(data["updated_at"], "updated_at")
    
    @staticmethod
    def validate_goal_response(data: Dict[str, Any]) -> None:
        """Validate goal response structure."""
        required_fields = ["goal_id", "user_id", "title", "status"]
        optional_fields = ["description", "target_date", "priority", "category", "created_at", "updated_at"]
        
        StructureValidator.validate_response_structure(data, required_fields, optional_fields)
        
        # Validate specific field types
        DataValidation.assert_valid_uuid(data["goal_id"], "goal_id")
        DataValidation.assert_valid_uuid(data["user_id"], "user_id")
        
        valid_statuses = ["active", "completed", "paused", "cancelled"]
        if data["status"] not in valid_statuses:
            raise ValidationError(f"status must be one of {valid_statuses}, got: {data['status']}")
    
    @staticmethod
    def validate_task_response(data: Dict[str, Any]) -> None:
        """Validate task response structure."""
        required_fields = ["task_id", "user_id", "title", "is_completed"]
        optional_fields = ["goal_id", "description", "priority", "due_date", "estimated_hours", "created_at", "updated_at"]
        
        StructureValidator.validate_response_structure(data, required_fields, optional_fields)
        
        # Validate specific field types
        DataValidation.assert_valid_uuid(data["task_id"], "task_id")
        DataValidation.assert_valid_uuid(data["user_id"], "user_id")
        
        if "goal_id" in data and data["goal_id"]:
            DataValidation.assert_valid_uuid(data["goal_id"], "goal_id")
        
        if not isinstance(data["is_completed"], bool):
            raise ValidationError(f"is_completed must be boolean, got: {type(data['is_completed'])}")
    
    @staticmethod
    def validate_error_response(data: Dict[str, Any]) -> None:
        """Validate error response structure."""
        # Handle both custom error format and FastAPI default format
        if "detail" in data:
            # FastAPI default error format
            detail = data["detail"]
            if isinstance(detail, list):
                # Validation error format - list of error objects
                for error in detail:
                    if not isinstance(error, dict):
                        raise ValidationError(f"error detail item must be dict, got: {type(error)}")
            elif not isinstance(detail, str):
                raise ValidationError(f"error detail must be string or list, got: {type(detail)}")
        else:
            # Custom error format
            required_fields = ["code", "message"]
            optional_fields = ["details", "request_id"]

            StructureValidator.validate_response_structure(data, required_fields, optional_fields)

            if not isinstance(data["code"], str):
                raise ValidationError(f"error code must be string, got: {type(data['code'])}")

            if not isinstance(data["message"], str):
                raise ValidationError(f"error message must be string, got: {type(data['message'])}")
    
    @staticmethod
    def validate_paginated_response(data: Dict[str, Any]) -> None:
        """Validate paginated response structure."""
        required_fields = ["items", "total", "skip", "limit"]
        optional_fields = ["has_more"]
        
        StructureValidator.validate_response_structure(data, required_fields, optional_fields)
        
        if not isinstance(data["items"], list):
            raise ValidationError(f"items must be list, got: {type(data['items'])}")
        
        for field in ["total", "skip", "limit"]:
            if not isinstance(data[field], int):
                raise ValidationError(f"{field} must be integer, got: {type(data[field])}")
    
    @staticmethod
    def validate_ai_suggestion_response(data: Dict[str, Any]) -> None:
        """Validate AI suggestion response structure."""
        required_fields = ["id", "type", "title", "description", "priority", "created_at"]
        optional_fields = ["person_id", "suggested_action", "metadata"]
        
        StructureValidator.validate_response_structure(data, required_fields, optional_fields)
        
        # Validate specific field types
        DataValidation.assert_valid_uuid(data["id"], "suggestion_id")
        DataValidation.assert_valid_iso_date(data["created_at"], "created_at")
        
        if "person_id" in data and data["person_id"]:
            DataValidation.assert_valid_uuid(data["person_id"], "person_id")
        
        valid_types = ["reconnect", "introduction", "collaboration", "follow_up"]
        if data["type"] not in valid_types:
            raise ValidationError(f"suggestion type must be one of {valid_types}, got: {data['type']}")
        
        valid_priorities = ["low", "medium", "high"]
        if data["priority"] not in valid_priorities:
            raise ValidationError(f"priority must be one of {valid_priorities}, got: {data['priority']}")


# Convenience assertion functions
def assert_successful_json_response(response: TestResponse) -> Dict[str, Any]:
    """Assert response is successful and return JSON data."""
    ResponseAssertions.assert_success_response(response)
    return ResponseAssertions.assert_json_response(response)


def assert_error_json_response(response: TestResponse, expected_code: Optional[int] = None) -> Dict[str, Any]:
    """Assert response is an error and return JSON data."""
    ResponseAssertions.assert_error_response(response, expected_code)
    data = ResponseAssertions.assert_json_response(response)
    StructureValidator.validate_error_response(data)
    return data


def assert_valid_user_response(response: TestResponse) -> Dict[str, Any]:
    """Assert response is a valid user response."""
    data = assert_successful_json_response(response)
    StructureValidator.validate_user_response(data)
    return data


def assert_valid_person_response(response: TestResponse) -> Dict[str, Any]:
    """Assert response is a valid person response."""
    data = assert_successful_json_response(response)
    StructureValidator.validate_person_response(data)
    return data


def assert_valid_goal_response(response: TestResponse) -> Dict[str, Any]:
    """Assert response is a valid goal response."""
    data = assert_successful_json_response(response)
    StructureValidator.validate_goal_response(data)
    return data


def assert_valid_task_response(response: TestResponse) -> Dict[str, Any]:
    """Assert response is a valid task response."""
    data = assert_successful_json_response(response)
    StructureValidator.validate_task_response(data)
    return data


def assert_valid_paginated_response(response: TestResponse) -> Dict[str, Any]:
    """Assert response is a valid paginated response."""
    data = assert_successful_json_response(response)
    StructureValidator.validate_paginated_response(data)
    return data