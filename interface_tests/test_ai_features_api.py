"""
AI Features API endpoint tests.
Tests Copilot chat, AI suggestions, network analysis, and relationship insights.
"""
import pytest
from typing import Dict, Any, List
from .client import APIClient, create_client
from .assertions import (
    assert_successful_json_response, 
    assert_error_json_response,
    ResponseAssertions,
    StructureValidator
)
from .config import get_config


@pytest.mark.ai
class TestCopilotAPI:
    """Test AI Copilot functionality."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and configuration."""
        self.config = get_config()
        self.client = create_client()
        self.auth_headers = {}
        
        if self.config.test_ai_features:
            # Authenticate user for tests
            _, self.auth_headers = self.client.authenticate_user("user1")
        
        yield
        self.client.close()
    
    def test_copilot_conversation_basic(self):
        """Test basic copilot conversation functionality."""
        if not self.config.test_ai_features:
            pytest.skip("AI features tests disabled")
        
        conversation_data = {
            "message": "Help me find a mentor in machine learning",
            "context": {
                "current_goals": ["learn_ml"],
                "user_background": "software_engineer"
            }
        }
        
        response = self.client.post("/api/v1/copilot/converse", 
                                   json_data=conversation_data, headers=self.auth_headers)
        
        if response.status_code == 501:  # Not implemented
            pytest.skip("Copilot conversation not implemented")
        
        conversation = assert_successful_json_response(response)
        
        # Verify response structure
        required_fields = ["response"]
        optional_fields = ["intent", "entities", "suggested_actions", "conversation_id", "confidence"]
        StructureValidator.validate_response_structure(conversation, required_fields, optional_fields)
        
        # Verify response content
        assert isinstance(conversation["response"], str)
        assert len(conversation["response"]) > 0
        
        if "confidence" in conversation:
            assert 0 <= conversation["confidence"] <= 1
    
    def test_copilot_conversation_with_context(self):
        """Test copilot conversation with conversation context."""
        if not self.config.test_ai_features:
            pytest.skip("AI features tests disabled")
        
        # Start conversation
        first_message = {
            "message": "I want to expand my network in AI",
            "context": {"goals": ["networking"]}
        }
        
        response1 = self.client.post("/api/v1/copilot/converse", 
                                    json_data=first_message, headers=self.auth_headers)
        
        if response1.status_code == 501:
            pytest.skip("Copilot conversation not implemented")
        
        conversation1 = assert_successful_json_response(response1)
        
        # Continue conversation with context
        conversation_id = conversation1.get("conversation_id", "test-conversation-id")
        
        follow_up_message = {
            "message": "What specific steps should I take?",
            "conversation_id": conversation_id
        }
        
        response2 = self.client.post("/api/v1/copilot/converse", 
                                    json_data=follow_up_message, headers=self.auth_headers)
        conversation2 = assert_successful_json_response(response2)
        
        # Verify follow-up response acknowledges context
        assert isinstance(conversation2["response"], str)
        assert len(conversation2["response"]) > 0
    
    def test_copilot_intent_recognition(self):
        """Test copilot intent recognition for different request types."""
        if not self.config.test_ai_features:
            pytest.skip("AI features tests disabled")
        
        test_cases = [
            {
                "message": "Create a task to call John about the project",
                "expected_intent": "create_task"
            },
            {
                "message": "Find me connections in the healthcare industry",
                "expected_intent": "find_connections"
            },
            {
                "message": "Set a goal to attend 5 networking events this quarter",
                "expected_intent": "create_goal"
            },
            {
                "message": "How is my network health looking?",
                "expected_intent": "network_analysis"
            }
        ]
        
        for case in test_cases:
            conversation_data = {"message": case["message"]}
            response = self.client.post("/api/v1/copilot/converse", 
                                       json_data=conversation_data, headers=self.auth_headers)
            
            if response.status_code == 501:
                pytest.skip("Copilot conversation not implemented")
            
            conversation = assert_successful_json_response(response)
            
            # Check if intent is recognized (optional feature)
            if "intent" in conversation:
                # This is optional - AI might not always detect exact intent
                pass
    
    def test_copilot_entity_extraction(self):
        """Test copilot entity extraction from user messages."""
        if not self.config.test_ai_features:
            pytest.skip("AI features tests disabled")
        
        conversation_data = {
            "message": "Remind me to contact Sarah Johnson at Microsoft tomorrow about the AI project"
        }
        
        response = self.client.post("/api/v1/copilot/converse", 
                                   json_data=conversation_data, headers=self.auth_headers)
        
        if response.status_code == 501:
            pytest.skip("Copilot conversation not implemented")
        
        conversation = assert_successful_json_response(response)
        
        # Check for entity extraction (optional feature)
        if "entities" in conversation:
            entities = conversation["entities"]
            assert isinstance(entities, list)
            
            # Look for expected entity types
            entity_types = [entity.get("type") for entity in entities]
            # Might include: person, company, time, project, etc.
    
    def test_copilot_suggested_actions(self):
        """Test copilot suggested actions generation."""
        if not self.config.test_ai_features:
            pytest.skip("AI features tests disabled")
        
        conversation_data = {
            "message": "I need to follow up with my contacts from last month's conference"
        }
        
        response = self.client.post("/api/v1/copilot/converse", 
                                   json_data=conversation_data, headers=self.auth_headers)
        
        if response.status_code == 501:
            pytest.skip("Copilot conversation not implemented")
        
        conversation = assert_successful_json_response(response)
        
        # Check for suggested actions (optional feature)
        if "suggested_actions" in conversation:
            actions = conversation["suggested_actions"]
            assert isinstance(actions, list)
            
            for action in actions:
                assert "type" in action
                assert "title" in action or "description" in action


@pytest.mark.ai
class TestAISuggestionsAPI:
    """Test AI suggestions functionality."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and configuration."""
        self.config = get_config()
        self.client = create_client()
        self.auth_headers = {}
        
        if self.config.test_ai_features:
            # Authenticate user for tests
            _, self.auth_headers = self.client.authenticate_user("user1")
        
        yield
        self.client.close()
    
    def test_get_ai_suggestions(self):
        """Test getting AI-generated suggestions."""
        if not self.config.test_ai_features:
            pytest.skip("AI features tests disabled")
        
        response = self.client.get("/api/v1/copilot/suggestions", headers=self.auth_headers)
        
        if response.status_code == 501:  # Not implemented
            pytest.skip("AI suggestions not implemented")
        
        suggestions = assert_successful_json_response(response)
        
        # Should return array of suggestions
        assert isinstance(suggestions, list)
        
        # Validate each suggestion
        for suggestion in suggestions:
            StructureValidator.validate_ai_suggestion_response(suggestion)
    
    def test_get_filtered_ai_suggestions(self):
        """Test getting AI suggestions with filters."""
        if not self.config.test_ai_features:
            pytest.skip("AI features tests disabled")
        
        # Test different filter combinations
        filter_params = [
            {"type": "reconnect"},
            {"priority": "high"},
            {"type": "reconnect", "priority": "medium"}
        ]
        
        for params in filter_params:
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
            response = self.client.get(f"/api/v1/copilot/suggestions?{query_string}", 
                                      headers=self.auth_headers)
            
            if response.status_code == 501:
                pytest.skip("AI suggestions not implemented")
            
            suggestions = assert_successful_json_response(response)
            assert isinstance(suggestions, list)
            
            # Verify filtering worked
            for suggestion in suggestions:
                if "type" in params:
                    assert suggestion["type"] == params["type"]
                if "priority" in params:
                    assert suggestion["priority"] == params["priority"]


@pytest.mark.ai
class TestNetworkAnalysisAPI:
    """Test AI network analysis functionality."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and configuration."""
        self.config = get_config()
        self.client = create_client()
        self.auth_headers = {}
        
        if self.config.test_ai_features:
            # Authenticate user for tests
            _, self.auth_headers = self.client.authenticate_user("user1")
        
        yield
        self.client.close()
    
    def test_network_health_diagnosis(self):
        """Test network health diagnosis."""
        if not self.config.test_ai_features:
            pytest.skip("AI features tests disabled")
        
        response = self.client.get("/api/v1/ai/network/diagnosis", headers=self.auth_headers)
        
        if response.status_code == 501:  # Not implemented
            pytest.skip("Network diagnosis not implemented")
        
        diagnosis = assert_successful_json_response(response)
        
        # Verify diagnosis structure
        required_fields = ["overall_health", "network_size"]
        optional_fields = ["active_connections", "dormant_connections", "insights", "recommendations", "generated_at"]
        StructureValidator.validate_response_structure(diagnosis, required_fields, optional_fields)
        
        # Verify data types
        assert isinstance(diagnosis["overall_health"], (int, float))
        assert 0 <= diagnosis["overall_health"] <= 1  # Assuming normalized score
        assert isinstance(diagnosis["network_size"], int)
        assert diagnosis["network_size"] >= 0
    
    def test_network_diagnosis_with_insights(self):
        """Test network diagnosis with detailed insights."""
        if not self.config.test_ai_features:
            pytest.skip("AI features tests disabled")
        
        response = self.client.get("/api/v1/ai/network/diagnosis?include_insights=true", 
                                  headers=self.auth_headers)
        
        if response.status_code == 501:
            pytest.skip("Network diagnosis not implemented")
        
        diagnosis = assert_successful_json_response(response)
        
        # Should include insights and recommendations
        if "insights" in diagnosis:
            insights = diagnosis["insights"]
            assert isinstance(insights, list)
            
            for insight in insights:
                assert "type" in insight  # e.g., "strength", "weakness"
                assert "message" in insight
        
        if "recommendations" in diagnosis:
            recommendations = diagnosis["recommendations"]
            assert isinstance(recommendations, list)
            
            for rec in recommendations:
                assert isinstance(rec, str)
                assert len(rec) > 0
    
    def test_referral_path_finding(self):
        """Test AI-powered referral path finding."""
        if not self.config.test_ai_features:
            pytest.skip("AI features tests disabled")
        
        # First, ensure we have some test contacts
        # This test assumes contacts exist or creates them
        
        referral_request = {
            "target_person_name": "Elon Musk",
            "target_criteria": {
                "industry": "technology",
                "role": "CEO",
                "company": "Tesla"
            },
            "max_degrees": 3,
            "purpose": "job_referral"
        }
        
        response = self.client.post("/api/v1/ai/network/referral-path", 
                                   json_data=referral_request, headers=self.auth_headers)
        
        if response.status_code == 501:  # Not implemented
            pytest.skip("Referral path finding not implemented")
        
        if response.status_code == 404:  # No path found
            pytest.skip("No referral path found (expected with test data)")
        
        paths = assert_successful_json_response(response)
        
        # Verify path structure
        required_fields = ["paths"]
        optional_fields = ["alternative_strategies"]
        StructureValidator.validate_response_structure(paths, required_fields, optional_fields)
        
        assert isinstance(paths["paths"], list)
        
        for path in paths["paths"]:
            assert "steps" in path
            assert "overall_strength" in path or "estimated_success_rate" in path
            
            # Verify path steps
            steps = path["steps"]
            assert isinstance(steps, list)
            assert len(steps) > 0
            
            for step in steps:
                required_step_fields = ["person_name", "relationship_strength"]
                optional_step_fields = ["person_id", "connection_type"]
                StructureValidator.validate_response_structure(step, required_step_fields, optional_step_fields)


@pytest.mark.ai
class TestRelationshipAnalysisAPI:
    """Test AI relationship analysis functionality."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and configuration."""
        self.config = get_config()
        self.client = create_client()
        self.auth_headers = {}
        
        if self.config.test_ai_features:
            # Authenticate user for tests
            _, self.auth_headers = self.client.authenticate_user("user1")
        
        yield
        self.client.close()
    
    def create_test_persons(self) -> tuple:
        """Create test persons for relationship analysis."""
        import uuid
        unique_id = str(uuid.uuid4())[:8]

        person1_data = {
            "first_name": f"Alice{unique_id}",
            "last_name": "Johnson",
            "contact_info": {"email": f"alice{unique_id}@example.com"}
        }
        person2_data = {
            "first_name": f"Bob{unique_id}",
            "last_name": "Smith",
            "contact_info": {"email": f"bob{unique_id}@example.com"}
        }
        
        response1 = self.client.post("/api/v1/persons/", json_data=person1_data, headers=self.auth_headers)
        response2 = self.client.post("/api/v1/persons/", json_data=person2_data, headers=self.auth_headers)
        
        person1 = assert_successful_json_response(response1)
        person2 = assert_successful_json_response(response2)
        
        return person1, person2
    
    def test_relationship_prism_analysis(self):
        """Test multi-dimensional relationship analysis."""
        if not self.config.test_ai_features:
            pytest.skip("AI features tests disabled")
        
        # Create test persons
        person1, person2 = self.create_test_persons()
        
        # Get relationship analysis
        response = self.client.get(
            f"/api/v1/persons/{person1['person_id']}/relationship-prism/{person2['person_id']}",
            headers=self.auth_headers
        )
        
        if response.status_code == 501:  # Not implemented
            pytest.skip("Relationship prism analysis not implemented")
        
        if response.status_code == 404:  # No relationship exists
            pytest.skip("No relationship exists between test persons")
        
        analysis = assert_successful_json_response(response)
        
        # Verify analysis structure
        required_fields = ["relationship_strength", "dimensions"]
        optional_fields = ["insights", "recommendations", "last_updated"]
        StructureValidator.validate_response_structure(analysis, required_fields, optional_fields)
        
        # Verify relationship dimensions
        dimensions = analysis["dimensions"]
        expected_dimensions = [
            "emotional_intimacy",
            "professional_collaboration", 
            "trust_level",
            "communication_frequency",
            "shared_experience_value",
            "reciprocity_balance"
        ]
        
        for dimension in expected_dimensions:
            if dimension in dimensions:
                assert isinstance(dimensions[dimension], (int, float))
                assert 0 <= dimensions[dimension] <= 100  # Assuming 0-100 scale
    
    def test_ai_features_performance(self):
        """Test AI features performance."""
        if not self.config.test_performance or not self.config.test_ai_features:
            pytest.skip("Performance tests disabled")
        
        # Test copilot response time
        conversation_data = {"message": "Help me with networking"}
        response = self.client.post("/api/v1/copilot/converse", 
                                   json_data=conversation_data, headers=self.auth_headers)
        
        if response.status_code == 200:
            ResponseAssertions.assert_response_time(response, 5.0)  # AI responses may be slower
        
        # Test suggestions response time
        suggestions_response = self.client.get("/api/v1/copilot/suggestions", headers=self.auth_headers)
        if suggestions_response.status_code == 200:
            ResponseAssertions.assert_response_time(suggestions_response, self.config.max_response_time)
    
    def test_ai_features_without_authentication(self):
        """Test that AI features require authentication."""
        if not self.config.test_auth:
            pytest.skip("Authentication tests disabled")
        
        # Try to access without authentication
        endpoints = [
            ("/api/v1/copilot/converse", "POST"),
            ("/api/v1/copilot/suggestions", "GET"),
            ("/api/v1/ai/network/diagnosis", "GET"),
            ("/api/v1/ai/network/referral-path", "POST")
        ]
        
        for endpoint, method in endpoints:
            if method == "GET":
                response = self.client.get(endpoint)
            elif method == "POST":
                response = self.client.post(endpoint, json_data={})
            
            assert_error_json_response(response, 401)  # Unauthorized