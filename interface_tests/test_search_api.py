"""
Search API endpoint tests.
Tests the dedicated search functionality with the new response format.
"""
import pytest
from typing import Dict, Any, List
from .client import APIClient, create_client
from .assertions import (
    assert_successful_json_response, 
    assert_error_json_response,
    ResponseAssertions
)
from .config import get_config


class TestSearchAPI:
    """Test search API functionality."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and configuration."""
        self.config = get_config()
        self.client = create_client()
        self.auth_headers = {}
        
        if self.config.test_search:
            # Authenticate user for tests
            user_data, auth_headers = self.client.authenticate_user("user1")
            self.auth_headers = auth_headers
            self.user_data = user_data
            
            # Create test persons for searching
            self.test_persons = []
            self.create_test_data()
        
        yield
        
        # Cleanup
        if hasattr(self, 'client'):
            self.client.close()
    
    def create_test_data(self):
        """Create test persons for search testing."""
        test_persons_data = [
            {
                "first_name": "<PERSON>",
                "last_name": "<PERSON><PERSON>",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "company": "TechCorp",
                    "title": "Software Engineer",
                    "industry": "Technology"
                },
                "personal_details": {
                    "tags": ["colleague", "tech"],
                    "notes": "Met at conference"
                }
            },
            {
                "first_name": "Jane",
                "last_name": "Smith",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "company": "Innovate Inc",
                    "title": "Product Manager",
                    "industry": "Technology"
                },
                "personal_details": {
                    "tags": ["friend", "product"],
                    "notes": "College friend"
                }
            },
            {
                "first_name": "Bob",
                "last_name": "Johnson",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "company": "Finance Corp",
                    "title": "Analyst",
                    "industry": "Finance"
                },
                "personal_details": {
                    "tags": ["colleague", "finance"],
                    "notes": "Former colleague"
                }
            }
        ]
        
        for person_data in test_persons_data:
            response = self.client.post(
                "/api/v1/persons",
                json_data=person_data,
                headers=self.auth_headers
            )
            if response.status_code in [200, 201]:
                person = response.json()
                self.test_persons.append(person)
    
    @pytest.mark.search
    def test_search_persons_basic(self):
        """Test basic person search functionality."""
        if not self.config.test_search:
            pytest.skip("Search tests disabled")
        
        # Search for "John"
        response = self.client.get(
            "/api/v1/search/persons?q=John&limit=10",
            headers=self.auth_headers
        )
        
        data = assert_successful_json_response(response)
        
        # Verify response structure
        assert "results" in data, "Response should have 'results' field"
        assert "pagination" in data, "Response should have 'pagination' field"
        assert "metadata" in data, "Response should have 'metadata' field"
        
        # Verify pagination structure
        pagination = data["pagination"]
        assert "total" in pagination
        assert "limit" in pagination
        assert "offset" in pagination
        assert "has_more" in pagination
        
        # Verify metadata structure
        metadata = data["metadata"]
        assert "query" in metadata
        assert metadata["query"] == "John"
        
        # Verify results
        results = data["results"]
        assert isinstance(results, list), "Results should be a list"
        
        if results:
            # Should find John Doe
            john_found = any(
                person.get("first_name") == "John" and person.get("last_name") == "Doe"
                for person in results
            )
            assert john_found, "Should find John Doe in search results"
            
            # Verify result structure
            first_result = results[0]
            required_fields = ["person_id", "first_name", "last_name", "full_name"]
            for field in required_fields:
                assert field in first_result, f"Result should have '{field}' field"
    
    @pytest.mark.search
    def test_search_persons_by_company(self):
        """Test searching persons by company."""
        if not self.config.test_search:
            pytest.skip("Search tests disabled")
        
        # Search for "TechCorp"
        response = self.client.get(
            "/api/v1/search/persons?q=TechCorp&limit=10",
            headers=self.auth_headers
        )
        
        data = assert_successful_json_response(response)
        results = data["results"]
        
        if results:
            # Should find person from TechCorp
            techcorp_found = any(
                person.get("company") == "TechCorp"
                for person in results
            )
            assert techcorp_found, "Should find TechCorp employee in search results"
    
    @pytest.mark.search
    def test_search_persons_with_filters(self):
        """Test searching persons with additional filters."""
        if not self.config.test_search:
            pytest.skip("Search tests disabled")
        
        # Search with company filter
        response = self.client.get(
            "/api/v1/search/persons?q=engineer&company=TechCorp&limit=10",
            headers=self.auth_headers
        )
        
        data = assert_successful_json_response(response)
        
        # Verify filters are reflected in metadata
        metadata = data["metadata"]
        filters_applied = metadata.get("filters_applied", {})
        assert filters_applied.get("company") == "TechCorp"
        
        results = data["results"]
        if results:
            # All results should be from TechCorp
            for person in results:
                if person.get("company"):
                    assert "TechCorp" in person.get("company", "")
    
    @pytest.mark.search
    def test_search_persons_with_tags(self):
        """Test searching persons with tag filters."""
        if not self.config.test_search:
            pytest.skip("Search tests disabled")
        
        # Search with tags filter
        response = self.client.get(
            "/api/v1/search/persons?q=colleague&tags=tech,colleague&limit=10",
            headers=self.auth_headers
        )
        
        data = assert_successful_json_response(response)
        
        # Verify filters are reflected in metadata
        metadata = data["metadata"]
        filters_applied = metadata.get("filters_applied", {})
        assert filters_applied.get("tags") == "tech,colleague"
        
        results = data["results"]
        if results:
            # Results should have relevant tags
            for person in results:
                tags = person.get("tags", [])
                if tags:
                    has_relevant_tag = any(tag in ["tech", "colleague"] for tag in tags)
                    assert has_relevant_tag, f"Person should have relevant tags: {tags}"
    
    @pytest.mark.search
    def test_search_persons_pagination(self):
        """Test search pagination functionality."""
        if not self.config.test_search:
            pytest.skip("Search tests disabled")
        
        # Search with small limit
        response = self.client.get(
            "/api/v1/search/persons?q=&limit=2&offset=0",
            headers=self.auth_headers
        )
        
        data = assert_successful_json_response(response)
        pagination = data["pagination"]
        
        # Verify pagination fields
        assert pagination["limit"] == 2
        assert pagination["offset"] == 0
        assert isinstance(pagination["total"], int)
        assert isinstance(pagination["has_more"], bool)
        
        # If there are more than 2 results, has_more should be True
        if pagination["total"] > 2:
            assert pagination["has_more"] == True
        
        # Test second page
        if pagination["has_more"]:
            response2 = self.client.get(
                "/api/v1/search/persons?q=&limit=2&offset=2",
                headers=self.auth_headers
            )
            
            data2 = assert_successful_json_response(response2)
            pagination2 = data2["pagination"]
            
            assert pagination2["offset"] == 2
            assert pagination2["total"] == pagination["total"]  # Total should be same
    
    @pytest.mark.search
    def test_search_persons_empty_query(self):
        """Test search with empty query returns all persons."""
        if not self.config.test_search:
            pytest.skip("Search tests disabled")
        
        # Search with empty query
        response = self.client.get(
            "/api/v1/search/persons?q=&limit=20",
            headers=self.auth_headers
        )
        
        data = assert_successful_json_response(response)
        
        # Should return results (all persons)
        assert "results" in data
        assert isinstance(data["results"], list)
        
        # Metadata should reflect empty query
        metadata = data["metadata"]
        assert metadata["query"] == ""
    
    @pytest.mark.search
    def test_search_persons_no_results(self):
        """Test search with query that returns no results."""
        if not self.config.test_search:
            pytest.skip("Search tests disabled")
        
        # Search for something that doesn't exist
        response = self.client.get(
            "/api/v1/search/persons?q=NonexistentPerson12345&limit=10",
            headers=self.auth_headers
        )
        
        data = assert_successful_json_response(response)
        
        # Should return empty results
        assert data["results"] == []
        assert data["pagination"]["total"] == 0
        assert data["pagination"]["has_more"] == False
        
        # Metadata should reflect the query
        assert data["metadata"]["query"] == "NonexistentPerson12345"
    
    @pytest.mark.search
    def test_search_persons_response_time(self):
        """Test search response time performance."""
        if not self.config.test_search or not self.config.test_performance:
            pytest.skip("Performance tests disabled")
        
        # Search for common term
        response = self.client.get(
            "/api/v1/search/persons?q=engineer&limit=20",
            headers=self.auth_headers
        )
        
        data = assert_successful_json_response(response)
        
        # Verify response time is reasonable (under 2 seconds)
        ResponseAssertions.assert_response_time(response, max_time=2.0)
        
        # Verify response structure is correct
        assert "results" in data
        assert "pagination" in data
        assert "metadata" in data
