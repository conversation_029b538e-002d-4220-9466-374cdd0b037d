"""
HTTP client for interface testing with authentication and retry logic.
"""
import time
import json
import logging
from typing import Dict, <PERSON>, Op<PERSON>, Tuple, Union
from dataclasses import dataclass
import httpx
from .config import get_config, TestUserConfig


@dataclass
class TestResponse:
    """Test response wrapper with additional utilities."""
    status_code: int
    headers: Dict[str, str]
    content: bytes
    url: str
    elapsed: float
    
    def json(self) -> Dict[str, Any]:
        """Parse response as JSON."""
        try:
            return json.loads(self.content.decode())
        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            raise ValueError(f"Invalid JSON response: {e}")
    
    def text(self) -> str:
        """Get response as text."""
        return self.content.decode()
    
    @property
    def is_success(self) -> bool:
        """Check if response indicates success (2xx status)."""
        return 200 <= self.status_code < 300
    
    @property
    def is_error(self) -> bool:
        """Check if response indicates an error (4xx or 5xx status)."""
        return self.status_code >= 400


class APIClient:
    """HTTP client for testing Nexus backend APIs."""
    
    def __init__(self, base_url: Optional[str] = None):
        """Initialize API client with configuration."""
        self.config = get_config()
        self.base_url = base_url or self.config.api.base_url
        self.session = httpx.Client(
            timeout=self.config.api.timeout,
            verify=self.config.api.verify_ssl
        )
        self.authenticated_users: Dict[str, Dict[str, Any]] = {}
        self.logger = logging.getLogger(__name__)
        
    def __enter__(self):
        """Context manager entry."""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        self.close()
        
    def close(self):
        """Close the HTTP session."""
        if hasattr(self, 'session'):
            self.session.close()
    
    def _make_request(
        self,
        method: str,
        endpoint: str,
        headers: Optional[Dict[str, str]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        data: Optional[Union[str, bytes]] = None,
        params: Optional[Dict[str, Any]] = None,
        files: Optional[Dict[str, Any]] = None
    ) -> TestResponse:
        """Make HTTP request with retry logic and timing."""
        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        # Combine session headers with request-specific headers
        request_headers = {**self.session.headers, **(headers or {})}
        
        # Set Content-Type if not already set and there's JSON data
        if "Content-Type" not in request_headers and json_data is not None:
            request_headers["Content-Type"] = "application/json"

        # Prepare request kwargs
        kwargs = {
            "headers": request_headers,
            "params": params
        }
        
        if json_data is not None:
            kwargs["json"] = json_data
        elif data is not None:
            kwargs["content"] = data
            
        if files is not None:
            # Remove Content-Type for file uploads
            if "Content-Type" in request_headers:
                del request_headers["Content-Type"]
            kwargs["files"] = files
        
        # Retry logic
        last_exception = None
        for attempt in range(self.config.api.max_retries + 1):
            try:
                start_time = time.time()
                
                if self.config.verbose_logging:
                    self.logger.info(f"Making {method} request to {url}")
                    if json_data and self.logger.isEnabledFor(logging.DEBUG):
                        self.logger.debug(f"Request data: {json.dumps(json_data, indent=2)}")
                
                response = self.session.request(method, url, **kwargs)
                elapsed = time.time() - start_time
                
                if self.config.verbose_logging:
                    self.logger.info(f"Response: {response.status_code} in {elapsed:.3f}s")
                    if response.status_code >= 400 and self.logger.isEnabledFor(logging.DEBUG):
                        self.logger.debug(f"Error response: {response.text}")
                
                return TestResponse(
                    status_code=response.status_code,
                    headers=dict(response.headers),
                    content=response.content,
                    url=str(response.url),
                    elapsed=elapsed
                )
                
            except Exception as e:
                last_exception = e
                if attempt < self.config.api.max_retries:
                    self.logger.warning(f"Request failed (attempt {attempt + 1}), retrying: {e}")
                    time.sleep(self.config.api.retry_delay)
                else:
                    self.logger.error(f"Request failed after {self.config.api.max_retries + 1} attempts: {e}")
        
        raise last_exception or Exception("Request failed")
    
    def get(self, endpoint: str, headers: Optional[Dict[str, str]] = None, 
            params: Optional[Dict[str, Any]] = None) -> TestResponse:
        """Make GET request."""
        return self._make_request("GET", endpoint, headers=headers, params=params)
    
    def post(self, endpoint: str, json_data: Optional[Dict[str, Any]] = None,
             headers: Optional[Dict[str, str]] = None, data: Optional[Union[str, bytes]] = None,
             files: Optional[Dict[str, Any]] = None) -> TestResponse:
        """Make POST request."""
        return self._make_request("POST", endpoint, headers=headers, json_data=json_data, 
                                data=data, files=files)
    
    def put(self, endpoint: str, json_data: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None) -> TestResponse:
        """Make PUT request."""
        return self._make_request("PUT", endpoint, headers=headers, json_data=json_data)
    
    def patch(self, endpoint: str, json_data: Optional[Dict[str, Any]] = None,
              headers: Optional[Dict[str, str]] = None) -> TestResponse:
        """Make PATCH request."""
        return self._make_request("PATCH", endpoint, headers=headers, json_data=json_data)
    
    def delete(self, endpoint: str, headers: Optional[Dict[str, str]] = None) -> TestResponse:
        """Make DELETE request."""
        return self._make_request("DELETE", endpoint, headers=headers)
    
    def register_user(self, user_data: Dict[str, Any]) -> TestResponse:
        """Register a new user."""
        # Make email unique by adding timestamp if it's a test email
        if user_data.get("email", "").endswith("@example.com"):
            import time
            timestamp = str(int(time.time() * 1000))  # milliseconds for uniqueness
            base_email = user_data["email"].replace("@example.com", "")
            user_data["email"] = f"{base_email}_{timestamp}@example.com"

        # Use local registration endpoint for testing to avoid Supabase email restrictions
        return self.post("/api/v1/auth/register-local", json_data=user_data)
    
    def login_user(self, email: str, password: str) -> TestResponse:
        """Login user and get authentication token."""
        login_data = {"email": email, "password": password}
        # Use local login endpoint for testing to avoid Supabase issues
        return self.post("/api/v1/auth/login-local", json_data=login_data)

    def logout_user(self, headers: Dict[str, str]) -> TestResponse:
        """Logout user and invalidate token."""
        # Use local logout endpoint for testing
        return self.post("/api/v1/auth/logout-local", headers=headers)
    
    def authenticate_user(self, user_key: str = "user1") -> Tuple[Dict[str, Any], Dict[str, str]]:
        """Authenticate a test user and return user data and auth headers."""
        if user_key in self.authenticated_users:
            return self.authenticated_users[user_key]["user_data"], self.authenticated_users[user_key]["headers"]
        
        if user_key not in self.config.test_users:
            raise ValueError(f"Unknown test user: {user_key}")
        
        user_config = self.config.test_users[user_key]
        
        # Register user if needed
        if self.config.create_test_users:
            user_data = {
                "email": user_config.email,
                "password": user_config.password,
                "first_name": user_config.first_name,
                "last_name": user_config.last_name
            }
            
            register_response = self.register_user(user_data)
            if register_response.status_code not in [200, 201, 409]:  # 409 = already exists
                raise Exception(f"Failed to register user {user_key}: {register_response.text()}")
        
        # Login user
        login_response = self.login_user(user_config.email, user_config.password)
        if not login_response.is_success:
            raise Exception(f"Failed to login user {user_key}: {login_response.text()}")
        
        login_data = login_response.json()
        auth_headers = {"Authorization": f"Bearer {login_data['access_token']}"}
        
        # Store authentication info
        self.authenticated_users[user_key] = {
            "user_data": login_data,
            "headers": auth_headers,
            "config": user_config
        }
        
        return login_data, auth_headers
    
    def get_auth_headers(self, user_key: str = "user1") -> Dict[str, str]:
        """Get authentication headers for a user, authenticating if needed."""
        _, headers = self.authenticate_user(user_key)
        return headers
    
    def cleanup_test_data(self, user_key: str = "user1") -> None:
        """Clean up test data for a user using the admin reset endpoint."""
        if not self.config.cleanup_data:
            return

        try:
            headers = self.get_auth_headers(user_key)

            # Use the admin reset endpoint to clean up all user data
            response = self.post("/api/v1/admin/reset-test-data", headers=headers)
            if response.is_success:
                self.logger.info(f"Successfully cleaned up test data for user {user_key}")
            else:
                self.logger.warning(f"Failed to cleanup test data for user {user_key}: {response.status_code} - {response.text}")
        except Exception as e:
            self.logger.warning(f"Failed to cleanup test data for user {user_key}: {e}")
    
    def health_check(self) -> bool:
        """Check if the API is healthy and responsive."""
        try:
            # Try a simple endpoint that doesn't require authentication
            response = self.get("/health", headers={})
            return response.is_success
        except Exception:
            # If /health doesn't exist, try root endpoint
            try:
                response = self.get("/", headers={})
                return response.status_code != 404
            except Exception:
                return False
    
    def wait_for_api(self, max_wait: int = 60) -> bool:
        """Wait for API to become available."""
        start_time = time.time()
        while time.time() - start_time < max_wait:
            if self.health_check():
                return True
            time.sleep(1)
        return False


def create_client(base_url: Optional[str] = None, token: Optional[str] = None) -> APIClient:
    """
    Create a new API client instance, with an optional authentication token.
    
    Args:
        base_url: The base URL for the API.
        token: An optional JWT token for authentication.
        
    Returns:
        An instance of APIClient.
    """
    client = APIClient(base_url)
    if token:
        client.session.headers["Authorization"] = f"Bearer {token}"
    return client
