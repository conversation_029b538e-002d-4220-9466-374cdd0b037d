"""
Interface test configuration module.
Provides configurable settings for testing the Nexus backend APIs.
"""
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, field, is_dataclass
from enum import Enum


class Environment(str, Enum):
    """Test environment types."""
    LOCAL = "local"
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


@dataclass
class APIConfig:
    """API endpoint configuration."""
    base_url: str
    timeout: int = 30
    verify_ssl: bool = True
    max_retries: int = 3
    retry_delay: float = 1.0


@dataclass
class TestUserConfig:
    """Test user configuration."""
    email: str
    password: str
    first_name: str = "Test"
    last_name: str = "User"


@dataclass
class TestConfig:
    """Main configuration for interface tests."""
    # Environment settings
    environment: Environment = Environment.LOCAL
    
    # API configuration
    api: APIConfig = field(default_factory=lambda: APIConfig(base_url="http://localhost:8010"))
    
    # Test data settings
    cleanup_data: bool = True
    create_test_users: bool = True
    use_existing_data: bool = False
    
    # Test execution settings
    parallel_execution: bool = False
    test_timeout: int = 300
    verbose_logging: bool = True
    
    # Performance test settings
    load_test_users: int = 10
    max_response_time: float = 2.0
    
    # Test user credentials
    test_users: Dict[str, TestUserConfig] = field(default_factory=dict)
    
    # Feature flags for test execution
    test_auth: bool = True
    test_crud: bool = True
    test_ai_features: bool = True
    test_search: bool = True
    test_integrations: bool = False  # External services
    test_performance: bool = False
    
    # Mock settings
    mock_ai_services: bool = True
    mock_external_apis: bool = True
    
    def __post_init__(self):
        """Initialize default test users if none provided."""
        if not self.test_users:
            self.test_users = {
                "user1": TestUserConfig(
                    email="<EMAIL>",
                    password="TestPassword123!",
                    first_name="Test",
                    last_name="User1"
                ),
                "user2": TestUserConfig(
                    email="<EMAIL>",
                    password="TestPassword123!",
                    first_name="Test",
                    last_name="User2"
                ),
                "admin": TestUserConfig(
                    email="<EMAIL>",
                    password="AdminPassword123!",
                    first_name="Admin",
                    last_name="Test"
                )
            }


def load_config() -> TestConfig:
    """Load test configuration from environment variables and defaults."""
    # Determine environment
    env_name = os.getenv("TEST_ENV", "local").lower()
    try:
        environment = Environment(env_name)
    except ValueError:
        environment = Environment.LOCAL
    
    # Load API configuration
    api_config = APIConfig(
        base_url=os.getenv("API_BASE_URL", "http://localhost:8010"),
        timeout=int(os.getenv("API_TIMEOUT", "30")),
        verify_ssl=os.getenv("VERIFY_SSL", "true").lower() == "true",
        max_retries=int(os.getenv("MAX_RETRIES", "3")),
        retry_delay=float(os.getenv("RETRY_DELAY", "1.0"))
    )
    
    # Load test execution settings
    config = TestConfig(
        environment=environment,
        api=api_config,
        cleanup_data=os.getenv("CLEANUP_DATA", "true").lower() == "true",
        create_test_users=os.getenv("CREATE_TEST_USERS", "true").lower() == "true",
        use_existing_data=os.getenv("USE_EXISTING_DATA", "false").lower() == "true",
        parallel_execution=os.getenv("PARALLEL_EXECUTION", "false").lower() == "true",
        test_timeout=int(os.getenv("TEST_TIMEOUT", "300")),
        verbose_logging=os.getenv("VERBOSE_LOGGING", "true").lower() == "true",
        load_test_users=int(os.getenv("LOAD_TEST_USERS", "10")),
        max_response_time=float(os.getenv("MAX_RESPONSE_TIME", "2.0")),
        test_auth=os.getenv("TEST_AUTH", "true").lower() == "true",
        test_crud=os.getenv("TEST_CRUD", "true").lower() == "true",
        test_ai_features=os.getenv("TEST_AI_FEATURES", "true").lower() == "true",
        test_search=os.getenv("TEST_SEARCH", "true").lower() == "true",
        test_integrations=os.getenv("TEST_INTEGRATIONS", "false").lower() == "true",
        test_performance=os.getenv("TEST_PERFORMANCE", "false").lower() == "true",
        mock_ai_services=os.getenv("MOCK_AI_SERVICES", "true").lower() == "true",
        mock_external_apis=os.getenv("MOCK_EXTERNAL_APIS", "true").lower() == "true"
    )
    
    # Override test user credentials from environment if provided
    
    for user_key in ["user1", "user2", "admin"]:
        email_env = f"TEST_{user_key.upper()}_EMAIL"
        password_env = f"TEST_{user_key.upper()}_PASSWORD"
        
        if os.getenv(email_env) and os.getenv(password_env):
            config.test_users[user_key].email = os.getenv(email_env)
            config.test_users[user_key].password = os.getenv(password_env)
    
    return config


# Global configuration instance
CONFIG = load_config()


def get_config() -> TestConfig:
    """Get the current test configuration."""
    return CONFIG


def update_config(**kwargs) -> None:
    """Update configuration parameters recursively."""
    global CONFIG

    def _update_recursive(config_obj, updates: Dict[str, Any]):
        for key, value in updates.items():
            if not hasattr(config_obj, key):
                continue

            current_value = getattr(config_obj, key)
            if is_dataclass(current_value) and isinstance(value, dict):
                _update_recursive(current_value, value)
            else:
                setattr(config_obj, key, value)

    _update_recursive(CONFIG, kwargs)


# Environment-specific configurations
ENVIRONMENT_CONFIGS = {
    Environment.LOCAL: {
    "api_base_url": "http://localhost:8010",
    "cleanup_data": True,
    "mock_ai_services": True,
    "mock_external_apis": True,
    "test_integrations": False,
    "verbose_logging": True
},
Environment.DEVELOPMENT: {
    "api_base_url": "https://dev-api.nexus.example.com",
        "cleanup_data": True,
        "mock_ai_services": False,
        "mock_external_apis": True,
        "test_integrations": True,
        "verbose_logging": True
    },
    Environment.STAGING: {
    "api_base_url": "https://staging-api.nexus.example.com",
    "cleanup_data": False,
    "mock_ai_services": False,
    "mock_external_apis": False,
    "test_integrations": True,
    "verbose_logging": False
},
Environment.PRODUCTION: {
    "api_base_url": "https://api.nexus.example.com",
        "cleanup_data": False,
        "mock_ai_services": False,
        "mock_external_apis": False,
        "test_integrations": False,
        "verbose_logging": False,
        "test_performance": True
    }
}


def apply_environment_config(environment: Environment) -> None:
    """Apply environment-specific configuration overrides."""
    if environment in ENVIRONMENT_CONFIGS:
        env_config = ENVIRONMENT_CONFIGS[environment]
        for key, value in env_config.items():
            if hasattr(CONFIG, key):
                setattr(CONFIG, key, value)
            elif hasattr(CONFIG.api, key.replace("api_", "")):
                setattr(CONFIG.api, key.replace("api_", ""), value)
