"""
Goals and Tasks API endpoint tests.
Tests CRUD operations for goals and tasks, task-goal relationships, and status management.
"""
import pytest
from typing import Dict, Any, List
from .client import APIClient, create_client
from .assertions import (
    assert_successful_json_response, 
    assert_error_json_response,
    assert_valid_goal_response,
    assert_valid_task_response,
    assert_valid_paginated_response,
    ResponseAssertions
)
from .config import get_config


class TestGoalsAPI:
    """Test goals API functionality."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and configuration."""
        self.config = get_config()
        self.client = create_client()
        self.auth_headers = {}
        
        if self.config.test_crud:
            # Authenticate user for tests
            _, self.auth_headers = self.client.authenticate_user("user1")
        
        yield
        
        # Cleanup if enabled
        if self.config.cleanup_data:
            self.client.cleanup_test_data("user1")
        self.client.close()
    
    def create_test_goal(self, **overrides) -> Dict[str, Any]:
        """Create a test goal and return the response data."""
        goal_data = {
            "title": "Learn Machine Learning",
            "description": "Master ML concepts and apply to real projects",
            "status": "active",
            "target_date": "2024-12-31T23:59:59Z",
            "priority": 1,
            "category": "career"
        }
        goal_data.update(overrides)
        
        response = self.client.post("/api/v1/goals", json_data=goal_data, headers=self.auth_headers)
        return assert_valid_goal_response(response)
    
    def test_create_goal_success(self):
        """Test successful goal creation."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        goal_data = {
            "title": "Build Network in AI",
            "description": "Connect with 10 AI professionals this quarter",
            "status": "active",
            "priority": 2,
            "category": "networking"
        }
        
        response = self.client.post("/api/v1/goals", json_data=goal_data, headers=self.auth_headers)
        goal = assert_valid_goal_response(response)
        
        # Verify goal data
        assert goal["title"] == goal_data["title"]
        assert goal["description"] == goal_data["description"]
        assert goal["status"] == goal_data["status"]
        assert goal["priority"] == goal_data["priority"]
        assert goal["category"] == goal_data["category"]
    
    def test_create_goal_minimal_data(self):
        """Test creating goal with minimal required data."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        minimal_data = {
            "title": "Minimal Goal",
            "status": "active"
        }
        
        response = self.client.post("/api/v1/goals", json_data=minimal_data, headers=self.auth_headers)
        goal = assert_valid_goal_response(response)
        
        assert goal["title"] == minimal_data["title"]
        assert goal["status"] == minimal_data["status"]
    
    def test_create_goal_invalid_data(self):
        """Test goal creation with invalid data."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        invalid_cases = [
            # Missing required fields
            {"description": "Missing title"},  # Missing title
            {"title": "Missing status"},       # Missing status (if required)
            {},                                # Missing both
            
            # Empty required fields
            {"title": "", "status": "active"},
            
            # Invalid status
            {"title": "Valid Title", "status": "invalid_status"},
            
            # Invalid priority
            {"title": "Valid Title", "status": "active", "priority": "not_a_number"},
            
            # Invalid date format
            {"title": "Valid Title", "status": "active", "target_date": "not-a-date"}
        ]
        
        for invalid_data in invalid_cases:
            response = self.client.post("/api/v1/goals", json_data=invalid_data, headers=self.auth_headers)
            assert_error_json_response(response, 422)  # Validation error
    
    def test_get_goal_by_id(self):
        """Test retrieving a goal by ID."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create a goal first
        created_goal = self.create_test_goal()
        goal_id = created_goal["goal_id"]
        
        # Retrieve the goal
        response = self.client.get(f"/api/v1/goals/{goal_id}", headers=self.auth_headers)
        goal = assert_valid_goal_response(response)
        
        # Verify data matches
        assert goal["goal_id"] == goal_id
        assert goal["title"] == created_goal["title"]
        assert goal["status"] == created_goal["status"]
    
    def test_update_goal(self):
        """Test updating goal information."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create a goal first
        created_goal = self.create_test_goal()
        goal_id = created_goal["goal_id"]
        
        # Update the goal
        update_data = {
            "title": "Updated ML Goal",
            "description": "Focus on deep learning and neural networks",
            "status": "paused",
            "priority": 3
        }
        
        response = self.client.put(f"/api/v1/goals/{goal_id}", 
                                  json_data=update_data, headers=self.auth_headers)
        updated_goal = assert_valid_goal_response(response)
        
        # Verify updates
        assert updated_goal["title"] == update_data["title"]
        assert updated_goal["description"] == update_data["description"]
        assert updated_goal["status"] == update_data["status"]
        assert updated_goal["priority"] == update_data["priority"]
    
    def test_delete_goal(self):
        """Test deleting a goal."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create a goal first
        created_goal = self.create_test_goal()
        goal_id = created_goal["goal_id"]
        
        # Delete the goal
        response = self.client.delete(f"/api/v1/goals/{goal_id}", headers=self.auth_headers)
        ResponseAssertions.assert_status_code(response, 204)  # No content
        
        # Verify goal is deleted
        get_response = self.client.get(f"/api/v1/goals/{goal_id}", headers=self.auth_headers)
        assert_error_json_response(get_response, 404)
    
    def test_list_goals(self):
        """Test listing goals with pagination."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create multiple goals
        goals = []
        for i in range(3):
            goal = self.create_test_goal(
                title=f"Goal {i}",
                description=f"Description for goal {i}",
                priority=i + 1
            )
            goals.append(goal)
        
        # List goals
        response = self.client.get("/api/v1/goals", headers=self.auth_headers)
        
        if response.status_code == 200:
            results = assert_successful_json_response(response)
            
            # Check if it's paginated or direct array
            if "items" in results:
                paginated = assert_valid_paginated_response(response)
                assert len(paginated["items"]) >= 3
                items = paginated["items"]
            else:
                items = results
                assert len(items) >= 3
            
            # Verify each item is a valid goal
            for item in items:
                assert "goal_id" in item
                assert "title" in item
                assert "status" in item


class TestTasksAPI:
    """Test tasks API functionality."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and configuration."""
        self.config = get_config()
        self.client = create_client()
        self.auth_headers = {}
        
        if self.config.test_crud:
            # Authenticate user for tests
            _, self.auth_headers = self.client.authenticate_user("user1")
        
        yield
        
        # Cleanup if enabled
        if self.config.cleanup_data:
            self.client.cleanup_test_data("user1")
        self.client.close()
    
    def create_test_goal(self) -> Dict[str, Any]:
        """Create a test goal for task association."""
        goal_data = {
            "title": "Test Goal for Tasks",
            "status": "active"
        }
        response = self.client.post("/api/v1/goals", json_data=goal_data, headers=self.auth_headers)
        return assert_valid_goal_response(response)
    
    def create_test_task(self, goal_id: str = None, **overrides) -> Dict[str, Any]:
        """Create a test task and return the response data."""
        task_data = {
            "title": "Complete ML course",
            "description": "Finish online machine learning course",
            "priority": 1,
            "is_completed": False,
            "due_date": "2024-08-01T10:00:00Z",
            "estimated_hours": 40
        }
        
        if goal_id:
            task_data["goal_id"] = goal_id
        
        task_data.update(overrides)
        
        response = self.client.post("/api/v1/tasks", json_data=task_data, headers=self.auth_headers)
        return assert_valid_task_response(response)
    
    def test_create_task_success(self):
        """Test successful task creation."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        task_data = {
            "title": "Research AI conferences",
            "description": "Find relevant AI conferences for networking",
            "priority": 2,
            "is_completed": False,
            "due_date": "2024-09-15T12:00:00Z"
        }
        
        response = self.client.post("/api/v1/tasks", json_data=task_data, headers=self.auth_headers)
        task = assert_valid_task_response(response)
        
        # Verify task data
        assert task["title"] == task_data["title"]
        assert task["description"] == task_data["description"]
        assert task["priority"] == task_data["priority"]
        assert task["is_completed"] == task_data["is_completed"]
    
    def test_create_task_with_goal(self):
        """Test creating task associated with a goal."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create a goal first
        goal = self.create_test_goal()
        
        # Create task associated with goal
        task_data = {
            "title": "Read ML textbook",
            "goal_id": goal["goal_id"],
            "priority": 1,
            "is_completed": False
        }
        
        response = self.client.post("/api/v1/tasks", json_data=task_data, headers=self.auth_headers)
        task = assert_valid_task_response(response)
        
        assert task["title"] == task_data["title"]
        assert task["goal_id"] == goal["goal_id"]
    
    def test_create_task_minimal_data(self):
        """Test creating task with minimal required data."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        minimal_data = {
            "title": "Minimal Task",
            "is_completed": False
        }
        
        response = self.client.post("/api/v1/tasks", json_data=minimal_data, headers=self.auth_headers)
        task = assert_valid_task_response(response)
        
        assert task["title"] == minimal_data["title"]
        assert task["is_completed"] == minimal_data["is_completed"]
    
    def test_get_task_by_id(self):
        """Test retrieving a task by ID."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create a task first
        created_task = self.create_test_task()
        task_id = created_task["task_id"]
        
        # Retrieve the task
        response = self.client.get(f"/api/v1/tasks/{task_id}", headers=self.auth_headers)
        task = assert_valid_task_response(response)
        
        # Verify data matches
        assert task["task_id"] == task_id
        assert task["title"] == created_task["title"]
        assert task["is_completed"] == created_task["is_completed"]
    
    def test_update_task(self):
        """Test updating task information."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create a task first
        created_task = self.create_test_task()
        task_id = created_task["task_id"]
        
        # Update the task
        update_data = {
            "title": "Updated ML Course Task",
            "description": "Focus on supervised learning algorithms",
            "priority": 3,
            "is_completed": True
        }
        
        response = self.client.put(f"/api/v1/tasks/{task_id}", 
                                  json_data=update_data, headers=self.auth_headers)
        updated_task = assert_valid_task_response(response)
        
        # Verify updates
        assert updated_task["title"] == update_data["title"]
        assert updated_task["description"] == update_data["description"]
        assert updated_task["priority"] == update_data["priority"]
        assert updated_task["is_completed"] == update_data["is_completed"]
    
    def test_complete_task(self):
        """Test marking a task as completed."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create an incomplete task
        created_task = self.create_test_task(is_completed=False)
        task_id = created_task["task_id"]
        
        # Mark as completed
        update_data = {"is_completed": True}
        response = self.client.put(f"/api/v1/tasks/{task_id}", 
                                  json_data=update_data, headers=self.auth_headers)
        updated_task = assert_valid_task_response(response)
        
        assert updated_task["is_completed"] is True
    
    def test_delete_task(self):
        """Test deleting a task."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create a task first
        created_task = self.create_test_task()
        task_id = created_task["task_id"]
        
        # Delete the task
        response = self.client.delete(f"/api/v1/tasks/{task_id}", headers=self.auth_headers)
        ResponseAssertions.assert_status_code(response, 204)  # No content
        
        # Verify task is deleted
        get_response = self.client.get(f"/api/v1/tasks/{task_id}", headers=self.auth_headers)
        assert_error_json_response(get_response, 404)
    
    def test_list_tasks(self):
        """Test listing tasks with filtering."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create goal and tasks
        goal = self.create_test_goal()
        
        # Create tasks with different states
        completed_task = self.create_test_task(
            goal_id=goal["goal_id"], 
            title="Completed Task", 
            is_completed=True
        )
        incomplete_task = self.create_test_task(
            goal_id=goal["goal_id"], 
            title="Incomplete Task", 
            is_completed=False
        )
        
        # List all tasks
        response = self.client.get("/api/v1/tasks", headers=self.auth_headers)
        results = assert_successful_json_response(response)
        
        if "items" in results:
            items = results["items"]
        else:
            items = results
        
        assert len(items) >= 2
        
        # Test filtering by completion status
        completed_response = self.client.get("/api/v1/tasks?completed=true", headers=self.auth_headers)
        if completed_response.status_code == 200:
            completed_results = assert_successful_json_response(completed_response)
            if "items" in completed_results:
                completed_items = completed_results["items"]
            else:
                completed_items = completed_results
            
            # All returned tasks should be completed
            for task in completed_items:
                assert task["is_completed"] is True
    
    def test_get_goal_dashboard(self):
        """Test getting goal dashboard with associated tasks."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create goal and associated tasks
        goal = self.create_test_goal()
        task1 = self.create_test_task(goal_id=goal["goal_id"], title="Task 1")
        task2 = self.create_test_task(goal_id=goal["goal_id"], title="Task 2", is_completed=True)
        
        # Get goal dashboard
        response = self.client.get(f"/api/v1/goals/{goal['goal_id']}/dashboard", headers=self.auth_headers)
        
        if response.status_code == 501:  # Not implemented yet
            pytest.skip("Goal dashboard not implemented")
        
        dashboard = assert_successful_json_response(response)
        
        # Verify dashboard contains goal and task information
        assert "goal" in dashboard or "goal_id" in dashboard
        # Additional assertions depend on dashboard structure
    
    def test_tasks_goal_relationship_integrity(self):
        """Test that task-goal relationships are maintained correctly."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create goal and task
        goal = self.create_test_goal()
        task = self.create_test_task(goal_id=goal["goal_id"])
        
        # Verify task is associated with goal
        assert task["goal_id"] == goal["goal_id"]
        
        # Delete goal and check what happens to task
        delete_response = self.client.delete(f"/api/v1/goals/{goal['goal_id']}", headers=self.auth_headers)
        ResponseAssertions.assert_status_code(delete_response, 204)
        
        # Check if task still exists (behavior depends on implementation)
        task_response = self.client.get(f"/api/v1/tasks/{task['task_id']}", headers=self.auth_headers)
        if task_response.status_code == 200:
            # Task exists, goal_id should be null or removed
            updated_task = assert_valid_task_response(task_response)
            assert updated_task.get("goal_id") is None or updated_task.get("goal_id") == ""
        else:
            # Task was deleted with goal (cascade delete)
            assert_error_json_response(task_response, 404)
    
    def test_cross_user_goals_tasks_isolation(self):
        """Test that users cannot access each other's goals and tasks."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create goal and task as user1
        user1_goal = self.create_test_goal()
        user1_task = self.create_test_task(goal_id=user1_goal["goal_id"])
        
        # Try to access as user2
        _, user2_headers = self.client.authenticate_user("user2")
        
        goal_response = self.client.get(f"/api/v1/goals/{user1_goal['goal_id']}", headers=user2_headers)
        assert_error_json_response(goal_response, 404)  # Should not be found for user2
        
        task_response = self.client.get(f"/api/v1/tasks/{user1_task['task_id']}", headers=user2_headers)
        assert_error_json_response(task_response, 404)  # Should not be found for user2