# Nexus Backend Interface Tests

A comprehensive test suite for testing the Nexus backend APIs and workflows. This framework provides configurable testing capabilities across different environments with support for authentication, CRUD operations, AI features, and complex workflow testing.

## Features

- **Configurable Environments**: Support for local, development, staging, and production environments
- **Authentication Testing**: Complete user registration, login, and authorization flow testing
- **CRUD Operations**: Comprehensive testing of contacts, goals, tasks, and relationships
- **AI Features**: Testing of copilot chat, suggestions, network analysis, and relationship insights
- **Workflow Integration**: End-to-end testing of complete user journeys and business processes
- **Performance Testing**: Response time validation and load testing capabilities
- **Detailed Reporting**: JSON output and comprehensive test reports

## Quick Start

### Installation

```bash
# Install dependencies
pip install -r interface_tests/requirements.txt

# Ensure the Nexus backend is running (default: http://localhost:8010)
uvicorn app.main:app --host 0.0.0.0 --port 8010 --reload
```

### Basic Usage

```bash
# Run all tests against local environment
python -m interface_tests.test_runner

# Run specific test suite
python -m interface_tests.test_runner --suite auth

# Test against different environment
python -m interface_tests.test_runner --env development --base-url https://dev-api.nexus.com

# Include performance tests
python -m interface_tests.test_runner --performance

# Save results to file
python -m interface_tests.test_runner --output results.json
```

## Configuration

### Environment Variables

```bash
# API Configuration
export API_BASE_URL="http://localhost:8010"
export API_TIMEOUT="30"
export VERIFY_SSL="true"

# Test Configuration  
export TEST_ENV="local"
export CLEANUP_DATA="true"
export VERBOSE_LOGGING="true"

# Feature Flags
export TEST_AUTH="true"
export TEST_CRUD="true"
export TEST_AI_FEATURES="true"
export TEST_PERFORMANCE="false"

# Test User Credentials
export TEST_USER1_EMAIL="<EMAIL>"
export TEST_USER1_PASSWORD="TestPassword123!"
```

### Configuration File

Create a `.env` file in the project root:

```env
API_BASE_URL=http://localhost:8010
TEST_ENV=local
TEST_AUTH=true
TEST_CRUD=true
TEST_AI_FEATURES=true
CLEANUP_DATA=true
VERBOSE_LOGGING=true
```

## Test Structure

### Core Components

- **`config.py`**: Environment and test configuration management
- **`client.py`**: HTTP client with authentication and retry logic
- **`assertions.py`**: Validation utilities and response assertions
- **`test_runner.py`**: Test execution and reporting framework

### Test Modules

- **`test_auth_api.py`**: Authentication and authorization tests
- **`test_persons_api.py`**: Contact/person management tests
- **`test_goals_tasks_api.py`**: Goal and task management tests
- **`test_ai_features_api.py`**: AI features (copilot, suggestions, analysis)
- **`test_workflow_integration.py`**: End-to-end workflow tests

### Test Categories

Tests are organized using pytest markers:

- `@pytest.mark.auth` - Authentication tests
- `@pytest.mark.crud` - CRUD operation tests
- `@pytest.mark.ai` - AI functionality tests
- `@pytest.mark.integration` - Workflow integration tests
- `@pytest.mark.performance` - Performance tests

## Usage Examples

### Programmatic Usage

```python
from interface_tests import InterfaceTestRunner, get_config

# Basic test execution
runner = InterfaceTestRunner()
results = runner.run_all_tests()

# Custom configuration
config_overrides = {
    "api": {"base_url": "https://api.example.com"},
    "test_ai_features": False,
    "verbose_logging": True
}
runner = InterfaceTestRunner(config_overrides)
auth_results = runner.run_authentication_tests()

# Access configuration
config = get_config()
print(f"Testing against: {config.api.base_url}")
```

### Direct API Testing

```python
from interface_tests import create_client, assert_valid_user_response

# Create authenticated client
with create_client() as client:
    # Authenticate user
    user_data, auth_headers = client.authenticate_user("user1")
    
    # Test API endpoint
    response = client.get("/api/v1/users/me", headers=auth_headers)
    user = assert_valid_user_response(response)
    
    print(f"Authenticated as: {user['email']}")
```

### Custom Test Cases

```python
import pytest
from interface_tests import create_client, assert_successful_json_response

class TestCustomWorkflow:
    @pytest.fixture(autouse=True)
    def setup(self):
        self.client = create_client()
        _, self.auth_headers = self.client.authenticate_user("user1")
        yield
        self.client.close()
    
    def test_custom_business_logic(self):
        # Create test data
        person_data = {
            "first_name": "Test",
            "last_name": "Person",
            "contact_info": {"email": "<EMAIL>"}
        }
        
        response = self.client.post("/api/v1/persons", 
                                   json_data=person_data, 
                                   headers=self.auth_headers)
        person = assert_successful_json_response(response)
        
        # Verify business logic
        assert person["first_name"] == "Test"
        assert "person_id" in person
```

## Environment-Specific Testing

### Local Development

```bash
# Test against local server
python -m interface_tests.test_runner --env local

# With custom configuration
TEST_ENV=local API_BASE_URL=http://localhost:8010 python -m interface_tests.test_runner
```

### Staging Environment

```bash
# Test against staging
python -m interface_tests.test_runner --env staging --base-url https://staging-api.nexus.com

# Skip tests that modify data
python -m interface_tests.test_runner --env staging --no-crud
```

### Production Monitoring

```bash
# Read-only tests against production
python -m interface_tests.test_runner --env production --suite auth --no-crud
```

## Test Data Management

### Test Users

The framework automatically manages test users:

- **user1**: Primary test user for most operations
- **user2**: Secondary user for isolation testing
- **admin**: Administrative user for privileged operations

### Data Cleanup

```python
# Automatic cleanup (default)
config = {"cleanup_data": True}

# Manual cleanup
with create_client() as client:
    client.cleanup_test_data("user1")
```

### Test Isolation

Each test user operates in isolation:
- Users cannot access each other's data
- Cross-user contamination is prevented
- Parallel test execution is supported

## Performance Testing

### Response Time Validation

```python
from interface_tests import ResponseAssertions

# Automatic response time checking
response = client.get("/api/v1/persons", headers=auth_headers)
ResponseAssertions.assert_response_time(response, max_time=2.0)
```

### Load Testing

```bash
# Enable performance tests
python -m interface_tests.test_runner --performance

# Custom performance criteria
MAX_RESPONSE_TIME=1.0 python -m interface_tests.test_runner --performance
```

## Troubleshooting

### Common Issues

1. **API Server Not Available**
   ```bash
   # Check server status
   curl http://localhost:8010/health

   # Start the server
   uvicorn app.main:app --host 0.0.0.0 --port 8010 --reload
   ```

2. **Authentication Failures**
   ```bash
   # Check test user configuration
   echo $TEST_USER1_EMAIL
   echo $TEST_USER1_PASSWORD
   
   # Reset test users
   CLEANUP_DATA=true python -m interface_tests.test_runner --suite auth
   ```

3. **Test Timeouts**
   ```bash
   # Increase timeout
   API_TIMEOUT=60 python -m interface_tests.test_runner
   ```

### Debug Mode

```bash
# Enable verbose logging
VERBOSE_LOGGING=true python -m interface_tests.test_runner

# Run specific test with pytest directly
python -m pytest interface_tests/test_auth_api.py::TestAuthenticationAPI::test_user_login_success -v -s
```

### Configuration Validation

```python
from interface_tests import get_config

config = get_config()
print(f"Environment: {config.environment}")
print(f"API URL: {config.api.base_url}")
print(f"Auth enabled: {config.test_auth}")
print(f"AI enabled: {config.test_ai_features}")
```

## Contributing

### Adding New Tests

1. **Create test module**: Follow naming pattern `test_*_api.py`
2. **Use base classes**: Inherit from appropriate test base classes
3. **Add markers**: Use pytest markers for categorization
4. **Follow patterns**: Use existing assertion and client patterns

### Test Organization

```python
class TestNewFeatureAPI:
    @pytest.fixture(autouse=True)
    def setup(self):
        self.config = get_config()
        self.client = create_client()
        # Setup code
        yield
        # Cleanup code
        self.client.close()
    
    @pytest.mark.crud
    def test_new_feature_crud(self):
        # Test implementation
        pass
    
    @pytest.mark.performance
    def test_new_feature_performance(self):
        # Performance test implementation
        pass
```

### Extending Configuration

```python
# Add new configuration options in config.py
@dataclass
class TestConfig:
    # Existing fields...
    test_new_feature: bool = True
    new_feature_timeout: int = 30
```

## API Reference

### Configuration Classes

- `TestConfig`: Main configuration container
- `APIConfig`: API connection settings  
- `TestUserConfig`: Test user credentials
- `Environment`: Environment enumeration

### Client Classes

- `APIClient`: Main HTTP client with authentication
- `TestResponse`: Response wrapper with utilities

### Assertion Functions

- `assert_successful_json_response()`: Validate successful JSON responses
- `assert_error_json_response()`: Validate error responses
- `assert_valid_*_response()`: Validate specific entity responses
- `ResponseAssertions`: Response validation utilities
- `DataValidation`: Data format validation
- `StructureValidator`: Complex structure validation

## License

This test framework is part of the Nexus project and follows the same licensing terms.