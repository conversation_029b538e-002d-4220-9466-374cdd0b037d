"""
Nexus Backend Interface Tests

A comprehensive test suite for testing the Nexus backend APIs and workflows.
Provides configurable testing framework with support for different environments.

Key Components:
- config.py: Configuration management for different test environments
- client.py: HTTP client with authentication and retry logic
- assertions.py: Test assertions and validation utilities
- test_*.py: Test modules for different API areas
- test_runner.py: Test execution and reporting utilities

Usage:
    from interface_tests import create_client, get_config
    from interface_tests.test_runner import InterfaceTestRunner
    
    # Run all tests
    runner = InterfaceTestRunner()
    results = runner.run_all_tests()
    
    # Run specific test suite
    auth_results = runner.run_authentication_tests()
"""

from .config import get_config, update_config, Environment, TestConfig
from .client import create_client, APIClient, TestResponse
from .assertions import (
    assert_successful_json_response,
    assert_error_json_response,
    assert_valid_user_response,
    assert_valid_person_response,
    assert_valid_goal_response,
    assert_valid_task_response,
    ResponseAssertions,
    DataValidation,
    StructureValidator
)

__version__ = "1.0.0"
__author__ = "Nexus Development Team"

__all__ = [
    # Configuration
    "get_config",
    "update_config", 
    "Environment",
    "TestConfig",
    
    # HTTP Client
    "create_client",
    "APIClient",
    "TestResponse",
    
    # Assertions
    "assert_successful_json_response",
    "assert_error_json_response",
    "assert_valid_user_response",
    "assert_valid_person_response", 
    "assert_valid_goal_response",
    "assert_valid_task_response",
    "ResponseAssertions",
    "DataValidation",
    "StructureValidator"
]