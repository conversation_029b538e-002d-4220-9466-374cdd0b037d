"""
Workflow integration tests.
Tests complete user journeys and business processes across multiple API endpoints.
"""
import pytest
from typing import Dict, Any, List
from .client import APIClient, create_client
from .assertions import (
    assert_successful_json_response, 
    assert_error_json_response,
    assert_valid_user_response,
    assert_valid_person_response,
    assert_valid_goal_response,
    assert_valid_task_response,
    ResponseAssertions
)
from .config import get_config


@pytest.mark.integration
class TestUserOnboardingWorkflow:
    """Test complete user onboarding workflow."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and configuration."""
        self.config = get_config()
        self.client = create_client()
        yield
        self.client.close()
    
    def test_complete_user_onboarding(self):
        """Test complete user onboarding from registration to first use."""
        if not self.config.test_auth or not self.config.test_crud:
            pytest.skip("Auth or CRUD tests disabled")
        
        # Step 1: User Registration
        user_data = {
            "email": "<EMAIL>",
            "password": "SecurePassword123!",
            "first_name": "Onboarding",
            "last_name": "Test"
        }
        
        register_response = self.client.register_user(user_data)
        user = assert_valid_user_response(register_response)
        assert user["email"] == user_data["email"]
        
        # Step 2: User Login
        login_response = self.client.login_user(user_data["email"], user_data["password"])
        login_data = assert_successful_json_response(login_response)
        auth_headers = {"Authorization": f"Bearer {login_data['access_token']}"}
        
        # Step 3: Get User Profile
        profile_response = self.client.get("/api/v1/users/me", headers=auth_headers)
        profile = assert_valid_user_response(profile_response)
        assert profile["user_id"] == user["user_id"]
        
        # Step 4: Create First Contacts
        contacts = [
            {
                "first_name": "Alice",
                "last_name": "Johnson", 
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {"company": "TechCorp", "title": "Engineering Manager"}
            },
            {
                "first_name": "Bob",
                "last_name": "Smith",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {"company": "InnovateCorp", "title": "Product Manager"}
            }
        ]
        
        created_contacts = []
        for contact_data in contacts:
            contact_response = self.client.post("/api/v1/persons/", json_data=contact_data, headers=auth_headers)
            contact = assert_valid_person_response(contact_response)
            created_contacts.append(contact)
        
        # Step 5: Create First Goal
        goal_data = {
            "title": "Expand Professional Network",
            "description": "Connect with 10 new professionals in my field",
            "status": "active",
            "category": "networking"
        }
        
        goal_response = self.client.post("/api/v1/goals/", json_data=goal_data, headers=auth_headers)
        goal = assert_valid_goal_response(goal_response)
        
        # Step 6: Create Tasks for Goal
        task_data = {
            "title": "Reach out to Alice about collaboration",
            "goal_id": goal["goal_id"],
            "priority": 1,
            "is_completed": False
        }
        
        task_response = self.client.post("/api/v1/tasks/", json_data=task_data, headers=auth_headers)
        task = assert_valid_task_response(task_response)
        assert task["goal_id"] == goal["goal_id"]
        
        # Step 7: Verify Data Consistency
        # List contacts
        contacts_response = self.client.get("/api/v1/persons/", headers=auth_headers)
        contacts_list = assert_successful_json_response(contacts_response)

        if "items" in contacts_list:
            items = contacts_list["items"]
        else:
            items = contacts_list

        assert len(items) >= 2  # At least our created contacts

        # List goals
        goals_response = self.client.get("/api/v1/goals/", headers=auth_headers)
        goals_list = assert_successful_json_response(goals_response)
        
        if "items" in goals_list:
            goal_items = goals_list["items"]
        else:
            goal_items = goals_list
        
        assert len(goal_items) >= 1  # At least our created goal


@pytest.mark.integration
class TestNetworkingWorkflow:
    """Test complete networking and relationship management workflow."""
    
    @pytest.fixture(autouse=True) 
    def setup(self):
        """Set up test client and authenticated user."""
        self.config = get_config()
        self.client = create_client()
        
        if self.config.test_crud:
            # Authenticate user for tests
            _, self.auth_headers = self.client.authenticate_user("user1")
        
        yield
        
        # Cleanup if enabled
        if self.config.cleanup_data:
            self.client.cleanup_test_data("user1")
        self.client.close()
    
    def test_networking_goal_achievement_workflow(self):
        """Test workflow for achieving a networking goal through the platform."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Step 1: Create networking goal
        networking_goal = {
            "title": "Find AI/ML Mentor",
            "description": "Connect with experienced AI/ML professional for mentorship",
            "status": "active",
            "priority": 1,
            "category": "career_development"
        }
        
        goal_response = self.client.post("/api/v1/goals/", json_data=networking_goal, headers=self.auth_headers)
        goal = assert_valid_goal_response(goal_response)

        # Step 2: Add potential mentor contacts
        potential_mentors = [
            {
                "first_name": "Dr. Sarah",
                "last_name": "Chen",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "company": "AI Research Institute",
                    "title": "Senior Research Scientist",
                    "department": "Machine Learning"
                },
                "personal_details": {
                    "interests": ["deep_learning", "computer_vision", "mentoring"]
                }
            },
            {
                "first_name": "Prof. Michael",
                "last_name": "Rodriguez",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "company": "Tech University",
                    "title": "Professor of Computer Science",
                    "department": "AI Lab"
                }
            }
        ]

        created_mentors = []
        for mentor_data in potential_mentors:
            mentor_response = self.client.post("/api/v1/persons/", json_data=mentor_data, headers=self.auth_headers)
            mentor = assert_valid_person_response(mentor_response)
            created_mentors.append(mentor)
        
        # Step 3: Create actionable tasks for the goal
        tasks = [
            {
                "title": "Research Dr. Chen's recent publications",
                "description": "Review her latest papers to prepare for meaningful conversation",
                "goal_id": goal["goal_id"],
                "priority": 2,
                "is_completed": False
            },
            {
                "title": "Attend Prof. Rodriguez's webinar",
                "description": "Join his upcoming webinar on neural networks",
                "goal_id": goal["goal_id"],
                "priority": 1,
                "is_completed": False
            },
            {
                "title": "Draft mentorship request email",
                "description": "Write thoughtful email requesting mentorship guidance",
                "goal_id": goal["goal_id"],
                "priority": 3,
                "is_completed": False
            }
        ]
        
        created_tasks = []
        for task_data in tasks:
            task_response = self.client.post("/api/v1/tasks/", json_data=task_data, headers=self.auth_headers)
            task = assert_valid_task_response(task_response)
            created_tasks.append(task)

        # Step 4: Complete tasks progressively
        for i, task in enumerate(created_tasks[:2]):  # Complete first 2 tasks
            update_data = {"is_completed": True}
            update_response = self.client.put(f"/api/v1/tasks/{task['task_id']}",
                                             json_data=update_data, headers=self.auth_headers)
            updated_task = assert_valid_task_response(update_response)
            assert updated_task["is_completed"] is True
        
        # Step 5: Apply relationship archetype to establish mentor relationship
        mentor = created_mentors[0]  # Dr. Chen
        
        archetype_data = {
            "target_person_id": mentor["person_id"],
            "archetype": "mentor",
            "archetype_details": {
                "expertise_areas": ["machine_learning", "research_methodology"],
                "mentorship_style": "academic",
                "connection_strength": "potential"
            }
        }
        
        archetype_response = self.client.post(
            f"/api/v1/persons/{mentor['person_id']}/apply-archetype",
            json_data=archetype_data,
            headers=self.auth_headers
        )
        
        if archetype_response.status_code == 501:
            pytest.skip("Relationship archetype functionality not implemented")
        elif archetype_response.status_code == 200:
            relationship = assert_successful_json_response(archetype_response)
            # Verify relationship was established
        
        # Step 6: Check goal progress
        # Get updated goal with task completion status
        goal_check_response = self.client.get(f"/api/v1/goals/{goal['goal_id']}", headers=self.auth_headers)
        updated_goal = assert_valid_goal_response(goal_check_response)
        
        # Verify we can still retrieve the goal
        assert updated_goal["goal_id"] == goal["goal_id"]
    
    def test_ai_assisted_networking_workflow(self):
        """Test AI-assisted networking workflow."""
        if not self.config.test_ai_features or not self.config.test_crud:
            pytest.skip("AI features or CRUD tests disabled")
        
        # Step 1: Create contacts representing network
        network_contacts = [
            {
                "first_name": "Emma",
                "last_name": "Wilson",
                "professional_info": {"company": "DataTech", "title": "Data Scientist"}
            },
            {
                "first_name": "James",
                "last_name": "Brown", 
                "professional_info": {"company": "ML Solutions", "title": "ML Engineer"}
            },
            {
                "first_name": "Lisa",
                "last_name": "Garcia",
                "professional_info": {"company": "AI Startup", "title": "CTO"}
            }
        ]
        
        for contact_data in network_contacts:
            contact_response = self.client.post("/api/v1/persons/", json_data=contact_data, headers=self.auth_headers)
            assert_valid_person_response(contact_response)
        
        # Step 2: Ask AI copilot for networking advice
        copilot_request = {
            "message": "How can I strengthen my AI network? I want to find collaboration opportunities.",
            "context": {
                "current_focus": "machine_learning",
                "networking_goals": ["collaboration", "knowledge_sharing"]
            }
        }
        
        copilot_response = self.client.post("/api/v1/copilot/converse", 
                                           json_data=copilot_request, headers=self.auth_headers)
        
        if copilot_response.status_code == 501:
            pytest.skip("Copilot functionality not implemented")
        
        copilot_advice = assert_successful_json_response(copilot_response)
        assert "response" in copilot_advice
        
        # Step 3: Get AI suggestions for network activities
        suggestions_response = self.client.get("/api/v1/copilot/suggestions", headers=self.auth_headers)
        
        if suggestions_response.status_code == 501:
            pytest.skip("AI suggestions not implemented")
        
        suggestions = assert_successful_json_response(suggestions_response)
        # AI might suggest reconnecting with contacts, scheduling meetings, etc.
        
        # Step 4: Get network health diagnosis
        diagnosis_response = self.client.get("/api/v1/ai/network/diagnosis", headers=self.auth_headers)
        
        if diagnosis_response.status_code == 501:
            pytest.skip("Network diagnosis not implemented")
        
        diagnosis = assert_successful_json_response(diagnosis_response)
        assert "network_size" in diagnosis
        assert diagnosis["network_size"] >= 3  # At least our created contacts


@pytest.mark.integration
class TestGoalManagementWorkflow:
    """Test complete goal management and task execution workflow."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and authenticated user."""
        self.config = get_config()
        self.client = create_client()
        
        if self.config.test_crud:
            # Authenticate user for tests
            _, self.auth_headers = self.client.authenticate_user("user2")
        
        yield
        
        # Cleanup if enabled
        if self.config.cleanup_data:
            self.client.cleanup_test_data("user2")
        self.client.close()
    
    def test_quarterly_goal_planning_workflow(self):
        """Test complete quarterly goal planning and execution workflow."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Step 1: Create quarterly goals
        quarterly_goals = [
            {
                "title": "Expand Industry Knowledge",
                "description": "Learn about emerging trends in my industry",
                "status": "active",
                "priority": 1,
                "category": "professional_development"
            },
            {
                "title": "Build Strategic Partnerships",
                "description": "Establish 3 new strategic business partnerships",
                "status": "active", 
                "priority": 2,
                "category": "business_development"
            },
            {
                "title": "Improve Team Collaboration",
                "description": "Enhance collaboration with cross-functional teams",
                "status": "active",
                "priority": 3,
                "category": "team_building"
            }
        ]
        
        created_goals = []
        for goal_data in quarterly_goals:
            goal_response = self.client.post("/api/v1/goals/", json_data=goal_data, headers=self.auth_headers)
            goal = assert_valid_goal_response(goal_response)
            created_goals.append(goal)
        
        # Step 2: Break down goals into specific tasks
        goal_tasks = {
            created_goals[0]["goal_id"]: [
                {
                    "title": "Subscribe to industry newsletters",
                    "priority": 1,
                    "is_completed": False
                },
                {
                    "title": "Attend virtual industry conference",
                    "priority": 2,
                    "is_completed": False
                },
                {
                    "title": "Complete online course on industry trends",
                    "priority": 3,
                    "is_completed": False
                }
            ],
            created_goals[1]["goal_id"]: [
                {
                    "title": "Identify potential partner companies",
                    "priority": 1,
                    "is_completed": False
                },
                {
                    "title": "Prepare partnership proposal template",
                    "priority": 2,
                    "is_completed": False
                },
                {
                    "title": "Schedule initial meetings with prospects",
                    "priority": 3,
                    "is_completed": False
                }
            ]
        }
        
        all_created_tasks = []
        for goal_id, tasks in goal_tasks.items():
            for task_data in tasks:
                task_data["goal_id"] = goal_id
                task_response = self.client.post("/api/v1/tasks/", json_data=task_data, headers=self.auth_headers)
                task = assert_valid_task_response(task_response)
                all_created_tasks.append(task)
        
        # Step 3: Execute tasks progressively
        # Complete some high-priority tasks
        high_priority_tasks = [t for t in all_created_tasks if t["priority"] == 1]
        
        for task in high_priority_tasks[:2]:  # Complete first 2 high-priority tasks
            update_data = {"is_completed": True}
            update_response = self.client.put(f"/api/v1/tasks/{task['task_id']}",
                                             json_data=update_data, headers=self.auth_headers)
            updated_task = assert_valid_task_response(update_response)
            assert updated_task["is_completed"] is True

        # Step 4: Update goal status based on progress
        # Mark first goal as completed since we've made progress
        goal_update = {"status": "completed"}
        update_response = self.client.put(f"/api/v1/goals/{created_goals[0]['goal_id']}",
                                         json_data=goal_update, headers=self.auth_headers)
        updated_goal = assert_valid_goal_response(update_response)
        assert updated_goal["status"] == "completed"

        # Step 5: Verify goal-task relationships remain intact
        for goal in created_goals:
            goal_response = self.client.get(f"/api/v1/goals/{goal['goal_id']}", headers=self.auth_headers)
            retrieved_goal = assert_valid_goal_response(goal_response)
            assert retrieved_goal["goal_id"] == goal["goal_id"]

        # Step 6: Get overview of all goals and tasks
        goals_response = self.client.get("/api/v1/goals/", headers=self.auth_headers)
        goals_list = assert_successful_json_response(goals_response)

        tasks_response = self.client.get("/api/v1/tasks/", headers=self.auth_headers)
        tasks_list = assert_successful_json_response(tasks_response)
        
        # Verify we can see our created items
        if "items" in goals_list:
            goal_items = goals_list["items"]
        else:
            goal_items = goals_list
        
        if "items" in tasks_list:
            task_items = tasks_list["items"]
        else:
            task_items = tasks_list
        
        assert len(goal_items) >= 3  # Our created goals
        assert len(task_items) >= 6  # Our created tasks
    
    def test_goal_dashboard_workflow(self):
        """Test goal dashboard and progress tracking workflow."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Step 1: Create a goal with tasks
        goal_data = {
            "title": "Complete Project Alpha",
            "description": "Deliver project alpha on time and within budget",
            "status": "active"
        }
        
        goal_response = self.client.post("/api/v1/goals/", json_data=goal_data, headers=self.auth_headers)
        goal = assert_valid_goal_response(goal_response)

        # Step 2: Create multiple tasks for the goal
        project_tasks = [
            {"title": "Complete requirements analysis", "priority": 1, "is_completed": True},
            {"title": "Design system architecture", "priority": 2, "is_completed": True},
            {"title": "Implement core features", "priority": 3, "is_completed": False},
            {"title": "Write test cases", "priority": 4, "is_completed": False},
            {"title": "Deploy to production", "priority": 5, "is_completed": False}
        ]

        for task_data in project_tasks:
            task_data["goal_id"] = goal["goal_id"]
            task_response = self.client.post("/api/v1/tasks/", json_data=task_data, headers=self.auth_headers)
            assert_valid_task_response(task_response)

        # Step 3: Try to get goal dashboard
        dashboard_response = self.client.get(f"/api/v1/goals/{goal['goal_id']}/dashboard",
                                           headers=self.auth_headers)
        
        if dashboard_response.status_code == 501:
            pytest.skip("Goal dashboard not implemented")
        
        dashboard = assert_successful_json_response(dashboard_response)
        
        # Dashboard should contain goal information and progress metrics
        # Exact structure depends on implementation
        assert "goal" in dashboard or "goal_id" in dashboard


@pytest.mark.integration
class TestDataIntegrityWorkflow:
    """Test data integrity across complex workflows."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and authenticated user."""
        self.config = get_config()
        self.client = create_client()
        
        if self.config.test_crud:
            # Authenticate user for tests
            _, self.auth_headers = self.client.authenticate_user("user1")
        
        yield
        self.client.close()
    
    def test_cross_entity_relationship_integrity(self):
        """Test data integrity when entities are related across the system."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")

        # Reset test data to ensure clean state
        self.client.post("/api/v1/admin/reset-test-data", headers=self.auth_headers)

        # Create interconnected data: person -> goal -> tasks
        
        # Step 1: Create a person representing a business contact
        person_data = {
            "first_name": "Alexandra",
            "last_name": "Thompson",
            "contact_info": {"email": "<EMAIL>"},
            "professional_info": {"company": "Business Partner Inc", "title": "CEO"}
        }
        
        person_response = self.client.post("/api/v1/persons/", json_data=person_data, headers=self.auth_headers)
        person = assert_valid_person_response(person_response)

        # Step 2: Create a goal related to this person
        goal_data = {
            "title": f"Collaborate with {person['first_name']} {person['last_name']}",
            "description": f"Establish business collaboration with {person_data['professional_info']['company']}",
            "status": "active"
        }

        goal_response = self.client.post("/api/v1/goals/", json_data=goal_data, headers=self.auth_headers)
        goal = assert_valid_goal_response(goal_response)
        
        # Step 3: Create tasks that reference both the goal and implicitly the person
        tasks = [
            {
                "title": f"Research {person_data['professional_info']['company']}",
                "goal_id": goal["goal_id"],
                "is_completed": False
            },
            {
                "title": f"Schedule meeting with {person['first_name']}",
                "goal_id": goal["goal_id"],
                "is_completed": False
            }
        ]
        
        created_tasks = []
        for task_data in tasks:
            task_response = self.client.post("/api/v1/tasks/", json_data=task_data, headers=self.auth_headers)
            task = assert_valid_task_response(task_response)
            created_tasks.append(task)

        # Step 4: Verify all relationships exist
        # Get person
        person_check = self.client.get(f"/api/v1/persons/{person['person_id']}", headers=self.auth_headers)
        assert_valid_person_response(person_check)

        # Get goal
        goal_check = self.client.get(f"/api/v1/goals/{goal['goal_id']}", headers=self.auth_headers)
        assert_valid_goal_response(goal_check)

        # Get tasks
        for task in created_tasks:
            task_check = self.client.get(f"/api/v1/tasks/{task['task_id']}", headers=self.auth_headers)
            verified_task = assert_valid_task_response(task_check)
            assert verified_task["goal_id"] == goal["goal_id"]
        
        # Step 5: Test cascading operations
        # Update person and verify consistency
        person_update = {
            "professional_info": {
                "company": "Business Partner Corp",  # Company name change
                "title": "CEO"
            }
        }
        
        update_response = self.client.put(f"/api/v1/persons/{person['person_id']}",
                                         json_data=person_update, headers=self.auth_headers)
        updated_person = assert_valid_person_response(update_response)
        assert updated_person["professional_info"]["company"] == "Business Partner Corp"

        # Goal and tasks should still exist and be consistent
        final_goal_check = self.client.get(f"/api/v1/goals/{goal['goal_id']}", headers=self.auth_headers)
        assert_valid_goal_response(final_goal_check)

        for task in created_tasks:
            final_task_check = self.client.get(f"/api/v1/tasks/{task['task_id']}", headers=self.auth_headers)
            assert_valid_task_response(final_task_check)


@pytest.mark.performance
class TestPerformanceWorkflow:
    """Test system performance under realistic workflow conditions."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and authenticated user."""
        self.config = get_config()
        self.client = create_client()
        
        if self.config.test_performance:
            # Authenticate user for tests
            _, self.auth_headers = self.client.authenticate_user("user1")
        
        yield
        self.client.close()
    
    def test_bulk_data_operations_performance(self):
        """Test performance when creating and managing bulk data."""
        if not self.config.test_performance:
            pytest.skip("Performance tests disabled")
        
        import time
        
        # Test creating multiple contacts in sequence
        start_time = time.time()
        
        contacts = []
        for i in range(10):  # Create 10 contacts
            contact_data = {
                "first_name": f"Contact{i}",
                "last_name": f"Performance{i}",
                "contact_info": {"email": f"contact{i}@perf-test.com"}
            }
            
            response = self.client.post("/api/v1/persons/", json_data=contact_data, headers=self.auth_headers)
            contact = assert_valid_person_response(response)
            contacts.append(contact)

            # Each individual request should be fast
            ResponseAssertions.assert_response_time(response, self.config.max_response_time)

        create_time = time.time() - start_time

        # Total time for 10 contacts should be reasonable (less than 10x single request time)
        assert create_time < (10 * self.config.max_response_time), f"Bulk creation took {create_time}s"

        # Test retrieving all contacts
        start_time = time.time()
        list_response = self.client.get("/api/v1/persons/?limit=100", headers=self.auth_headers)
        list_time = time.time() - start_time
        
        assert_successful_json_response(list_response)
        ResponseAssertions.assert_response_time(list_response, self.config.max_response_time)
        
        print(f"Performance metrics: Create 10 contacts: {create_time:.3f}s, List contacts: {list_time:.3f}s")