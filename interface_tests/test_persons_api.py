"""
Persons/Contacts API endpoint tests.
Tests CRUD operations for contacts, search functionality, and relationship management.
"""
import pytest
import io
from typing import Dict, Any, List
from .client import APIClient, create_client
from .assertions import (
    assert_successful_json_response, 
    assert_error_json_response,
    assert_valid_person_response,
    assert_valid_paginated_response,
    ResponseAssertions
)
from .config import get_config


class TestPersonsAPI:
    """Test contacts/persons API functionality."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and configuration."""
        self.config = get_config()
        self.client = create_client()
        self.auth_headers = {}
        
        if self.config.test_crud:
            # Authenticate user for tests
            _, self.auth_headers = self.client.authenticate_user("user1")
        
        yield
        
        # Cleanup if enabled
        if self.config.cleanup_data:
            self.client.cleanup_test_data("user1")
        self.client.close()
    
    def create_test_person(self, **overrides) -> Dict[str, Any]:
        """Create a test person and return the response data."""
        person_data = {
            "first_name": "<PERSON>",
            "last_name": "<PERSON>e",
            "contact_info": {
                "email": "<EMAIL>",
                "phone": "+1234567890"
            },
            "professional_info": {
                "title": "Software Engineer",
                "company": "TechCorp",
                "department": "Engineering"
            },
            "social_profiles": {
                "linkedin": "https://linkedin.com/in/johndoe"
            },
            "personal_details": {
                "birthday": "1990-01-15",
                "interests": ["coding", "reading"]
            }
        }
        person_data.update(overrides)
        
        response = self.client.post("/api/v1/persons/", json_data=person_data, headers=self.auth_headers)
        return assert_valid_person_response(response)
    
    @pytest.mark.crud
    def test_create_person_success(self):
        """Test successful person creation."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        person_data = {
            "first_name": "Jane",
            "last_name": "Smith",
            "contact_info": {
                "email": "<EMAIL>",
                "phone": "+1987654321"
            },
            "professional_info": {
                "title": "Product Manager",
                "company": "InnovateCorp"
            }
        }
        
        response = self.client.post("/api/v1/persons/", json_data=person_data, headers=self.auth_headers)
        person = assert_valid_person_response(response)
        
        # Verify person data
        assert person["first_name"] == person_data["first_name"]
        assert person["last_name"] == person_data["last_name"]
        assert person["contact_info"]["email"] == person_data["contact_info"]["email"]
        assert person["professional_info"]["title"] == person_data["professional_info"]["title"]
    
    @pytest.mark.crud
    def test_create_person_minimal_data(self):
        """Test creating person with minimal required data."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        minimal_data = {
            "first_name": "Minimal",
            "last_name": "User"
        }
        
        response = self.client.post("/api/v1/persons/", json_data=minimal_data, headers=self.auth_headers)
        person = assert_valid_person_response(response)
        
        assert person["first_name"] == minimal_data["first_name"]
        assert person["last_name"] == minimal_data["last_name"]
    
    @pytest.mark.crud
    def test_create_person_invalid_data(self):
        """Test person creation with invalid data."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        invalid_cases = [
            # Missing required fields
            {"first_name": "John"},  # Missing last_name
            {"last_name": "Doe"},    # Missing first_name
            {},                      # Missing both
            
            # Empty required fields
            {"first_name": "", "last_name": "Doe"},
            {"first_name": "John", "last_name": ""},
            
            # Invalid email format
            {
                "first_name": "John",
                "last_name": "Doe",
                "contact_info": {"email": "invalid-email"}
            }
        ]
        
        for invalid_data in invalid_cases:
            response = self.client.post("/api/v1/persons/", json_data=invalid_data, headers=self.auth_headers)
            assert_error_json_response(response, 422)  # Validation error
    
    @pytest.mark.crud
    def test_get_person_by_id(self):
        """Test retrieving a person by ID."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create a person first
        created_person = self.create_test_person()
        person_id = created_person["person_id"]
        
        # Retrieve the person
        response = self.client.get(f"/api/v1/persons/{person_id}", headers=self.auth_headers)
        person = assert_valid_person_response(response)
        
        # Verify data matches
        assert person["person_id"] == person_id
        assert person["first_name"] == created_person["first_name"]
        assert person["last_name"] == created_person["last_name"]
    
    @pytest.mark.crud
    def test_get_person_not_found(self):
        """Test retrieving non-existent person returns 404."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        fake_id = "12345678-1234-1234-1234-123456789012"
        response = self.client.get(f"/api/v1/persons/{fake_id}", headers=self.auth_headers)
        assert_error_json_response(response, 404)
    
    @pytest.mark.crud
    def test_get_person_invalid_id(self):
        """Test retrieving person with invalid ID format."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        invalid_id = "not-a-valid-uuid"
        response = self.client.get(f"/api/v1/persons/{invalid_id}", headers=self.auth_headers)
        assert_error_json_response(response, 400)  # Bad request for invalid UUID
    
    @pytest.mark.crud
    def test_update_person(self):
        """Test updating person information."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create a person first
        created_person = self.create_test_person()
        person_id = created_person["person_id"]
        
        # Update the person
        update_data = {
            "professional_info": {
                "title": "Senior Software Engineer",
                "company": "NewTechCorp",
                "department": "Engineering"
            },
            "personal_details": {
                "interests": ["coding", "photography", "hiking"]
            }
        }
        
        response = self.client.put(f"/api/v1/persons/{person_id}", 
                                  json_data=update_data, headers=self.auth_headers)
        updated_person = assert_valid_person_response(response)
        
        # Verify updates
        assert updated_person["professional_info"]["title"] == update_data["professional_info"]["title"]
        assert updated_person["professional_info"]["company"] == update_data["professional_info"]["company"]
        assert updated_person["personal_details"]["interests"] == update_data["personal_details"]["interests"]
        
        # Verify unchanged fields remain the same
        assert updated_person["first_name"] == created_person["first_name"]
        assert updated_person["last_name"] == created_person["last_name"]
    
    @pytest.mark.crud
    def test_delete_person(self):
        """Test deleting a person."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create a person first
        created_person = self.create_test_person()
        person_id = created_person["person_id"]
        
        # Delete the person
        response = self.client.delete(f"/api/v1/persons/{person_id}", headers=self.auth_headers)
        ResponseAssertions.assert_status_code(response, 204)  # No content
        
        # Verify person is deleted
        get_response = self.client.get(f"/api/v1/persons/{person_id}", headers=self.auth_headers)
        assert_error_json_response(get_response, 404)
    
    @pytest.mark.crud
    def test_list_persons(self):
        """Test listing persons with pagination."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create multiple persons
        persons = []
        for i in range(5):
            person = self.create_test_person(
                first_name=f"Person{i}",
                last_name=f"Test{i}",
                contact_info={"email": f"person{i}@example.com"}
            )
            persons.append(person)
        
        # List persons with pagination
        response = self.client.get("/api/v1/persons/?skip=0&limit=3", headers=self.auth_headers)
        paginated = assert_valid_paginated_response(response)
        
        # Verify pagination structure
        assert len(paginated["items"]) <= 3
        assert paginated["total"] >= 5  # At least the ones we created
        assert paginated["skip"] == 0
        assert paginated["limit"] == 3
        
        # Verify each item is a valid person
        for item in paginated["items"]:
            assert "person_id" in item
            assert "first_name" in item
            assert "last_name" in item
    
    @pytest.mark.search
    def test_search_persons(self):
        """Test searching persons by name."""
        if not self.config.test_search:
            pytest.skip("Search tests disabled")
        
        # Create persons with searchable names
        test_persons = [
            {"first_name": "Alice", "last_name": "Johnson", "contact_info": {"email": "<EMAIL>"}},
            {"first_name": "Bob", "last_name": "Johnson", "contact_info": {"email": "<EMAIL>"}},
            {"first_name": "Charlie", "last_name": "Smith", "contact_info": {"email": "<EMAIL>"}}
        ]
        
        for person_data in test_persons:
            self.create_test_person(**person_data)
        
        # Search by first name
        response = self.client.get("/api/v1/persons/?search=Alice", headers=self.auth_headers)
        results = assert_successful_json_response(response)
        
        if "items" in results:  # Paginated response
            items = results["items"]
        else:  # Direct array response
            items = results
        
        # Should find Alice
        alice_found = any(item["first_name"] == "Alice" for item in items)
        assert alice_found, "Alice not found in search results"
        
        # Search by last name
        response = self.client.get("/api/v1/persons/?search=Johnson", headers=self.auth_headers)
        results = assert_successful_json_response(response)
        
        if "items" in results:
            items = results["items"]
        else:
            items = results
        
        # Should find both Johnson entries
        johnson_count = sum(1 for item in items if item["last_name"] == "Johnson")
        assert johnson_count >= 2, "Both Johnson entries should be found"
    
    @pytest.mark.search
    def test_search_persons_by_company(self):
        """Test searching persons by company."""
        if not self.config.test_search:
            pytest.skip("Search tests disabled")
        
        # Create persons at different companies
        self.create_test_person(
            first_name="Employee1",
            last_name="TestCorp",
            professional_info={"company": "TestCorp", "title": "Engineer"}
        )
        self.create_test_person(
            first_name="Employee2",
            last_name="TestCorp",
            professional_info={"company": "TestCorp", "title": "Manager"}
        )
        self.create_test_person(
            first_name="Employee3",
            last_name="OtherCorp",
            professional_info={"company": "OtherCorp", "title": "Engineer"}
        )
        
        # Search by company
        response = self.client.get("/api/v1/persons/?company=TestCorp", headers=self.auth_headers)
        results = assert_successful_json_response(response)
        
        if "items" in results:
            items = results["items"]
        else:
            items = results
        
        # All results should be from TestCorp
        for item in items:
            if "professional_info" in item and item["professional_info"]:
                if "company" in item["professional_info"]:
                    assert item["professional_info"]["company"] == "TestCorp"
    
    @pytest.mark.crud
    def test_apply_relationship_archetype(self):
        """Test applying relationship archetype between persons."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create two persons
        person1 = self.create_test_person(first_name="Mentor", last_name="Person")
        person2 = self.create_test_person(first_name="Mentee", last_name="Person")
        
        # Apply mentor archetype
        archetype_data = {
            "target_person_id": person2["person_id"],
            "archetype": "mentor",
            "archetype_details": {
                "expertise_areas": ["AI", "Machine Learning"],
                "mentorship_style": "hands_on"
            }
        }
        
        response = self.client.post(
            f"/api/v1/persons/{person1['person_id']}/apply-archetype",
            json_data=archetype_data,
            headers=self.auth_headers
        )
        
        if response.status_code == 501:  # Not implemented yet
            pytest.skip("Relationship archetype functionality not implemented")
        
        relationship = assert_successful_json_response(response)
        
        # Verify relationship was created
        assert "from_person_id" in relationship or "relationship_id" in relationship
    
    @pytest.mark.crud
    def test_cross_user_person_isolation(self):
        """Test that users cannot access each other's persons."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")
        
        # Create person as user1
        user1_person = self.create_test_person(first_name="User1", last_name="Person")
        
        # Try to access as user2
        _, user2_headers = self.client.authenticate_user("user2")
        response = self.client.get(f"/api/v1/persons/{user1_person['person_id']}", headers=user2_headers)
        assert_error_json_response(response, 404)  # Should not be found for user2
    
    @pytest.mark.performance
    def test_persons_api_performance(self):
        """Test persons API performance."""
        if not self.config.test_performance:
            pytest.skip("Performance tests disabled")
        
        # Test create performance
        person_data = {
            "first_name": "Performance",
            "last_name": "Test",
            "contact_info": {"email": "<EMAIL>"}
        }
        
        response = self.client.post("/api/v1/persons/", json_data=person_data, headers=self.auth_headers)
        assert_successful_json_response(response)
        ResponseAssertions.assert_response_time(response, self.config.max_response_time)
        
        # Test list performance
        list_response = self.client.get("/api/v1/persons/?limit=100", headers=self.auth_headers)
        assert_successful_json_response(list_response)
        ResponseAssertions.assert_response_time(list_response, self.config.max_response_time)
    
    @pytest.mark.auth
    def test_persons_api_without_authentication(self):
        """Test that persons API requires authentication."""
        if not self.config.test_auth:
            pytest.skip("Authentication tests disabled")
        
        # Try to access without authentication
        endpoints = [
            ("/api/v1/persons/", "GET"),
            ("/api/v1/persons/", "POST"),
            ("/api/v1/persons/fake-id", "GET"),
            ("/api/v1/persons/fake-id", "PUT"),
            ("/api/v1/persons/fake-id", "DELETE")
        ]
        
        for endpoint, method in endpoints:
            if method == "GET":
                response = self.client.get(endpoint)
            elif method == "POST":
                response = self.client.post(endpoint, json_data={})
            elif method == "PUT":
                response = self.client.put(endpoint, json_data={})
            elif method == "DELETE":
                response = self.client.delete(endpoint)
            
            assert_error_json_response(response, 401)  # Unauthorized

    @pytest.mark.crud
    def test_add_tags_to_person(self):
        """Test adding tags to a person."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")

        # Create a person first
        created_person = self.create_test_person()
        person_id = created_person["person_id"]

        # Add tags
        tags = ["colleague", "tech", "startup"]
        response = self.client.post(
            f"/api/v1/persons/{person_id}/tags",
            json_data=tags,
            headers=self.auth_headers
        )

        result = assert_successful_json_response(response)
        assert result["person_id"] == person_id
        assert set(result["tags"]) >= set(tags)  # Should contain at least the added tags

    @pytest.mark.crud
    def test_remove_tags_from_person(self):
        """Test removing tags from a person."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")

        # Create a person with tags
        created_person = self.create_test_person(
            personal_details={"tags": ["colleague", "tech", "startup", "friend"]}
        )
        person_id = created_person["person_id"]

        # Remove some tags
        tags_to_remove = ["tech", "startup"]
        # Use POST for tag removal since DELETE doesn't support json_data in our client
        response = self.client.post(
            f"/api/v1/persons/{person_id}/remove-tags",
            json_data=tags_to_remove,
            headers=self.auth_headers
        )

        result = assert_successful_json_response(response)
        assert result["person_id"] == person_id
        # Should not contain removed tags
        for tag in tags_to_remove:
            assert tag not in result["tags"]
        # Should still contain other tags
        assert "colleague" in result["tags"]
        assert "friend" in result["tags"]

    @pytest.mark.crud
    def test_get_all_tags(self):
        """Test getting all unique tags."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")

        # Create persons with different tags
        self.create_test_person(
            first_name="Person1",
            personal_details={"tags": ["tech", "startup"]}
        )
        self.create_test_person(
            first_name="Person2",
            personal_details={"tags": ["colleague", "tech"]}
        )

        response = self.client.get("/api/v1/persons/tags", headers=self.auth_headers)
        tags = assert_successful_json_response(response)

        # Should contain all unique tags
        expected_tags = {"tech", "startup", "colleague"}
        assert expected_tags.issubset(set(tags))

    @pytest.mark.crud
    def test_create_relationship(self):
        """Test creating a relationship between two persons."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")

        # Create two persons
        person1 = self.create_test_person(first_name="Person1", last_name="Test")
        person2 = self.create_test_person(first_name="Person2", last_name="Test")

        # Create relationship
        relationship_data = {
            "target_person_id": person2["person_id"],
            "archetype": "mentor",
            "relationship_foundation": {
                "how_met": "conference",
                "context": "AI/ML discussion"
            },
            "relationship_depth": {
                "trust_level": 0.7,
                "communication_frequency": "weekly"
            }
        }

        response = self.client.post(
            f"/api/v1/persons/{person1['person_id']}/relationships",
            json_data=relationship_data,
            headers=self.auth_headers
        )

        result = assert_successful_json_response(response)
        assert result["from_person_id"] == person1["person_id"]
        assert result["to_person_id"] == person2["person_id"]
        assert "relationship_id" in result

    @pytest.mark.crud
    def test_get_person_relationships(self):
        """Test getting relationships for a person."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")

        # Create two persons
        person1 = self.create_test_person(first_name="Person1", last_name="Test")
        person2 = self.create_test_person(first_name="Person2", last_name="Test")

        # Create relationship
        relationship_data = {
            "target_person_id": person2["person_id"],
            "archetype": "colleague"
        }

        self.client.post(
            f"/api/v1/persons/{person1['person_id']}/relationships",
            json_data=relationship_data,
            headers=self.auth_headers
        )

        # Get relationships
        response = self.client.get(
            f"/api/v1/persons/{person1['person_id']}/relationships",
            headers=self.auth_headers
        )

        relationships = assert_successful_json_response(response)
        assert isinstance(relationships, list)
        assert len(relationships) >= 1

        # Check relationship structure
        rel = relationships[0]
        assert "relationship_id" in rel
        assert "from_person_id" in rel
        assert "to_person_id" in rel
        assert "archetype" in rel

    @pytest.mark.crud
    def test_import_persons_from_csv(self):
        """Test importing persons from CSV file."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")

        # Create CSV content
        csv_content = """first_name,last_name,email,phone,company,title,tags
John,Doe,<EMAIL>,+1234567890,TechCorp,Engineer,"tech,colleague"
Jane,Smith,<EMAIL>,+1987654321,StartupInc,Manager,"startup,leader"
"""

        # Create a file-like object
        csv_file = io.BytesIO(csv_content.encode('utf-8'))

        # Import persons
        response = self.client.post(
            "/api/v1/persons/import",
            files={"file": ("test.csv", csv_file, "text/csv")},
            headers=self.auth_headers
        )

        result = assert_successful_json_response(response)
        assert result["imported_count"] >= 2
        assert "errors" in result

    @pytest.mark.crud
    def test_export_persons_to_csv(self):
        """Test exporting persons to CSV file."""
        if not self.config.test_crud:
            pytest.skip("CRUD tests disabled")

        # Create some test persons
        self.create_test_person(
            first_name="Export1",
            last_name="Test",
            contact_info={"email": "<EMAIL>"},
            professional_info={"company": "TestCorp"}
        )

        # Export persons
        response = self.client.get("/api/v1/persons/export", headers=self.auth_headers)

        # Check response
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/csv; charset=utf-8"
        assert "attachment" in response.headers.get("content-disposition", "")

        # Check CSV content contains headers
        content = response.content.decode('utf-8')
        assert "first_name" in content
        assert "last_name" in content
        assert "Export1" in content

    @pytest.mark.crud
    def test_search_persons_with_tags(self):
        """Test searching persons with tag filtering."""
        if not self.config.test_search:
            pytest.skip("Search tests disabled")

        # Create persons with specific tags
        self.create_test_person(
            first_name="TaggedPerson1",
            last_name="Test",
            personal_details={"tags": ["ai", "research"]}
        )
        self.create_test_person(
            first_name="TaggedPerson2",
            last_name="Test",
            personal_details={"tags": ["business", "sales"]}
        )

        # Search by name should find tagged persons
        response = self.client.get("/api/v1/persons/?search=TaggedPerson", headers=self.auth_headers)
        results = assert_successful_json_response(response)

        if "items" in results:
            items = results["items"]
        else:
            items = results

        # Should find both tagged persons
        found_names = [item["first_name"] for item in items]
        assert "TaggedPerson1" in found_names
        assert "TaggedPerson2" in found_names