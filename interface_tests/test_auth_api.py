"""
Authentication API endpoint tests.
Tests user registration, login, token management, and authorization.
"""
import pytest
from typing import Dict, Any
from .client import APIClient, create_client
from .assertions import (
    assert_successful_json_response, 
    assert_error_json_response,
    assert_valid_user_response,
    ResponseAssertions
)
from .config import get_config


class TestAuthenticationAPI:
    """Test authentication and authorization functionality."""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test client and configuration."""
        self.config = get_config()
        self.client = create_client()
        yield
        self.client.close()
    
    @pytest.mark.auth
    def test_user_registration_success(self):
        """Test successful user registration."""
        if not self.config.test_auth:
            pytest.skip("Authentication tests disabled")
        
        user_data = {
            "email": "<EMAIL>",
            "password": "SecurePassword123!",
            "first_name": "New",
            "last_name": "User"
        }
        
        response = self.client.register_user(user_data)
        
        # Should return user data
        user = assert_valid_user_response(response)
        assert user["email"] == user_data["email"]
        assert user["first_name"] == user_data["first_name"]
        assert user["last_name"] == user_data["last_name"]
        assert user["is_active"] is True
    
    @pytest.mark.auth
    def test_user_registration_duplicate_email(self):
        """Test registration with duplicate email fails."""
        if not self.config.test_auth:
            pytest.skip("Authentication tests disabled")

        user_data = {
            "email": "<EMAIL>",  # Use non-example.com domain to avoid auto-modification
            "password": "SecurePassword123!",
            "first_name": "First",
            "last_name": "User"
        }

        # Try to delete user first if they exist
        try:
            login_response = self.client.login_user(user_data["email"], user_data["password"])
            if login_response.is_success:
                token = login_response.json()["access_token"]
                headers = {"Authorization": f"Bearer {token}"}
                self.client.delete("/api/v1/admin/delete-user", headers=headers)
        except:
            pass  # User doesn't exist, which is fine

        # Register user first time - use direct API call to avoid email modification
        response1 = self.client.post("/api/v1/auth/register-local", json_data=user_data)
        assert_successful_json_response(response1)

        # Try to register same email again - use direct API call
        response2 = self.client.post("/api/v1/auth/register-local", json_data=user_data)
        error = assert_error_json_response(response2, 409)  # Conflict
        # Handle both FastAPI format (detail) and custom format (message)
        error_text = error.get("detail", error.get("message", "")).lower()
        assert "email" in error_text or "duplicate" in error_text or "already" in error_text
    
    @pytest.mark.auth
    def test_user_registration_invalid_data(self):
        """Test registration with invalid data fails."""
        if not self.config.test_auth:
            pytest.skip("Authentication tests disabled")
        
        # Test cases for invalid data
        invalid_cases = [
            # Missing required fields
            {
                "email": "<EMAIL>",
                "first_name": "Test"
                # Missing password and last_name
            },
            # Invalid email format
            {
                "email": "invalid-email",
                "password": "SecurePassword123!",
                "first_name": "Test",
                "last_name": "User"
            },
            # Weak password
            {
                "email": "<EMAIL>",
                "password": "123",
                "first_name": "Test",
                "last_name": "User"
            },
            # Empty fields
            {
                "email": "",
                "password": "SecurePassword123!",
                "first_name": "Test",
                "last_name": "User"
            }
        ]
        
        for invalid_data in invalid_cases:
            response = self.client.register_user(invalid_data)
            assert_error_json_response(response, 422)  # Validation error
    
    @pytest.mark.auth
    def test_user_login_success(self):
        """Test successful user login."""
        if not self.config.test_auth:
            pytest.skip("Authentication tests disabled")
        
        # Register a user first
        user_data = {
            "email": "<EMAIL>",
            "password": "SecurePassword123!",
            "first_name": "Login",
            "last_name": "Test"
        }
        register_response = self.client.register_user(user_data)
        assert_successful_json_response(register_response)
        
        # Login with correct credentials
        login_response = self.client.login_user(user_data["email"], user_data["password"])
        login_data = assert_successful_json_response(login_response)
        
        # Verify response structure
        required_fields = ["access_token", "token_type", "expires_in"]
        for field in required_fields:
            assert field in login_data, f"Missing field: {field}"
        
        assert login_data["token_type"] == "bearer"
        assert isinstance(login_data["expires_in"], int)
        assert login_data["expires_in"] > 0
        assert len(login_data["access_token"]) > 50  # JWT tokens are long
    
    @pytest.mark.auth
    def test_user_login_invalid_credentials(self):
        """Test login with invalid credentials fails."""
        if not self.config.test_auth:
            pytest.skip("Authentication tests disabled")
        
        # Test with non-existent user
        response1 = self.client.login_user("<EMAIL>", "password")
        assert_error_json_response(response1, 401)
        
        # Register a user
        user_data = {
            "email": "<EMAIL>",
            "password": "CorrectPassword123!",
            "first_name": "Wrong",
            "last_name": "Password"
        }
        register_response = self.client.register_user(user_data)
        assert_successful_json_response(register_response)
        
        # Test with wrong password
        response2 = self.client.login_user(user_data["email"], "WrongPassword123!")
        assert_error_json_response(response2, 401)
    
    @pytest.mark.auth
    def test_protected_endpoint_without_token(self):
        """Test accessing protected endpoint without authentication fails."""
        if not self.config.test_auth:
            pytest.skip("Authentication tests disabled")
        
        # Try to access user profile without authentication
        response = self.client.get("/api/v1/users/me")
        assert_error_json_response(response, 401)
    
    @pytest.mark.auth
    def test_protected_endpoint_with_invalid_token(self):
        """Test accessing protected endpoint with invalid token fails."""
        if not self.config.test_auth:
            pytest.skip("Authentication tests disabled")
        
        invalid_headers = {"Authorization": "Bearer invalid_token_here"}
        response = self.client.get("/api/v1/users/me", headers=invalid_headers)
        assert_error_json_response(response, 401)
    
    @pytest.mark.auth
    def test_protected_endpoint_with_valid_token(self):
        """Test accessing protected endpoint with valid token succeeds."""
        if not self.config.test_auth:
            pytest.skip("Authentication tests disabled")
        
        # Authenticate user
        user_data, auth_headers = self.client.authenticate_user("user1")
        
        # Access protected endpoint
        response = self.client.get("/api/v1/users/me", headers=auth_headers)
        profile = assert_valid_user_response(response)
        
        # Verify the profile data
        assert "user_id" in profile
        assert "email" in profile
        assert profile["is_active"] is True
    
    @pytest.mark.auth
    def test_token_refresh(self):
        """Test token refresh functionality if supported."""
        if not self.config.test_auth:
            pytest.skip("Authentication tests disabled")
        
        # Authenticate user to get refresh token
        user_config = self.config.test_users["user1"]
        login_response = self.client.login_user(user_config.email, user_config.password)
        login_data = assert_successful_json_response(login_response)
        
        if "refresh_token" not in login_data:
            pytest.skip("Refresh token not supported")
        
        # Try to refresh token
        refresh_data = {"refresh_token": login_data["refresh_token"]}
        refresh_response = self.client.post("/api/v1/auth/refresh", json_data=refresh_data)
        
        if refresh_response.status_code == 501:  # Not implemented
            pytest.skip("Token refresh not implemented")
        
        new_tokens = assert_successful_json_response(refresh_response)
        assert "access_token" in new_tokens
        assert new_tokens["access_token"] != login_data["access_token"]
    
    @pytest.mark.auth
    def test_user_profile_update(self):
        """Test updating user profile."""
        if not self.config.test_auth:
            pytest.skip("Authentication tests disabled")
        
        # Authenticate user
        user_data, auth_headers = self.client.authenticate_user("user2")
        
        # Update profile with all required preference factors
        update_data = {
            "emotional_weight": 0.2,
            "value_weight": 0.2,
            "trust_weight": 0.2,
            "information_weight": 0.15,
            "role_weight": 0.15,
            "coercive_weight": 0.1
        }
        
        response = self.client.put("/api/v1/users/me/preferences", 
                                 json_data=update_data, headers=auth_headers)
        
        if response.status_code == 501:  # Not implemented yet
            pytest.skip("User preferences update not implemented")
        
        updated_profile = assert_successful_json_response(response)
        # Verify update was applied (structure depends on implementation)
    
    @pytest.mark.auth
    def test_cross_user_data_isolation(self):
        """Test that users cannot access each other's data."""
        if not self.config.test_auth:
            pytest.skip("Authentication tests disabled")
        
        # Authenticate two different users
        user1_data, user1_headers = self.client.authenticate_user("user1")
        user2_data, user2_headers = self.client.authenticate_user("user2")
        
        # Each user should only see their own profile
        user1_profile_response = self.client.get("/api/v1/users/me", headers=user1_headers)
        user1_profile = assert_valid_user_response(user1_profile_response)
        
        user2_profile_response = self.client.get("/api/v1/users/me", headers=user2_headers)
        user2_profile = assert_valid_user_response(user2_profile_response)
        
        # Profiles should be different
        assert user1_profile["user_id"] != user2_profile["user_id"]
        assert user1_profile["email"] != user2_profile["email"]
    
    @pytest.mark.auth
    def test_logout_functionality(self):
        """Test user logout functionality if supported."""
        if not self.config.test_auth:
            pytest.skip("Authentication tests disabled")
        
        # Authenticate user
        user_data, auth_headers = self.client.authenticate_user("user1")
        
        # Verify token works
        response1 = self.client.get("/api/v1/users/me", headers=auth_headers)
        assert_successful_json_response(response1)
        
        # Try to logout
        logout_response = self.client.logout_user(auth_headers)
        
        if logout_response.status_code == 501:  # Not implemented
            pytest.skip("Logout functionality not implemented")
        
        ResponseAssertions.assert_success_response(logout_response)
        
        # Token should no longer work after logout
        response2 = self.client.get("/api/v1/users/me", headers=auth_headers)
        assert_error_json_response(response2, 401)
    
    @pytest.mark.auth
    @pytest.mark.performance
    def test_authentication_performance(self):
        """Test authentication performance."""
        if not self.config.test_auth or not self.config.test_performance:
            pytest.skip("Performance tests disabled")
        
        user_config = self.config.test_users["user1"]
        
        # Test login performance
        login_response = self.client.login_user(user_config.email, user_config.password)
        assert_successful_json_response(login_response)
        ResponseAssertions.assert_response_time(login_response, self.config.max_response_time)
        
        # Test protected endpoint performance
        auth_headers = {"Authorization": f"Bearer {login_response.json()['access_token']}"}
        profile_response = self.client.get("/api/v1/users/me", headers=auth_headers)
        assert_successful_json_response(profile_response)
        ResponseAssertions.assert_response_time(profile_response, self.config.max_response_time)