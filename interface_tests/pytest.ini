[tool:pytest]
# Pytest configuration for interface tests

# Test discovery
testpaths = interface_tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Test markers
markers =
    auth: Authentication and authorization tests
    crud: CRUD operation tests  
    ai: AI features and functionality tests
    integration: Workflow integration tests
    performance: Performance and load tests
    search: Search functionality tests
    unit: Unit tests
    slow: Slow running tests

# Output options
addopts = 
    -v
    --strict-markers
    --tb=short
    --durations=10
    --color=yes

# Coverage options (if pytest-cov is installed)
# addopts = 
#     --cov=interface_tests
#     --cov-report=html
#     --cov-report=term-missing
#     --cov-fail-under=80

# Minimum Python version
minversion = 3.8

# Test timeout (if pytest-timeout is installed)
timeout = 300

# Parallel execution (if pytest-xdist is installed)
# addopts = -n auto

# Filter warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning