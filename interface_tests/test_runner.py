"""
Test runner for interface tests.
Provides utilities to run tests with different configurations and generate reports.
"""
import os
import sys
import time
import json
import subprocess
from typing import Dict, Any, List, Optional
from pathlib import Path
from dataclasses import dataclass, asdict
from .config import get_config, Environment, update_config
from .client import create_client


@dataclass
class TestResult:
    """Test execution result."""
    test_name: str
    status: str  # "PASSED", "FAILED", "SKIPPED", "ERROR"
    duration: float
    error_message: Optional[str] = None
    output: Optional[str] = None


@dataclass
class TestSuiteResult:
    """Test suite execution result."""
    suite_name: str
    total_tests: int
    passed: int
    failed: int
    skipped: int
    errors: int
    duration: float
    tests: List[TestResult]


class InterfaceTestRunner:
    """Runner for interface tests with configuration management."""
    
    def __init__(self, config_overrides: Optional[Dict[str, Any]] = None):
        """Initialize test runner with optional configuration overrides."""
        self.config = get_config()
        
        if config_overrides:
            update_config(**config_overrides)
            self.config = get_config()
        
        self.results: List[TestSuiteResult] = []
    
    def check_api_availability(self) -> bool:
        """Check if the API server is available before running tests."""
        print(f"Checking API availability at {self.config.api.base_url}...")
        
        with create_client() as client:
            if client.wait_for_api(max_wait=30):
                print("✓ API server is available")
                return True
            else:
                print("✗ API server is not available")
                return False
    
    def run_pytest_suite(self, test_pattern: str, markers: Optional[List[str]] = None) -> TestSuiteResult:
        """Run a pytest suite and return results."""
        cmd = ["python", "-m", "pytest", "-v", "--tb=short"]
        
        # Add markers if specified
        if markers:
            marker_expression = " and ".join(markers)
            cmd.extend(["-m", marker_expression])
        
        # Add test pattern
        cmd.append(test_pattern)
        
        # Add JSON report for parsing
        report_file = f"test_report_{int(time.time())}.json"
        cmd.extend(["--json-report", f"--json-report-file={report_file}"])
        
        print(f"Running: {' '.join(cmd)}")
        
        start_time = time.time()
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, 
                                  cwd=Path(__file__).parent)
            duration = time.time() - start_time
            
            # Parse JSON report if available
            if os.path.exists(report_file):
                try:
                    with open(report_file, 'r') as f:
                        report_data = json.load(f)
                    
                    suite_result = self._parse_pytest_report(report_data, test_pattern, duration)
                    os.unlink(report_file)  # Clean up report file
                    return suite_result
                except Exception as e:
                    print(f"Warning: Could not parse test report: {e}")
            
            # Fallback to basic parsing
            return self._parse_pytest_output(result, test_pattern, duration)
            
        except Exception as e:
            return TestSuiteResult(
                suite_name=test_pattern,
                total_tests=0,
                passed=0,
                failed=1,
                skipped=0,
                errors=1,
                duration=time.time() - start_time,
                tests=[TestResult(
                    test_name="test_runner_error",
                    status="ERROR",
                    duration=0,
                    error_message=str(e)
                )]
            )
    
    def _parse_pytest_report(self, report_data: Dict[str, Any], suite_name: str, duration: float) -> TestSuiteResult:
        """Parse pytest JSON report."""
        summary = report_data.get("summary", {})
        tests = []
        
        for test in report_data.get("tests", []):
            test_result = TestResult(
                test_name=test.get("nodeid", "unknown"),
                status=test.get("outcome", "UNKNOWN").upper(),
                duration=test.get("duration", 0),
                error_message=test.get("call", {}).get("longrepr") if test.get("outcome") == "failed" else None
            )
            tests.append(test_result)
        
        return TestSuiteResult(
            suite_name=suite_name,
            total_tests=summary.get("total", 0),
            passed=summary.get("passed", 0),
            failed=summary.get("failed", 0),
            skipped=summary.get("skipped", 0),
            errors=summary.get("error", 0),
            duration=duration,
            tests=tests
        )
    
    def _parse_pytest_output(self, result: subprocess.CompletedProcess, suite_name: str, duration: float) -> TestSuiteResult:
        """Parse pytest output as fallback."""
        output = result.stdout + result.stderr
        lines = output.split('\n')
        
        # Simple parsing - look for test results
        passed = failed = skipped = errors = 0
        tests = []
        
        for line in lines:
            if " PASSED " in line:
                passed += 1
                test_name = line.split(" PASSED ")[0].strip()
                tests.append(TestResult(test_name, "PASSED", 0))
            elif " FAILED " in line:
                failed += 1
                test_name = line.split(" FAILED ")[0].strip()
                tests.append(TestResult(test_name, "FAILED", 0))
            elif " SKIPPED " in line:
                skipped += 1
                test_name = line.split(" SKIPPED ")[0].strip()
                tests.append(TestResult(test_name, "SKIPPED", 0))
            elif " ERROR " in line:
                errors += 1
                test_name = line.split(" ERROR ")[0].strip()
                tests.append(TestResult(test_name, "ERROR", 0))
        
        total = passed + failed + skipped + errors
        
        return TestSuiteResult(
            suite_name=suite_name,
            total_tests=total,
            passed=passed,
            failed=failed,
            skipped=skipped,
            errors=errors,
            duration=duration,
            tests=tests
        )
    
    def run_authentication_tests(self) -> TestSuiteResult:
        """Run authentication-related tests."""
        if not self.config.test_auth:
            print("Skipping authentication tests (disabled in config)")
            return self._create_empty_suite("authentication")
        
        return self.run_pytest_suite("test_auth_api.py", ["auth"])
    
    def run_crud_tests(self) -> TestSuiteResult:
        """Run CRUD operation tests."""
        if not self.config.test_crud:
            print("Skipping CRUD tests (disabled in config)")
            return self._create_empty_suite("crud")

        result1 = self.run_pytest_suite("test_persons_api.py", ["crud"])
        result2 = self.run_pytest_suite("test_goals_tasks_api.py", ["crud"])

        # Combine results
        return self._combine_suite_results("crud_operations", [result1, result2])

    def run_search_tests(self) -> TestSuiteResult:
        """Run search functionality tests."""
        if not self.config.test_search:
            print("Skipping search tests (disabled in config)")
            return self._create_empty_suite("search")

        return self.run_pytest_suite("test_search_api.py", ["search"])
    
    def run_ai_tests(self) -> TestSuiteResult:
        """Run AI features tests."""
        if not self.config.test_ai_features:
            print("Skipping AI tests (disabled in config)")
            return self._create_empty_suite("ai_features")
        
        return self.run_pytest_suite("test_ai_features_api.py", ["ai"])
    
    def run_workflow_tests(self) -> TestSuiteResult:
        """Run workflow integration tests."""
        return self.run_pytest_suite("test_workflow_integration.py", ["integration"])
    
    def run_performance_tests(self) -> TestSuiteResult:
        """Run performance tests."""
        if not self.config.test_performance:
            print("Skipping performance tests (disabled in config)")
            return self._create_empty_suite("performance")
        
        return self.run_pytest_suite("*", ["performance"])
    
    def run_all_tests(self) -> List[TestSuiteResult]:
        """Run all enabled test suites."""
        print(f"Running interface tests for {self.config.environment.value} environment")
        print(f"API Base URL: {self.config.api.base_url}")
        print(f"Configuration: Auth={self.config.test_auth}, CRUD={self.config.test_crud}, AI={self.config.test_ai_features}")
        print("=" * 80)
        
        # Check API availability first
        if not self.check_api_availability():
            print("Cannot run tests - API server is not available")
            return []
        
        test_suites = [
            ("Authentication", self.run_authentication_tests),
            ("CRUD Operations", self.run_crud_tests),
            ("Search Functionality", self.run_search_tests),
            ("AI Features", self.run_ai_tests),
            ("Workflow Integration", self.run_workflow_tests),
            ("Performance", self.run_performance_tests)
        ]
        
        results = []
        total_start_time = time.time()
        
        for suite_name, test_func in test_suites:
            print(f"\n🧪 Running {suite_name} Tests...")
            print("-" * 50)
            
            suite_result = test_func()
            results.append(suite_result)
            
            # Print suite summary
            self._print_suite_summary(suite_result)
        
        total_duration = time.time() - total_start_time
        self._print_overall_summary(results, total_duration)
        
        return results
    
    def _create_empty_suite(self, name: str) -> TestSuiteResult:
        """Create an empty test suite result."""
        return TestSuiteResult(
            suite_name=name,
            total_tests=0,
            passed=0,
            failed=0,
            skipped=0,
            errors=0,
            duration=0,
            tests=[]
        )
    
    def _combine_suite_results(self, name: str, results: List[TestSuiteResult]) -> TestSuiteResult:
        """Combine multiple test suite results."""
        combined_tests = []
        total_tests = passed = failed = skipped = errors = 0
        total_duration = 0
        
        for result in results:
            combined_tests.extend(result.tests)
            total_tests += result.total_tests
            passed += result.passed
            failed += result.failed
            skipped += result.skipped
            errors += result.errors
            total_duration += result.duration
        
        return TestSuiteResult(
            suite_name=name,
            total_tests=total_tests,
            passed=passed,
            failed=failed,
            skipped=skipped,
            errors=errors,
            duration=total_duration,
            tests=combined_tests
        )
    
    def _print_suite_summary(self, result: TestSuiteResult):
        """Print summary for a test suite."""
        if result.total_tests == 0:
            print("   No tests run (disabled or skipped)")
            return
        
        status_icon = "✅" if result.failed == 0 and result.errors == 0 else "❌"
        print(f"   {status_icon} {result.suite_name}: {result.passed}/{result.total_tests} passed")
        
        if result.failed > 0:
            print(f"      ❌ {result.failed} failed")
        if result.skipped > 0:
            print(f"      ⏭️  {result.skipped} skipped")
        if result.errors > 0:
            print(f"      🚨 {result.errors} errors")
        
        print(f"      ⏱️  Duration: {result.duration:.2f}s")
    
    def _print_overall_summary(self, results: List[TestSuiteResult], total_duration: float):
        """Print overall test summary."""
        total_tests = sum(r.total_tests for r in results)
        total_passed = sum(r.passed for r in results)
        total_failed = sum(r.failed for r in results)
        total_skipped = sum(r.skipped for r in results)
        total_errors = sum(r.errors for r in results)
        
        print("\n" + "=" * 80)
        print("📊 OVERALL TEST SUMMARY")
        print("=" * 80)
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {total_passed}")
        print(f"❌ Failed: {total_failed}")
        print(f"⏭️  Skipped: {total_skipped}")
        print(f"🚨 Errors: {total_errors}")
        print(f"⏱️  Total Duration: {total_duration:.2f}s")
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        if total_failed > 0 or total_errors > 0:
            print("\n🚨 FAILED TESTS:")
            for result in results:
                for test in result.tests:
                    if test.status in ["FAILED", "ERROR"]:
                        print(f"   ❌ {test.test_name} ({test.status})")
                        if test.error_message:
                            print(f"      {test.error_message[:100]}...")
    
    def save_results_json(self, filepath: str):
        """Save test results to JSON file."""
        data = {
            "environment": self.config.environment.value,
            "api_base_url": self.config.api.base_url,
            "timestamp": time.time(),
            "results": [asdict(result) for result in self.results]
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
        
        print(f"Results saved to {filepath}")


def main():
    """Main entry point for test runner."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run Nexus Backend Interface Tests")
    parser.add_argument("--env", choices=["local", "development", "staging", "production"],
                       default="local", help="Environment to test against")
    parser.add_argument("--base-url", help="Override API base URL")
    parser.add_argument("--suite", choices=["auth", "crud", "ai", "workflow", "performance", "all"],
                       default="all", help="Test suite to run")
    parser.add_argument("--no-auth", action="store_true", help="Skip authentication tests")
    parser.add_argument("--no-ai", action="store_true", help="Skip AI features tests")
    parser.add_argument("--performance", action="store_true", help="Include performance tests")
    parser.add_argument("--output", help="Save results to JSON file")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    # Configure based on arguments
    config_overrides = {
        "environment": Environment(args.env)
    }
    
    if args.base_url:
        config_overrides["api"] = {"base_url": args.base_url}
    
    if args.no_auth:
        config_overrides["test_auth"] = False
    
    if args.no_ai:
        config_overrides["test_ai_features"] = False
    
    if args.performance:
        config_overrides["test_performance"] = True
    
    if args.verbose:
        config_overrides["verbose_logging"] = True
    
    # Create and run tests
    runner = InterfaceTestRunner(config_overrides)
    
    if args.suite == "all":
        results = runner.run_all_tests()
    elif args.suite == "auth":
        results = [runner.run_authentication_tests()]
    elif args.suite == "crud":
        results = [runner.run_crud_tests()]
    elif args.suite == "ai":
        results = [runner.run_ai_tests()]
    elif args.suite == "workflow":
        results = [runner.run_workflow_tests()]
    elif args.suite == "performance":
        results = [runner.run_performance_tests()]
    
    runner.results = results
    
    # Save results if requested
    if args.output:
        runner.save_results_json(args.output)
    
    # Exit with error code if tests failed
    total_failed = sum(r.failed + r.errors for r in results)
    sys.exit(1 if total_failed > 0 else 0)


if __name__ == "__main__":
    main()