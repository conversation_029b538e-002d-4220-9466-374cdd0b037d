{"permissions": {"allow": ["<PERSON><PERSON>(claude mcp:*)", "Bash(claude-code mcp list)", "Bash(npx @modelcontextprotocol/server-zen:*)", "Bash(find:*)", "Bash(npx @modelcontextprotocol/cli list-servers:*)", "<PERSON><PERSON>(python:*)", "Bash(npm install:*)", "<PERSON><PERSON>(env)", "Bash(lsof:*)", "Bash(grep:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(echo:*)", "Bash(ls:*)", "Bash(pg_isready:*)", "Bash(rm:*)", "<PERSON><PERSON>(task-master init:*)", "<PERSON><PERSON>(task-master list:*)", "Bash(rg:*)", "<PERSON><PERSON>(task-master parse-prd:*)", "<PERSON><PERSON>(task-master:*)", "<PERSON>sh(alembic init:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(uvicorn:*)", "Bash(timeout 300 python3 test_copilot_function_calling.py)", "Bash(timeout 300 python3 test_copilot_llm.py)", "Bash(timeout 300 python3 test_copilot_e2e.py)", "Bash(timeout 120 python3 test_copilot_quick.py)", "Bash(timeout 120 python3 test_copilot_function_calling.py)", "Bash(timeout 600 python3 test_copilot_e2e.py)", "Bash(kill:*)", "Bash(alembic revision:*)", "Bash(alembic upgrade:*)", "<PERSON><PERSON>(alembic current:*)"], "deny": []}}