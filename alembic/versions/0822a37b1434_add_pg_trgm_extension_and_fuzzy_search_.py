"""Add pg_trgm extension and fuzzy search indexes

Revision ID: 0822a37b1434
Revises: 
Create Date: 2025-07-08 13:15:39.812278

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '0822a37b1434'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create pg_trgm extension for fuzzy search functionality
    op.execute('CREATE EXTENSION IF NOT EXISTS pg_trgm;')
    
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('knows',
    sa.Column('from_person_id', postgresql.UUID(), nullable=False),
    sa.Column('to_person_id', postgresql.UUID(), nullable=False),
    sa.Column('user_id', postgresql.UUID(), nullable=False),
    sa.Column('archetype', sa.String(length=100), nullable=True),
    sa.Column('relationship_foundation', postgresql.JSONB(), nullable=True),
    sa.Column('relationship_depth', postgresql.JSONB(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['from_person_id'], ['persons.person_id'], ),
    sa.ForeignKeyConstraint(['to_person_id'], ['persons.person_id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.user_id'], ),
    sa.PrimaryKeyConstraint('from_person_id', 'to_person_id')
    )
    op.create_index('idx_knows_archetype', 'knows', ['archetype'], unique=False)
    op.create_index('idx_knows_user_from', 'knows', ['user_id', 'from_person_id'], unique=False)
    op.create_index('idx_knows_user_to', 'knows', ['user_id', 'to_person_id'], unique=False)
    op.create_index(op.f('ix_knows_user_id'), 'knows', ['user_id'], unique=False)
    
    # Create GIN indexes for fuzzy search functionality on name fields
    op.execute('CREATE INDEX IF NOT EXISTS idx_persons_first_name_gin ON persons USING GIN (first_name gin_trgm_ops);')
    op.execute('CREATE INDEX IF NOT EXISTS idx_persons_last_name_gin ON persons USING GIN (last_name gin_trgm_ops);')
    op.execute('CREATE INDEX IF NOT EXISTS idx_organizations_name_gin ON organizations USING GIN (name gin_trgm_ops);')
    
    # Create combined trigram index for full name search
    op.execute('CREATE INDEX IF NOT EXISTS idx_persons_full_name_gin ON persons USING GIN ((first_name || \' \' || last_name) gin_trgm_ops);')
    
    op.alter_column('users', 'hashed_password',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'hashed_password',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    
    # Drop GIN indexes for fuzzy search
    op.execute('DROP INDEX IF EXISTS idx_persons_full_name_gin;')
    op.execute('DROP INDEX IF EXISTS idx_organizations_name_gin;')
    op.execute('DROP INDEX IF EXISTS idx_persons_last_name_gin;')
    op.execute('DROP INDEX IF EXISTS idx_persons_first_name_gin;')
    
    op.drop_index(op.f('ix_knows_user_id'), table_name='knows')
    op.drop_index('idx_knows_user_to', table_name='knows')
    op.drop_index('idx_knows_user_from', table_name='knows')
    op.drop_index('idx_knows_archetype', table_name='knows')
    op.drop_table('knows')
    
    # Note: We don't drop the pg_trgm extension as it might be used by other applications
    # op.execute('DROP EXTENSION IF EXISTS pg_trgm;')
    # ### end Alembic commands ###
