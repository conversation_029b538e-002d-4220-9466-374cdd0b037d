"""Add conversation tables for stateless LLM service

Revision ID: add_conversation_tables
Revises: 0822a37b1434
Create Date: 2025-07-11 12:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
import uuid

# revision identifiers, used by Alembic.
revision = 'add_conversation_tables'
down_revision = '0822a37b1434'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create conversation and conversation_message tables"""
    
    # Create conversations table
    op.create_table('conversations',
        sa.Column('conversation_id', sa.String(36), nullable=False),
        sa.Column('user_id', sa.String(36), nullable=False),
        sa.Column('title', sa.String(length=255), nullable=True),
        sa.Column('context', sa.JSON(), nullable=True, default={}),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('last_message_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.user_id'], ),
        sa.PrimaryKeyConstraint('conversation_id')
    )
    op.create_index('ix_conversations_user_id', 'conversations', ['user_id'], unique=False)

    # Create conversation_messages table
    op.create_table('conversation_messages',
        sa.Column('message_id', sa.String(36), nullable=False),
        sa.Column('conversation_id', sa.String(36), nullable=False),
        sa.Column('role', sa.String(length=20), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('tool_calls', sa.JSON(), nullable=True),
        sa.Column('tool_call_id', sa.String(length=100), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True, default={}),
        sa.Column('sequence_number', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['conversation_id'], ['conversations.conversation_id'], ),
        sa.PrimaryKeyConstraint('message_id')
    )
    op.create_index('ix_conversation_messages_conversation_id', 'conversation_messages', ['conversation_id'], unique=False)


def downgrade() -> None:
    """Drop conversation tables"""
    op.drop_index('ix_conversation_messages_conversation_id', table_name='conversation_messages')
    op.drop_table('conversation_messages')
    op.drop_index('ix_conversations_user_id', table_name='conversations')
    op.drop_table('conversations')