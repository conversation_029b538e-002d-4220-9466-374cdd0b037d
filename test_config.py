#!/usr/bin/env python3
"""
Test configuration loading
"""

import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config():
    """Test configuration loading"""
    try:
        print("Testing configuration loading...")
        
        # Import and test settings
        from app.core.config import settings
        
        print("✓ Configuration loaded successfully!")
        print(f"Project Name: {settings.PROJECT_NAME}")
        print(f"Debug Mode: {settings.DEBUG}")
        print(f"Database URL: {settings.DATABASE_URL[:50]}..." if len(settings.DATABASE_URL) > 50 else settings.DATABASE_URL)
        print(f"Allowed Hosts: {settings.ALLOWED_HOSTS}")
        print(f"API Version: {settings.API_V1_STR}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_config()
    sys.exit(0 if success else 1)