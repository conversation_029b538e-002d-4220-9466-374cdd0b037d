#!/usr/bin/env python3
"""
Debug Copilot Issues - Minimal test to identify problems
"""

import requests
import json
import time

BASE_URL = "http://localhost:8010"

def test_copilot_debug():
    session = requests.Session()
    
    print("🔍 Debugging Copilot Issues")
    print("=" * 40)
    
    # 1. Setup auth
    user_data = {
        "email": f"debug.{int(time.time())}@example.com",
        "password": "TestPassword123!",
        "first_name": "Debug",
        "last_name": "Test"
    }
    
    print("1. Setting up authentication...")
    response = session.post(f"{BASE_URL}/api/v1/auth/register-local", json=user_data)
    print(f"   Register: {response.status_code}")
    
    response = session.post(f"{BASE_URL}/api/v1/auth/login-local", json={
        "email": user_data["email"], 
        "password": user_data["password"]
    })
    print(f"   Login: {response.status_code}")
    
    if response.status_code != 200:
        print(f"   Login failed: {response.text}")
        return
    
    token = response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    # 2. Test basic conversation
    print("\n2. Testing basic conversation...")
    message_data = {"message": "Hello, this is a test"}
    
    try:
        response = session.post(
            f"{BASE_URL}/api/v1/copilot/converse",
            json=message_data,
            headers=headers,
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response keys: {list(data.keys())}")
            print(f"   Response field: '{data.get('response', 'MISSING')}'")
            print(f"   Response type: {type(data.get('response'))}")
            print(f"   Response length: {len(str(data.get('response', '')))}")
            print(f"   Conversation ID: {data.get('conversation_id', 'MISSING')}")
            print(f"   Tool calls: {len(data.get('tool_calls', []))}")
            
            if data.get('error'):
                print(f"   Error: {data.get('error')}")
        else:
            print(f"   Error response: {response.text}")
            
    except Exception as e:
        print(f"   Exception: {e}")

if __name__ == "__main__":
    test_copilot_debug()
