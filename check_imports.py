#!/usr/bin/env python3
"""
Check all imports to identify issues
"""

def check_imports():
    print("🔍 Checking imports...")
    
    try:
        print("1. Basic imports...")
        import sys
        import os
        print("   ✅ sys, os")
        
        print("2. FastAPI imports...")
        import fastapi
        print("   ✅ fastapi")
        
        print("3. SQLAlchemy imports...")
        import sqlalchemy
        print("   ✅ sqlalchemy")
        
        print("4. OpenAI imports...")
        from openai import OpenAI
        print("   ✅ openai")
        
        print("5. App config...")
        from app.core.config import settings
        print(f"   ✅ config (OpenAI key: {bool(settings.OPENAI_API_KEY)})")
        
        print("6. App main...")
        from app.main import app
        print("   ✅ app.main")
        
        print("7. LLM service...")
        from app.core.llm_service import LLMService
        print("   ✅ llm_service")
        
        print("8. Intelligent copilot...")
        from app.services.intelligent_copilot import IntelligentCopilot
        print("   ✅ intelligent_copilot")
        
        print("9. Copilot endpoint...")
        from app.api.v1.endpoints.copilot import router
        print("   ✅ copilot endpoint")
        
        print("\n🎉 All imports successful!")
        return True
        
    except Exception as e:
        print(f"\n❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_imports()
