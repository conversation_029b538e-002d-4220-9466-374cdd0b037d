#!/usr/bin/env python3
"""
Test Copilot Function Calling - Specific tests for LLM tool usage
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8010"

def setup_auth():
    """Setup authentication and return headers"""
    session = requests.Session()
    
    user_data = {
        "email": f"functest.{int(time.time())}@example.com",
        "password": "TestPassword123!",
        "first_name": "Function",
        "last_name": "Test"
    }
    
    # Register
    response = session.post(f"{BASE_URL}/api/v1/auth/register-local", json=user_data)
    if response.status_code not in [200, 201]:
        raise Exception(f"Registration failed: {response.text}")
    
    # Login
    login_data = {"email": user_data["email"], "password": user_data["password"]}
    response = session.post(f"{BASE_URL}/api/v1/auth/login-local", json=login_data)
    if response.status_code != 200:
        raise Exception(f"Login failed: {response.text}")
    
    token = response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    return session, headers

def create_test_data(session, headers):
    """Create some test persons for network operations"""
    test_persons = [
        {
            "first_name": "Alice",
            "last_name": "Johnson",
            "contact_info": {"email": "<EMAIL>"},
            "professional_info": {
                "company": "TechCorp",
                "title": "Software Engineer",
                "industry": "Technology"
            }
        },
        {
            "first_name": "Bob",
            "last_name": "Smith",
            "contact_info": {"email": "<EMAIL>"},
            "professional_info": {
                "company": "AI Company",
                "title": "ML Engineer",
                "industry": "AI"
            }
        }
    ]
    
    created_persons = []
    for person_data in test_persons:
        try:
            response = session.post(
                f"{BASE_URL}/api/v1/persons",
                json=person_data,
                headers=headers
            )
            if response.status_code in [200, 201]:
                created_persons.append(response.json())
        except Exception as e:
            print(f"Warning: Failed to create test person: {e}")
    
    return created_persons

def test_function_calling():
    """Test specific function calling scenarios"""
    print("🔧 Testing Copilot Function Calling")
    print("=" * 50)
    
    # Setup
    session, headers = setup_auth()
    print("✅ Authentication setup complete")
    
    # Create test data
    persons = create_test_data(session, headers)
    print(f"✅ Created {len(persons)} test persons")
    
    conversation_id = None
    test_results = []
    
    # Test scenarios that should trigger function calls
    test_scenarios = [
        {
            "name": "Network Search Request",
            "message": "Please search my network for people who work at technology companies",
            "expected_tool": "search_network"
        },
        {
            "name": "Task Creation Request", 
            "message": "Create a task for me to follow up with Alice Johnson next week",
            "expected_tool": "create_task"
        },
        {
            "name": "Network Health Request",
            "message": "Can you analyze my network health and give me a diagnosis?",
            "expected_tool": "get_network_health"
        },
        {
            "name": "Goal Creation Request",
            "message": "I want to set a goal to connect with 5 new people in the AI industry",
            "expected_tool": "create_goal"
        },
        {
            "name": "Recent Tasks Request",
            "message": "Show me my recent tasks and what I need to do",
            "expected_tool": "get_recent_tasks"
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{i}. Testing: {scenario['name']}")
        
        message_data = {
            "message": scenario["message"]
        }
        
        if conversation_id:
            message_data["conversation_id"] = conversation_id
        
        try:
            response = session.post(
                f"{BASE_URL}/api/v1/copilot/converse",
                json=message_data,
                headers=headers,
                timeout=45  # Longer timeout for LLM processing
            )
            
            if response.status_code == 200:
                data = response.json()
                conversation_id = data.get("conversation_id")
                
                tool_calls = data.get("tool_calls", [])
                response_text = data.get("response", "")
                
                # Check if expected tool was called
                expected_tool_called = any(
                    tc.get("name") == scenario["expected_tool"] 
                    for tc in tool_calls
                )
                
                # Check if response is relevant even without tool calls
                response_relevant = any(
                    keyword in response_text.lower() 
                    for keyword in ["network", "task", "goal", "health", "search", "analyze"]
                )
                
                if expected_tool_called:
                    print(f"   ✅ Expected tool '{scenario['expected_tool']}' was called")
                    tool_status = [tc for tc in tool_calls if tc.get("name") == scenario["expected_tool"]][0]
                    print(f"   📊 Tool status: {tool_status.get('status')}")
                elif tool_calls:
                    print(f"   ⚠️  Different tools called: {[tc.get('name') for tc in tool_calls]}")
                elif response_relevant:
                    print(f"   ⚠️  No tools called, but response is relevant")
                else:
                    print(f"   ❌ No relevant tools called and response not relevant")
                
                print(f"   💬 Response: {response_text[:100]}...")
                
                test_results.append({
                    "scenario": scenario["name"],
                    "expected_tool": scenario["expected_tool"],
                    "tools_called": [tc.get("name") for tc in tool_calls],
                    "expected_tool_called": expected_tool_called,
                    "response_relevant": response_relevant,
                    "success": expected_tool_called or response_relevant
                })
                
            else:
                print(f"   ❌ Request failed: {response.status_code} - {response.text}")
                test_results.append({
                    "scenario": scenario["name"],
                    "success": False,
                    "error": f"HTTP {response.status_code}"
                })
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            test_results.append({
                "scenario": scenario["name"],
                "success": False,
                "error": str(e)
            })
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 FUNCTION CALLING TEST SUMMARY")
    print("=" * 50)
    
    successful_tests = sum(1 for r in test_results if r.get("success", False))
    total_tests = len(test_results)
    
    for result in test_results:
        status = "✅" if result.get("success", False) else "❌"
        print(f"{status} {result['scenario']}")
        
        if result.get("expected_tool_called"):
            print(f"   └─ ✅ Expected tool called")
        elif result.get("tools_called"):
            print(f"   └─ ⚠️  Other tools: {result['tools_called']}")
        elif result.get("response_relevant"):
            print(f"   └─ ⚠️  Relevant response without tools")
        elif result.get("error"):
            print(f"   └─ ❌ Error: {result['error']}")
    
    print(f"\n🎯 Results: {successful_tests}/{total_tests} scenarios successful")
    print(f"📊 Success Rate: {(successful_tests/total_tests*100):.1f}%")
    
    # Save detailed report
    report = {
        "timestamp": datetime.now().isoformat(),
        "test_type": "function_calling_test",
        "total_scenarios": total_tests,
        "successful_scenarios": successful_tests,
        "success_rate": successful_tests/total_tests*100,
        "conversation_id": conversation_id,
        "test_results": test_results,
        "test_persons_created": len(persons)
    }
    
    filename = f"copilot_function_calling_report_{int(time.time())}.json"
    with open(filename, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"📄 Detailed report saved: {filename}")
    
    if successful_tests == total_tests:
        print("\n🎉 All function calling tests successful!")
    else:
        print(f"\n⚠️  {total_tests - successful_tests} test(s) need attention.")
        print("Note: Some scenarios may work with LLM reasoning even without exact tool calls.")

if __name__ == "__main__":
    test_function_calling()
