# 🤖 Nexus LLM-Powered Copilot

## 🚀 Overview

The Nexus Copilot has been enhanced with Large Language Model (LLM) capabilities and function calling to provide intelligent, context-aware assistance for managing your professional network. The copilot can now understand natural language, execute complex tasks, and provide personalized insights.

## ✨ Key Features

### 🧠 **Intelligent Conversation**
- Natural language understanding and processing
- Context-aware multi-turn conversations
- Intent recognition and entity extraction
- Personalized responses based on your network data

### 🛠️ **Function Calling**
- **Network Search**: Find people, companies, or connections
- **Task Management**: Create and manage networking tasks
- **Goal Setting**: Set and track professional goals
- **Network Analysis**: Get health insights and recommendations
- **Relationship Analysis**: Analyze connection strength and quality
- **AI Suggestions**: Receive proactive networking recommendations

### 💬 **Conversation Management**
- Persistent conversation history
- Context preservation across sessions
- Conversation analysis and insights

## 🔧 Setup and Configuration

### 1. **OpenAI API Configuration**

To enable LLM functionality, you need an OpenAI API key:

1. Get your API key from [OpenAI Platform](https://platform.openai.com/api-keys)
2. Copy `.env.copilot.example` to `.env`
3. Update the configuration:

```bash
# Required for LLM functionality
OPENAI_API_KEY=sk-your-openai-api-key-here

# Optional: Use different model
OPENAI_MODEL_NAME=gpt-4  # or gpt-3.5-turbo

# Optional: Use different API endpoint
OPENAI_API_BASE=https://api.openai.com/v1
```

### 2. **Start the Server**

```bash
uvicorn app.main:app --host 0.0.0.0 --port 8010 --reload
```

### 3. **Test the Functionality**

```bash
python test_copilot_llm.py
```

## 📡 API Endpoints

### **POST /api/v1/copilot/converse**
Intelligent conversation with function calling

```json
{
  "message": "Can you help me find connections at tech companies?",
  "conversation_id": "optional-conversation-id",
  "context": {}
}
```

**Response:**
```json
{
  "response": "I found 5 connections at tech companies...",
  "conversation_id": "uuid",
  "tool_calls": [
    {
      "name": "search_network",
      "arguments": {"query": "tech companies"},
      "result": {...},
      "status": "success"
    }
  ],
  "timestamp": "2025-01-09T10:30:00Z"
}
```

### **GET /api/v1/copilot/suggestions**
Enhanced AI suggestions with LLM analysis

```json
{
  "suggestions": [...],
  "network_insights": {...},
  "llm_recommendations": "Based on your network...",
  "context": {...}
}
```

### **POST /api/v1/copilot/analyze**
Analyze message intent and entities

```json
{
  "message": "I want to create a goal to expand my AI network"
}
```

**Response:**
```json
{
  "intent": "create_goal",
  "confidence": 0.95,
  "entities": [
    {"type": "industry", "value": "AI", "confidence": 0.9}
  ],
  "suggested_actions": [...]
}
```

### **GET /api/v1/copilot/conversation/{id}**
Get conversation history

### **DELETE /api/v1/copilot/conversation/{id}**
Clear conversation history

## 🎯 Usage Examples

### **Network Search**
```
User: "Find people who work at Google or Microsoft"
Copilot: [Searches network and returns relevant connections]
```

### **Task Creation**
```
User: "Remind me to follow up with Sarah about the project next week"
Copilot: [Creates task with due date and person association]
```

### **Network Analysis**
```
User: "How healthy is my professional network?"
Copilot: [Analyzes network metrics and provides insights]
```

### **Goal Setting**
```
User: "I want to connect with 10 new people in the AI industry this quarter"
Copilot: [Creates goal with metrics and suggests action plan]
```

### **Relationship Insights**
```
User: "Tell me about my relationship with John Smith"
Copilot: [Analyzes relationship strength and interaction history]
```

## 🔧 Available Tools

The LLM can call these functions to help you:

| Tool | Description | Example Use |
|------|-------------|-------------|
| `search_network` | Search your network | "Find engineers at startups" |
| `get_network_health` | Analyze network health | "How's my network doing?" |
| `create_task` | Create networking tasks | "Remind me to call John" |
| `create_goal` | Set networking goals | "Goal: 5 new connections" |
| `get_recent_tasks` | View recent tasks | "What are my pending tasks?" |
| `get_goals` | View goals | "Show my networking goals" |
| `find_mutual_connections` | Find mutual connections | "Who do John and I both know?" |
| `get_ai_suggestions` | Get AI recommendations | "Any networking suggestions?" |
| `analyze_relationship_strength` | Analyze relationships | "How strong is my connection with Sarah?" |

## 🎨 Conversation Examples

### **Multi-turn Conversation**
```
User: "I'm looking for connections in the AI space"
Copilot: "I found 8 people in your network working in AI. Would you like me to show you the details or help you plan outreach?"

User: "Show me the top 3 and help me plan outreach"
Copilot: [Shows top connections and creates outreach tasks]

User: "Also create a goal to meet 5 new AI professionals this month"
Copilot: [Creates goal with tracking metrics]
```

### **Complex Request**
```
User: "I want to strengthen my relationship with people at tech companies. Can you analyze my current connections, suggest who to reach out to, and create some follow-up tasks?"

Copilot: [Executes multiple tools: network analysis, connection search, task creation, and provides comprehensive response]
```

## 🔄 Fallback Behavior

If OpenAI API is not configured:
- Basic conversation still works with predefined responses
- Simple intent analysis using keyword matching
- Core functionality remains available
- Suggestions use the existing AI engine

## 🚨 Error Handling

The system gracefully handles:
- API rate limits and errors
- Invalid tool calls
- Network connectivity issues
- Malformed requests

## 📊 Performance

- **Response Time**: Typically 1-3 seconds for simple queries
- **Function Calls**: 2-5 seconds for complex operations
- **Context**: Maintains last 10 messages for efficiency
- **Caching**: Tool results cached when appropriate

## 🔒 Privacy and Security

- Conversations are stored locally (not sent to external services except OpenAI)
- User data is never shared between accounts
- API keys are securely managed
- All network data remains private

## 🧪 Testing

Run the comprehensive test suite:

```bash
python test_copilot_llm.py
```

This tests:
- ✅ Basic conversation
- ✅ Network search functionality
- ✅ Task creation
- ✅ Network health analysis
- ✅ Intent analysis
- ✅ AI suggestions

## 🛠️ Development

### **Adding New Tools**

1. Add function to `CopilotTools` class
2. Add schema to `TOOL_SCHEMAS`
3. Register in `IntelligentCopilot._register_tools()`

### **Customizing Prompts**

Modify `IntelligentCopilot._get_system_prompt()` to customize the copilot's behavior and personality.

### **Extending Functionality**

The modular design allows easy extension:
- Add new endpoints in `copilot.py`
- Create new tool functions
- Enhance conversation analysis
- Add custom LLM providers

## 📈 Future Enhancements

- **Voice Integration**: Speech-to-text and text-to-speech
- **Multi-modal**: Image and document analysis
- **Advanced Analytics**: Predictive networking insights
- **Integration**: Calendar, email, and CRM connections
- **Collaboration**: Team networking features

## 🆘 Troubleshooting

### **Common Issues**

1. **"AI assistant not configured"**
   - Set `OPENAI_API_KEY` in environment variables

2. **Slow responses**
   - Check internet connection
   - Consider using `gpt-3.5-turbo` for faster responses

3. **Tool calls failing**
   - Check database connectivity
   - Verify user permissions

4. **Empty suggestions**
   - Add more data to your network
   - Check AI engine configuration

### **Getting Help**

- Check the test output: `python test_copilot_llm.py`
- Review API logs for detailed error messages
- Ensure all dependencies are installed
- Verify environment configuration

---

**🎉 The LLM-powered Copilot transforms Nexus into an intelligent networking assistant that understands natural language and can execute complex tasks to help you build meaningful professional relationships!**
