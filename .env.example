# Environment Configuration Example
# Copy this file to .env and fill in your actual values

# Basic Settings
DEBUG=True
PROJECT_NAME="Nexus Backend"
VERSION="1.0.0"

# Security
SECRET_KEY=your-super-secret-key-here-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=10080

# Database - Choose one of the following options:

# Option 1: Local PostgreSQL (for debugging)
# DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/nexus_db

# Option 2: Supabase (production/cloud)
SUPABASE_URL="your-supabase-url-here"
SUPABASE_KEY="your-supabase-key-here"
# DATABASE_URL=postgresql+asyncpg://postgres:[YOUR-PASSWORD]@[YOUR-SUBDOMAIN].supabase.co:5432/postgres

# Option 3: SQLite (testing/development)
# DATABASE_URL=sqlite+aiosqlite:///./nexus.db

# CORS
ALLOWED_HOSTS=http://localhost:3000,http://localhost:8080

# AI/LLM Settings
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_MODEL_NAME=gpt-4

# Redis (optional)
REDIS_URL=redis://localhost:6379

# File Storage
UPLOAD_DIR=uploads
MAX_UPLOAD_SIZE=10485760

# Logging
LOG_LEVEL=INFO
