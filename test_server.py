#!/usr/bin/env python3
"""
Simple test script to check if the server is running
"""
import time
import requests
import sys

def test_server():
    """Test if the server is responding"""
    url = "http://localhost:8010/health"
    max_attempts = 10
    
    for attempt in range(max_attempts):
        try:
            print(f"Attempt {attempt + 1}: Testing {url}")
            response = requests.get(url, timeout=5)
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.json()}")
            return True
        except requests.exceptions.ConnectionError:
            print(f"Connection failed, waiting 2 seconds...")
            time.sleep(2)
        except Exception as e:
            print(f"Error: {e}")
            time.sleep(2)
    
    print("Server is not responding after all attempts")
    return False

if __name__ == "__main__":
    if test_server():
        print("✅ Server is running successfully!")
        sys.exit(0)
    else:
        print("❌ Server is not responding")
        sys.exit(1)
