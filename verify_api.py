#!/usr/bin/env python3
"""
Simple API verification script to test core functionality
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8010"

def test_health_check():
    """Test basic health check"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Health check: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_docs_endpoint():
    """Test API documentation endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/docs")
        print(f"Docs endpoint: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"Docs endpoint failed: {e}")
        return False

def test_openapi_spec():
    """Test OpenAPI specification endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/openapi.json")
        print(f"OpenAPI spec: {response.status_code}")
        if response.status_code == 200:
            spec = response.json()
            print(f"API title: {spec.get('info', {}).get('title', 'Unknown')}")
            print(f"API version: {spec.get('info', {}).get('version', 'Unknown')}")
            print(f"Available paths: {len(spec.get('paths', {}))}")
        return response.status_code == 200
    except Exception as e:
        print(f"OpenAPI spec failed: {e}")
        return False

def test_auth_endpoint():
    """Test authentication endpoint exists"""
    try:
        # This should return 422 (validation error) for missing data, not 404
        response = requests.post(f"{BASE_URL}/api/v1/auth/login")
        print(f"Auth endpoint: {response.status_code}")
        return response.status_code in [422, 400]  # Expected validation errors
    except Exception as e:
        print(f"Auth endpoint failed: {e}")
        return False

def main():
    """Run all verification tests"""
    print("🔍 Verifying Nexus Backend API...")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health_check),
        ("Documentation", test_docs_endpoint),
        ("OpenAPI Specification", test_openapi_spec),
        ("Authentication Endpoint", test_auth_endpoint),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {status}")
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All API verification tests passed!")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
