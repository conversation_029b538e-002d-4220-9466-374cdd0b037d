/home/<USER>/.local/lib/python3.12/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
INFO:     Started server process [20507]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8010 (Press CTRL+C to quit)
DB_SOURCE: sqlite
Selected DATABASE_URL: sqlite:///./nexus.db
INFO:     127.0.0.1:45394 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "POST /api/v1/auth/register-local HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "POST /api/v1/auth/login-local HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "POST /api/v1/persons HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:45394 - "POST /api/v1/persons/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "POST /api/v1/persons HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:45394 - "POST /api/v1/persons/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "POST /api/v1/persons HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:45394 - "POST /api/v1/persons/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "POST /api/v1/copilot/converse HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "POST /api/v1/copilot/converse HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "POST /api/v1/copilot/converse HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "POST /api/v1/copilot/converse HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "POST /api/v1/copilot/converse HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "POST /api/v1/copilot/analyze HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "GET /api/v1/copilot/suggestions?limit=5 HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "GET /api/v1/copilot/conversation/5f0a9c2b-48b4-4fa9-a7e4-1ddc86e3a576 HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "POST /api/v1/copilot/converse HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "POST /api/v1/copilot/converse HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "POST /api/v1/copilot/converse HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "POST /api/v1/copilot/converse HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "DELETE /api/v1/tasks/7ba16b24-7a5f-48e9-a825-9be7dae714a1 HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "DELETE /api/v1/goals/491f4950-c94c-48d5-947f-942f37f8b883 HTTP/1.1" 200 OK
INFO:     127.0.0.1:45394 - "DELETE /api/v1/persons/6c138586-b1cc-45f7-b945-70988da596a0 HTTP/1.1" 204 No Content
INFO:     127.0.0.1:45394 - "DELETE /api/v1/persons/6b4b84b7-7130-4151-bde9-1ca39dcf336a HTTP/1.1" 204 No Content
INFO:     127.0.0.1:45394 - "DELETE /api/v1/persons/10a65e82-65a6-4989-98c6-9af668e175f9 HTTP/1.1" 204 No Content
INFO:     127.0.0.1:45410 - "POST /api/v1/auth/register-local HTTP/1.1" 200 OK
INFO:     127.0.0.1:45422 - "POST /api/v1/auth/login-local HTTP/1.1" 200 OK
INFO:     127.0.0.1:45434 - "POST /api/v1/copilot/converse HTTP/1.1" 200 OK
INFO:     127.0.0.1:42882 - "POST /api/v1/copilot/converse HTTP/1.1" 200 OK
INFO:     127.0.0.1:42894 - "GET /api/v1/copilot/conversation/2ca366c6-b99a-43b6-9fbf-52275e337b15 HTTP/1.1" 200 OK
INFO:     127.0.0.1:42898 - "POST /api/v1/copilot/converse HTTP/1.1" 200 OK
INFO:     127.0.0.1:45602 - "GET /api/v1/copilot/conversation/2ca366c6-b99a-43b6-9fbf-52275e337b15 HTTP/1.1" 200 OK
