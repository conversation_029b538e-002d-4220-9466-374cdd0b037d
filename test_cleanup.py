#!/usr/bin/env python3
"""
Test the new cleanup functionality
"""

import requests
import json

BASE_URL = "http://localhost:8010"

def test_cleanup():
    print("Testing cleanup functionality...")

    # 1. Try to register user first (in case they don't exist)
    register_data = {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "first_name": "Test",
        "last_name": "User"
    }

    register_response = requests.post(f"{BASE_URL}/api/v1/auth/register-local", json=register_data)
    print(f"Registration: {register_response.status_code}")

    # 2. Login to get token
    login_data = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }

    login_response = requests.post(f"{BASE_URL}/api/v1/auth/login-local", json=login_data)
    print(f"Login: {login_response.status_code}")

    if login_response.status_code != 200:
        print(f"Login failed: {login_response.text}")
        return
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print(f"Token: {token[:50]}...")  # Print first 50 chars of token

    # Test JWT decoding
    try:
        from jose import jwt
        payload = jwt.decode(token, key="", options={"verify_signature": False, "verify_aud": False})
        print(f"JWT payload: {payload}")
    except Exception as e:
        print(f"JWT decode error: {e}")

    # 3. Check current data status
    status_response = requests.get(f"{BASE_URL}/api/v1/admin/test-data-status", headers=headers)
    print(f"Data status check: {status_response.status_code}")
    if status_response.status_code == 200:
        status_data = status_response.json()
        print(f"Current data counts: {status_data.get('data_counts', {})}")
        print(f"Total records: {status_data.get('total_records', 0)}")
    
    # 4. Test cleanup
    cleanup_response = requests.post(f"{BASE_URL}/api/v1/admin/reset-test-data", headers=headers)
    print(f"Cleanup: {cleanup_response.status_code}")
    if cleanup_response.status_code == 200:
        cleanup_data = cleanup_response.json()
        print(f"Cleanup result: {cleanup_data}")
    else:
        print(f"Cleanup failed: {cleanup_response.text}")
    
    # 5. Check data status after cleanup
    status_response2 = requests.get(f"{BASE_URL}/api/v1/admin/test-data-status", headers=headers)
    print(f"Data status check after cleanup: {status_response2.status_code}")
    if status_response2.status_code == 200:
        status_data2 = status_response2.json()
        print(f"Data counts after cleanup: {status_data2.get('data_counts', {})}")
        print(f"Total records after cleanup: {status_data2.get('total_records', 0)}")

if __name__ == "__main__":
    test_cleanup()
