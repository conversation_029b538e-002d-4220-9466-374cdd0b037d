# Nexus 后端技术文档 v3.0 (最终完整版)

**文档版本:** 3.0  
**日期:** 2025年6月18日  
**核心架构:** 后端主导，用户授权的灵活隐私模型

---

## **第一部分：总览与核心设计**

### **1. 愿景与目标**

本文档旨在为 Nexus 后端的开发提供一份全面、清晰、可执行的生产级技术指南。它将确保产品需求（PRD）中的每一项功能，都对应到具体、可实现的技术设计。

*   **核心架构定位**: 系统以**云端后端为核心**，负责主要业务逻辑、AI 计算和数据持久化。
*   **灵活的隐私保护**: 用户的姓名信息将被用于 AI 分析，而其他敏感 PII（如电话、邮箱）则由用户自主选择是否上传。

### **2. 需求可追溯性矩阵 (Requirements Traceability Matrix)**

本矩阵是文档的核心，旨在确保所有产品功能都得到完整的设计覆盖。

| PRD 功能模块 (章节) | 功能点 | 后端服务模块 | 核心数据库实体 | 主要 API 端点 |
| :--- | :--- | :--- | :--- | :--- |
| **10.1 领航者中心** | 对话式接口 | `copilot.service` | `Interaction`, `Task`, `Person` | `POST /api/v1/copilot/converse` |
| | AI 主动建议 | `ai_engine.service` | `Person`, `Knows`, `Interaction` | `GET /api/v1/copilot/suggestions` |
| | 偏好校准建议 | `ai_engine.service` | `User_Profile` | `GET /api/v1/users/me/calibration-request`, `POST /api/v1/users/me/calibrate` |
| **10.2 人脉库** | CRUD: Person/Org | `contacts.service` | `Person`, `Organization` | `POST, GET, PUT, DELETE /api/v1/persons`, `.../organizations` |
| | 模糊搜索 | `contacts.service` | `Person` | `GET /api/v1/search/persons?q=...` |
| | 多维关系透视镜 | `ai_engine.service` | `Knows` | `GET /api/v1/persons/{id}/relationship-prism/{other_id}` |
| | 关系原型应用 | `contacts.service` | `Knows` | `POST /api/v1/persons/{id}/apply-archetype` |
| **10.3 关系图谱** | 网络图谱可视化 | `graph.service` | `Person`, `Knows` | `GET /api/v1/graph/full` |
| | 网络健康诊断 | `ai_engine.service` | `Person`, `Knows` | `GET /api/v1/ai/network/diagnosis` |
| | 引荐路径查找 | `ai_engine.service` | `Person`, `Knows` | `POST /api/v1/ai/network/referral-path` |
| **10.4 行动中心** | CRUD: Goal/Task | `goals_tasks.service` | `Goal`, `Task` | `POST, GET, PUT, DELETE /api/v1/goals`, `.../tasks` |
| | 目标情报仪表盘 | `goals_tasks.service` | `Goal`, `Task`, `Person` | `GET /api/v1/goals/{id}/dashboard` |
| **10.5 设置与集成** | 关系决策偏好设置 | `users.service` | `User_Profile` | `PUT /api/v1/users/me/preferences` |
| | 数据导入/导出 | `data_portability.service` | All | `POST /api/v1/data/export`, `POST /api/v1/data/import` |
| | 系统集成 | `integrations.service` | `Interaction` | `POST /api/v1/integrations/calendar/sync` |

---

## **第二部分：数据库设计 (v3.0 深度重构) - 集成 Supabase**

本次重构引入了多态关联、标签系统和用户行为日志，并全面迁移至 **Supabase** 作为后端的数据库和认证服务核心。这简化了基础设施管理，并提供了强大的实时功能。

**核心变化**:
*   **数据库**: 使用 Supabase 提供的 PostgreSQL 数据库。连接信息通过环境变量 `DATABASE_URL` 配置。
*   **认证**: 用户的注册、登录、密码管理和第三方认证（未来）完全由 Supabase Auth 处理。本地 `users` 表仅作为 Supabase 用户的镜像，用于存储应用特定的信息和关联关系。
*   **实时功能**: 未来可利用 Supabase Realtime 服务实现数据变更的实时推送。

### **3. 核心实体与关系 (Core Entities & Relationships)**

#### **3.1 完整 ER 图**

```mermaid
erDiagram
    Person {
        UUID person_id PK
        UUID user_id FK
        string first_name
        string last_name
        jsonb contact_info
        jsonb social_profiles
        jsonb personal_details
    }

    Organization {
        UUID org_id PK
        UUID user_id FK
        string name
        jsonb details
    }

    Knows {
        UUID from_person_id PK, FK
        UUID to_person_id PK, FK
        UUID user_id FK
        string archetype "关系原型"
        jsonb relationship_foundation
        jsonb relationship_depth
    }

    Interaction {
        UUID interaction_id PK
        UUID user_id FK
        string type
        string content_summary
        timestamp occurred_at
    }

    Interaction_Participant {
        UUID person_id PK, FK
        UUID interaction_id PK, FK
    }

    Works_At {
        UUID person_id PK, FK
        UUID org_id PK, FK
        string role
    }

    Goal {
        UUID goal_id PK
        UUID user_id FK
        string title
        string description
        string status
    }

    Task {
        UUID task_id PK
        UUID user_id FK
        UUID goal_id FK "Optional"
        string title
        integer priority
        boolean is_completed
        timestamp due_date
    }

    Note {
        UUID note_id PK
        UUID user_id FK
        string content
    }

    User_Profile {
        UUID user_id PK, FK
        jsonb stated_decision_factors
        jsonb learned_decision_factors
    }

    Tag {
        UUID tag_id PK
        UUID user_id FK
        string name
    }

    Taggings {
        UUID tag_id PK, FK
        UUID taggable_id PK "多态关联 ID"
        string taggable_type PK "多态关联类型 (e.g., 'Person', 'Goal')"
    }

    User_Action_Log {
        UUID log_id PK
        UUID user_id FK
        string action_type "e.g., 'TASK_PRIORITIZED', 'RELATIONSHIP_SCORED'"
        jsonb details "动作相关的上下文数据"
        timestamp created_at
    }

    User_Profile ||--|| Person : "belongs to"
    Person       }o--|| Knows : "is source of (from)"
    Person       }o--|| Knows : "is target of (to)"
    Person       ||--|{ Interaction_Participant : "participates in"
    Interaction  ||--|{ Interaction_Participant : "has participant"
    Person       }o--|{ Works_At : "works at"
    Organization ||--|{ Works_At : "employs"
    Goal         ||--|{ Task : "contains"
    Tag          ||--|{ Taggings : "is applied via"
```

#### **3.2 表结构详情**

本节详细描述了每个表的字段、类型和用途，作为对 ER 图的补充。

##### **1. `Person` - 核心联系人表**
*   `person_id` (UUID, PK): 实体的唯一标识符。
*   `user_id` (UUID, FK): 指向 `User_Profile` 表，标识该联系人属于哪个用户。
*   `first_name` (string): 名字。
*   `last_name` (string): 姓氏。
*   `contact_info` (jsonb): 结构化联系信息，如 `{"email": "...", "phone": "..."}`。
*   `social_profiles` (jsonb): 社交媒体链接，如 `{"linkedin": "...", "twitter": "..."}`。
*   `personal_details` (jsonb): 其他个人详情，如生日、纪念日等。

##### **2. `Organization` - 组织/公司表**
*   `org_id` (UUID, PK): 组织的唯一标识符。
*   `user_id` (UUID, FK): 标识该组织属于哪个用户。
*   `name` (string): 组织或公司的官方名称。
*   `details` (jsonb): 其他组织详情，如地址、行业、网站等。

##### **3. `Knows` - 关系表**
*   `from_person_id` (UUID, PK, FK): 关系的发起方，指向 `Person`。
*   `to_person_id` (UUID, PK, FK): 关系的接收方，指向 `Person`。
*   `user_id` (UUID, FK): 标识该关系属于哪个用户。
*   `archetype` (string): 关系原型，如 "朋友", "同事", "导师"。
*   `relationship_foundation` (jsonb): 关系基础，记录如何认识、共同点等。
*   `relationship_depth` (jsonb): 关系深度，可以包含多个维度的评分。

##### **4. `Interaction` - 互动记录表**
*   `interaction_id` (UUID, PK): 互动的唯一标识符。
*   `user_id` (UUID, FK): 标识该互动属于哪个用户。
*   `type` (string): 互动类型，如 "会议", "电话", "邮件"。
*   `content_summary` (string): AI 生成或用户输入的互动内容摘要。
*   `occurred_at` (timestamp): 互动发生的具体时间。

##### **5. `Interaction_Participant` - 互动参与者关联表**
*   `person_id` (UUID, PK, FK): 参与互动的联系人 ID，指向 `Person`。
*   `interaction_id` (UUID, PK, FK): 对应的互动 ID，指向 `Interaction`。

##### **6. `Works_At` - 工作关系表**
*   `person_id` (UUID, PK, FK): 联系人 ID，指向 `Person`。
*   `org_id` (UUID, PK, FK): 组织 ID，指向 `Organization`。
*   `role` (string): 在该组织中的职位或角色。

##### **7. `Goal` - 目标表**
*   `goal_id` (UUID, PK): 目标的唯一标识符。
*   `user_id` (UUID, FK): 标识该目标属于哪个用户。
*   `title` (string): 目标标题。
*   `description` (string): 目标的详细描述。
*   `status` (string): 目标当前状态，如 "进行中", "已完成", "已搁置"。

##### **8. `Task` - 任务表**
*   `task_id` (UUID, PK): 任务的唯一标识符。
*   `user_id` (UUID, FK): 标识该任务属于哪个用户。
*   `goal_id` (UUID, FK, Optional): 关联的目标 ID，可以为空。
*   `title` (string): 任务标题。
*   `priority` (integer): 任务优先级（例如 1-5）。
*   `is_completed` (boolean): 标记任务是否已完成。
*   `due_date` (timestamp): 任务的截止日期。

##### **9. `Note` - 笔记表**
*   `note_id` (UUID, PK): 笔记的唯一标识符。
*   `user_id` (UUID, FK): 标识该笔记属于哪个用户。
*   `content` (string): 笔记的文本内容。

##### **10. `User_Profile` - 用户画像表**
*   `user_id` (UUID, PK, FK): 用户的唯一标识符，通常与认证系统的用户 ID 关联。
*   `stated_decision_factors` (jsonb): 用户明确陈述的关系决策偏好因子。
*   `learned_decision_factors` (jsonb): 系统通过用户行为学习到的隐式决策偏好因子。

##### **11. `Tag` - 标签表**
*   `tag_id` (UUID, PK): 标签的唯一标识符。
*   `user_id` (UUID, FK): 标识该标签属于哪个用户。
*   `name` (string): 标签的名称，具有唯一性约束（在用户维度）。

##### **12. `Taggings` - 标签关联表 (多态)**
*   `tag_id` (UUID, PK, FK): 标签 ID，指向 `Tag`。
*   `taggable_id` (UUID, PK): 被标记对象的 ID (例如一个 `person_id` 或 `goal_id`)。
*   `taggable_type` (string, PK): 被标记对象的类型，值为表名，如 'Person', 'Goal'。

##### **13. `User_Action_Log` - 用户行为日志表**
*   `log_id` (UUID, PK): 日志记录的唯一标识符。
*   `user_id` (UUID, FK): 执行操作的用户 ID。
*   `action_type` (string): 描述用户行为的类型，如 'TASK_COMPLETED', 'RELATIONSHIP_ARCHETYPE_SET'。
*   `details` (jsonb): 与行为相关的上下文数据。
*   `created_at` (timestamp): 行为发生的时间。

### **4. 关系深度模型 (洋葱理论) 与六维度设计**

为了实现 PRD 中提到的高级关系分析功能，我们设计了一个基于“洋葱关系理论”的动态模型。该模型认为关系是多层且动态变化的，其核心数据载体是 `Knows` 表中的 `relationship_depth` (JSONB) 字段。

#### **4.1 洋葱关系理论的体现**

该理论将关系看作由不同层次的“亲密度”和“功能性”组成。我们的模型通过在 `relationship_depth` 字段中定义多个可量化的维度来实现这一点，这些维度共同描绘出一段关系的完整画像。

一个 `relationship_depth` 字段的示例结构如下：

```json
{
  "overall_score": 85,
  "trend": "positive",
  "last_updated_by_interaction": "interaction_uuid_123",
  "dimensions": {
    "emotional_intimacy": 70,
    "professional_collaboration": 90,
    "trust_level": 80,
    "communication_frequency": 95,
    "shared_experience_value": 60,
    "reciprocity_balance": 75
  },
  "history": [
    {"timestamp": "2025-05-20T10:00:00Z", "event": "Created", "score": 50},
    {"timestamp": "2025-06-18T14:30:00Z", "event": "Interaction: Project Kick-off", "score_change": "+15"}
  ]
}
```

#### **4.2 关系数据的六个核心维度**

我们在 `dimensions` 对象中预定义了六个核心维度来量化关系：

*   **情感亲密度 (Emotional Intimacy)**: 衡量双方在个人情感层面的连接深度。
*   **职业协作度 (Professional Collaboration)**: 评估双方在工作或项目中的协作效率与和谐度。
*   **信任水平 (Trust Level)**: 代表一方对另一方可靠性、诚信和能力的信赖程度。
*   **沟通频率 (Communication Frequency)**: 追踪双方互动的频次和近因度（最近一次互动的时间）。
*   **共同经验价值 (Shared Experience Value)**: 记录并量化双方共同经历的重大事件（如共同完成一个项目、一起参加一次旅行）所带来的关系增益。
*   **互惠平衡度 (Reciprocity Balance)**: 分析关系中的“给予”和“索取”是否平衡，例如一方是否总是在寻求帮助，而另一方总是在提供价值。

#### **4.3 互动如何导致关系变化**

系统通过以下流程实现由互动驱动的关系动态更新：

1.  **捕获互动**: 当一个新的 `Interaction` 记录被创建时（例如，通过日历同步、邮件集成或手动录入），系统会触发一个事件。
2.  **AI 引擎分析**: `ai_engine.service` 会消费这个事件。它利用自然语言处理（NLP）技术分析 `Interaction` 的 `content_summary`，提取关键信息，如互动的情感色彩（积极/消极）、核心议题（工作/私人）、以及参与者的贡献。
3.  **维度分数更新**: 根据分析结果，AI 引擎计算出本次互动对六个维度的影响。例如：
    *   一次成功的项目结案会议会显著提升 `professional_collaboration` 和 `trust_level`。
    *   一次频繁的私人通话会增加 `communication_frequency` 和 `emotional_intimacy`。
    *   用户在领航者中心标记某次互动“非常有帮助”，会提升 `reciprocity_balance`。
4.  **记录变化**: 更新后的维度分数、总体得分 (`overall_score`) 和趋势 (`trend`) 将被写回对应双方的 `Knows.relationship_depth` 字段。同时，变化的事件和原因会被追加到 `history` 数组中，确保了关系演变的可追溯性。
5.  **记录日志**: `User_Action_Log` 表会记录此次由系统（AI）自动完成的更新，`action_type` 可能为 `RELATIONSHIP_SCORE_AUTOUDPATED`，并在 `details` 中包含互动 ID 和分数变化详情。

这个闭环系统确保了用户的每一次互动都能被量化，并持续、动态地塑造其数字化的关系图谱，完美支撑了“多维关系透视镜”和“网络健康诊断”等高级功能。

### **5. 索引策略**
*   **B-Tree 索引**: 在所有外键字段 (`user_id`, `goal_id` 等) 和高频查询的字段上创建。
*   **GIN 索引**: 在 `Person.contact_info`, `Note.content` (用于全文搜索) 等 `JSONB` 或文本字段上创建。
*   **模糊搜索索引**: 使用 `pg_trgm` 扩展，在 `Person.first_name` 和 `last_name` 上创建 GIN/GIST 索引以支持高效的模糊搜索。

---

## **第三部分：后端服务与业务逻辑**

### **6. 领域服务设计 (全面)**

本节详细阐述了与需求可追溯性矩阵中对应的所有后端服务模块。

#### **6.1 `users.service` (用户服务)**
*   **职责**: 管理用户账户、认证、以及用户画像（决策偏好）。
*   **核心函数**:
    *   `get_user_profile(user_id)`: 获取用户的个人资料，包括陈述型和学习型决策因子。
    *   `update_preferences(user_id, preferences_data)`: 更新用户的陈述型决策因子。
    *   `get_calibration_request(user_id)`: 检查并返回待处理的偏好校准请求。
    *   `confirm_calibration(user_id, calibration_data)`: 用户处理校准请求（接受或拒绝）。

#### **6.2 `contacts.service` (人脉服务)**
*   **职责**: 管理 `Person`, `Organization` 实体及其关系 (`Knows`, `Works_At`) 的核心 CRUD 和业务逻辑。
*   **核心函数**:
    *   `create_person(user, person_data)`: 创建新的联系人。
    *   `get_person(user, person_id)`: 获取联系人详情。
    *   `search_persons(user, query)`: 根据关键词进行模糊搜索。
    *   `apply_archetype_to_relationship(user, person_id, archetype_data)`: 为关系应用原型模板。

#### **6.3 `copilot.service` (领航者服务)**
*   **职责**: 作为 NLU (自然语言理解) 入口，解析用户输入并协调其他服务。
*   **核心逻辑**: `handle_conversational_input` 函数将通过一系列分类器和实体提取器，将 "提醒我明天联系李四讨论A项目" 这样的输入，分解为 `(intent='create_task', entities={'contact': '李四', 'topic': 'A项目', 'time': 'tomorrow'})`，然后调用 `goals_tasks.service.create_task`。

#### **6.4 `goals_tasks.service` (目标与任务服务)**
*   **职责**: 管理 `Goal` 和 `Task` 实体，包括它们的层级关系和状态。
*   **核心函数**:
    *   `create_goal(user, goal_data)`: 创建新目标。
    *   `get_goal_dashboard(user, goal_id)`: 获取目标情报仪表盘所需的数据。
    *   `create_task_for_goal(user, goal_id, task_data)`: 为目标创建任务。
    *   `update_task_status(user, task_id, status)`: 更新任务状态。

#### **6.5 `graph.service` (图谱服务)**
*   **职责**: 负责提供网络图谱的可视化数据。它将原始的节点和边数据处理成前端可以直接渲染的格式（如 D3.js 或 Vis.js 兼容的格式）。
*   **核心函数**:
    *   `get_full_graph_data(user)`: 获取完整的、用于前端渲染的图谱数据。
    *   `get_filtered_graph_data(user, filter_params)`: 根据标签、组织等参数获取过滤后的图谱数据。

#### **6.6 `ai_engine.service` (AI 引擎服务)**
*   **职责**: 提供所有复杂的分析和学习功能。支持连接到任何与 OpenAI API 兼容的 LLM 服务。
*   **配置**: 通过环境变量进行配置：
    *   `OPENAI_API_KEY`: API 密钥。
    *   `OPENAI_API_BASE`: API 的基础 URL (例如 `https://api.openai.com/v1`)。
    *   `OPENAI_MODEL_NAME`: 要使用的模型名称 (例如 `gpt-4`, `claude-3-opus-20240229`)。
*   **核心函数**:
    *   `get_active_suggestions(user)`: 获取AI生成的主动建议。
    *   `get_relationship_prism(user, person1_id, person2_id)`: 获取两人关系的多维透视镜分析。
    *   `get_network_diagnosis(user)`: 获取网络健康诊断报告。
    *   `find_referral_path(user, start_person_id, end_person_id)`: 查找引荐路径。
    *   `run_preference_learning_job(user_id)`: (后台任务) 执行偏好学习算法。

#### **6.7 `data_portability.service` (数据可移植性服务)**
*   **职责**: 处理数据的导入和导出任务，通常是异步的。
*   **核心函数**:
    *   `trigger_export_job(user, format)`: 触发一个后台任务来生成用户数据的导出文件。
    *   `check_export_job_status(job_id)`: 检查导出任务的状态并返回下载链接。
    *   `handle_import_file(user, file_stream)`: 处理上传的数据文件并导入。

#### **6.8 `integrations.service` (集成服务)**
*   **职责**: 管理与外部服务（如 Google Calendar）的集成和数据同步。
*   **核心函数**:
    *   `sync_calendar_events(user, calendar_token)`: 从用户日历同步事件，创建为 `Interaction` 记录。

### **7. 核心用户流程与算法设计**

本节将 PRD 中的功能点串联成完整的用户流程，并详细阐述支持这些流程的核心算法设计，展现系统各部分如何协同工作。

#### **7.1 流程一：新用户引导与图谱冷启动 (Onboarding & Cold Start)**

这是用户首次与 Nexus 互动的关键流程，目标是快速帮助用户建立初始人脉图谱并感知产品价值。

1.  **数据导入**:
    *   **触发**: 用户通过 `data_portability.service` 上传联系人文件 (CSV/VCF) 或通过 `integrations.service` 连接外部账户 (如 Google Contacts)。
    *   **处理**: 后端服务解析数据，为每条记录创建 `Person` 实体。系统会进行初步的去重和实体对齐（例如，基于姓名和邮箱/电话）。
    *   **挑战**: 数据清洗和实体识别的准确性。

2.  **关系原型引导**:
    *   **触发**: 导入完成后，系统引导用户为核心联系人应用 `archetype` (关系原型)。
    *   **交互**: 前端展示联系人列表，用户通过 `contacts.service` 的 `apply_archetype_to_relationship` 接口批量或单个设置关系，如“同事”、“朋友”。
    *   **算法**: 此阶段不涉及复杂算法，但为后续的“关系深度”计算提供了基础分类。`Knows` 关系被创建，但 `relationship_depth` 字段处于初始状态。

3.  **首次价值交付**:
    *   **触发**: 用户完成初步关系设置。
    *   **交付**: `ai_engine.service` 立即进行一次基础的 `get_network_diagnosis` 计算，并向用户展示一个简版的网络健康报告，例如“您的人脉网络在‘技术’领域有较多专家，但在‘市场’领域相对薄弱”，并给出第一个 `suggestion`：“建议您与在市场部工作的[某同事]建立更深的联系”。
    *   **目的**: 让用户在完成最基础的设置后，立刻获得超越简单 CRM 的 AI 洞察。

#### **7.2 流程二：日常主动式关系管理 (Proactive Relationship Management)**

此流程是 Nexus 的核心价值体现，系统从被动记录工具转变为主动的智能助理。

1.  **持续数据输入**:
    *   用户通过 `integrations.service` 同步日历和邮件，系统自动创建 `Interaction` 记录。
    *   用户通过 `copilot.service` 的对话式接口手动记录互动或想法，如“刚和张三开完会，项目进展顺利”。

2.  **关系深度动态更新 (核心算法)**:
    *   **触发**: `Interaction` 记录被创建或更新。
    *   **算法**: 如 **4.3 节** 所述，`ai_engine.service` 被触发。
        1.  **NLP 分析**: 使用预训练的情感分析和主题建模模型，解析 `content_summary`。例如，识别出“进展顺利”（积极情感）、“项目”（职业协作）。
        2.  **维度映射**: 将 NLP 分析结果映射到六大关系维度。例如，“积极情感” -> `+5 emotional_intimacy`，“职业协作” -> `+10 professional_collaboration`。
        3.  **分数衰减**: (后台任务) 系统会定期运行一个衰减函数，降低所有关系中 `communication_frequency` 的分数，模拟关系的自然冷却，从而突出近期有互动的关系。
        4.  **更新**: 将计算出的变化更新到 `Knows.relationship_depth`，并记录到 `history` 和 `User_Action_Log`。

3.  **生成主动建议 (核心算法)**:
    *   **触发**: `ai_engine.service.get_active_suggestions` 被调用（或由后台任务定期生成）。
    *   **算法**:
        1.  **机会扫描**: 算法扫描所有 `Knows` 关系，寻找满足特定条件的“机会点”。
            *   **关系冷却**: `communication_frequency` 低于阈值且 `overall_score` 较高的关系 -> 生成“失联提醒”建议。
            *   **目标关联**: 某个 `Goal` 的描述与某个 `Person` 的标签/公司/角色高度相关，但该 `Person` 与用户的关系深度 (`overall_score`) 不高 -> 生成“为达目标，建议加深与此人联系”的建议。
            *   **网络洞察**: 发现网络中的结构洞（Structural Hole），即用户连接了两个互不相识但合作潜力巨大的社群 -> 生成“建议将 A 和 B 互相介绍”的引荐建议。
        2.  **建议优先级排序**: 根据用户的 `learned_decision_factors` 和建议的潜在价值对所有候选建议进行排序，确保推送最相关的建议。

#### **7.3 流程三：目标导向的精准人脉拓展 (Goal-Oriented Networking)**

此流程帮助用户利用其现有网络，高效达成特定目标。

1.  **设定目标**: 用户通过 `goals_tasks.service` 创建一个 `Goal`，例如“为我的新项目寻找天使投资人”。

2.  **查找引荐路径 (核心算法)**:
    *   **触发**: 用户在目标仪表盘上点击“查找关键人物”或直接使用 `find_referral_path` 功能。
    *   **算法 (`ai_engine.service.find_referral_path`)**:
        1.  **目标定义**: 首先，系统需要定义“谁是关键人物”。这可以通过 `Goal` 的描述和标签，在 `Person` 数据库中进行向量相似度搜索，找到一批目标人物（例如，标签为“投资人”且行业为“SaaS”的联系人）。
        2.  **图算法应用**: 将用户的关系网络 (`Person` 为节点, `Knows` 为边) 抽象成一个加权图。边的权重是 `Knows.relationship_depth.overall_score` 的倒数（分数越高，权重越低，路径越短）。
        3.  **路径搜索**: 以用户为起点，以每个目标人物为终点，运行 **Dijkstra** 或 **A*** 算法，查找加权最短路径。
        4.  **结果呈现**: 返回 1-3 条最佳引荐路径，并附上路径上每个人的信息和关系深度，例如：“您 -> 李四 (朋友, 90分) -> 王五 (前同事, 75分) -> 目标投资人”。

3.  **执行与追踪**:
    *   用户根据引荐路径，创建 `Task`，如“请李四帮忙引荐王五”。
    *   `Task` 的完成情况和后续的 `Interaction` 会反过来更新关系深度，形成一个完整的闭环。

#### **7.4 算法四：用户偏好自适应学习 (Preference Learning)**

该算法使系统能够从“千人一面”进化到“千人千面”，为每个用户提供高度个性化的体验。

*   **触发**: 由后台任务 `ai_engine.service.run_preference_learning_job` 定期执行 (例如，每周一次)。
*   **数据源**: `User_Action_Log` 表。
*   **算法**:
    1.  **行为向量化**: 分析过去一段时间用户的行为日志。
        *   用户频繁与 `archetype` 为“同事”且 `professional_collaboration` 分数高的人互动 -> 提取特征向量 `(archetype=colleague, dimension=professional, action=interact)`。
        *   用户优先完成与 `Goal` A 相关的 `Task`，而忽略了与 `Goal` B 相关的 -> 提取特征 `(goal_topic=A, action=prioritize)`。
        *   用户接受了“失联提醒”建议，但忽略了“引荐”建议 -> 提取特征 `(suggestion_type=reconnect, action=accept)`。
    2.  **因子更新**: 使用简单的**加权平均**或更复杂的**梯度提升树 (GBT)** 模型，根据这些行为向量，调整 `User_Profile.learned_decision_factors` 中的权重。
    3.  **应用**: 更新后的 `learned_decision_factors` 会被用于 **7.2 节** 中的建议优先级排序，从而使未来的建议更符合用户的真实偏好。

---

## **第四部分：API 规约**

### **8. 通用约定**

#### **8.1 API 版本控制**
*   **版本**: `/api/v1`
*   **版本策略**: 使用语义化版本控制，主版本号变更表示不兼容的API变更
*   **向后兼容**: 在同一主版本内保持向后兼容性

#### **8.2 认证与授权**
*   **认证方式**: `Authorization: Bearer <JWT>`
*   **Token 有效期**: Access Token 1小时，Refresh Token 30天
*   **权限模型**: 基于用户的资源隔离，用户只能访问自己的数据
*   **认证失败**: 返回 `401 Unauthorized`
*   **权限不足**: 返回 `403 Forbidden`

#### **8.3 请求与响应格式**
*   **Content-Type**: `application/json`
*   **字符编码**: UTF-8
*   **日期时间格式**: ISO 8601 (例如: `2024-06-22T10:00:00Z`)
*   **UUID 格式**: 标准 UUID v4 格式

#### **8.4 分页规范**
*   **查询参数**: `?skip=0&limit=100`
*   **默认限制**: 100条记录
*   **最大限制**: 1000条记录
*   **响应格式**:
```json
{
  "items": [...],
  "total": 1500,
  "skip": 0,
  "limit": 100,
  "has_more": true
}
```

#### **8.5 错误处理规范**
*   **标准错误响应格式**:
```json
{
  "code": "ERROR_CODE",
  "message": "Human readable error message",
  "details": {
    "field": "specific error details",
    "validation_errors": [...]
  },
  "request_id": "uuid-for-tracking"
}
```

*   **HTTP 状态码使用规范**:
    *   `200 OK`: 成功获取资源
    *   `201 Created`: 成功创建资源
    *   `204 No Content`: 成功删除资源或无内容返回
    *   `400 Bad Request`: 请求参数错误
    *   `401 Unauthorized`: 认证失败
    *   `403 Forbidden`: 权限不足
    *   `404 Not Found`: 资源不存在
    *   `409 Conflict`: 资源冲突（如重复创建）
    *   `422 Unprocessable Entity`: 数据验证失败
    *   `429 Too Many Requests`: 请求频率限制
    *   `500 Internal Server Error`: 服务器内部错误
    *   `502 Bad Gateway`: 外部服务错误
    *   `503 Service Unavailable`: 服务暂时不可用

*   **常见错误代码**:
    *   `VALIDATION_ERROR`: 数据验证失败
    *   `RESOURCE_NOT_FOUND`: 资源不存在
    *   `DUPLICATE_RESOURCE`: 资源重复
    *   `UNAUTHORIZED_ACCESS`: 未授权访问
    *   `RATE_LIMIT_EXCEEDED`: 请求频率超限
    *   `EXTERNAL_SERVICE_ERROR`: 外部服务错误
    *   `AI_SERVICE_UNAVAILABLE`: AI服务不可用

#### **8.6 性能要求**
*   **响应时间**:
    *   简单查询 (GET /persons/{id}): < 200ms
    *   复杂查询 (搜索): < 500ms
    *   AI分析 (关系透视镜): < 2s
    *   数据导出 (异步): 立即返回任务ID
*   **并发处理**: 支持每秒1000个并发请求
*   **可用性**: 99.9% SLA
*   **数据一致性**: 强一致性用于关键操作，最终一致性用于分析数据

#### **8.7 安全要求**
*   **数据加密**: 传输层使用TLS 1.3，敏感数据存储加密
*   **输入验证**: 所有用户输入进行严格验证和清理
*   **SQL注入防护**: 使用参数化查询
*   **XSS防护**: 输出编码和CSP策略
*   **CSRF防护**: 使用CSRF Token
*   **敏感信息**: 不在日志中记录密码、Token等敏感信息

### **9. API 端点详解**

#### **9.1 认证端点 (`/api/v1/auth`)**

##### **用户注册**
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "first_name": "John",
  "last_name": "Doe"
}
```

**响应 (201 Created)**:
```json
{
  "user_id": "uuid",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "is_active": true,
  "created_at": "2024-06-22T10:00:00Z"
}
```

**错误响应**:
*   `400`: 邮箱已存在、密码强度不足
*   `422`: 数据验证失败

##### **用户登录**
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

**响应 (200 OK)**:
```json
{
  "access_token": "jwt_token_here",
  "token_type": "bearer",
  "expires_in": 3600,
  "refresh_token": "refresh_token_here"
}
```

#### **9.2 用户管理端点 (`/api/v1/users`)**

##### **获取当前用户信息**
```http
GET /api/v1/users/me
Authorization: Bearer <token>
```

**响应 (200 OK)**:
```json
{
  "user_id": "uuid",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "is_active": true,
  "settings": {
    "notification_preferences": {...},
    "privacy_settings": {...}
  },
  "created_at": "2024-06-22T10:00:00Z",
  "last_login": "2024-06-22T10:00:00Z"
}
```

##### **更新用户偏好**
```http
PUT /api/v1/users/me/preferences
Authorization: Bearer <token>
Content-Type: application/json

{
  "stated_decision_factors": {
    "relationship_importance": 0.8,
    "professional_focus": 0.9,
    "personal_connection": 0.7
  },
  "notification_settings": {
    "email_notifications": true,
    "push_notifications": false
  }
}
```

#### **9.3 人脉管理端点 (`/api/v1/persons`)**

##### **创建联系人**
```http
POST /api/v1/persons
Authorization: Bearer <token>
Content-Type: application/json

{
  "first_name": "Jane",
  "last_name": "Smith",
  "contact_info": {
    "email": "<EMAIL>",
    "phone": "+1234567890"
  },
  "professional_info": {
    "title": "Software Engineer",
    "company": "TechCorp",
    "department": "Engineering"
  },
  "social_profiles": {
    "linkedin": "https://linkedin.com/in/janesmith"
  },
  "personal_details": {
    "birthday": "1990-01-15",
    "interests": ["coding", "photography"]
  }
}
```

**响应 (201 Created)**:
```json
{
  "person_id": "uuid",
  "user_id": "uuid",
  "first_name": "Jane",
  "last_name": "Smith",
  "contact_info": {...},
  "professional_info": {...},
  "social_profiles": {...},
  "personal_details": {...},
  "created_at": "2024-06-22T10:00:00Z",
  "updated_at": "2024-06-22T10:00:00Z"
}
```

##### **获取联系人列表**
```http
GET /api/v1/persons?skip=0&limit=100&search=jane
Authorization: Bearer <token>
```

**查询参数**:
*   `skip`: 跳过记录数 (默认: 0)
*   `limit`: 返回记录数 (默认: 100, 最大: 1000)
*   `search`: 搜索关键词 (可选)
*   `company`: 按公司筛选 (可选)
*   `archetype`: 按关系类型筛选 (可选)

##### **获取特定联系人**
```http
GET /api/v1/persons/{person_id}
Authorization: Bearer <token>
```

##### **更新联系人**
```http
PUT /api/v1/persons/{person_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "professional_info": {
    "title": "Senior Software Engineer",
    "company": "NewTechCorp"
  }
}
```

##### **删除联系人**
```http
DELETE /api/v1/persons/{person_id}
Authorization: Bearer <token>
```

**响应 (204 No Content)**

##### **应用关系原型**
```http
POST /api/v1/persons/{person_id}/apply-archetype
Authorization: Bearer <token>
Content-Type: application/json

{
  "target_person_id": "uuid",
  "archetype": "mentor",
  "archetype_details": {
    "expertise_areas": ["AI", "Machine Learning"],
    "mentorship_style": "hands_on"
  }
}
```

#### **9.4 组织管理端点 (`/api/v1/organizations`)**

##### **创建组织**
```http
POST /api/v1/organizations
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "TechCorp Industries",
  "description": "A leading technology company",
  "details": {
    "industry": "Technology",
    "size": "1000-5000",
    "website": "https://techcorp.com",
    "headquarters": "San Francisco, CA"
  }
}
```

#### **9.5 目标和任务管理端点 (`/api/v1/goals`, `/api/v1/tasks`)**

##### **创建目标**
```http
POST /api/v1/goals
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Find a mentor in AI/ML",
  "description": "Connect with experienced professionals in AI and ML",
  "status": "active",
  "target_date": "2024-12-31T23:59:59Z",
  "priority": 1,
  "category": "career"
}
```

##### **创建任务**
```http
POST /api/v1/tasks
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Research potential mentors",
  "description": "Create a list of potential AI/ML mentors",
  "goal_id": "uuid",
  "priority": 1,
  "is_completed": false,
  "due_date": "2024-07-01T10:00:00Z",
  "estimated_hours": 4
}
```

#### **9.6 AI功能端点 (`/api/v1/copilot`, `/api/v1/ai`)**

##### **Copilot对话**
```http
POST /api/v1/copilot/converse
Authorization: Bearer <token>
Content-Type: application/json

{
  "message": "Help me find a mentor in machine learning",
  "context": {
    "current_goals": ["learn_ml"],
    "user_background": "software_engineer"
  },
  "conversation_id": "uuid"
}
```

**响应 (200 OK)**:
```json
{
  "response": "I can help you find ML mentors. Based on your network...",
  "intent": "find_mentor",
  "entities": [
    {"type": "skill", "value": "machine learning"},
    {"type": "role", "value": "mentor"}
  ],
  "suggested_actions": [
    {
      "type": "create_task",
      "title": "Research ML mentors in network",
      "description": "Analyze existing contacts for ML expertise"
    }
  ],
  "conversation_id": "uuid",
  "confidence": 0.85
}
```

##### **获取AI建议**
```http
GET /api/v1/copilot/suggestions?type=reconnect&priority=high
Authorization: Bearer <token>
```

**响应 (200 OK)**:
```json
[
  {
    "id": "uuid",
    "type": "reconnect",
    "title": "Reconnect with John Doe",
    "description": "You haven't spoken with John in 3 months",
    "priority": "medium",
    "suggested_action": "Send a message to catch up",
    "person_id": "uuid",
    "created_at": "2024-06-22T10:00:00Z"
  }
]
```

##### **关系透视镜分析**
```http
GET /api/v1/persons/{person_id}/relationship-prism/{other_person_id}
Authorization: Bearer <token>
```

**响应 (200 OK)**:
```json
{
  "relationship_strength": 0.75,
  "dimensions": {
    "emotional_intimacy": 70,
    "professional_collaboration": 85,
    "trust_level": 80,
    "communication_frequency": 60,
    "shared_experience_value": 75,
    "reciprocity_balance": 70
  },
  "insights": [
    {
      "type": "strength",
      "message": "Strong professional collaboration"
    }
  ],
  "recommendations": [
    "Consider scheduling regular check-ins",
    "Explore opportunities for deeper collaboration"
  ],
  "last_updated": "2024-06-22T10:00:00Z"
}
```

##### **网络健康诊断**
```http
GET /api/v1/ai/network/diagnosis?include_insights=true
Authorization: Bearer <token>
```

**响应 (200 OK)**:
```json
{
  "overall_health": 0.75,
  "network_size": 150,
  "active_connections": 45,
  "dormant_connections": 105,
  "insights": [
    {
      "type": "strength",
      "message": "Strong connections in technology sector"
    },
    {
      "type": "weakness",
      "message": "Limited connections in marketing domain"
    }
  ],
  "recommendations": [
    "Reconnect with 5 dormant connections this month",
    "Attend marketing industry events"
  ],
  "generated_at": "2024-06-22T10:00:00Z"
}
```

##### **引荐路径查找**
```http
POST /api/v1/ai/network/referral-path
Authorization: Bearer <token>
Content-Type: application/json

{
  "target_person_name": "Elon Musk",
  "target_criteria": {
    "industry": "technology",
    "role": "CEO",
    "company": "Tesla"
  },
  "max_degrees": 3,
  "purpose": "job_referral"
}
```

**响应 (200 OK)**:
```json
{
  "paths": [
    {
      "steps": [
        {
          "person_id": "uuid",
          "person_name": "John Doe",
          "relationship_strength": 0.85,
          "connection_type": "direct"
        },
        {
          "person_id": "uuid",
          "person_name": "Jane Smith",
          "relationship_strength": 0.70,
          "connection_type": "colleague"
        },
        {
          "person_id": "uuid",
          "person_name": "Elon Musk",
          "relationship_strength": 0.60,
          "connection_type": "industry_contact"
        }
      ],
      "overall_strength": 0.72,
      "estimated_success_rate": 0.65
    }
  ],
  "alternative_strategies": [
    "Attend Tesla events",
    "Connect through industry conferences"
  ]
}
```

#### **9.7 数据导入导出端点 (`/api/v1/data`)**

##### **数据导出**
```http
POST /api/v1/data/export
Authorization: Bearer <token>
Content-Type: application/json

{
  "format": "json",
  "include": ["persons", "organizations", "goals", "tasks"],
  "options": {
    "include_relationships": true,
    "include_metadata": true,
    "compress": true
  }
}
```

**响应 (202 Accepted)**:
```json
{
  "task_id": "uuid",
  "status": "pending",
  "estimated_completion": "2024-06-22T10:05:00Z"
}
```

##### **检查导出状态**
```http
GET /api/v1/data/export/status/{task_id}
Authorization: Bearer <token>
```

##### **数据导入**
```http
POST /api/v1/data/import
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: contacts.csv
format: csv
options: {"mapping": {...}, "skip_duplicates": true}
```

#### **9.8 搜索端点 (`/api/v1/search`)**

##### **搜索联系人**
```http
GET /api/v1/search/persons?q=john&company=techcorp&limit=50
Authorization: Bearer <token>
```

##### **搜索组织**
```http
GET /api/v1/search/organizations?q=technology&industry=tech
Authorization: Bearer <token>
```

#### **9.9 图谱可视化端点 (`/api/v1/graph`)**

##### **获取完整网络图谱**
```http
GET /api/v1/graph/full?layout=force&node_types=person,organization
Authorization: Bearer <token>
```

**响应 (200 OK)**:
```json
{
  "nodes": [
    {
      "id": "uuid",
      "label": "John Doe",
      "type": "person",
      "size": 10,
      "color": "#3498db",
      "metadata": {
        "company": "TechCorp",
        "title": "Engineer"
      }
    }
  ],
  "edges": [
    {
      "source": "uuid1",
      "target": "uuid2",
      "weight": 0.85,
      "type": "knows",
      "label": "colleague"
    }
  ],
  "metadata": {
    "total_nodes": 150,
    "total_edges": 300,
    "generated_at": "2024-06-22T10:00:00Z"
  }
}
```

#### **9.10 集成端点 (`/api/v1/integrations`)**

##### **日历同步**
```http
POST /api/v1/integrations/calendar/sync
Authorization: Bearer <token>
Content-Type: application/json

{
  "connection_id": "uuid",
  "date_range": {
    "start": "2024-06-01",
    "end": "2024-06-30"
  },
  "options": {
    "create_persons_from_attendees": true,
    "update_interaction_history": true
  }
}
```

#### **9.11 异步任务端点 (`/api/v1/tasks/async`)**

##### **创建异步任务**
```http
POST /api/v1/tasks/async
Authorization: Bearer <token>
Content-Type: application/json

{
  "task_type": "data_export",
  "parameters": {
    "format": "json",
    "include": ["persons"]
  },
  "priority": "normal"
}
```

##### **获取任务状态**
```http
GET /api/v1/tasks/async/status/{task_id}
Authorization: Bearer <token>
```

**响应 (200 OK)**:
```json
{
  "task_id": "uuid",
  "status": "processing",
  "progress": {
    "percentage": 65,
    "current_item": 650,
    "total_items": 1000,
    "message": "Processing persons data..."
  },
  "created_at": "2024-06-22T10:00:00Z",
  "started_at": "2024-06-22T10:01:00Z",
  "estimated_completion": "2024-06-22T10:05:00Z"
}
```

---

## **第五部分：测试策略与质量保证**

### **10. 测试架构设计**

#### **10.1 测试金字塔**
*   **单元测试 (70%)**: 测试单个函数和类的逻辑
*   **集成测试 (20%)**: 测试服务间的交互和数据库操作
*   **端到端测试 (10%)**: 测试完整的用户流程

#### **10.2 测试工具栈**
*   **测试框架**: pytest
*   **HTTP客户端**: FastAPI TestClient
*   **数据库**: SQLite (测试环境)
*   **Mock工具**: unittest.mock
*   **覆盖率**: pytest-cov
*   **性能测试**: locust

#### **10.3 测试数据管理**
*   **测试数据工厂**: 使用工厂模式生成测试数据
*   **数据隔离**: 每个测试使用独立的数据库事务
*   **清理策略**: 测试后自动清理数据

### **11. 测试用例分类**

#### **11.1 认证和用户管理测试**
*   用户注册（成功、失败、边界条件）
*   用户登录（有效凭据、无效凭据、账户状态）
*   Token验证（有效、过期、格式错误）
*   用户偏好管理（创建、更新、验证）
*   跨用户数据隔离验证

#### **11.2 核心实体CRUD测试**
*   **Person实体**: 创建、读取、更新、删除、搜索
*   **Organization实体**: 完整CRUD操作和业务逻辑
*   **Goal实体**: 状态管理、优先级、关联关系
*   **Task实体**: 完成状态、截止日期、目标关联
*   数据验证和约束检查

#### **11.3 关系管理测试**
*   Knows关系的创建和管理
*   关系深度计算和更新
*   关系原型应用
*   WorksAt关系管理
*   关系数据一致性验证

#### **11.4 AI功能测试**
*   Copilot对话处理（意图识别、实体提取）
*   AI建议生成和过滤
*   网络诊断算法
*   引荐路径查找
*   AI服务故障处理

#### **11.5 搜索和图谱测试**
*   模糊搜索算法
*   搜索结果排序和分页
*   图谱数据生成
*   图谱过滤和布局
*   搜索性能测试

#### **11.6 数据导入导出测试**
*   CSV/JSON格式导入
*   数据验证和清理
*   重复数据处理
*   异步任务处理
*   大文件处理性能

#### **11.7 集成服务测试**
*   日历同步功能
*   邮件集成
*   社交媒体集成
*   外部API错误处理
*   Webhook处理

#### **11.8 异步任务测试**
*   任务创建和调度
*   任务状态跟踪
*   任务取消和重试
*   任务优先级处理
*   任务结果获取

### **12. 测试执行策略**

#### **12.1 持续集成测试**
*   每次提交触发单元测试
*   Pull Request触发完整测试套件
*   主分支部署前执行集成测试
*   定期执行性能回归测试

#### **12.2 测试环境管理**
*   开发环境：本地SQLite数据库
*   测试环境：独立的PostgreSQL实例
*   预生产环境：生产环境的镜像
*   生产环境：只读监控测试

#### **12.3 测试数据策略**
*   使用工厂模式生成一致的测试数据
*   为不同测试场景准备专门的数据集
*   定期更新测试数据以反映真实使用场景
*   敏感数据脱敏处理

### **13. 质量指标和监控**

#### **13.1 代码覆盖率要求**
*   单元测试覆盖率: ≥ 90%
*   集成测试覆盖率: ≥ 80%
*   关键业务逻辑覆盖率: 100%
*   新增代码覆盖率: ≥ 95%

#### **13.2 性能基准**
*   API响应时间: P95 < 500ms
*   数据库查询时间: P95 < 100ms
*   AI分析响应时间: P95 < 2s
*   并发用户支持: 1000+ 同时在线

#### **13.3 可靠性指标**
*   系统可用性: 99.9%
*   错误率: < 0.1%
*   数据一致性: 100%
*   安全漏洞: 0个高危漏洞

### **14. API测试最佳实践**

#### **14.1 测试用例设计原则**
*   **独立性**: 每个测试用例应该独立运行，不依赖其他测试的状态
*   **可重复性**: 测试结果应该在任何环境下都是一致的
*   **清晰性**: 测试名称和断言应该清楚表达测试意图
*   **完整性**: 覆盖正常流程、异常情况和边界条件

#### **14.2 测试数据管理**
```python
# 使用工厂模式创建测试数据
class TestDataFactory:
    @staticmethod
    def create_user_data(email="<EMAIL>"):
        return {
            "email": email,
            "password": "testpassword123",
            "first_name": "Test",
            "last_name": "User"
        }

    @staticmethod
    def create_person_data(**kwargs):
        default_data = {
            "first_name": "John",
            "last_name": "Doe",
            "contact_info": {"email": "<EMAIL>"}
        }
        default_data.update(kwargs)
        return default_data
```

#### **14.3 认证测试模式**
```python
class BaseAPITestCase:
    def authenticate_user(self, client, user_data=None):
        """认证用户并返回认证头"""
        if user_data is None:
            user_data = TestDataFactory.create_user_data()

        # 注册用户
        register_response = client.post("/api/v1/auth/register", json=user_data)
        assert register_response.status_code == 200

        # 登录获取token
        login_data = {"email": user_data["email"], "password": user_data["password"]}
        login_response = client.post("/api/v1/auth/login", json=login_data)
        assert login_response.status_code == 200

        token = login_response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
```

#### **14.4 错误处理测试**
```python
def test_api_error_handling(client):
    """测试API错误处理"""
    # 测试未认证访问
    response = client.get("/api/v1/persons")
    assert response.status_code == 401

    # 测试无效数据
    auth_headers = authenticate_user(client)
    invalid_data = {"first_name": ""}  # 空名称
    response = client.post("/api/v1/persons", json=invalid_data, headers=auth_headers)
    assert response.status_code == 422

    # 测试资源不存在
    response = client.get("/api/v1/persons/nonexistent-id", headers=auth_headers)
    assert response.status_code == 404
```

#### **14.5 性能测试**
```python
import time

def test_api_response_time(client):
    """测试API响应时间"""
    auth_headers = authenticate_user(client)

    start_time = time.time()
    response = client.get("/api/v1/persons", headers=auth_headers)
    end_time = time.time()

    assert response.status_code == 200
    assert (end_time - start_time) < 0.5  # 响应时间小于500ms
```

#### **14.6 集成测试模式**
```python
def test_complete_workflow(client):
    """测试完整的业务流程"""
    auth_headers = authenticate_user(client)

    # 1. 创建联系人
    person_data = TestDataFactory.create_person_data()
    person_response = client.post("/api/v1/persons", json=person_data, headers=auth_headers)
    person = person_response.json()

    # 2. 创建目标
    goal_data = {"title": "Connect with John", "status": "active"}
    goal_response = client.post("/api/v1/goals", json=goal_data, headers=auth_headers)
    goal = goal_response.json()

    # 3. 创建任务
    task_data = {"title": "Send message", "goal_id": goal["goal_id"]}
    task_response = client.post("/api/v1/tasks", json=task_data, headers=auth_headers)

    # 4. 验证关联关系
    assert task_response.status_code == 201
    task = task_response.json()
    assert task["goal_id"] == goal["goal_id"]
```

#### **14.7 测试执行命令**
```bash
# 运行所有测试
pytest app/tests/

# 运行特定测试文件
pytest app/tests/test_auth.py

# 运行带覆盖率的测试
pytest --cov=app --cov-report=html app/tests/

# 运行性能测试
pytest -m performance app/tests/

# 运行集成测试
pytest -m integration app/tests/
```

---

## **第六部分：部署与运维**

### **15. 基础设施即代码 (IaC)**
*   **工具**: 使用 Terraform 管理所有云资源。
*   **结构**: 在 Monorepo 中创建 `infra/` 目录，包含 `gcp-cloudflare/` 和 `volcengine/` 两个子目录。
*   **环境管理**: 开发、测试、预生产、生产环境的基础设施配置
*   **安全配置**: 网络安全组、IAM角色、密钥管理

### **16. CI/CD 流水线**
*   **工具**: 使用 GitHub Actions。
*   **测试阶段**:
    1.  **代码质量检查**: ESLint、Black、isort 代码格式化
    2.  **单元测试**: pytest 运行所有单元测试，要求覆盖率 ≥ 90%
    3.  **集成测试**: 数据库集成测试和API端点测试
    4.  **安全扫描**: 依赖漏洞扫描和代码安全分析
*   **构建阶段**:
    1.  **Docker构建**: 多阶段构建优化镜像大小
    2.  **镜像扫描**: 容器安全漏洞扫描
    3.  **镜像推送**: 推送到容器仓库并打标签
*   **部署阶段**:
    1.  **环境部署**: 根据分支策略自动部署到对应环境
    2.  **健康检查**: 部署后自动运行健康检查
    3.  **回滚机制**: 部署失败时自动回滚到上一版本

### **17. 监控与可观测性**
*   **工具**: 遵循 OpenTelemetry 标准。
*   **实现**:
    *   **FastAPI**: 使用 `opentelemetry-instrumentation-fastapi` 自动生成追踪数据。
    *   **日志**: 使用 `structlog` 输出 JSON 格式的结构化日志。
    *   **采集**: 部署 OpenTelemetry Collector 统一收集和转发遥测数据。
*   **监控指标**:
    *   **应用指标**: 请求量、响应时间、错误率、并发用户数
    *   **业务指标**: 用户活跃度、API使用情况、AI功能调用次数
    *   **基础设施指标**: CPU、内存、磁盘、网络使用率
*   **告警策略**:
    *   **严重告警**: 服务不可用、数据库连接失败、安全事件
    *   **警告告警**: 响应时间超阈值、错误率上升、资源使用率高
    *   **信息告警**: 部署完成、定期任务执行、系统维护

### **18. 安全与合规**
*   **数据保护**:
    *   个人信息加密存储
    *   传输层TLS 1.3加密
    *   定期数据备份和恢复测试
*   **访问控制**:
    *   基于角色的访问控制(RBAC)
    *   API密钥管理和轮换
    *   审计日志记录
*   **合规要求**:
    *   GDPR数据保护合规
    *   SOC 2 Type II认证准备
    *   定期安全评估和渗透测试

### **19. 性能优化**
*   **数据库优化**:
    *   索引优化和查询性能调优
    *   连接池配置和监控
    *   读写分离和缓存策略
*   **API优化**:
    *   响应压缩和缓存
    *   分页和限流机制
    *   异步处理长时间任务
*   **AI服务优化**:
    *   模型推理缓存
    *   批处理优化
    *   负载均衡和自动扩缩容

### **20. 灾难恢复**
*   **备份策略**:
    *   数据库每日全量备份
    *   增量备份每小时执行
    *   跨区域备份存储
*   **恢复计划**:
    *   RTO (恢复时间目标): 4小时
    *   RPO (恢复点目标): 1小时
    *   定期恢复演练和测试
*   **高可用架构**:
    *   多可用区部署
    *   自动故障转移
    *   负载均衡和健康检查
