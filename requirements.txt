# Core FastAPI dependencies
fastapi>=0.100.0
uvicorn[standard]>=0.24.0

# Database
sqlalchemy>=2.0.23
alembic>=1.12.1
psycopg2-binary>=2.9.9

# Data validation and settings
pydantic[email]>=2.0.0

# Authentication and security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# AI and ML
openai>=1.3.7
langchain>=0.0.349
langchain-openai>=0.0.2
numpy>=1.24.4

# Graph processing
networkx>=3.2.1

# Utilities
python-dotenv>=1.0.0
structlog>=23.2.0
httpx>=0.25.2

# Development and testing
pytest>=7.4.3
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0

# MCP (Model Context Protocol)
mcp

# Optional: Supabase (if using as backend service)
supabase>=2.0.2
