openapi: 3.0.3
info:
  title: Nexus 人员管理 API
  description: |
    Nexus 人员管理系统的完整API接口文档
    
    ## 功能特性
    - 完整的人员CRUD操作
    - 高级搜索和过滤功能
    - 标签管理系统
    - 人员关系管理
    - CSV数据导入导出
    - 用户数据隔离和安全认证
    
    ## 认证方式
    所有API都需要Bearer Token认证
    
  version: 1.0.0
  contact:
    name: Nexus 开发团队
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8010/api/v1
    description: 本地开发服务器
  - url: https://api.nexus.com/v1
    description: 生产环境服务器

security:
  - bearerAuth: []

paths:
  /auth/login-local:
    post:
      tags:
        - 认证
      summary: 用户登录
      description: 使用邮箱和密码登录获取访问令牌
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                password:
                  type: string
                  format: password
                  example: "testpassword123"
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  token_type:
                    type: string
                    example: "bearer"
                  expires_in:
                    type: integer
                    example: 3600
        '401':
          description: 认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /persons/:
    get:
      tags:
        - 人员管理
      summary: 获取人员列表
      description: 获取当前用户的人员列表，支持搜索和分页
      parameters:
        - name: search
          in: query
          description: 按姓名搜索
          schema:
            type: string
            example: "张三"
        - name: company
          in: query
          description: 按公司搜索
          schema:
            type: string
            example: "阿里巴巴"
        - name: skip
          in: query
          description: 跳过的记录数
          schema:
            type: integer
            default: 0
            example: 0
        - name: limit
          in: query
          description: 返回的记录数
          schema:
            type: integer
            default: 20
            maximum: 100
            example: 20
      responses:
        '200':
          description: 成功获取人员列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/Person'
                  total:
                    type: integer
                    example: 100
                  page:
                    type: integer
                    example: 1
                  size:
                    type: integer
                    example: 20
                  pages:
                    type: integer
                    example: 5
        '401':
          $ref: '#/components/responses/Unauthorized'

    post:
      tags:
        - 人员管理
      summary: 创建人员
      description: 创建新的人员记录
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PersonCreate'
      responses:
        '201':
          description: 人员创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Person'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'

  /persons/{person_id}:
    get:
      tags:
        - 人员管理
      summary: 获取人员详情
      description: 根据ID获取特定人员的详细信息
      parameters:
        - name: person_id
          in: path
          required: true
          description: 人员ID
          schema:
            type: string
            format: uuid
            example: "123e4567-e89b-12d3-a456-************"
      responses:
        '200':
          description: 成功获取人员详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Person'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'

    put:
      tags:
        - 人员管理
      summary: 更新人员信息
      description: 更新指定人员的信息
      parameters:
        - name: person_id
          in: path
          required: true
          description: 人员ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PersonUpdate'
      responses:
        '200':
          description: 人员更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Person'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'

    delete:
      tags:
        - 人员管理
      summary: 删除人员
      description: 删除指定的人员记录
      parameters:
        - name: person_id
          in: path
          required: true
          description: 人员ID
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: 人员删除成功
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /persons/tags:
    get:
      tags:
        - 标签管理
      summary: 获取所有标签
      description: 获取当前用户所有人员的唯一标签列表
      responses:
        '200':
          description: 成功获取标签列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  tags:
                    type: array
                    items:
                      type: string
                    example: ["同事", "朋友", "技术", "管理", "客户"]
        '401':
          $ref: '#/components/responses/Unauthorized'

  /persons/{person_id}/tags:
    post:
      tags:
        - 标签管理
      summary: 添加标签
      description: 为指定人员添加标签
      parameters:
        - name: person_id
          in: path
          required: true
          description: 人员ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - tags
              properties:
                tags:
                  type: array
                  items:
                    type: string
                  example: ["导师", "技术领袖"]
      responses:
        '200':
          description: 标签添加成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Tags added successfully"
                  person_id:
                    type: string
                    format: uuid
                  tags:
                    type: array
                    items:
                      type: string
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /persons/{person_id}/remove-tags:
    post:
      tags:
        - 标签管理
      summary: 移除标签
      description: 从指定人员移除标签
      parameters:
        - name: person_id
          in: path
          required: true
          description: 人员ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - tags
              properties:
                tags:
                  type: array
                  items:
                    type: string
                  example: ["要移除的标签"]
      responses:
        '200':
          description: 标签移除成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Tags removed successfully"
                  person_id:
                    type: string
                    format: uuid
                  tags:
                    type: array
                    items:
                      type: string

  /persons/{person_id}/relationships:
    get:
      tags:
        - 关系管理
      summary: 获取人员关系
      description: 获取指定人员的所有关系
      parameters:
        - name: person_id
          in: path
          required: true
          description: 人员ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 成功获取关系列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  relationships:
                    type: array
                    items:
                      $ref: '#/components/schemas/Relationship'

    post:
      tags:
        - 关系管理
      summary: 创建关系
      description: 在两个人员之间创建关系
      parameters:
        - name: person_id
          in: path
          required: true
          description: 关系发起人ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RelationshipCreate'
      responses:
        '201':
          description: 关系创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  relationship_id:
                    type: string
                    example: "123e4567-e89b-12d3-a456-************-456e7890-e89b-12d3-a456-************"
                  from_person_id:
                    type: string
                    format: uuid
                  to_person_id:
                    type: string
                    format: uuid
                  archetype:
                    type: string
                    example: "colleague"
                  created_at:
                    type: string
                    format: date-time
                  message:
                    type: string
                    example: "Relationship created successfully"

  /persons/export:
    get:
      tags:
        - 数据导入导出
      summary: 导出人员数据
      description: 将当前用户的所有人员数据导出为CSV文件
      responses:
        '200':
          description: 导出成功
          content:
            text/csv:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              description: 文件下载头
              schema:
                type: string
                example: 'attachment; filename="persons_export_20250104.csv"'

  /persons/import:
    post:
      tags:
        - 数据导入导出
      summary: 导入人员数据
      description: 从CSV文件导入人员数据
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: CSV文件，包含人员数据
      responses:
        '200':
          description: 导入成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Import completed successfully"
                  imported_count:
                    type: integer
                    example: 10
                  failed_count:
                    type: integer
                    example: 0
                  errors:
                    type: array
                    items:
                      type: string

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Person:
      type: object
      properties:
        person_id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        first_name:
          type: string
          example: "张"
        last_name:
          type: string
          example: "三"
        professional_info:
          type: object
          properties:
            company:
              type: string
              example: "阿里巴巴"
            position:
              type: string
              example: "高级软件工程师"
            industry:
              type: string
              example: "互联网"
        contact_info:
          type: object
          properties:
            email:
              type: string
              format: email
              example: "<EMAIL>"
            phone:
              type: string
              example: "+86 138 0000 0000"
            address:
              type: string
              example: "杭州市西湖区"
        tags:
          type: array
          items:
            type: string
          example: ["同事", "技术专家", "阿里"]
        notes:
          type: string
          example: "负责前端架构设计，技术能力很强"
        created_at:
          type: string
          format: date-time
          example: "2025-01-04T10:30:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-01-04T10:30:00Z"

    PersonCreate:
      type: object
      required:
        - first_name
        - last_name
      properties:
        first_name:
          type: string
          example: "张"
        last_name:
          type: string
          example: "三"
        professional_info:
          type: object
          properties:
            company:
              type: string
            position:
              type: string
            industry:
              type: string
        contact_info:
          type: object
          properties:
            email:
              type: string
              format: email
            phone:
              type: string
            address:
              type: string
        tags:
          type: array
          items:
            type: string
        notes:
          type: string

    PersonUpdate:
      allOf:
        - $ref: '#/components/schemas/PersonCreate'

    Relationship:
      type: object
      properties:
        person_id:
          type: string
          format: uuid
        person_name:
          type: string
          example: "李四"
        archetype:
          type: string
          enum: [colleague, friend, mentor, client, partner, family, acquaintance]
          example: "colleague"
        direction:
          type: string
          enum: [incoming, outgoing]
          example: "outgoing"
        relationship_foundation:
          type: object
          properties:
            how_met:
              type: string
              example: "工作项目"
            context:
              type: string
              example: "同一个开发团队"
        relationship_depth:
          type: object
          properties:
            overall_score:
              type: integer
              minimum: 0
              maximum: 100
              example: 75
            trust_level:
              type: integer
              minimum: 0
              maximum: 100
              example: 80
            communication_frequency:
              type: integer
              minimum: 0
              maximum: 100
              example: 70

    RelationshipCreate:
      type: object
      required:
        - target_person_id
        - archetype
      properties:
        target_person_id:
          type: string
          format: uuid
          example: "456e7890-e89b-12d3-a456-************"
        archetype:
          type: string
          enum: [colleague, friend, mentor, client, partner, family, acquaintance]
          example: "colleague"
        relationship_foundation:
          type: object
          properties:
            how_met:
              type: string
            context:
              type: string
        relationship_depth:
          type: object
          properties:
            overall_score:
              type: integer
              minimum: 0
              maximum: 100
            trust_level:
              type: integer
              minimum: 0
              maximum: 100
            communication_frequency:
              type: integer
              minimum: 0
              maximum: 100

    Error:
      type: object
      properties:
        detail:
          type: string
          example: "错误描述信息"

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Unauthorized:
      description: 未认证或令牌无效
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    NotFound:
      description: 资源不存在
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    ValidationError:
      description: 数据验证失败
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

tags:
  - name: 认证
    description: 用户认证相关接口
  - name: 人员管理
    description: 人员的增删改查操作
  - name: 标签管理
    description: 人员标签的管理功能
  - name: 关系管理
    description: 人员关系的创建和查询
  - name: 数据导入导出
    description: CSV格式的数据导入导出功能
