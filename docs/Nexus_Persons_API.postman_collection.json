{"info": {"name": "Nexus 人员管理 API", "description": "Nexus 人员管理系统的完整API接口集合", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8010/api/v1", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "person_id", "value": "", "type": "string"}, {"key": "person_id_2", "value": "", "type": "string"}], "item": [{"name": "认证", "item": [{"name": "用户注册", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"testpassword123\"\n}"}, "url": {"raw": "{{base_url}}/auth/register-local", "host": ["{{base_url}}"], "path": ["auth", "register-local"]}}}, {"name": "用户登录", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('access_token', response.access_token);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"testpassword123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login-local", "host": ["{{base_url}}"], "path": ["auth", "login-local"]}}}]}, {"name": "人员管理", "item": [{"name": "创建人员", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('person_id', response.person_id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"张\",\n  \"last_name\": \"三\",\n  \"professional_info\": {\n    \"company\": \"阿里巴巴\",\n    \"position\": \"高级软件工程师\",\n    \"industry\": \"互联网\"\n  },\n  \"contact_info\": {\n    \"email\": \"z<PERSON><PERSON>@alibaba.com\",\n    \"phone\": \"+86 138 0000 0000\"\n  },\n  \"tags\": [\"同事\", \"技术专家\", \"阿里\"],\n  \"notes\": \"负责前端架构设计，技术能力很强\"\n}"}, "url": {"raw": "{{base_url}}/persons/", "host": ["{{base_url}}"], "path": ["persons", ""]}}}, {"name": "创建第二个人员", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('person_id_2', response.person_id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"李\",\n  \"last_name\": \"四\",\n  \"professional_info\": {\n    \"company\": \"腾讯\",\n    \"position\": \"产品经理\",\n    \"industry\": \"互联网\"\n  },\n  \"contact_info\": {\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+86 139 0000 0000\"\n  },\n  \"tags\": [\"朋友\", \"产品\", \"腾讯\"],\n  \"notes\": \"微信团队的产品经理，对用户体验有深刻理解\"\n}"}, "url": {"raw": "{{base_url}}/persons/", "host": ["{{base_url}}"], "path": ["persons", ""]}}}, {"name": "获取人员列表", "request": {"method": "GET", "url": {"raw": "{{base_url}}/persons/?skip=0&limit=20", "host": ["{{base_url}}"], "path": ["persons", ""], "query": [{"key": "skip", "value": "0"}, {"key": "limit", "value": "20"}]}}}, {"name": "获取人员详情", "request": {"method": "GET", "url": {"raw": "{{base_url}}/persons/{{person_id}}", "host": ["{{base_url}}"], "path": ["persons", "{{person_id}}"]}}}, {"name": "更新人员信息", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"张\",\n  \"last_name\": \"三\",\n  \"professional_info\": {\n    \"company\": \"阿里巴巴\",\n    \"position\": \"技术专家\",\n    \"industry\": \"互联网\"\n  },\n  \"contact_info\": {\n    \"email\": \"z<PERSON><PERSON>@alibaba.com\",\n    \"phone\": \"+86 138 0000 0000\"\n  },\n  \"tags\": [\"同事\", \"技术专家\", \"阿里\", \"架构师\"],\n  \"notes\": \"已升职为技术专家，负责整体技术架构\"\n}"}, "url": {"raw": "{{base_url}}/persons/{{person_id}}", "host": ["{{base_url}}"], "path": ["persons", "{{person_id}}"]}}}, {"name": "删除人员", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/persons/{{person_id}}", "host": ["{{base_url}}"], "path": ["persons", "{{person_id}}"]}}}]}, {"name": "搜索功能", "item": [{"name": "按姓名搜索", "request": {"method": "GET", "url": {"raw": "{{base_url}}/persons/?search=张", "host": ["{{base_url}}"], "path": ["persons", ""], "query": [{"key": "search", "value": "张"}]}}}, {"name": "按公司搜索", "request": {"method": "GET", "url": {"raw": "{{base_url}}/persons/?company=阿里", "host": ["{{base_url}}"], "path": ["persons", ""], "query": [{"key": "company", "value": "阿里"}]}}}, {"name": "组合搜索", "request": {"method": "GET", "url": {"raw": "{{base_url}}/persons/?search=李&company=腾讯", "host": ["{{base_url}}"], "path": ["persons", ""], "query": [{"key": "search", "value": "李"}, {"key": "company", "value": "腾讯"}]}}}]}, {"name": "标签管理", "item": [{"name": "添加标签", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tags\": [\"导师\", \"技术领袖\"]\n}"}, "url": {"raw": "{{base_url}}/persons/{{person_id}}/tags", "host": ["{{base_url}}"], "path": ["persons", "{{person_id}}", "tags"]}}}, {"name": "移除标签", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tags\": [\"同事\"]\n}"}, "url": {"raw": "{{base_url}}/persons/{{person_id}}/remove-tags", "host": ["{{base_url}}"], "path": ["persons", "{{person_id}}", "remove-tags"]}}}, {"name": "获取所有标签", "request": {"method": "GET", "url": {"raw": "{{base_url}}/persons/tags", "host": ["{{base_url}}"], "path": ["persons", "tags"]}}}]}, {"name": "关系管理", "item": [{"name": "创建关系", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"target_person_id\": \"{{person_id_2}}\",\n  \"archetype\": \"colleague\",\n  \"relationship_foundation\": {\n    \"how_met\": \"技术会议\",\n    \"context\": \"在阿里巴巴技术大会上认识\"\n  },\n  \"relationship_depth\": {\n    \"overall_score\": 75,\n    \"trust_level\": 80,\n    \"communication_frequency\": 60\n  }\n}"}, "url": {"raw": "{{base_url}}/persons/{{person_id}}/relationships", "host": ["{{base_url}}"], "path": ["persons", "{{person_id}}", "relationships"]}}}, {"name": "获取人员关系", "request": {"method": "GET", "url": {"raw": "{{base_url}}/persons/{{person_id}}/relationships", "host": ["{{base_url}}"], "path": ["persons", "{{person_id}}", "relationships"]}}}, {"name": "应用关系原型", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"archetype\": \"mentor\"\n}"}, "url": {"raw": "{{base_url}}/persons/{{person_id}}/apply-archetype", "host": ["{{base_url}}"], "path": ["persons", "{{person_id}}", "apply-archetype"]}}}]}, {"name": "数据导入导出", "item": [{"name": "导出CSV", "request": {"method": "GET", "url": {"raw": "{{base_url}}/persons/export", "host": ["{{base_url}}"], "path": ["persons", "export"]}}}, {"name": "导入CSV", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/persons/import", "host": ["{{base_url}}"], "path": ["persons", "import"]}}}]}]}