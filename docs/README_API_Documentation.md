# Nexus 人员管理 API 文档集合

**版本:** v1.0  
**更新日期:** 2025年1月4日  
**状态:** ✅ 生产就绪

---

## 📋 文档概览

本文档集合为前端开发团队提供了完整的Nexus人员管理API接口文档，包含所有必要的信息来进行前后端集成。

### 🎯 **API 功能特性**

- ✅ **完整的人员CRUD操作** - 创建、读取、更新、删除人员
- ✅ **高级搜索功能** - 按姓名、公司等多维度搜索
- ✅ **标签管理系统** - 灵活的标签添加、移除和查询
- ✅ **关系管理** - 人员间关系的创建和查询
- ✅ **数据导入导出** - CSV格式的批量数据处理
- ✅ **用户数据隔离** - 确保数据安全和隐私
- ✅ **JWT认证** - 安全的API访问控制

### 📊 **测试覆盖率**

- **总测试用例**: 23个
- **通过率**: 100% (22/22 通过，1个跳过)
- **功能覆盖**: 完整覆盖所有API端点
- **错误处理**: 全面的错误场景测试

---

## 📚 文档列表

### 1. **主要API文档**

#### 📖 [API_Documentation_Persons.md](./API_Documentation_Persons.md)
**完整的API接口文档**
- 详细的接口说明和参数
- 请求/响应示例
- 错误处理说明
- 数据模型定义
- 使用注意事项

#### 🧪 [API_Testing_Examples.md](./API_Testing_Examples.md)
**API测试示例和集成指南**
- cURL命令示例
- JavaScript/TypeScript集成代码
- React和Vue示例
- 常见问题解决方案
- 性能优化建议

### 2. **工具和规范**

#### 📮 [Nexus_Persons_API.postman_collection.json](./Nexus_Persons_API.postman_collection.json)
**Postman集合文件**
- 可直接导入Postman的完整API集合
- 包含所有端点的预配置请求
- 自动化的环境变量设置
- 测试脚本和断言

#### 📋 [openapi_persons.yaml](./openapi_persons.yaml)
**OpenAPI 3.0规范文档**
- 标准化的API规范
- 可用于代码生成
- 支持Swagger UI展示
- 完整的数据模型定义

---

## 🚀 快速开始

### 1. **环境准备**

```bash
# 确保后端服务正在运行
uvicorn app.main:app --host 0.0.0.0 --port 8010 --reload

# 验证服务状态
curl http://localhost:8010/health
```

### 2. **获取访问令牌**

```bash
# 用户登录
curl -X POST "http://localhost:8010/api/v1/auth/login-local" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your_password"
  }'
```

### 3. **测试API**

```bash
# 创建人员
curl -X POST "http://localhost:8010/api/v1/persons/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "first_name": "张",
    "last_name": "三",
    "professional_info": {
      "company": "科技公司",
      "position": "软件工程师"
    },
    "tags": ["同事", "技术"]
  }'
```

---

## 🛠️ 前端集成指南

### **推荐的集成方式**

1. **使用TypeScript**: 利用提供的类型定义确保类型安全
2. **错误处理**: 实现统一的错误处理机制
3. **认证管理**: 使用Token自动刷新机制
4. **数据缓存**: 合理缓存不经常变化的数据
5. **分页处理**: 对大量数据使用分页加载

### **示例代码结构**

```typescript
// api/persons.ts
export class PersonsAPI {
  constructor(private baseURL: string, private token: string) {}
  
  async getPersons(filters?: PersonFilters): Promise<PersonsResponse> {
    // 实现获取人员列表
  }
  
  async createPerson(data: PersonCreate): Promise<Person> {
    // 实现创建人员
  }
  
  // ... 其他方法
}

// hooks/usePersons.ts (React)
export const usePersons = () => {
  // 自定义Hook实现
}

// composables/usePersons.ts (Vue)
export const usePersons = () => {
  // Composition API实现
}
```

---

## 📋 API端点总览

### **认证**
- `POST /auth/login-local` - 用户登录

### **人员管理**
- `GET /persons/` - 获取人员列表 (支持搜索和分页)
- `POST /persons/` - 创建人员
- `GET /persons/{id}` - 获取人员详情
- `PUT /persons/{id}` - 更新人员信息
- `DELETE /persons/{id}` - 删除人员

### **标签管理**
- `GET /persons/tags` - 获取所有标签
- `POST /persons/{id}/tags` - 添加标签
- `POST /persons/{id}/remove-tags` - 移除标签

### **关系管理**
- `POST /persons/{id}/relationships` - 创建关系
- `GET /persons/{id}/relationships` - 获取关系
- `POST /persons/{id}/apply-archetype` - 应用关系原型

### **数据导入导出**
- `GET /persons/export` - 导出CSV
- `POST /persons/import` - 导入CSV

---

## 🔧 开发工具推荐

### **API测试工具**
1. **Postman** - 导入提供的collection文件
2. **Insomnia** - 支持OpenAPI导入
3. **Thunder Client** - VS Code插件
4. **curl** - 命令行测试

### **文档查看工具**
1. **Swagger UI** - 在线查看OpenAPI文档
2. **Redoc** - 美观的API文档展示
3. **Postman** - 集成的文档查看

### **代码生成工具**
1. **OpenAPI Generator** - 从OpenAPI生成客户端代码
2. **Swagger Codegen** - 多语言代码生成
3. **orval** - TypeScript React Query代码生成

---

## 📊 数据模型

### **核心实体**

```typescript
interface Person {
  person_id: string;           // UUID
  first_name: string;          // 姓
  last_name: string;           // 名
  professional_info?: {        // 职业信息
    company?: string;
    position?: string;
    industry?: string;
  };
  contact_info?: {             // 联系信息
    email?: string;
    phone?: string;
    address?: string;
  };
  tags?: string[];             // 标签列表
  notes?: string;              // 备注
  created_at: string;          // 创建时间
  updated_at: string;          // 更新时间
}
```

### **关系类型**
- `colleague` - 同事
- `friend` - 朋友
- `mentor` - 导师
- `client` - 客户
- `partner` - 合作伙伴
- `family` - 家人
- `acquaintance` - 熟人

---

## ⚠️ 重要注意事项

### **安全性**
- 所有API都需要Bearer Token认证
- 用户只能访问自己创建的数据
- 敏感信息传输使用HTTPS

### **限制**
- 人员列表每次最多返回100条记录
- 搜索关键词最少2个字符
- 标签名称最长50个字符
- CSV文件大小限制10MB

### **性能**
- 建议使用分页获取大量数据
- 实现适当的客户端缓存
- 避免频繁的API调用

### **错误处理**
- 根据HTTP状态码进行错误分类
- 实现用户友好的错误提示
- 记录错误日志用于调试

---

## 🆘 支持和反馈

### **技术支持**
- **文档问题**: 请检查是否有更新版本
- **API问题**: 查看错误响应中的详细信息
- **集成问题**: 参考提供的示例代码

### **联系方式**
- **开发团队**: <EMAIL>
- **技术支持**: <EMAIL>
- **问题反馈**: 请提供详细的错误信息和重现步骤

### **更新日志**
- **v1.0.0** (2025-01-04): 初始版本发布
  - 完整的人员管理API
  - 搜索和标签功能
  - 关系管理系统
  - CSV导入导出
  - 完整的测试覆盖

---

## 📈 后续计划

### **即将推出的功能**
- 批量操作API
- 高级搜索过滤器
- 数据统计和分析接口
- 实时通知功能
- 移动端优化

### **性能优化**
- GraphQL支持
- 缓存策略优化
- 数据库查询优化
- API响应时间改进

---

**文档维护**: 本文档会随着API的更新而持续维护，请定期检查最新版本。

**最后更新**: 2025年1月4日  
**文档版本**: v1.0.0  
**API版本**: v1.0.0
