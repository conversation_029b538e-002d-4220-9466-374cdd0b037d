# Nexus Backend Testing Guide

This document provides comprehensive guidance on testing the Nexus Backend application.

## Table of Contents

1. [Test Architecture](#test-architecture)
2. [Running Tests](#running-tests)
3. [Test Categories](#test-categories)
4. [Writing Tests](#writing-tests)
5. [Coverage Requirements](#coverage-requirements)
6. [Continuous Integration](#continuous-integration)
7. [Performance Testing](#performance-testing)
8. [Troubleshooting](#troubleshooting)

## Test Architecture

### Test Pyramid

Our testing strategy follows the test pyramid principle:

- **Unit Tests (70%)**: Fast, isolated tests for individual functions and classes
- **Integration Tests (20%)**: Tests for service interactions and database operations
- **End-to-End Tests (10%)**: Complete user workflow tests

### Test Tools

- **Framework**: pytest
- **HTTP Client**: FastAPI TestClient
- **Database**: SQLite (test), PostgreSQL (CI)
- **Mocking**: unittest.mock
- **Coverage**: pytest-cov
- **Performance**: pytest-benchmark

## Running Tests

### Prerequisites

```bash
# Install dependencies
poetry install

# Set up test database
export DATABASE_URL="sqlite:///./test.db"
```

### Quick Start

```bash
# Run all tests
python scripts/run_tests.py

# Run specific test types
python scripts/run_tests.py --type unit
python scripts/run_tests.py --type integration
python scripts/run_tests.py --type performance

# Run tests with verbose output
python scripts/run_tests.py --verbose

# Run specific test file
python scripts/run_tests.py --file test_auth.py

# Run tests by marker
python scripts/run_tests.py --marker auth
```

### Direct pytest Commands

```bash
# Run all tests with coverage
pytest --cov=app --cov-report=html

# Run unit tests only
pytest -m unit

# Run integration tests only
pytest -m integration

# Run specific test file
pytest app/tests/test_auth.py

# Run specific test function
pytest app/tests/test_auth.py::TestAuthentication::test_register_user_success

# Run tests matching pattern
pytest -k "test_auth"

# Run tests with verbose output
pytest -v

# Run tests and stop on first failure
pytest -x

# Run failed tests from last run
pytest --lf
```

## Test Categories

### Unit Tests

Test individual functions and classes in isolation.

**Markers**: `@pytest.mark.unit`

**Examples**:
- Authentication logic
- Data validation
- Business rule calculations
- Utility functions

### Integration Tests

Test interactions between components.

**Markers**: `@pytest.mark.integration`

**Examples**:
- Database operations
- API endpoint workflows
- Service integrations
- External API interactions

### Performance Tests

Test system performance and response times.

**Markers**: `@pytest.mark.performance`

**Examples**:
- API response time benchmarks
- Database query performance
- Concurrent user handling
- Memory usage tests

### Test Markers

Available test markers:

```python
@pytest.mark.unit          # Unit tests
@pytest.mark.integration   # Integration tests
@pytest.mark.performance   # Performance tests
@pytest.mark.slow          # Slow running tests
@pytest.mark.auth          # Authentication tests
@pytest.mark.crud          # CRUD operation tests
@pytest.mark.ai            # AI functionality tests
@pytest.mark.search        # Search functionality tests
@pytest.mark.graph         # Graph visualization tests
@pytest.mark.data_portability  # Data import/export tests
@pytest.mark.external      # External service tests
@pytest.mark.async_tasks   # Async task tests
@pytest.mark.relationships # Relationship management tests
```

## Writing Tests

### Test Structure

```python
"""
Test module for authentication functionality
"""

import pytest
from fastapi.testclient import TestClient

from app.tests.base import BaseAPITestCase
from app.tests.utils import TestDataFactory


class TestAuthentication(BaseAPITestCase):
    """Test authentication endpoints"""
    
    def test_register_user_success(self, client: TestClient):
        """Test successful user registration."""
        # Arrange
        user_data = TestDataFactory.create_user_data()
        
        # Act
        response = client.post("/api/v1/auth/register", json=user_data)
        
        # Assert
        data = self.assert_successful_response(response, 201)
        assert data["email"] == user_data["email"]
        self.assertions.assert_valid_uuid(data["user_id"])
```

### Test Naming Conventions

- Test files: `test_*.py`
- Test classes: `Test*`
- Test methods: `test_*`
- Use descriptive names that explain what is being tested

### Test Data Management

Use the `TestDataFactory` for consistent test data:

```python
from app.tests.utils import TestDataFactory

# Create user data
user_data = TestDataFactory.create_user_data(
    email="<EMAIL>",
    first_name="Custom"
)

# Create person data
person_data = TestDataFactory.create_person_data(
    first_name="John",
    last_name="Doe",
    email="<EMAIL>"
)
```

### Authentication in Tests

Use the base test class for authenticated requests:

```python
class TestProtectedEndpoint(BaseAPITestCase):
    def test_protected_endpoint(self, client: TestClient):
        # Authenticate user
        self.authenticate_user(client)
        
        # Make authenticated request
        response = client.get("/api/v1/protected", headers=self.auth_headers)
        
        # Assert response
        self.assert_successful_response(response)
```

### Mocking External Services

```python
from unittest.mock import patch, Mock

@patch('app.services.ai_service.OpenAI')
def test_ai_service_integration(self, mock_openai, client: TestClient):
    # Setup mock
    mock_response = Mock()
    mock_response.choices[0].message.content = "Mock AI response"
    mock_openai.return_value.chat.completions.create.return_value = mock_response
    
    # Test with mocked service
    self.authenticate_user(client)
    response = client.post("/api/v1/copilot/converse", 
                          json={"message": "test"}, 
                          headers=self.auth_headers)
    
    self.assert_successful_response(response)
```

## Coverage Requirements

### Minimum Coverage Thresholds

- **Overall Coverage**: 90%
- **Unit Tests**: 95%
- **Integration Tests**: 80%
- **Critical Business Logic**: 100%

### Coverage Reports

```bash
# Generate HTML coverage report
pytest --cov=app --cov-report=html

# View coverage in terminal
pytest --cov=app --cov-report=term-missing

# Generate XML coverage report (for CI)
pytest --cov=app --cov-report=xml

# Fail if coverage below threshold
pytest --cov=app --cov-fail-under=90
```

### Coverage Analysis

Coverage reports are generated in:
- HTML: `htmlcov/index.html`
- XML: `coverage.xml`
- Terminal: Real-time output

## Continuous Integration

### GitHub Actions

Our CI pipeline runs:

1. **Code Quality**: Black, isort, flake8, mypy
2. **Unit Tests**: Fast, isolated tests
3. **Integration Tests**: Database and API tests
4. **Security Scan**: Dependency and code security
5. **Performance Tests**: Response time benchmarks

### Test Environments

- **Local**: SQLite database
- **CI**: PostgreSQL + Redis
- **Staging**: Production-like environment

### Required Checks

All PRs must pass:
- ✅ Code formatting (Black, isort)
- ✅ Linting (flake8)
- ✅ Unit tests (90% coverage)
- ✅ Integration tests (80% coverage)
- ✅ Security scan (no high-risk vulnerabilities)

## Performance Testing

### Response Time Benchmarks

```python
@pytest.mark.performance
def test_api_response_time(client: TestClient, benchmark):
    """Test API response time benchmark"""
    auth_headers = authenticate_user(client)
    
    def api_call():
        return client.get("/api/v1/persons", headers=auth_headers)
    
    result = benchmark(api_call)
    assert result.status_code == 200
    assert benchmark.stats.mean < 0.5  # 500ms threshold
```

### Load Testing

```bash
# Install locust for load testing
pip install locust

# Run load test
locust -f tests/load_test.py --host=http://localhost:8000
```

## Troubleshooting

### Common Issues

**Database Connection Errors**:
```bash
# Reset test database
rm test.db
export DATABASE_URL="sqlite:///./test.db"
```

**Import Errors**:
```bash
# Ensure PYTHONPATH is set
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

**Slow Tests**:
```bash
# Run only fast tests
pytest -m "not slow"

# Profile slow tests
pytest --durations=10
```

**Coverage Issues**:
```bash
# Check which lines are missing coverage
pytest --cov=app --cov-report=term-missing

# Generate detailed HTML report
pytest --cov=app --cov-report=html
open htmlcov/index.html
```

### Debug Mode

```bash
# Run tests with debug output
pytest -s -vv

# Drop into debugger on failure
pytest --pdb

# Run specific test with debug
pytest -s -vv app/tests/test_auth.py::test_specific_function
```

### Test Data Cleanup

Tests automatically clean up data, but if needed:

```bash
# Manual cleanup
rm test.db
rm -rf htmlcov/
rm -rf .pytest_cache/
```

## Best Practices

1. **Write tests first** (TDD approach)
2. **Keep tests independent** (no shared state)
3. **Use descriptive test names**
4. **Test edge cases and error conditions**
5. **Mock external dependencies**
6. **Maintain high coverage** (>90%)
7. **Run tests frequently** during development
8. **Review test failures** carefully
9. **Update tests** when changing functionality
10. **Document complex test scenarios**

## Resources

- [pytest Documentation](https://docs.pytest.org/)
- [FastAPI Testing](https://fastapi.tiangolo.com/tutorial/testing/)
- [Coverage.py Documentation](https://coverage.readthedocs.io/)
- [Testing Best Practices](https://docs.python-guide.org/writing/tests/)
