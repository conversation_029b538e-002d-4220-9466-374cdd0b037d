# Nexus 人员管理 API 接口文档

**版本:** v1.0  
**更新日期:** 2025年1月4日  
**基础URL:** `http://localhost:8010/api/v1`

---

## 目录

1. [认证说明](#认证说明)
2. [通用响应格式](#通用响应格式)
3. [错误处理](#错误处理)
4. [人员基础管理](#人员基础管理)
5. [搜索功能](#搜索功能)
6. [标签管理](#标签管理)
7. [关系管理](#关系管理)
8. [数据导入导出](#数据导入导出)
9. [数据模型](#数据模型)

---

## 认证说明

所有API请求都需要在请求头中包含认证令牌：

```http
Authorization: Bearer <your_access_token>
```

### 获取访问令牌

```http
POST /api/v1/auth/login-local
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your_password"
}
```

**响应:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

---

## 通用响应格式

### 成功响应
- **2xx状态码**: 请求成功
- **Content-Type**: `application/json`

### 分页响应格式
```json
{
  "items": [...],
  "total": 100,
  "page": 1,
  "size": 20,
  "pages": 5
}
```

---

## 错误处理

### 错误响应格式
```json
{
  "detail": "错误描述信息"
}
```

### 常见错误状态码
- **400 Bad Request**: 请求参数错误
- **401 Unauthorized**: 未认证或令牌无效
- **403 Forbidden**: 权限不足
- **404 Not Found**: 资源不存在
- **422 Unprocessable Entity**: 数据验证失败
- **500 Internal Server Error**: 服务器内部错误

---

## 人员基础管理

### 1. 创建人员

```http
POST /api/v1/persons/
Content-Type: application/json
Authorization: Bearer <token>

{
  "first_name": "张",
  "last_name": "三",
  "professional_info": {
    "company": "科技公司",
    "position": "软件工程师",
    "industry": "科技"
  },
  "contact_info": {
    "email": "<EMAIL>",
    "phone": "+86 138 0000 0000"
  },
  "tags": ["同事", "技术"],
  "notes": "优秀的前端开发工程师"
}
```

**响应 (201 Created):**
```json
{
  "person_id": "123e4567-e89b-12d3-a456-426614174000",
  "first_name": "张",
  "last_name": "三",
  "professional_info": {
    "company": "科技公司",
    "position": "软件工程师",
    "industry": "科技"
  },
  "contact_info": {
    "email": "<EMAIL>",
    "phone": "+86 138 0000 0000"
  },
  "tags": ["同事", "技术"],
  "notes": "优秀的前端开发工程师",
  "created_at": "2025-01-04T10:30:00Z",
  "updated_at": "2025-01-04T10:30:00Z"
}
```

### 2. 获取人员详情

```http
GET /api/v1/persons/{person_id}
Authorization: Bearer <token>
```

**响应 (200 OK):**
```json
{
  "person_id": "123e4567-e89b-12d3-a456-426614174000",
  "first_name": "张",
  "last_name": "三",
  "professional_info": {
    "company": "科技公司",
    "position": "软件工程师",
    "industry": "科技"
  },
  "contact_info": {
    "email": "<EMAIL>",
    "phone": "+86 138 0000 0000"
  },
  "tags": ["同事", "技术"],
  "notes": "优秀的前端开发工程师",
  "created_at": "2025-01-04T10:30:00Z",
  "updated_at": "2025-01-04T10:30:00Z"
}
```

### 3. 更新人员信息

```http
PUT /api/v1/persons/{person_id}
Content-Type: application/json
Authorization: Bearer <token>

{
  "first_name": "张",
  "last_name": "三",
  "professional_info": {
    "company": "新科技公司",
    "position": "高级软件工程师",
    "industry": "科技"
  },
  "contact_info": {
    "email": "<EMAIL>",
    "phone": "+86 138 0000 0000"
  },
  "tags": ["同事", "技术", "朋友"],
  "notes": "已升职为高级工程师"
}
```

**响应 (200 OK):** 返回更新后的人员信息

### 4. 删除人员

```http
DELETE /api/v1/persons/{person_id}
Authorization: Bearer <token>
```

**响应 (204 No Content):** 无响应体

### 5. 获取人员列表

```http
GET /api/v1/persons/?skip=0&limit=20
Authorization: Bearer <token>
```

**查询参数:**
- `skip` (可选): 跳过的记录数，默认为0
- `limit` (可选): 返回的记录数，默认为20，最大100

**响应 (200 OK):**
```json
{
  "items": [
    {
      "person_id": "123e4567-e89b-12d3-a456-426614174000",
      "first_name": "张",
      "last_name": "三",
      "professional_info": {
        "company": "科技公司",
        "position": "软件工程师"
      },
      "tags": ["同事", "技术"],
      "created_at": "2025-01-04T10:30:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "size": 20,
  "pages": 1
}
```

---

## 搜索功能

### 1. 按姓名搜索

```http
GET /api/v1/persons/?search=张三&skip=0&limit=20
Authorization: Bearer <token>
```

**查询参数:**
- `search`: 搜索关键词，支持姓名模糊搜索
- `skip` (可选): 跳过的记录数
- `limit` (可选): 返回的记录数

### 2. 按公司搜索

```http
GET /api/v1/persons/?company=科技公司&skip=0&limit=20
Authorization: Bearer <token>
```

**查询参数:**
- `company`: 公司名称，支持模糊搜索
- `skip` (可选): 跳过的记录数
- `limit` (可选): 返回的记录数

### 3. 组合搜索

```http
GET /api/v1/persons/?search=张&company=科技&skip=0&limit=20
Authorization: Bearer <token>
```

**响应格式:** 与获取人员列表相同

---

## 标签管理

### 1. 为人员添加标签

```http
POST /api/v1/persons/{person_id}/tags
Content-Type: application/json
Authorization: Bearer <token>

{
  "tags": ["新标签1", "新标签2"]
}
```

**响应 (200 OK):**
```json
{
  "message": "Tags added successfully",
  "person_id": "123e4567-e89b-12d3-a456-426614174000",
  "tags": ["原有标签", "新标签1", "新标签2"]
}
```

### 2. 从人员移除标签

```http
POST /api/v1/persons/{person_id}/remove-tags
Content-Type: application/json
Authorization: Bearer <token>

{
  "tags": ["要移除的标签1", "要移除的标签2"]
}
```

**响应 (200 OK):**
```json
{
  "message": "Tags removed successfully",
  "person_id": "123e4567-e89b-12d3-a456-426614174000",
  "tags": ["剩余标签"]
}
```

### 3. 获取所有标签

```http
GET /api/v1/persons/tags
Authorization: Bearer <token>
```

**响应 (200 OK):**
```json
{
  "tags": ["同事", "朋友", "技术", "管理", "客户"]
}
```

---

## 关系管理

### 1. 创建人员关系

```http
POST /api/v1/persons/{person_id}/relationships
Content-Type: application/json
Authorization: Bearer <token>

{
  "target_person_id": "456e7890-e89b-12d3-a456-426614174001",
  "archetype": "colleague",
  "relationship_foundation": {
    "how_met": "工作项目",
    "context": "同一个开发团队"
  },
  "relationship_depth": {
    "overall_score": 75,
    "trust_level": 80,
    "communication_frequency": 70
  }
}
```

**响应 (201 Created):**
```json
{
  "relationship_id": "123e4567-e89b-12d3-a456-426614174000-456e7890-e89b-12d3-a456-426614174001",
  "from_person_id": "123e4567-e89b-12d3-a456-426614174000",
  "to_person_id": "456e7890-e89b-12d3-a456-426614174001",
  "archetype": "colleague",
  "created_at": "2025-01-04T10:30:00Z",
  "message": "Relationship created successfully"
}
```

### 2. 获取人员关系

```http
GET /api/v1/persons/{person_id}/relationships
Authorization: Bearer <token>
```

**响应 (200 OK):**
```json
{
  "relationships": [
    {
      "person_id": "456e7890-e89b-12d3-a456-426614174001",
      "person_name": "李四",
      "archetype": "colleague",
      "direction": "outgoing",
      "relationship_foundation": {
        "how_met": "工作项目",
        "context": "同一个开发团队"
      },
      "relationship_depth": {
        "overall_score": 75,
        "trust_level": 80
      }
    }
  ]
}
```

### 3. 应用关系原型

```http
POST /api/v1/persons/{person_id}/apply-archetype
Content-Type: application/json
Authorization: Bearer <token>

{
  "archetype": "mentor"
}
```

**响应 (200 OK):**
```json
{
  "message": "Archetype applied successfully",
  "person_id": "123e4567-e89b-12d3-a456-426614174000",
  "archetype": "mentor",
  "suggested_actions": [
    "定期安排一对一会议",
    "分享行业经验和见解",
    "提供职业发展建议"
  ],
  "last_updated": "2025-01-04T10:30:00Z"
}
```

---

## 数据导入导出

### 1. 从CSV导入人员

```http
POST /api/v1/persons/import
Content-Type: multipart/form-data
Authorization: Bearer <token>

file: [CSV文件]
```

**CSV格式要求:**
```csv
first_name,last_name,company,position,email,phone,tags,notes
张,三,科技公司,软件工程师,<EMAIL>,+86 138 0000 0000,"同事,技术",优秀的工程师
李,四,设计公司,UI设计师,<EMAIL>,+86 139 0000 0000,"同事,设计",创意十足
```

**响应 (200 OK):**
```json
{
  "message": "Import completed successfully",
  "imported_count": 2,
  "failed_count": 0,
  "errors": []
}
```

### 2. 导出人员到CSV

```http
GET /api/v1/persons/export
Authorization: Bearer <token>
```

**响应 (200 OK):**
- **Content-Type**: `text/csv`
- **Content-Disposition**: `attachment; filename="persons_export_20250104.csv"`

返回CSV文件下载

---

## 数据模型

### Person 模型

```typescript
interface Person {
  person_id: string;           // UUID
  first_name: string;          // 姓
  last_name: string;           // 名
  professional_info?: {        // 职业信息
    company?: string;
    position?: string;
    industry?: string;
  };
  contact_info?: {             // 联系信息
    email?: string;
    phone?: string;
    address?: string;
  };
  tags?: string[];             // 标签列表
  notes?: string;              // 备注
  created_at: string;          // 创建时间 (ISO 8601)
  updated_at: string;          // 更新时间 (ISO 8601)
}
```

### Relationship 模型

```typescript
interface Relationship {
  from_person_id: string;      // 关系发起人ID
  to_person_id: string;        // 关系目标人ID
  archetype: string;           // 关系类型
  relationship_foundation?: {   // 关系基础
    how_met?: string;
    context?: string;
  };
  relationship_depth?: {       // 关系深度
    overall_score?: number;    // 总体评分 (0-100)
    trust_level?: number;      // 信任度 (0-100)
    communication_frequency?: number; // 沟通频率 (0-100)
  };
}
```

### 关系类型 (Archetype)

- `colleague`: 同事
- `friend`: 朋友
- `mentor`: 导师
- `client`: 客户
- `partner`: 合作伙伴
- `family`: 家人
- `acquaintance`: 熟人

---

## 使用示例

### JavaScript/TypeScript 示例

```typescript
// 创建人员
const createPerson = async (personData: any) => {
  const response = await fetch('/api/v1/persons/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(personData)
  });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  return await response.json();
};

// 搜索人员
const searchPersons = async (query: string, page: number = 1) => {
  const response = await fetch(
    `/api/v1/persons/?search=${encodeURIComponent(query)}&skip=${(page-1)*20}&limit=20`,
    {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    }
  );
  
  return await response.json();
};

// 添加标签
const addTags = async (personId: string, tags: string[]) => {
  const response = await fetch(`/api/v1/persons/${personId}/tags`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify({ tags })
  });
  
  return await response.json();
};
```

---

## 注意事项

1. **认证**: 所有API都需要有效的Bearer token
2. **权限**: 用户只能访问自己创建的人员数据
3. **限制**: 
   - 人员列表每次最多返回100条记录
   - 搜索关键词最少2个字符
   - 标签名称最长50个字符
4. **性能**: 建议使用分页获取大量数据
5. **错误处理**: 请根据HTTP状态码和响应消息进行适当的错误处理

---

**文档维护**: 如有疑问或发现问题，请联系后端开发团队。
