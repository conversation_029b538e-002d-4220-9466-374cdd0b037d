# Nexus 人员管理 API 测试示例

**版本:** v1.0  
**更新日期:** 2025年1月4日  
**基础URL:** `http://localhost:8010/api/v1`

---

## 快速开始

### 1. 环境准备

确保后端服务正在运行：
```bash
# 启动后端服务
uvicorn app.main:app --host 0.0.0.0 --port 8010 --reload
```

### 2. 获取访问令牌

```bash
# 注册用户（如果还没有账户）
curl -X POST "http://localhost:8010/api/v1/auth/register-local" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123"
  }'

# 登录获取令牌
curl -X POST "http://localhost:8010/api/v1/auth/login-local" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123"
  }'
```

**响应示例:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

---

## API 测试示例

### 1. 创建人员

```bash
curl -X POST "http://localhost:8010/api/v1/persons/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "first_name": "张",
    "last_name": "三",
    "professional_info": {
      "company": "阿里巴巴",
      "position": "高级软件工程师",
      "industry": "互联网"
    },
    "contact_info": {
      "email": "<EMAIL>",
      "phone": "+86 138 0000 0000"
    },
    "tags": ["同事", "技术专家", "阿里"],
    "notes": "负责前端架构设计，技术能力很强"
  }'
```

### 2. 创建更多测试数据

```bash
# 创建第二个人员
curl -X POST "http://localhost:8010/api/v1/persons/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "first_name": "李",
    "last_name": "四",
    "professional_info": {
      "company": "腾讯",
      "position": "产品经理",
      "industry": "互联网"
    },
    "contact_info": {
      "email": "<EMAIL>",
      "phone": "+86 139 0000 0000"
    },
    "tags": ["朋友", "产品", "腾讯"],
    "notes": "微信团队的产品经理，对用户体验有深刻理解"
  }'

# 创建第三个人员
curl -X POST "http://localhost:8010/api/v1/persons/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "first_name": "王",
    "last_name": "五",
    "professional_info": {
      "company": "字节跳动",
      "position": "数据科学家",
      "industry": "互联网"
    },
    "contact_info": {
      "email": "<EMAIL>"
    },
    "tags": ["同事", "数据", "AI"],
    "notes": "专注于推荐算法和机器学习"
  }'
```

### 3. 获取人员列表

```bash
# 获取所有人员（分页）
curl -X GET "http://localhost:8010/api/v1/persons/?skip=0&limit=10" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 获取第二页
curl -X GET "http://localhost:8010/api/v1/persons/?skip=10&limit=10" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 4. 搜索功能测试

```bash
# 按姓名搜索
curl -X GET "http://localhost:8010/api/v1/persons/?search=张" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 按公司搜索
curl -X GET "http://localhost:8010/api/v1/persons/?company=阿里" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 组合搜索
curl -X GET "http://localhost:8010/api/v1/persons/?search=李&company=腾讯" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 5. 获取特定人员详情

```bash
# 使用创建人员时返回的person_id
curl -X GET "http://localhost:8010/api/v1/persons/PERSON_ID_HERE" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 6. 更新人员信息

```bash
curl -X PUT "http://localhost:8010/api/v1/persons/PERSON_ID_HERE" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "first_name": "张",
    "last_name": "三",
    "professional_info": {
      "company": "阿里巴巴",
      "position": "技术专家",
      "industry": "互联网"
    },
    "contact_info": {
      "email": "<EMAIL>",
      "phone": "+86 138 0000 0000"
    },
    "tags": ["同事", "技术专家", "阿里", "架构师"],
    "notes": "已升职为技术专家，负责整体技术架构"
  }'
```

### 7. 标签管理

```bash
# 添加标签
curl -X POST "http://localhost:8010/api/v1/persons/PERSON_ID_HERE/tags" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "tags": ["导师", "技术领袖"]
  }'

# 移除标签
curl -X POST "http://localhost:8010/api/v1/persons/PERSON_ID_HERE/remove-tags" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "tags": ["同事"]
  }'

# 获取所有标签
curl -X GET "http://localhost:8010/api/v1/persons/tags" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 8. 关系管理

```bash
# 创建关系（需要两个有效的person_id）
curl -X POST "http://localhost:8010/api/v1/persons/PERSON_ID_1/relationships" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "target_person_id": "PERSON_ID_2",
    "archetype": "colleague",
    "relationship_foundation": {
      "how_met": "技术会议",
      "context": "在阿里巴巴技术大会上认识"
    },
    "relationship_depth": {
      "overall_score": 75,
      "trust_level": 80,
      "communication_frequency": 60
    }
  }'

# 获取人员关系
curl -X GET "http://localhost:8010/api/v1/persons/PERSON_ID_1/relationships" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# 应用关系原型
curl -X POST "http://localhost:8010/api/v1/persons/PERSON_ID_1/apply-archetype" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "archetype": "mentor"
  }'
```

### 9. CSV 导入导出

```bash
# 创建测试CSV文件
cat > test_persons.csv << EOF
first_name,last_name,company,position,email,phone,tags,notes
赵,六,百度,算法工程师,<EMAIL>,+86 150 0000 0000,"同事,算法",搜索算法专家
钱,七,美团,后端工程师,<EMAIL>,+86 151 0000 0000,"朋友,后端",负责订单系统开发
孙,八,滴滴,数据分析师,<EMAIL>,+86 152 0000 0000,"同事,数据",专注出行数据分析
EOF

# 导入CSV
curl -X POST "http://localhost:8010/api/v1/persons/import" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "file=@test_persons.csv"

# 导出CSV
curl -X GET "http://localhost:8010/api/v1/persons/export" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -o "exported_persons.csv"
```

### 10. 删除人员

```bash
# 删除特定人员
curl -X DELETE "http://localhost:8010/api/v1/persons/PERSON_ID_HERE" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

## JavaScript 前端集成示例

### React Hook 示例

```typescript
import { useState, useEffect } from 'react';

// 自定义Hook用于人员管理
export const usePersons = (accessToken: string) => {
  const [persons, setPersons] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const baseURL = 'http://localhost:8010/api/v1';
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${accessToken}`
  };

  // 获取人员列表
  const fetchPersons = async (search?: string, company?: string, page = 1) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (search) params.append('search', search);
      if (company) params.append('company', company);
      params.append('skip', ((page - 1) * 20).toString());
      params.append('limit', '20');

      const response = await fetch(`${baseURL}/persons/?${params}`, { headers });
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      
      const data = await response.json();
      setPersons(data.items);
      return data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // 创建人员
  const createPerson = async (personData: any) => {
    try {
      const response = await fetch(`${baseURL}/persons/`, {
        method: 'POST',
        headers,
        body: JSON.stringify(personData)
      });
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      
      const newPerson = await response.json();
      setPersons(prev => [...prev, newPerson]);
      return newPerson;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // 更新人员
  const updatePerson = async (personId: string, personData: any) => {
    try {
      const response = await fetch(`${baseURL}/persons/${personId}`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(personData)
      });
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      
      const updatedPerson = await response.json();
      setPersons(prev => prev.map(p => 
        p.person_id === personId ? updatedPerson : p
      ));
      return updatedPerson;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // 删除人员
  const deletePerson = async (personId: string) => {
    try {
      const response = await fetch(`${baseURL}/persons/${personId}`, {
        method: 'DELETE',
        headers
      });
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      
      setPersons(prev => prev.filter(p => p.person_id !== personId));
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // 添加标签
  const addTags = async (personId: string, tags: string[]) => {
    try {
      const response = await fetch(`${baseURL}/persons/${personId}/tags`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ tags })
      });
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      
      return await response.json();
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  return {
    persons,
    loading,
    error,
    fetchPersons,
    createPerson,
    updatePerson,
    deletePerson,
    addTags
  };
};
```

### Vue 3 Composition API 示例

```typescript
import { ref, reactive } from 'vue';

export const usePersonsAPI = (accessToken: string) => {
  const persons = ref([]);
  const loading = ref(false);
  const error = ref(null);

  const baseURL = 'http://localhost:8010/api/v1';
  
  const request = async (url: string, options: RequestInit = {}) => {
    const response = await fetch(`${baseURL}${url}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        ...options.headers
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  };

  const fetchPersons = async (filters = {}) => {
    loading.value = true;
    try {
      const params = new URLSearchParams(filters);
      const data = await request(`/persons/?${params}`);
      persons.value = data.items;
      return data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const createPerson = async (personData: any) => {
    const newPerson = await request('/persons/', {
      method: 'POST',
      body: JSON.stringify(personData)
    });
    persons.value.push(newPerson);
    return newPerson;
  };

  return {
    persons,
    loading,
    error,
    fetchPersons,
    createPerson
  };
};
```

---

## 常见问题解决

### 1. 认证问题
```bash
# 如果收到401错误，检查token是否有效
curl -X GET "http://localhost:8010/api/v1/users/me" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 2. 数据验证错误
- 确保必填字段不为空
- 检查UUID格式是否正确
- 验证邮箱格式

### 3. 权限问题
- 用户只能访问自己创建的数据
- 确保使用正确的用户token

---

## 性能建议

1. **分页**: 大量数据时使用分页参数
2. **搜索**: 搜索关键词至少2个字符
3. **缓存**: 前端可以缓存不经常变化的数据
4. **批量操作**: 尽量减少API调用次数

---

**测试完成后**: 请确保清理测试数据，或使用专门的测试环境。
