# Nexus Backend Makefile
# Provides convenient commands for development, testing, and deployment

.PHONY: help install test lint format clean coverage docs docker

# Default target
help: ## Show this help message
	@echo "Nexus Backend Development Commands"
	@echo "=================================="
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Installation and Setup
install: ## Install dependencies using Poetry
	poetry install

install-dev: ## Install development dependencies
	poetry install --with dev,test

setup: ## Initial project setup
	poetry install --with dev,test
	poetry run pre-commit install
	@echo "✅ Project setup complete"

# Code Quality
lint: ## Run all linting tools
	poetry run black --check app/
	poetry run isort --check-only app/
	poetry run flake8 app/
	poetry run mypy app/

format: ## Format code using black and isort
	poetry run black app/
	poetry run isort app/
	@echo "✅ Code formatted"

format-check: ## Check code formatting without making changes
	poetry run black --check app/
	poetry run isort --check-only app/

security: ## Run security checks
	poetry run safety check
	poetry run bandit -r app/

# Testing
test: ## Run all tests
	python scripts/run_tests.py

test-unit: ## Run unit tests only
	python scripts/run_tests.py --type unit

test-integration: ## Run integration tests only
	python scripts/run_tests.py --type integration

test-performance: ## Run performance tests only
	python scripts/run_tests.py --type performance

test-auth: ## Run authentication tests
	python scripts/run_tests.py --marker auth

test-crud: ## Run CRUD tests
	python scripts/run_tests.py --marker crud

test-ai: ## Run AI functionality tests
	python scripts/run_tests.py --marker ai

test-search: ## Run search functionality tests
	python scripts/run_tests.py --marker search

test-graph: ## Run graph visualization tests
	python scripts/run_tests.py --marker graph

test-data: ## Run data portability tests
	python scripts/run_tests.py --marker data_portability

test-external: ## Run external integration tests
	python scripts/run_tests.py --marker external

test-async: ## Run async task tests
	python scripts/run_tests.py --marker async_tasks

test-relationships: ## Run relationship management tests
	python scripts/run_tests.py --marker relationships

test-verbose: ## Run tests with verbose output
	python scripts/run_tests.py --verbose

test-fast: ## Run only fast tests (exclude slow tests)
	poetry run pytest -m "not slow"

test-failed: ## Re-run only failed tests from last run
	poetry run pytest --lf

test-debug: ## Run tests with debug output
	poetry run pytest -s -vv

# Coverage
coverage: ## Generate test coverage report
	poetry run pytest --cov=app --cov-report=html --cov-report=term-missing
	@echo "📊 Coverage report generated in htmlcov/"

coverage-xml: ## Generate XML coverage report
	poetry run pytest --cov=app --cov-report=xml

coverage-analysis: ## Run detailed coverage analysis
	python scripts/coverage_analysis.py --save-report

coverage-trends: ## Update coverage trends
	python scripts/coverage_analysis.py --trends-only

coverage-open: ## Open coverage report in browser
	@if [ -f htmlcov/index.html ]; then \
		python -m webbrowser htmlcov/index.html; \
	else \
		echo "❌ Coverage report not found. Run 'make coverage' first."; \
	fi

# Database
db-create: ## Create all database tables from models
	python scripts/create_db.py

db-upgrade: ## Run database migrations
	poetry run alembic upgrade head

db-downgrade: ## Rollback database migration
	poetry run alembic downgrade -1

db-revision: ## Create new database migration
	@read -p "Enter migration message: " message; \
	poetry run alembic revision --autogenerate -m "$$message"

db-reset: ## Reset test database
	rm -f test.db
	@echo "✅ Test database reset"

# Development Server
dev: ## Start development server
	poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

dev-debug: ## Start development server with debug logging
	poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 --log-level debug

# Docker
docker-build: ## Build Docker image
	docker build -t nexus-backend .

docker-run: ## Run Docker container
	docker run -p 8000:8000 nexus-backend

docker-test: ## Run tests in Docker container
	docker build -f Dockerfile.test -t nexus-backend-test .
	docker run --rm nexus-backend-test

# Documentation
docs: ## Generate API documentation
	poetry run python scripts/generate_docs.py

docs-serve: ## Serve documentation locally
	poetry run mkdocs serve

# Cleanup
clean: ## Clean up generated files
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf coverage.xml
	rm -rf test-results.xml
	rm -rf .pytest_cache/
	rm -rf __pycache__/
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	rm -f test.db
	@echo "✅ Cleanup complete"

clean-reports: ## Clean up test and coverage reports
	rm -rf test-reports/
	rm -rf coverage-reports/
	rm -rf htmlcov/
	@echo "✅ Reports cleaned"

# CI/CD
ci-test: ## Run CI test suite
	poetry run black --check app/
	poetry run isort --check-only app/
	poetry run flake8 app/
	poetry run pytest --cov=app --cov-report=xml --cov-fail-under=90

ci-security: ## Run CI security checks
	poetry run safety check --json --output safety-report.json || true
	poetry run bandit -r app/ -f json -o bandit-report.json || true

# Performance
benchmark: ## Run performance benchmarks
	poetry run pytest -m performance --benchmark-only

profile: ## Profile application performance
	poetry run python -m cProfile -o profile.stats scripts/profile_app.py

# Utilities
check: ## Run all quality checks
	make lint
	make security
	make test-unit
	@echo "✅ All checks passed"

pre-commit: ## Run pre-commit hooks
	poetry run pre-commit run --all-files

update-deps: ## Update dependencies
	poetry update
	poetry export -f requirements.txt --output requirements.txt --without-hashes

# Environment
env-example: ## Create example environment file
	cp .env.example .env
	@echo "✅ Created .env file from example"

# Quick commands
quick-test: ## Quick test run (unit tests only)
	poetry run pytest -m unit --tb=short

quick-check: ## Quick quality check
	poetry run black --check app/
	poetry run pytest -m unit --tb=short

# Help for specific test categories
test-help: ## Show available test categories
	@echo "Available test markers:"
	@echo "  unit          - Unit tests"
	@echo "  integration   - Integration tests"
	@echo "  performance   - Performance tests"
	@echo "  slow          - Slow running tests"
	@echo "  auth          - Authentication tests"
	@echo "  crud          - CRUD operation tests"
	@echo "  ai            - AI functionality tests"
	@echo "  search        - Search functionality tests"
	@echo "  graph         - Graph visualization tests"
	@echo "  data_portability - Data import/export tests"
	@echo "  external      - External service tests"
	@echo "  async_tasks   - Async task processing tests"
	@echo "  relationships - Relationship management tests"

# Development workflow
workflow: ## Complete development workflow
	make format
	make lint
	make test-unit
	make coverage
	@echo "🎉 Development workflow complete!"

# Release preparation
release-check: ## Check if ready for release
	make lint
	make security
	make test
	make coverage-analysis
	@echo "🚀 Release checks complete!"
