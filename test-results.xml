<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="1" skipped="0" tests="2" time="16.488" timestamp="2025-07-10T11:54:04.054120+08:00" hostname="JDZGGSM25060018"><testcase classname="app.tests.test_users.TestUserProfile" name="test_get_user_profile_success" time="8.354" /><testcase classname="app.tests.test_users.TestUserProfile" name="test_get_user_profile_unauthorized" time="4.511"><failure message="AssertionError: Expected message to contain 'unauthorized'">app\tests\test_users.py:39: in test_get_user_profile_unauthorized
    self.assert_unauthorized_response(response)
app\tests\base.py:102: in assert_unauthorized_response
    return self.assert_error_response(response, 401, "unauthorized")
app\tests\base.py:95: in assert_error_response
    self.assertions.assert_error_response(
app\tests\utils.py:216: in assert_error_response
    expected_message_contains.lower() in message.lower()
E   AssertionError: Expected message to contain 'unauthorized'</failure></testcase></testsuite></testsuites>