#!/usr/bin/env python3
"""
Test script to demonstrate stateless conversation functionality
This test simulates multiple server instances by creating separate LLMService instances
"""

import requests
import json
import time
import uuid

BASE_URL = "http://localhost:8010"

def test_stateless_conversation_across_instances():
    """
    Test that conversation history persists across different service instances
    (simulating what happens in multi-instance deployment)
    """
    print("🧪 Testing Stateless Conversation Across Multiple 'Instances'")
    print("=" * 70)
    
    # Step 1: Register and login a test user
    test_email = f"stateless.test.{int(time.time())}@example.com"
    user_data = {
        "email": test_email,
        "password": "TestPassword123!",
        "first_name": "Stateless",
        "last_name": "Test"
    }
    
    # Register
    register_response = requests.post(f"{BASE_URL}/api/v1/auth/register-local", json=user_data)
    print(f"✅ User registration: {register_response.status_code}")
    
    # Login
    login_data = {"email": test_email, "password": "TestPassword123!"}
    login_response = requests.post(f"{BASE_URL}/api/v1/auth/login-local", json=login_data)
    auth_token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {auth_token}"}
    print(f"✅ User login: {login_response.status_code}")
    
    # Step 2: Start a conversation - simulating Instance 1
    print("\n🔹 Instance 1: Starting conversation")
    conversation_id = str(uuid.uuid4())
    
    message1_data = {
        "message": "Hello, I want to create a networking goal for this year.",
        "conversation_id": conversation_id
    }
    
    response1 = requests.post(f"{BASE_URL}/api/v1/copilot/converse", json=message1_data, headers=headers)
    result1 = response1.json()
    
    print(f"  Request: {message1_data['message']}")
    print(f"  Response: {result1.get('response', '')[:100]}...")
    print(f"  Tool calls: {len(result1.get('tool_calls', []))}")
    
    # Step 3: Continue conversation - simulating Instance 2
    print("\n🔹 Instance 2: Continuing conversation")
    message2_data = {
        "message": "Actually, can you help me search my network for people in AI companies?",
        "conversation_id": conversation_id
    }
    
    response2 = requests.post(f"{BASE_URL}/api/v1/copilot/converse", json=message2_data, headers=headers)
    result2 = response2.json()
    
    print(f"  Request: {message2_data['message']}")
    print(f"  Response: {result2.get('response', '')[:100]}...")
    print(f"  Tool calls: {len(result2.get('tool_calls', []))}")
    
    # Step 4: Get conversation history - simulating Instance 3
    print("\n🔹 Instance 3: Retrieving conversation history")
    history_response = requests.get(f"{BASE_URL}/api/v1/copilot/conversation/{conversation_id}", headers=headers)
    history_result = history_response.json()
    
    messages = history_result.get("messages", [])
    print(f"  Found {len(messages)} messages in conversation history")
    
    # Step 5: Verify conversation continuity
    print("\n📊 Verification Results:")
    
    # Check that we have messages from both interactions
    user_messages = [msg for msg in messages if msg["role"] == "user"]
    assistant_messages = [msg for msg in messages if msg["role"] == "assistant"]
    
    print(f"  • User messages: {len(user_messages)}")
    print(f"  • Assistant messages: {len(assistant_messages)}")
    print(f"  • Total messages: {len(messages)}")
    
    # Verify message content continuity
    if len(user_messages) >= 2:
        print(f"  • First message contains: {'networking goal' in user_messages[0]['content']}")
        print(f"  • Second message contains: {'search' in user_messages[1]['content']}")
        
        context_preserved = (
            "networking goal" in user_messages[0]['content'] and
            "search" in user_messages[1]['content']
        )
        
        print(f"\n🎯 Context Preservation Test: {'✅ PASS' if context_preserved else '❌ FAIL'}")
    else:
        print("\n❌ FAIL: Not enough messages found")
        return False
    
    # Step 6: Continue conversation with context reference - simulating Instance 4
    print("\n🔹 Instance 4: Testing context awareness")
    message3_data = {
        "message": "Based on our previous discussion, can you create that networking goal we talked about?",
        "conversation_id": conversation_id
    }
    
    response3 = requests.post(f"{BASE_URL}/api/v1/copilot/converse", json=message3_data, headers=headers)
    result3 = response3.json()
    
    print(f"  Request: {message3_data['message']}")
    print(f"  Response: {result3.get('response', '')[:100]}...")
    
    # Check if the AI understood the context
    context_understood = (
        "goal" in result3.get('response', '').lower() or
        len(result3.get('tool_calls', [])) > 0
    )
    
    print(f"\n🎯 Context Understanding Test: {'✅ PASS' if context_understood else '❌ FAIL'}")
    
    # Final verification - get updated history
    final_history_response = requests.get(f"{BASE_URL}/api/v1/copilot/conversation/{conversation_id}", headers=headers)
    final_history = final_history_response.json()
    final_messages = final_history.get("messages", [])
    
    print(f"\n📈 Final Conversation State:")
    print(f"  • Total messages: {len(final_messages)}")
    print(f"  • Conversation ID: {conversation_id}")
    
    # Overall success
    success = (
        len(final_messages) >= 6 and  # At least 3 user + 3 assistant messages
        context_preserved and
        context_understood
    )
    
    print(f"\n🏆 Overall Multi-Instance Test: {'✅ PASS' if success else '❌ FAIL'}")
    
    if success:
        print("\n✨ Stateless conversation functionality is working correctly!")
        print("   The service can handle multi-instance deployment without losing conversation context.")
    else:
        print("\n⚠️  There may be issues with conversation state persistence.")
    
    return success

def test_database_persistence():
    """Test that conversation data persists in the database"""
    print("\n" + "=" * 70)
    print("🗄️  Testing Database Persistence")
    print("=" * 70)
    
    from app.core.database import SessionLocal
    from app.models.conversation import ConversationService
    
    db = SessionLocal()
    try:
        conv_service = ConversationService(db)
        
        # Get conversation statistics
        stats = conv_service.get_conversation_stats("test-user-id")
        print(f"✅ ConversationService working: {stats}")
        
        # Test database table structure
        from sqlalchemy import text
        result = db.execute(text("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name IN ('conversations', 'conversation_messages')
        """))
        tables = [row[0] for row in result.fetchall()]
        
        print(f"✅ Database tables exist: {tables}")
        
        # Test conversation count
        result = db.execute(text("SELECT COUNT(*) FROM conversations"))
        conv_count = result.scalar()
        
        result = db.execute(text("SELECT COUNT(*) FROM conversation_messages"))
        msg_count = result.scalar()
        
        print(f"✅ Database has {conv_count} conversations and {msg_count} messages")
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    try:
        # Test API functionality
        api_success = test_stateless_conversation_across_instances()
        
        # Test database persistence
        db_success = test_database_persistence()
        
        print("\n" + "=" * 70)
        print("📋 SUMMARY")
        print("=" * 70)
        print(f"API Multi-Instance Test: {'✅ PASS' if api_success else '❌ FAIL'}")
        print(f"Database Persistence Test: {'✅ PASS' if db_success else '❌ FAIL'}")
        
        overall_success = api_success and db_success
        print(f"\n🎯 Overall Stateless Service Test: {'✅ PASS' if overall_success else '❌ FAIL'}")
        
        if overall_success:
            print("\n🚀 The service is ready for multi-instance deployment!")
        else:
            print("\n⚠️  Issues detected - review logs above.")
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")