#!/usr/bin/env python3
"""
Test script for the new LLM-powered Copilot functionality
Tests the intelligent conversation and function calling capabilities
"""

import requests
import json
import sys
import os
from typing import Dict, Any

# Add the app directory to the path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

BASE_URL = "http://localhost:8010"

class CopilotTester:
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.auth_token = None
        self.conversation_id = None
        
    def authenticate(self) -> bool:
        """Authenticate and get auth token"""
        try:
            # Try to register a test user
            user_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "first_name": "Copilot",
                "last_name": "Tester"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/auth/register-local",
                json=user_data
            )
            
            # Login (whether registration succeeded or user already exists)
            login_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/auth/login-local",
                json=login_data
            )
            
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get("access_token")
                print(f"✅ Authentication successful")
                return bool(self.auth_token)
            else:
                print(f"❌ Authentication failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authorization headers"""
        if not self.auth_token:
            return {}
        return {"Authorization": f"Bearer {self.auth_token}"}
    
    def test_basic_conversation(self) -> bool:
        """Test basic conversation functionality"""
        try:
            print("\n🧪 Testing basic conversation...")
            
            message_data = {
                "message": "Hello! Can you help me with my professional network?"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/copilot/converse",
                json=message_data,
                headers=self.get_auth_headers()
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Response: {data.get('response', 'No response')}")
                self.conversation_id = data.get('conversation_id')
                print(f"Conversation ID: {self.conversation_id}")
                return True
            else:
                print(f"Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Basic conversation test failed: {e}")
            return False
    
    def test_network_search(self) -> bool:
        """Test network search functionality"""
        try:
            print("\n🧪 Testing network search...")
            
            message_data = {
                "message": "Can you search my network for people who work at tech companies?",
                "conversation_id": self.conversation_id
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/copilot/converse",
                json=message_data,
                headers=self.get_auth_headers()
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Response: {data.get('response', 'No response')}")
                
                # Check if tool calls were made
                tool_calls = data.get('tool_calls', [])
                if tool_calls:
                    print(f"Tool calls made: {len(tool_calls)}")
                    for tool_call in tool_calls:
                        print(f"  - {tool_call.get('name')}: {tool_call.get('status')}")
                
                return True
            else:
                print(f"Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Network search test failed: {e}")
            return False
    
    def test_task_creation(self) -> bool:
        """Test task creation functionality"""
        try:
            print("\n🧪 Testing task creation...")
            
            message_data = {
                "message": "Please create a task to follow up with John Smith about the project collaboration",
                "conversation_id": self.conversation_id
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/copilot/converse",
                json=message_data,
                headers=self.get_auth_headers()
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Response: {data.get('response', 'No response')}")
                
                # Check if tool calls were made
                tool_calls = data.get('tool_calls', [])
                if tool_calls:
                    print(f"Tool calls made: {len(tool_calls)}")
                    for tool_call in tool_calls:
                        print(f"  - {tool_call.get('name')}: {tool_call.get('status')}")
                        if tool_call.get('result'):
                            print(f"    Result: {tool_call.get('result')}")
                
                return True
            else:
                print(f"Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Task creation test failed: {e}")
            return False
    
    def test_network_health_analysis(self) -> bool:
        """Test network health analysis"""
        try:
            print("\n🧪 Testing network health analysis...")
            
            message_data = {
                "message": "Can you analyze my network health and give me insights?",
                "conversation_id": self.conversation_id
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/copilot/converse",
                json=message_data,
                headers=self.get_auth_headers()
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Response: {data.get('response', 'No response')}")
                
                # Check if tool calls were made
                tool_calls = data.get('tool_calls', [])
                if tool_calls:
                    print(f"Tool calls made: {len(tool_calls)}")
                    for tool_call in tool_calls:
                        print(f"  - {tool_call.get('name')}: {tool_call.get('status')}")
                
                return True
            else:
                print(f"Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Network health analysis test failed: {e}")
            return False
    
    def test_intent_analysis(self) -> bool:
        """Test intent analysis endpoint"""
        try:
            print("\n🧪 Testing intent analysis...")
            
            message_data = {
                "message": "I want to create a goal to expand my network in the AI industry"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/copilot/analyze",
                json=message_data,
                headers=self.get_auth_headers()
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Intent: {data.get('intent', 'unknown')}")
                print(f"Confidence: {data.get('confidence', 0.0)}")
                print(f"Entities: {data.get('entities', [])}")
                return True
            else:
                print(f"Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Intent analysis test failed: {e}")
            return False
    
    def test_suggestions(self) -> bool:
        """Test AI suggestions endpoint"""
        try:
            print("\n🧪 Testing AI suggestions...")
            
            response = self.session.get(
                f"{self.base_url}/api/v1/copilot/suggestions?limit=5",
                headers=self.get_auth_headers()
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                suggestions = data.get('suggestions', [])
                print(f"Suggestions received: {len(suggestions)}")
                
                if suggestions:
                    for i, suggestion in enumerate(suggestions[:3]):
                        print(f"  {i+1}. {suggestion.get('title', 'No title')}")
                
                return True
            else:
                print(f"Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Suggestions test failed: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all copilot tests"""
        print("🤖 Testing LLM-Powered Copilot Functionality")
        print("=" * 60)
        
        results = {}
        
        # Authentication
        if not self.authenticate():
            print("❌ Cannot proceed without authentication")
            return {"authentication": False}
        
        # Run tests
        test_methods = [
            ("basic_conversation", self.test_basic_conversation),
            ("network_search", self.test_network_search),
            ("task_creation", self.test_task_creation),
            ("network_health_analysis", self.test_network_health_analysis),
            ("intent_analysis", self.test_intent_analysis),
            ("suggestions", self.test_suggestions)
        ]
        
        for test_name, test_method in test_methods:
            try:
                results[test_name] = test_method()
                status = "✅ PASS" if results[test_name] else "❌ FAIL"
                print(f"{status} {test_name}")
            except Exception as e:
                print(f"❌ ERROR {test_name}: {e}")
                results[test_name] = False
        
        return results

def main():
    """Main test runner"""
    print("🤖 LLM-Powered Copilot Test Suite")
    print("Testing intelligent conversation and function calling")
    print("=" * 60)
    
    tester = CopilotTester()
    results = tester.run_all_tests()
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY:")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {test_name.replace('_', ' ').title()}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All copilot tests passed! LLM functionality is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        print("\nNote: If OpenAI API key is not configured, some features will use fallback implementations.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
