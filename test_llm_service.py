#!/usr/bin/env python3
"""
Test LLM Service directly to identify issues
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

async def test_llm_service():
    print("🧪 Testing LLM Service directly")
    print("=" * 40)
    
    try:
        from app.core.llm_service import LLMService
        from app.core.config import settings
        
        print(f"✅ LLM Service imported successfully")
        print(f"✅ OpenAI API Key configured: {bool(settings.OPENAI_API_KEY)}")
        
        # Initialize LLM service
        llm = LLMService()
        print(f"✅ LLM Service initialized")
        
        # Test basic conversation
        response = await llm.process_conversation(
            user_message="Hello, this is a test",
            conversation_id="test-123"
        )
        
        print(f"✅ Conversation processed")
        print(f"   Response keys: {list(response.keys())}")
        print(f"   Response: '{response.get('response', 'MISSING')}'")
        print(f"   Response type: {type(response.get('response'))}")
        print(f"   Tool calls: {len(response.get('tool_calls', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_llm_service())
    if result:
        print("\n🎉 LLM Service test passed!")
    else:
        print("\n💥 LLM Service test failed!")
