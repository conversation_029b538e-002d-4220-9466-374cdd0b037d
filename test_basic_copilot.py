#!/usr/bin/env python3
"""
Basic Copilot Test - Simple test to verify functionality
"""

import requests
import json
import time

BASE_URL = "http://localhost:8010"

def main():
    print("🤖 Basic Copilot Test")
    print("=" * 40)
    
    session = requests.Session()
    
    # 1. Test health
    print("1. Testing health endpoint...")
    try:
        response = session.get(f"{BASE_URL}/health", timeout=10)
        print(f"   Health: {response.status_code}")
        if response.status_code != 200:
            print(f"   Error: {response.text}")
            return
    except Exception as e:
        print(f"   Health failed: {e}")
        return
    
    # 2. Register user
    print("2. Registering user...")
    user_data = {
        "email": f"basictest.{int(time.time())}@example.com",
        "password": "TestPassword123!",
        "first_name": "Basic",
        "last_name": "Test"
    }
    
    try:
        response = session.post(f"{BASE_URL}/api/v1/auth/register-local", json=user_data, timeout=10)
        print(f"   Register: {response.status_code}")
        if response.status_code not in [200, 201]:
            print(f"   Register error: {response.text}")
            return
    except Exception as e:
        print(f"   Register failed: {e}")
        return
    
    # 3. Login
    print("3. Logging in...")
    login_data = {"email": user_data["email"], "password": user_data["password"]}
    
    try:
        response = session.post(f"{BASE_URL}/api/v1/auth/login-local", json=login_data, timeout=10)
        print(f"   Login: {response.status_code}")
        if response.status_code != 200:
            print(f"   Login error: {response.text}")
            return
        
        token = response.json().get("access_token")
        headers = {"Authorization": f"Bearer {token}"}
        print(f"   Token received: {bool(token)}")
        
    except Exception as e:
        print(f"   Login failed: {e}")
        return
    
    # 4. Test copilot conversation
    print("4. Testing copilot conversation...")
    message_data = {"message": "Hello, can you help me?"}
    
    try:
        response = session.post(
            f"{BASE_URL}/api/v1/copilot/converse",
            json=message_data,
            headers=headers,
            timeout=30
        )
        print(f"   Copilot: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response present: {bool(data.get('response'))}")
            print(f"   Response length: {len(str(data.get('response', '')))}")
            print(f"   Conversation ID: {bool(data.get('conversation_id'))}")
            print(f"   Tool calls: {len(data.get('tool_calls', []))}")
            
            # Test with function calling request
            print("5. Testing function calling...")
            fc_message = {
                "message": "Can you search my network for connections?",
                "conversation_id": data.get('conversation_id')
            }
            
            response2 = session.post(
                f"{BASE_URL}/api/v1/copilot/converse",
                json=fc_message,
                headers=headers,
                timeout=30
            )
            print(f"   Function calling: {response2.status_code}")
            
            if response2.status_code == 200:
                data2 = response2.json()
                tool_calls = data2.get('tool_calls', [])
                print(f"   Tool calls made: {len(tool_calls)}")
                for tc in tool_calls:
                    print(f"     - {tc.get('name')}: {tc.get('status')}")
            else:
                print(f"   Function calling error: {response2.text}")
                
        else:
            print(f"   Copilot error: {response.text}")
            
    except Exception as e:
        print(f"   Copilot failed: {e}")
        return
    
    print("\n✅ Basic copilot test completed!")

if __name__ == "__main__":
    main()
