# Nexus Backend API Documentation

## 🚀 Overview

Nexus Backend provides a comprehensive REST API for managing personal and professional networks. The API supports user management, relationship tracking, AI-powered insights, and network analysis.

**Base URL**: `http://localhost:8010`  
**API Version**: v1  
**Authentication**: <PERSON><PERSON> (JWT)

## 📋 Quick Start

### 1. Start the Server
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8010 --reload
```

### 2. Check Health
```bash
curl http://localhost:8010/health
```

### 3. View Interactive Documentation
- **Swagger UI**: http://localhost:8010/docs
- **ReDoc**: http://localhost:8010/redoc
- **OpenAPI Spec**: http://localhost:8010/openapi.json

## 🔐 Authentication

### Register User
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>"
}
```

### Login
```http
POST /api/v1/auth/login
Content-Type: application/x-www-form-urlencoded

username=<EMAIL>&password=SecurePassword123!
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

### Using Authentication
Include the token in all subsequent requests:
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 👥 Person Management

### Create Person
```http
POST /api/v1/persons
Authorization: Bearer {token}
Content-Type: application/json

{
  "first_name": "Jane",
  "last_name": "Smith",
  "contact_info": {
    "email": "<EMAIL>",
    "phone": "******-0123"
  },
  "professional_info": {
    "company": "Tech Corp",
    "title": "Software Engineer",
    "industry": "Technology"
  },
  "personal_details": {
    "tags": ["colleague", "friend"],
    "notes": "Met at tech conference"
  }
}
```

**Response:**
```json
{
  "person_id": "123e4567-e89b-12d3-a456-************",
  "first_name": "Jane",
  "last_name": "Smith",
  "contact_info": {
    "email": "<EMAIL>",
    "phone": "******-0123"
  },
  "professional_info": {
    "company": "Tech Corp",
    "title": "Software Engineer",
    "industry": "Technology"
  },
  "personal_details": {
    "tags": ["colleague", "friend"],
    "notes": "Met at tech conference"
  },
  "created_at": "2025-01-09T10:30:00Z",
  "updated_at": "2025-01-09T10:30:00Z"
}
```

### Get All Persons
```http
GET /api/v1/persons?limit=20&offset=0
Authorization: Bearer {token}
```

### Get Person by ID
```http
GET /api/v1/persons/{person_id}
Authorization: Bearer {token}
```

### Update Person
```http
PUT /api/v1/persons/{person_id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "professional_info": {
    "company": "New Company",
    "title": "Senior Engineer"
  }
}
```

### Delete Person
```http
DELETE /api/v1/persons/{person_id}
Authorization: Bearer {token}
```

## 🏢 Organization Management

### Create Organization
```http
POST /api/v1/organizations
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Tech Innovations Inc",
  "description": "Leading technology company",
  "details": {
    "industry": "Technology",
    "size": "500-1000",
    "location": "San Francisco, CA"
  }
}
```

**Response:**
```json
{
  "org_id": "456e7890-e89b-12d3-a456-************",
  "name": "Tech Innovations Inc",
  "description": "Leading technology company",
  "details": {
    "industry": "Technology",
    "size": "500-1000",
    "location": "San Francisco, CA"
  },
  "user_id": "user-uuid",
  "employee_count": 0,
  "connection_count": 0,
  "created_at": "2025-01-09T10:30:00Z",
  "updated_at": "2025-01-09T10:30:00Z"
}
```

### Get Organizations
```http
GET /api/v1/organizations
Authorization: Bearer {token}
```

## 🔗 Relationship Management

### Create Relationship
```http
POST /api/v1/relationships
Authorization: Bearer {token}
Content-Type: application/json

{
  "to_person_id": "123e4567-e89b-12d3-a456-************",
  "archetype": "Professional Colleague",
  "relationship_depth": {
    "closeness": 7,
    "frequency": 6,
    "duration": 8,
    "overall_score": 70
  },
  "context": {
    "how_met": "Tech conference",
    "shared_interests": ["technology", "innovation"],
    "interaction_frequency": "weekly"
  }
}
```

### Update Relationship Dimensions
```http
PUT /api/v1/relationships/{relationship_id}/dimensions
Authorization: Bearer {token}
Content-Type: application/json

{
  "relationship_depth": {
    "closeness": 8,
    "frequency": 7,
    "duration": 8,
    "overall_score": 77
  }
}
```

### Get Relationships
```http
GET /api/v1/relationships?archetype=Professional%20Colleague
Authorization: Bearer {token}
```

## 🔍 Search Functionality

### Search Persons
```http
GET /api/v1/search/persons?q=Jane&limit=10&offset=0
Authorization: Bearer {token}
```

**Response:**
```json
{
  "results": [
    {
      "person_id": "123e4567-e89b-12d3-a456-************",
      "first_name": "Jane",
      "last_name": "Smith",
      "full_name": "Jane Smith",
      "company": "Tech Corp",
      "title": "Software Engineer",
      "tags": ["colleague", "friend"],
      "match_score": 0.95,
      "relevance_score": 0.95,
      "match_fields": ["first_name", "full_name"]
    }
  ],
  "pagination": {
    "total": 1,
    "limit": 10,
    "offset": 0,
    "has_more": false
  },
  "metadata": {
    "query": "Jane",
    "similarity_threshold": 0.3,
    "filters_applied": {
      "company": null,
      "tags": null,
      "relationship_type": null,
      "interaction_since": null
    }
  }
}
```

### Advanced Search with Filters
```http
GET /api/v1/search/persons?q=engineer&company=tech&tags=colleague,friend&limit=20
Authorization: Bearer {token}
```

## 🧠 AI Features

### Get AI Suggestions
```http
GET /api/v1/ai/suggestions?type=person&limit=5
Authorization: Bearer {token}
```

**Response:**
```json
{
  "suggestions": [
    {
      "suggestion_id": "ai-001",
      "type": "person",
      "title": "Connect with John Doe",
      "description": "Based on your network, you might want to connect with John Doe",
      "confidence": 0.85,
      "reasoning": "Shared connections and similar professional background",
      "action_data": {
        "person_id": "789e0123-e89b-12d3-a456-426614174002"
      }
    }
  ],
  "metadata": {
    "total_suggestions": 1,
    "suggestion_types": ["person"],
    "generated_at": "2025-01-09T10:30:00Z"
  }
}
```

### Get Dormant Relationships
```http
GET /api/v1/ai/dormant-relationships?days=90
Authorization: Bearer {token}
```

## 📊 Network Health Analysis

### Get Network Health Diagnosis
```http
GET /api/v1/network/health
Authorization: Bearer {token}
```

**Response:**
```json
{
  "overall_health_score": 75,
  "network_size": 45,
  "active_relationships": 32,
  "dormant_relationships": 13,
  "network_metrics": {
    "density": 0.65,
    "clustering_coefficient": 0.72,
    "average_path_length": 2.3
  },
  "diversity_metrics": {
    "industry_diversity": 0.8,
    "role_diversity": 0.7,
    "geographic_diversity": 0.6
  },
  "activity_metrics": {
    "interaction_frequency": 0.75,
    "response_rate": 0.85,
    "engagement_quality": 0.8
  },
  "recommendations": [
    {
      "type": "reconnect",
      "priority": "high",
      "description": "Reconnect with 5 dormant relationships",
      "action_items": ["Schedule coffee with Sarah", "Send update to Mike"]
    }
  ],
  "trends": {
    "growth_rate": 0.15,
    "engagement_trend": "increasing",
    "diversity_trend": "stable"
  }
}
```

## 👤 User Management

### Get Current User Profile
```http
GET /api/v1/users/me
Authorization: Bearer {token}
```

### Update User Profile
```http
PUT /api/v1/users/me
Authorization: Bearer {token}
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Doe",
  "preferences": {
    "notification_frequency": "daily",
    "privacy_level": "medium"
  }
}
```

## 📈 Goals and Tasks

### Create Goal
```http
POST /api/v1/goals
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "Expand Professional Network",
  "description": "Connect with 10 new professionals in my industry",
  "target_date": "2025-06-01",
  "category": "networking",
  "metrics": {
    "target_value": 10,
    "current_value": 0,
    "unit": "connections"
  }
}
```

### Create Task
```http
POST /api/v1/tasks
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "Follow up with Jane Smith",
  "description": "Send follow-up email about collaboration opportunity",
  "due_date": "2025-01-15",
  "priority": "high",
  "related_person_id": "123e4567-e89b-12d3-a456-************"
}
```

## 📊 Data Import/Export

### Export User Data
```http
GET /api/v1/data/export?format=json
Authorization: Bearer {token}
```

**Response:**
```json
{
  "export_id": "export-123",
  "format": "json",
  "data": {
    "persons": [...],
    "relationships": [...],
    "organizations": [...],
    "goals": [...],
    "tasks": [...]
  },
  "metadata": {
    "exported_at": "2025-01-09T10:30:00Z",
    "total_records": 150,
    "version": "1.0"
  }
}
```

### Import Data
```http
POST /api/v1/data/import
Authorization: Bearer {token}
Content-Type: application/json

{
  "format": "json",
  "data": {
    "persons": [...],
    "relationships": [...]
  },
  "options": {
    "merge_strategy": "update",
    "validate_data": true
  }
}
```

## 🔄 Interaction Tracking

### Record Interaction
```http
POST /api/v1/interactions
Authorization: Bearer {token}
Content-Type: application/json

{
  "interaction_type": "meeting",
  "occurred_at": "2025-01-09T14:30:00Z",
  "duration_minutes": 60,
  "participants": [
    {"person_id": "123e4567-e89b-12d3-a456-************"}
  ],
  "details": {
    "location": "Coffee shop",
    "topics": ["project collaboration", "industry trends"],
    "outcome": "positive",
    "follow_up_required": true
  },
  "notes": "Great discussion about potential collaboration"
}
```

### Get Interactions
```http
GET /api/v1/interactions?person_id={person_id}&limit=20
Authorization: Bearer {token}
```

## 🛠️ Error Handling

### Standard Error Response
```json
{
  "detail": "Error description",
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2025-01-09T10:30:00Z",
  "path": "/api/v1/persons",
  "request_id": "req-123456"
}
```

### Common HTTP Status Codes
- `200 OK` - Successful GET, PUT requests
- `201 Created` - Successful POST requests
- `204 No Content` - Successful DELETE requests
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Missing or invalid authentication
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `422 Unprocessable Entity` - Validation errors
- `500 Internal Server Error` - Server errors

### Validation Errors
```json
{
  "detail": [
    {
      "loc": ["body", "email"],
      "msg": "field required",
      "type": "value_error.missing"
    },
    {
      "loc": ["body", "password"],
      "msg": "ensure this value has at least 8 characters",
      "type": "value_error.any_str.min_length"
    }
  ]
}
```

## 📝 Request/Response Examples

### Pagination
Most list endpoints support pagination:
```http
GET /api/v1/persons?limit=20&offset=40
```

### Filtering
Many endpoints support filtering:
```http
GET /api/v1/relationships?archetype=Professional&status=active
```

### Sorting
Some endpoints support sorting:
```http
GET /api/v1/persons?sort_by=last_name&sort_order=asc
```

## 🔧 Development Tools

### Health Check
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-09T10:30:00Z",
  "version": "1.0.0",
  "database": "connected",
  "services": {
    "ai_engine": "operational",
    "search": "operational",
    "notifications": "operational"
  }
}
```

### API Information
```http
GET /api/v1/info
```

**Response:**
```json
{
  "name": "Nexus Backend API",
  "version": "1.0.0",
  "description": "Personal and professional network management API",
  "documentation": "http://localhost:8010/docs",
  "contact": {
    "name": "Nexus Team",
    "email": "<EMAIL>"
  }
}
```

## 🚀 Rate Limiting

The API implements rate limiting to ensure fair usage:

- **Authenticated requests**: 1000 requests per hour
- **Unauthenticated requests**: 100 requests per hour
- **Search requests**: 500 requests per hour

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## 🔒 Security

### HTTPS
In production, all API calls must use HTTPS.

### CORS
The API supports CORS for web applications. Configure allowed origins in the server settings.

### Data Privacy
- All personal data is encrypted at rest
- API logs do not contain sensitive information
- User data is isolated per account

## 📞 Support

### Getting Help
- **Documentation**: http://localhost:8010/docs
- **Issues**: Create an issue in the project repository
- **Email**: <EMAIL>

### Testing
Use the interface test suite to validate API functionality:
```bash
python -m interface_tests.test_runner --base-url http://localhost:8010
```
```
