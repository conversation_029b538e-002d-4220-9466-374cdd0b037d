[pytest]
# Test discovery
testpaths = app/tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Test execution options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=app
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-report=term-missing
    --cov-fail-under=90
    --cov-branch
    --junitxml=test-results.xml

# Test markers
markers =
    unit: Unit tests
    integration: Integration tests
    performance: Performance tests
    slow: Slow running tests
    auth: Authentication related tests
    crud: CRUD operation tests
    ai: AI functionality tests
    search: Search functionality tests
    graph: Graph visualization tests
    data_portability: Data import/export tests
    external: External service integration tests
    async_tasks: Async task processing tests
    relationships: Relationship management tests

# Minimum version requirements
minversion = 6.0

# Test timeout (in seconds)
# timeout = 300

# Disable warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:supabase.*
    ignore::UserWarning:openai.*

# Test collection

# Coverage configuration
[coverage:run]
source = app
omit = 
    app/tests/*
    app/migrations/*
    app/scripts/*
    app/main.py
    */venv/*
    */virtualenv/*
    */.tox/*
    */site-packages/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

show_missing = True
precision = 2
skip_covered = False
skip_empty = True

[coverage:html]
directory = htmlcov
title = Nexus Backend Test Coverage Report

[coverage:xml]
output = coverage.xml
