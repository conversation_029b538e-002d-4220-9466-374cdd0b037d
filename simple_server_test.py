#!/usr/bin/env python3
"""
Simple server test to verify basic functionality
"""

import subprocess
import time
import requests
import sys

def start_server():
    """Start the server in background"""
    try:
        print("🚀 Starting server...")
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "app.main:app", 
            "--host", "127.0.0.1", 
            "--port", "8012"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for server to start
        time.sleep(5)
        
        # Test if server is running
        response = requests.get("http://localhost:8012/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server started successfully")
            return process
        else:
            print(f"❌ Server health check failed: {response.status_code}")
            process.terminate()
            return None
            
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return None

def test_basic_endpoints(base_url="http://localhost:8012"):
    """Test basic endpoints"""
    print("\n🧪 Testing basic endpoints...")
    
    # Test health
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        print(f"✅ Health: {response.status_code}")
    except Exception as e:
        print(f"❌ Health failed: {e}")
        return False
    
    # Test register
    try:
        user_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "first_name": "Test",
            "last_name": "User"
        }
        response = requests.post(f"{base_url}/api/v1/auth/register-local", json=user_data, timeout=10)
        print(f"✅ Register: {response.status_code}")
    except Exception as e:
        print(f"❌ Register failed: {e}")
        return False
    
    # Test login
    try:
        login_data = {"email": "<EMAIL>", "password": "TestPassword123!"}
        response = requests.post(f"{base_url}/api/v1/auth/login-local", json=login_data, timeout=10)
        print(f"✅ Login: {response.status_code}")
        
        if response.status_code == 200:
            token = response.json().get("access_token")
            headers = {"Authorization": f"Bearer {token}"}
            
            # Test copilot endpoint
            try:
                message_data = {"message": "Hello"}
                response = requests.post(
                    f"{base_url}/api/v1/copilot/converse",
                    json=message_data,
                    headers=headers,
                    timeout=30
                )
                print(f"✅ Copilot: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   Response present: {bool(data.get('response'))}")
                    print(f"   Conversation ID: {bool(data.get('conversation_id'))}")
                    return True
                else:
                    print(f"   Copilot error: {response.text}")
                    
            except Exception as e:
                print(f"❌ Copilot failed: {e}")
                
    except Exception as e:
        print(f"❌ Login failed: {e}")
    
    return False

def main():
    print("🔧 Simple Server Test")
    print("=" * 40)
    
    # Start server
    server_process = start_server()
    if not server_process:
        return 1
    
    try:
        # Test endpoints
        success = test_basic_endpoints()
        
        if success:
            print("\n🎉 All tests passed!")
            return 0
        else:
            print("\n💥 Some tests failed!")
            return 1
            
    finally:
        # Clean up
        if server_process:
            print("\n🧹 Stopping server...")
            server_process.terminate()
            server_process.wait()

if __name__ == "__main__":
    sys.exit(main())
