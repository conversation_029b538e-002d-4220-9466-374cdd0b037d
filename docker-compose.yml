version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: nexus_db
      POSTGRES_USER: nexus_user
      POSTGRES_PASSWORD: nexus_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U nexus_user -d nexus_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis (for caching and sessions)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nexus Backend API
  api:
    build: .
    ports:
      - "8010:8000"
    environment:
      - DEBUG=True
      - DATABASE_URL=postgresql+asyncpg://nexus_user:nexus_password@db:5432/nexus_db
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-super-secret-key-for-development
      - ALLOWED_HOSTS=http://localhost:3000,http://localhost:8080
    volumes:
      - ./app:/app/app
      - ./uploads:/app/uploads
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
