# Nexus Backend - 关系智能领航者

Nexus是一个基于AI的个人关系管理平台，帮助用户智能地管理和分析他们的人际网络。

## 🚀 特性

- **智能关系分析**: 基于六维模型的深度关系分析
- **AI驱动的建议**: 主动式AI助手提供个性化建议
- **网络图谱可视化**: 交互式人际网络地图
- **目标导向的网络拓展**: 基于目标的引荐路径查找
- **隐私优先**: 混合架构保护用户隐私
- **对话式交互**: 自然语言处理的智能助手

## 🏗️ 技术架构

- **后端框架**: FastAPI + Python 3.11
- **数据库**: PostgreSQL + Redis
- **AI/ML**: OpenAI GPT + NetworkX
- **认证**: JWT Token
- **部署**: Docker + Docker Compose
- **监控**: Prometheus + Grafana

## 📋 系统要求

- Python 3.11+
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose (推荐)

## 🚀 快速开始

### 使用Docker Compose (推荐)

1. **克隆项目**
```bash
git clone https://github.com/linyouyu/Nexus_backend.git
cd Nexus_backend
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，填入必要的配置
```

3. **启动服务**
```bash
docker-compose up -d
```

4. **运行数据库迁移**
```bash
docker-compose exec api alembic upgrade head
```

5. **访问API文档**
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

### 本地开发

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **配置数据库**
```bash
# 创建PostgreSQL数据库
createdb nexus_db

# 设置环境变量
export DATABASE_URL="postgresql+asyncpg://username:password@localhost:5432/nexus_db"
export SECRET_KEY="your-secret-key"
```

3. **运行迁移**
```bash
alembic upgrade head
```

4. **启动开发服务器**
```bash
uvicorn app.main:app --reload
```

## 📚 API文档

### 认证端点
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新Token

### 核心功能端点
- `GET /api/v1/users/me` - 获取当前用户信息
- `POST /api/v1/persons` - 创建联系人
- `GET /api/v1/persons` - 获取联系人列表
- `POST /api/v1/goals` - 创建目标
- `POST /api/v1/tasks` - 创建任务

### AI功能端点
- `POST /api/v1/copilot/converse` - 与AI助手对话
- `GET /api/v1/copilot/suggestions` - 获取AI建议
- `GET /api/v1/ai/network/diagnosis` - 网络健康诊断
- `POST /api/v1/ai/network/referral-path` - 查找引荐路径

### 数据管理端点
- `POST /api/v1/data/export` - 导出数据
- `POST /api/v1/data/import` - 导入数据
- `GET /api/v1/graph/full` - 获取完整网络图谱

## 🗄️ 数据库设计

系统采用基于图的数据模型，核心实体包括：

- **User**: 用户账户
- **Person**: 个人联系人（包括用户自己）
- **Organization**: 组织/公司
- **Knows**: 关系连接（六维模型）
- **Interaction**: 互动记录
- **Goal**: 目标
- **Task**: 任务

详细的数据库设计请参考技术文档。

## 🤖 AI功能

### 六维关系模型
- 情感亲密度 (Emotional Intimacy)
- 职业协作度 (Professional Collaboration)  
- 信任水平 (Trust Level)
- 沟通频率 (Communication Frequency)
- 共同经验价值 (Shared Experience Value)
- 互惠平衡度 (Reciprocity Balance)

### 智能建议类型
- 关系维护提醒
- 网络拓展建议
- 目标导向的人脉推荐
- 引荐路径优化

## 🔒 安全特性

- JWT Token认证
- 密码哈希存储
- CORS保护
- 速率限制
- 安全头部
- 数据加密

## 🚀 部署

### 生产环境部署

1. **使用生产配置**
```bash
docker-compose -f docker-compose.prod.yml up -d
```

2. **配置Nginx反向代理**
```bash
# 编辑 nginx/nginx.conf
# 配置SSL证书
```

3. **设置监控**
- Prometheus: http://localhost:9090
- Grafana: http://localhost:3000

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `DEBUG` | 调试模式 | `False` |
| `SECRET_KEY` | JWT密钥 | 必填 |
| `DATABASE_URL` | 数据库连接 | 必填 |
| `REDIS_URL` | Redis连接 | 可选 |
| `OPENAI_API_KEY` | OpenAI API密钥 | 可选 |
| `ALLOWED_HOSTS` | 允许的主机 | `*` |

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest app/tests/test_auth.py

# 生成覆盖率报告
pytest --cov=app --cov-report=html
```

## 📈 监控和日志

- **结构化日志**: 使用structlog进行JSON格式日志
- **指标监控**: Prometheus收集应用指标
- **可视化**: Grafana仪表盘
- **健康检查**: `/health`端点

## 🤝 贡献

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：
- 创建Issue
- 发送邮件至: <EMAIL>

## 🗺️ 路线图

- [ ] 移动端应用
- [ ] 高级AI分析
- [ ] 社交媒体集成
- [ ] 团队协作功能
- [ ] 企业版功能
