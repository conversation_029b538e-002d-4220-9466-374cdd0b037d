#!/usr/bin/env python3
"""
Network Health Performance Testing Script
Tests network health diagnosis performance with various network configurations
"""

import sys
import os
import time
import uuid
import json
from typing import Dict, List, Any
from datetime import datetime, timedelta

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.core.database import <PERSON><PERSON>ocal
from app.models.person import Person
from app.models.relationship import Knows
from app.models.interaction import Interaction
from app.services.network_health_service import NetworkHealthService
from app.services.ai_engine_service import AIEngineService


class NetworkHealthPerformanceTester:
    """Test network health diagnosis performance"""
    
    def __init__(self):
        self.db = SessionLocal()
        self.health_service = NetworkHealthService(self.db)
        self.ai_engine = AIEngineService(self.db)
        self.test_user_id = uuid.uuid4()
        self.test_results = []
    
    def cleanup(self):
        """Clean up database and close connections"""
        try:
            # Delete test data
            self.db.query(Interaction).filter(
                Interaction.user_id == self.test_user_id
            ).delete()
            self.db.query(Knows).filter(
                Knows.user_id == self.test_user_id
            ).delete()
            self.db.query(Person).filter(
                Person.user_id == self.test_user_id
            ).delete()
            self.db.commit()
        except Exception as e:
            print(f"Cleanup error: {e}")
            self.db.rollback()
        finally:
            self.db.close()
    
    def create_test_network(
        self, 
        num_persons: int, 
        network_type: str = "balanced",
        activity_level: str = "medium"
    ) -> List[uuid.UUID]:
        """Create test network with specified characteristics"""
        print(f"Creating {network_type} network with {num_persons} persons, {activity_level} activity")
        
        person_ids = [uuid.uuid4() for _ in range(num_persons)]
        
        # Create user person
        user_person = Person(
            person_id=person_ids[0],
            user_id=self.test_user_id,
            first_name="Test",
            last_name="User",
            is_user=True
        )
        persons = [user_person]
        
        # Define industry/company distributions
        if network_type == "diverse":
            industries = ["Technology", "Finance", "Healthcare", "Education", "Consulting", 
                         "Manufacturing", "Retail", "Media", "Legal", "Real Estate"]
            companies = [f"{ind}Corp" for ind in industries]
        elif network_type == "concentrated":
            industries = ["Technology"] * 8 + ["Finance"] * 2
            companies = ["TechCorp"] * 7 + ["TechStart"] * 2 + ["FinanceCorp"]
        else:  # balanced
            industries = ["Technology"] * 4 + ["Finance"] * 2 + ["Healthcare"] * 2 + ["Education", "Consulting"]
            companies = [f"{ind}Inc" for ind in industries]
        
        # Create persons with varying profiles
        for i in range(1, num_persons):
            industry = industries[i % len(industries)]
            company = companies[i % len(companies)]
            
            person = Person(
                person_id=person_ids[i],
                user_id=self.test_user_id,
                first_name=f"Person",
                last_name=f"{i}",
                professional_info={
                    "company": company,
                    "title": f"Title{i % 5}",
                    "industry": industry
                },
                personal_details={
                    "tags": [industry.lower(), f"tag{i % 3}"]
                },
                is_user=False
            )
            persons.append(person)
        
        self.db.add_all(persons)
        
        # Create relationships with varying strengths
        relationships = []
        archetypes = ["colleague", "friend", "mentor", "client", "acquaintance"]
        
        # Ensure user has connections to most persons
        connection_ratio = 0.8 if num_persons < 50 else 0.6 if num_persons < 100 else 0.4
        num_connections = int(num_persons * connection_ratio)
        
        for i in range(1, min(num_connections, num_persons)):
            # Vary relationship strength based on network type
            if network_type == "strong":
                base_score = 75
            elif network_type == "weak":
                base_score = 40
            else:
                base_score = 55
            
            score = base_score + (i % 30)  # Add some variation
            
            rel = Knows(
                from_person_id=person_ids[0],
                to_person_id=person_ids[i],
                user_id=self.test_user_id,
                archetype=archetypes[i % len(archetypes)],
                relationship_depth={
                    "overall_score": min(100, score),
                    "dimensions": {
                        "emotional_intimacy": score - 10,
                        "professional_collaboration": score + 5,
                        "trust_level": score,
                        "communication_frequency": score - 5,
                        "shared_experience_value": score,
                        "reciprocity_balance": score + 3
                    },
                    "history": [
                        {
                            "timestamp": datetime.utcnow().isoformat() + "Z",
                            "event": "Relationship created",
                            "score_change": "+0"
                        }
                    ]
                }
            )
            relationships.append(rel)
        
        self.db.add_all(relationships)
        
        # Create interactions based on activity level
        interactions = []
        now = datetime.utcnow()
        
        if activity_level == "high":
            interaction_ratio = 0.6
            max_interactions_per_person = 8
        elif activity_level == "low":
            interaction_ratio = 0.2
            max_interactions_per_person = 2
        else:  # medium
            interaction_ratio = 0.4
            max_interactions_per_person = 4
        
        active_relationships = int(len(relationships) * interaction_ratio)
        
        for i in range(active_relationships):
            person_id = person_ids[1 + (i % (num_persons - 1))]
            num_interactions = 1 + (i % max_interactions_per_person)
            
            for j in range(num_interactions):
                days_ago = 1 + (j * 15) + (i % 30)  # Spread over 90 days
                interaction = Interaction(
                    user_id=self.test_user_id,
                    person_id=person_id,
                    interaction_type=["meeting", "email", "call", "event"][j % 4],
                    interaction_date=now - timedelta(days=days_ago),
                    interaction_context={
                        "notes": f"Test interaction {j+1}",
                        "duration": 30 + (j * 15)
                    }
                )
                interactions.append(interaction)
        
        self.db.add_all(interactions)
        self.db.commit()
        
        print(f"Created {len(persons)} persons, {len(relationships)} relationships, {len(interactions)} interactions")
        return person_ids
    
    def test_health_diagnosis_performance(
        self, 
        network_size: int, 
        network_type: str = "balanced",
        activity_level: str = "medium"
    ) -> Dict[str, Any]:
        """Test health diagnosis performance"""
        print(f"\nTesting health diagnosis: {network_size} nodes, {network_type}, {activity_level} activity")
        
        # Create test network
        person_ids = self.create_test_network(network_size, network_type, activity_level)
        
        # Test core health service performance
        start_time = time.time()
        
        try:
            # Note: In actual implementation, this would be awaited
            # diagnosis = await self.health_service.diagnose_network_health(self.test_user_id)
            
            # For now, test individual components
            persons = self.db.query(Person).filter(Person.user_id == self.test_user_id).all()
            relationships = self.db.query(Knows).filter(Knows.user_id == self.test_user_id).all()
            
            # Test graph building
            graph_start = time.time()
            graph = self.health_service._build_network_graph(persons, relationships)
            graph_time = time.time() - graph_start
            
            # Test diversity calculation
            diversity_start = time.time()
            diversity_score = self.health_service._calculate_shannon_diversity(
                [p.professional_info.get("industry", "unknown") for p in persons if p.professional_info]
            )
            diversity_time = time.time() - diversity_start
            
            # Test centrality calculation
            centrality_start = time.time()
            centrality = self.health_service._calculate_user_centrality(graph, self.test_user_id)
            centrality_time = time.time() - centrality_start
            
            # Test structural analysis
            structural_start = time.time()
            structural_holes = self.health_service._count_structural_holes(graph, self.test_user_id)
            influence_score = self.health_service._calculate_influence_score(graph, self.test_user_id, relationships)
            structural_time = time.time() - structural_start
            
            total_time = time.time() - start_time
            
            # Calculate metrics
            node_count = graph.number_of_nodes()
            edge_count = graph.number_of_edges()
            
            result = {
                "network_size": network_size,
                "network_type": network_type,
                "activity_level": activity_level,
                "performance": {
                    "total_time": total_time,
                    "graph_build_time": graph_time,
                    "diversity_calc_time": diversity_time,
                    "centrality_calc_time": centrality_time,
                    "structural_analysis_time": structural_time
                },
                "network_stats": {
                    "nodes": node_count,
                    "edges": edge_count,
                    "density": nx.density(graph) if node_count > 1 else 0,
                    "diversity_score": diversity_score,
                    "centrality_score": centrality,
                    "structural_holes": structural_holes,
                    "influence_score": influence_score
                },
                "success": True,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            print(f"  Total time: {total_time:.3f}s")
            print(f"  Graph build: {graph_time:.3f}s")
            print(f"  Diversity calc: {diversity_time:.3f}s")
            print(f"  Centrality calc: {centrality_time:.3f}s")
            print(f"  Structural analysis: {structural_time:.3f}s")
            print(f"  Network density: {result['network_stats']['density']:.3f}")
            print(f"  Diversity score: {diversity_score:.3f}")
            
            return result
            
        except Exception as e:
            print(f"  Error: {str(e)}")
            return {
                "network_size": network_size,
                "network_type": network_type,
                "activity_level": activity_level,
                "error": str(e),
                "success": False,
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def test_ai_engine_integration(self, network_size: int = 30) -> Dict[str, Any]:
        """Test AI engine integration performance"""
        print(f"\nTesting AI Engine health diagnosis integration with {network_size} nodes")
        
        person_ids = self.create_test_network(network_size, "balanced", "medium")
        
        start_time = time.time()
        
        try:
            # Test personalized next steps generation
            mock_diagnosis = {
                "overall_health_score": 65,
                "network_size": network_size,
                "recommendations": [
                    {
                        "category": "Network Growth",
                        "priority": "high",
                        "title": "Expand your network",
                        "description": "Build more connections",
                        "actions": ["Attend events", "Join groups"]
                    }
                ],
                "metrics": {
                    "network_metrics": {
                        "dormant_relationships": 8,
                        "active_relationships": 15
                    },
                    "diversity_metrics": {
                        "overall_diversity": 0.6
                    }
                }
            }
            
            next_steps_start = time.time()
            next_steps = self.ai_engine._generate_personalized_next_steps(mock_diagnosis)
            next_steps_time = time.time() - next_steps_start
            
            # Test peer comparison
            peer_start = time.time()
            # peer_comparison = await self.ai_engine._generate_peer_comparison(self.test_user_id, mock_diagnosis)
            # For now, just test the method exists
            peer_method_exists = hasattr(self.ai_engine, '_generate_peer_comparison')
            peer_time = time.time() - peer_start
            
            total_time = time.time() - start_time
            
            result = {
                "network_size": network_size,
                "ai_integration": {
                    "total_time": total_time,
                    "next_steps_time": next_steps_time,
                    "peer_comparison_time": peer_time,
                    "next_steps_generated": len(next_steps),
                    "peer_method_available": peer_method_exists
                },
                "success": True,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            print(f"  Total time: {total_time:.3f}s")
            print(f"  Next steps generation: {next_steps_time:.3f}s")
            print(f"  Generated {len(next_steps)} next steps")
            
            return result
            
        except Exception as e:
            print(f"  Error: {str(e)}")
            return {
                "network_size": network_size,
                "error": str(e),
                "success": False,
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def run_comprehensive_tests(self):
        """Run comprehensive network health performance tests"""
        print("=" * 60)
        print("NETWORK HEALTH DIAGNOSIS PERFORMANCE TESTING")
        print("=" * 60)
        
        try:
            # Test different network sizes
            network_sizes = [10, 25, 50, 100]
            network_types = ["diverse", "concentrated", "balanced"]
            activity_levels = ["low", "medium", "high"]
            
            # Basic performance tests
            for size in network_sizes:
                for net_type in network_types:
                    for activity in activity_levels:
                        result = self.test_health_diagnosis_performance(size, net_type, activity)
                        self.test_results.append(result)
                        
                        # Clean up between tests
                        self.db.query(Interaction).filter(Interaction.user_id == self.test_user_id).delete()
                        self.db.query(Knows).filter(Knows.user_id == self.test_user_id).delete()
                        self.db.query(Person).filter(Person.user_id == self.test_user_id).delete()
                        self.db.commit()
            
            # AI Integration tests
            for size in [20, 50]:
                ai_result = self.test_ai_engine_integration(size)
                self.test_results.append({"test_type": "ai_integration", **ai_result})
                
                # Clean up
                self.db.query(Interaction).filter(Interaction.user_id == self.test_user_id).delete()
                self.db.query(Knows).filter(Knows.user_id == self.test_user_id).delete()
                self.db.query(Person).filter(Person.user_id == self.test_user_id).delete()
                self.db.commit()
            
            # Generate performance report
            self.generate_performance_report()
            
        except Exception as e:
            print(f"Error during testing: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.cleanup()
    
    def generate_performance_report(self):
        """Generate comprehensive performance report"""
        print("\n" + "=" * 60)
        print("NETWORK HEALTH PERFORMANCE REPORT")
        print("=" * 60)
        
        # Filter successful results
        successful_results = [r for r in self.test_results if r.get("success")]
        
        if not successful_results:
            print("No successful test results to analyze")
            return
        
        # Network size performance analysis
        print("\nPerformance by Network Size:")
        print("-" * 30)
        size_performance = {}
        
        for result in successful_results:
            if "network_size" in result and "performance" in result:
                size = result["network_size"]
                total_time = result["performance"]["total_time"]
                
                if size not in size_performance:
                    size_performance[size] = []
                size_performance[size].append(total_time)
        
        for size in sorted(size_performance.keys()):
            times = size_performance[size]
            avg_time = sum(times) / len(times)
            print(f"  {size} nodes: {avg_time:.3f}s average ({len(times)} tests)")
        
        # Network type analysis
        print("\nPerformance by Network Type:")
        print("-" * 30)
        type_performance = {}
        
        for result in successful_results:
            if "network_type" in result and "performance" in result:
                net_type = result["network_type"]
                total_time = result["performance"]["total_time"]
                
                if net_type not in type_performance:
                    type_performance[net_type] = []
                type_performance[net_type].append(total_time)
        
        for net_type in type_performance:
            times = type_performance[net_type]
            avg_time = sum(times) / len(times)
            print(f"  {net_type}: {avg_time:.3f}s average ({len(times)} tests)")
        
        # AI Integration analysis
        ai_results = [r for r in self.test_results if r.get("test_type") == "ai_integration"]
        if ai_results:
            print("\nAI Integration Performance:")
            print("-" * 30)
            for result in ai_results:
                if "ai_integration" in result:
                    ai_perf = result["ai_integration"]
                    print(f"  Network size {result['network_size']}:")
                    print(f"    Total time: {ai_perf['total_time']:.3f}s")
                    print(f"    Next steps: {ai_perf['next_steps_generated']} generated in {ai_perf['next_steps_time']:.3f}s")
        
        # Performance bottleneck analysis
        print("\nPerformance Bottleneck Analysis:")
        print("-" * 30)
        
        graph_times = []
        diversity_times = []
        centrality_times = []
        structural_times = []
        
        for result in successful_results:
            if "performance" in result:
                perf = result["performance"]
                graph_times.append(perf.get("graph_build_time", 0))
                diversity_times.append(perf.get("diversity_calc_time", 0))
                centrality_times.append(perf.get("centrality_calc_time", 0))
                structural_times.append(perf.get("structural_analysis_time", 0))
        
        if graph_times:
            print(f"  Graph building: {sum(graph_times)/len(graph_times):.3f}s average")
            print(f"  Diversity calculation: {sum(diversity_times)/len(diversity_times):.3f}s average")
            print(f"  Centrality calculation: {sum(centrality_times)/len(centrality_times):.3f}s average")
            print(f"  Structural analysis: {sum(structural_times)/len(structural_times):.3f}s average")
        
        # Save detailed results
        report_file = f"network_health_performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"\nDetailed results saved to: {report_file}")


def main():
    """Main function to run network health performance tests"""
    tester = NetworkHealthPerformanceTester()
    
    try:
        tester.run_comprehensive_tests()
    except KeyboardInterrupt:
        print("\nTesting interrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        tester.cleanup()


if __name__ == "__main__":
    main()