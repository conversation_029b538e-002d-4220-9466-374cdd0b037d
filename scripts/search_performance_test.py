#!/usr/bin/env python3
"""
Search Performance Test and Benchmark Script
Tests comprehensive search functionality and measures performance metrics
"""

import sys
import os
import time
import statistics
import json
from typing import List, Dict, Any

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.core.database import SessionLocal
from app.models.user import User
from app.models.person import Person
from app.api.v1.endpoints.search import search_persons


class SearchPerformanceTester:
    """Comprehensive search performance testing and benchmarking"""
    
    def __init__(self):
        self.db = SessionLocal()
        self.test_user = None
        self.test_persons = []
        
    def setup_test_environment(self):
        """Set up test user and sample data"""
        print("Setting up test environment...")
        
        # Get or create test user
        self.test_user = self.db.query(User).first()
        if not self.test_user:
            print("No user found in database. Please create a user first.")
            return False
            
        print(f"Using test user: {self.test_user.email}")
        
        # Check existing persons
        existing_persons = self.db.query(Person).filter(
            Person.user_id == self.test_user.user_id,
            Person.is_user == False
        ).all()
        
        print(f"Found {len(existing_persons)} existing persons")
        return True
    
    def test_basic_search_functionality(self):
        """Test basic search functionality"""
        print("\n=== Basic Search Functionality Tests ===")
        
        test_cases = [
            {"query": "John", "description": "Name search"},
            {"query": "Tech", "description": "Company search"},
            {"query": "Jo", "description": "Partial match"},
            {"query": "Smith", "description": "Last name search"},
        ]
        
        results = {}
        for test_case in test_cases:
            query = test_case["query"]
            description = test_case["description"]
            
            try:
                start_time = time.time()
                result = search_persons(
                    q=query,
                    limit=10,
                    offset=0,
                    similarity_threshold=0.2,
                    company=None,
                    tags=None,
                    relationship_type=None,
                    interaction_since=None,
                    include_metadata=True,
                    current_user=self.test_user,
                    db=self.db
                )
                end_time = time.time()
                
                response_time = end_time - start_time
                result_count = len(result.get("results", []))
                total_available = result.get("pagination", {}).get("total", 0)
                
                results[query] = {
                    "description": description,
                    "response_time": response_time,
                    "result_count": result_count,
                    "total_available": total_available,
                    "success": True
                }
                
                print(f"✅ {description} ('{query}'): {result_count} results in {response_time:.3f}s")
                
                # Show sample results
                for i, person in enumerate(result.get("results", [])[:3]):
                    relevance = person.get("relevance_score", 0)
                    match_fields = person.get("match_fields", [])
                    print(f"   {i+1}. {person['full_name']} (relevance: {relevance:.3f}, fields: {match_fields})")
                
            except Exception as e:
                results[query] = {
                    "description": description,
                    "error": str(e),
                    "success": False
                }
                print(f"❌ {description} ('{query}'): Error - {e}")
        
        return results
    
    def test_pagination_functionality(self):
        """Test pagination functionality"""
        print("\n=== Pagination Functionality Tests ===")
        
        pagination_tests = [
            {"limit": 2, "offset": 0, "description": "First page, 2 results"},
            {"limit": 5, "offset": 0, "description": "First page, 5 results"},
            {"limit": 2, "offset": 2, "description": "Second page, 2 results"},
            {"limit": 10, "offset": 0, "description": "Large page size"},
        ]
        
        results = {}
        for test in pagination_tests:
            try:
                start_time = time.time()
                result = search_persons(
                    q="a",  # Broad search
                    limit=test["limit"],
                    offset=test["offset"],
                    similarity_threshold=0.1,
                    company=None,
                    tags=None,
                    relationship_type=None,
                    interaction_since=None,
                    include_metadata=True,
                    current_user=self.test_user,
                    db=self.db
                )
                end_time = time.time()
                
                response_time = end_time - start_time
                pagination = result.get("pagination", {})
                actual_results = len(result.get("results", []))
                
                results[f"{test['limit']}-{test['offset']}"] = {
                    "description": test["description"],
                    "response_time": response_time,
                    "requested_limit": test["limit"],
                    "requested_offset": test["offset"],
                    "actual_results": actual_results,
                    "pagination_metadata": pagination,
                    "success": True
                }
                
                print(f"✅ {test['description']}: {actual_results} results, page {pagination.get('current_page', '?')} of {pagination.get('total_pages', '?')}")
                print(f"   Response time: {response_time:.3f}s")
                print(f"   Total available: {pagination.get('total', 0)}")
                
            except Exception as e:
                results[f"{test['limit']}-{test['offset']}"] = {
                    "description": test["description"],
                    "error": str(e),
                    "success": False
                }
                print(f"❌ {test['description']}: Error - {e}")
        
        return results
    
    def test_filtering_functionality(self):
        """Test filtering functionality"""
        print("\n=== Filtering Functionality Tests ===")
        
        filter_tests = [
            {
                "params": {"company": "Tech"},
                "description": "Company filter"
            },
            {
                "params": {"relationship_type": "friend"},
                "description": "Relationship type filter"
            },
            {
                "params": {"tags": "tech"},
                "description": "Tag filter"
            },
            {
                "params": {"company": "Design", "tags": "startup"},
                "description": "Combined filters"
            }
        ]
        
        results = {}
        for test in filter_tests:
            try:
                start_time = time.time()
                result = search_persons(
                    q="a",  # Broad search to test filtering
                    limit=10,
                    offset=0,
                    similarity_threshold=0.1,
                    company=test["params"].get("company"),
                    tags=test["params"].get("tags"),
                    relationship_type=test["params"].get("relationship_type"),
                    interaction_since=test["params"].get("interaction_since"),
                    include_metadata=True,
                    current_user=self.test_user,
                    db=self.db
                )
                end_time = time.time()
                
                response_time = end_time - start_time
                result_count = len(result.get("results", []))
                filters_applied = result.get("metadata", {}).get("filters_applied", {})
                
                results[test["description"]] = {
                    "description": test["description"],
                    "response_time": response_time,
                    "result_count": result_count,
                    "filters_applied": filters_applied,
                    "params": test["params"],
                    "success": True
                }
                
                print(f"✅ {test['description']}: {result_count} results in {response_time:.3f}s")
                print(f"   Applied filters: {filters_applied}")
                
            except Exception as e:
                results[test["description"]] = {
                    "description": test["description"],
                    "error": str(e),
                    "success": False
                }
                print(f"❌ {test['description']}: Error - {e}")
        
        return results
    
    def test_relevance_scoring(self):
        """Test relevance scoring and ranking"""
        print("\n=== Relevance Scoring Tests ===")
        
        try:
            result = search_persons(
                q="John",
                limit=10,
                offset=0,
                similarity_threshold=0.2,
                company=None,
                tags=None,
                relationship_type=None,
                interaction_since=None,
                include_metadata=True,
                current_user=self.test_user,
                db=self.db
            )
            
            results = result.get("results", [])
            
            if len(results) > 1:
                print(f"✅ Found {len(results)} results for relevance testing")
                
                # Check if results are properly ordered
                relevance_scores = []
                for i, person in enumerate(results):
                    relevance_score = person.get("relevance_score", 0)
                    match_score = person.get("match_score", 0)
                    boost_details = person.get("boost_details", {})
                    match_fields = person.get("match_fields", [])
                    
                    relevance_scores.append(relevance_score)
                    
                    print(f"   {i+1}. {person['full_name']}")
                    print(f"      Relevance: {relevance_score:.3f}, Match: {match_score:.3f}")
                    print(f"      Boosts: {boost_details}")
                    print(f"      Fields: {match_fields}")
                
                # Verify ordering
                is_properly_ordered = all(
                    relevance_scores[i] >= relevance_scores[i+1] 
                    for i in range(len(relevance_scores)-1)
                )
                
                if is_properly_ordered:
                    print("✅ Results are properly ordered by relevance score")
                else:
                    print("❌ Results are NOT properly ordered by relevance score")
                
                return {
                    "success": True,
                    "result_count": len(results),
                    "properly_ordered": is_properly_ordered,
                    "relevance_scores": relevance_scores
                }
            else:
                print("⚠️  Not enough results to test relevance ordering")
                return {"success": True, "result_count": len(results)}
                
        except Exception as e:
            print(f"❌ Relevance scoring test failed: {e}")
            return {"success": False, "error": str(e)}
    
    def test_search_performance(self, iterations: int = 5):
        """Test search performance with multiple iterations"""
        print(f"\n=== Search Performance Tests ({iterations} iterations) ===")
        
        performance_queries = [
            {"query": "John", "description": "Common name search"},
            {"query": "Tech", "description": "Company search"},
            {"query": "Jo", "description": "Partial match"},
            {"query": "a", "description": "Very broad search"},
        ]
        
        results = {}
        for test in performance_queries:
            query = test["query"]
            description = test["description"]
            response_times = []
            
            print(f"\nTesting {description} ('{query}')...")
            
            for i in range(iterations):
                try:
                    start_time = time.time()
                    result = search_persons(
                        q=query,
                        limit=10,
                        offset=0,
                        similarity_threshold=0.2,
                        company=None,
                        tags=None,
                        relationship_type=None,
                        interaction_since=None,
                        include_metadata=True,
                        current_user=self.test_user,
                        db=self.db
                    )
                    end_time = time.time()
                    
                    response_time = end_time - start_time
                    response_times.append(response_time)
                    
                    print(f"  Iteration {i+1}: {response_time:.3f}s")
                    
                except Exception as e:
                    print(f"  Iteration {i+1}: Error - {e}")
                    continue
            
            if response_times:
                perf_stats = {
                    "description": description,
                    "query": query,
                    "iterations": len(response_times),
                    "avg_response_time": statistics.mean(response_times),
                    "min_response_time": min(response_times),
                    "max_response_time": max(response_times),
                    "median_response_time": statistics.median(response_times),
                    "std_deviation": statistics.stdev(response_times) if len(response_times) > 1 else 0
                }
                
                results[query] = perf_stats
                
                print(f"✅ {description} performance:")
                print(f"   Average: {perf_stats['avg_response_time']:.3f}s")
                print(f"   Min: {perf_stats['min_response_time']:.3f}s")
                print(f"   Max: {perf_stats['max_response_time']:.3f}s")
                print(f"   Median: {perf_stats['median_response_time']:.3f}s")
                
                # Performance assertions
                if perf_stats['avg_response_time'] > 2.0:
                    print(f"⚠️  Average response time ({perf_stats['avg_response_time']:.3f}s) exceeds 2s threshold")
                if perf_stats['max_response_time'] > 5.0:
                    print(f"⚠️  Maximum response time ({perf_stats['max_response_time']:.3f}s) exceeds 5s threshold")
            else:
                print(f"❌ No successful iterations for {description}")
        
        return results
    
    def generate_performance_report(self, results: Dict[str, Any]):
        """Generate a comprehensive performance report"""
        print("\n" + "="*60)
        print("SEARCH PERFORMANCE REPORT")
        print("="*60)
        
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "test_user": self.test_user.email if self.test_user else "Unknown",
            "results": results
        }
        
        # Summary statistics
        all_response_times = []
        for category, tests in results.items():
            if isinstance(tests, dict):
                for test_name, test_result in tests.items():
                    if isinstance(test_result, dict) and test_result.get("success") and "response_time" in test_result:
                        all_response_times.append(test_result["response_time"])
        
        if all_response_times:
            print(f"\nOverall Performance Summary:")
            print(f"  Total tests conducted: {len(all_response_times)}")
            print(f"  Average response time: {statistics.mean(all_response_times):.3f}s")
            print(f"  Fastest response: {min(all_response_times):.3f}s")
            print(f"  Slowest response: {max(all_response_times):.3f}s")
            print(f"  Median response: {statistics.median(all_response_times):.3f}s")
            
            # Performance grade
            avg_time = statistics.mean(all_response_times)
            if avg_time < 0.5:
                grade = "A (Excellent)"
            elif avg_time < 1.0:
                grade = "B (Good)"
            elif avg_time < 2.0:
                grade = "C (Acceptable)"
            elif avg_time < 5.0:
                grade = "D (Needs Improvement)"
            else:
                grade = "F (Poor)"
            
            print(f"  Performance Grade: {grade}")
        
        # Save detailed report
        report_file = f"search_performance_report_{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\nDetailed report saved to: {report_file}")
        return report
    
    def run_comprehensive_tests(self):
        """Run all comprehensive search tests"""
        print("🚀 Starting Comprehensive Search Testing Suite")
        print("=" * 60)
        
        if not self.setup_test_environment():
            print("❌ Failed to set up test environment")
            return
        
        all_results = {}
        
        # Run all test categories
        all_results["basic_functionality"] = self.test_basic_search_functionality()
        all_results["pagination"] = self.test_pagination_functionality()
        all_results["filtering"] = self.test_filtering_functionality()
        all_results["relevance_scoring"] = self.test_relevance_scoring()
        all_results["performance"] = self.test_search_performance(iterations=3)
        
        # Generate comprehensive report
        self.generate_performance_report(all_results)
        
        print("\n🎉 Comprehensive search testing completed!")
        
    def cleanup(self):
        """Clean up resources"""
        if self.db:
            self.db.close()


def main():
    """Main function to run search performance tests"""
    tester = SearchPerformanceTester()
    
    try:
        tester.run_comprehensive_tests()
    finally:
        tester.cleanup()


if __name__ == "__main__":
    main()