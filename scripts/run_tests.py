#!/usr/bin/env python3
"""
Comprehensive test runner script for Nexus Backend
Provides various test execution modes and reporting options
"""

import os
import sys
import subprocess
import argparse
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional


class TestRunner:
    """Test runner with comprehensive reporting and analysis"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.test_dir = project_root / "app" / "tests"
        self.reports_dir = project_root / "test-reports"
        self.reports_dir.mkdir(exist_ok=True)
        
    def run_command(self, cmd: List[str], capture_output: bool = True) -> subprocess.CompletedProcess:
        """Run a command and return the result"""
        print(f"Running: {' '.join(cmd)}")
        return subprocess.run(
            cmd,
            cwd=self.project_root,
            capture_output=capture_output,
            text=True
        )
    
    def run_unit_tests(self, verbose: bool = False) -> Dict:
        """Run unit tests"""
        print("\n🧪 Running Unit Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "-m", "unit",
            "--cov=app",
            "--cov-report=html:test-reports/unit-coverage",
            "--cov-report=xml:test-reports/unit-coverage.xml",
            "--junitxml=test-reports/unit-results.xml"
        ]
        
        if verbose:
            cmd.append("-v")
        
        result = self.run_command(cmd, capture_output=False)
        
        return {
            "type": "unit",
            "success": result.returncode == 0,
            "returncode": result.returncode
        }
    
    def run_integration_tests(self, verbose: bool = False) -> Dict:
        """Run integration tests"""
        print("\n🔗 Running Integration Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "-m", "integration",
            "--cov=app",
            "--cov-report=html:test-reports/integration-coverage",
            "--cov-report=xml:test-reports/integration-coverage.xml",
            "--junitxml=test-reports/integration-results.xml"
        ]
        
        if verbose:
            cmd.append("-v")
        
        result = self.run_command(cmd, capture_output=False)
        
        return {
            "type": "integration",
            "success": result.returncode == 0,
            "returncode": result.returncode
        }
    
    def run_performance_tests(self, verbose: bool = False) -> Dict:
        """Run performance tests"""
        print("\n⚡ Running Performance Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "-m", "performance",
            "--junitxml=test-reports/performance-results.xml"
        ]
        
        if verbose:
            cmd.append("-v")
        
        result = self.run_command(cmd, capture_output=False)
        
        return {
            "type": "performance",
            "success": result.returncode == 0,
            "returncode": result.returncode
        }
    
    def run_all_tests(self, verbose: bool = False) -> Dict:
        """Run all tests with comprehensive coverage"""
        print("\n🚀 Running All Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "--cov=app",
            "--cov-report=html:test-reports/full-coverage",
            "--cov-report=xml:test-reports/full-coverage.xml",
            "--cov-report=term-missing",
            "--junitxml=test-reports/full-results.xml",
            "--cov-fail-under=90"
        ]
        
        if verbose:
            cmd.append("-v")
        
        result = self.run_command(cmd, capture_output=False)
        
        return {
            "type": "all",
            "success": result.returncode == 0,
            "returncode": result.returncode
        }
    
    def run_specific_test_file(self, test_file: str, verbose: bool = False) -> Dict:
        """Run tests from a specific file"""
        print(f"\n📄 Running tests from {test_file}...")
        
        cmd = [
            "python", "-m", "pytest",
            f"app/tests/{test_file}",
            "--cov=app",
            "--cov-report=term-missing"
        ]
        
        if verbose:
            cmd.append("-v")
        
        result = self.run_command(cmd, capture_output=False)
        
        return {
            "type": "specific",
            "file": test_file,
            "success": result.returncode == 0,
            "returncode": result.returncode
        }
    
    def run_tests_by_marker(self, marker: str, verbose: bool = False) -> Dict:
        """Run tests with specific marker"""
        print(f"\n🏷️  Running tests with marker: {marker}...")
        
        cmd = [
            "python", "-m", "pytest",
            "-m", marker,
            "--cov=app",
            "--cov-report=term-missing",
            f"--junitxml=test-reports/{marker}-results.xml"
        ]
        
        if verbose:
            cmd.append("-v")
        
        result = self.run_command(cmd, capture_output=False)
        
        return {
            "type": "marker",
            "marker": marker,
            "success": result.returncode == 0,
            "returncode": result.returncode
        }
    
    def generate_test_report(self, results: List[Dict]) -> None:
        """Generate comprehensive test report"""
        print("\n📊 Generating Test Report...")
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "project": "Nexus Backend",
            "test_results": results,
            "summary": {
                "total_runs": len(results),
                "successful_runs": sum(1 for r in results if r["success"]),
                "failed_runs": sum(1 for r in results if not r["success"])
            }
        }
        
        # Save JSON report
        report_file = self.reports_dir / f"test-report-{datetime.now().strftime('%Y%m%d-%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📄 Test report saved to: {report_file}")
        
        # Print summary
        print("\n📈 Test Summary:")
        print(f"  Total test runs: {report['summary']['total_runs']}")
        print(f"  Successful runs: {report['summary']['successful_runs']}")
        print(f"  Failed runs: {report['summary']['failed_runs']}")
        
        for result in results:
            status = "✅" if result["success"] else "❌"
            print(f"  {status} {result['type']}: {'PASSED' if result['success'] else 'FAILED'}")
    
    def check_coverage_threshold(self, threshold: float = 90.0) -> bool:
        """Check if coverage meets threshold"""
        coverage_file = self.reports_dir / "full-coverage.xml"
        
        if not coverage_file.exists():
            print(f"⚠️  Coverage file not found: {coverage_file}")
            return False
        
        try:
            import xml.etree.ElementTree as ET
            tree = ET.parse(coverage_file)
            root = tree.getroot()
            
            coverage_elem = root.find('.//coverage')
            if coverage_elem is not None:
                line_rate = float(coverage_elem.get('line-rate', 0)) * 100
                branch_rate = float(coverage_elem.get('branch-rate', 0)) * 100
                
                print(f"\n📊 Coverage Analysis:")
                print(f"  Line Coverage: {line_rate:.2f}%")
                print(f"  Branch Coverage: {branch_rate:.2f}%")
                print(f"  Threshold: {threshold}%")
                
                meets_threshold = line_rate >= threshold
                status = "✅" if meets_threshold else "❌"
                print(f"  {status} Coverage {'meets' if meets_threshold else 'below'} threshold")
                
                return meets_threshold
        except Exception as e:
            print(f"⚠️  Error parsing coverage file: {e}")
            return False
        
        return False
    
    def lint_code(self) -> Dict:
        """Run code linting"""
        print("\n🔍 Running Code Linting...")
        
        # Run black for formatting check
        black_result = self.run_command(["python", "-m", "black", "--check", "app/"])
        
        # Run isort for import sorting check
        isort_result = self.run_command(["python", "-m", "isort", "--check-only", "app/"])
        
        # Run flake8 for style check
        flake8_result = self.run_command(["python", "-m", "flake8", "app/"])
        
        success = all([
            black_result.returncode == 0,
            isort_result.returncode == 0,
            flake8_result.returncode == 0
        ])
        
        return {
            "type": "lint",
            "success": success,
            "black": black_result.returncode == 0,
            "isort": isort_result.returncode == 0,
            "flake8": flake8_result.returncode == 0
        }


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Nexus Backend Test Runner")
    parser.add_argument("--type", choices=["unit", "integration", "performance", "all", "lint"], 
                       default="all", help="Type of tests to run")
    parser.add_argument("--file", help="Specific test file to run")
    parser.add_argument("--marker", help="Run tests with specific marker")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--coverage-threshold", type=float, default=90.0, 
                       help="Coverage threshold percentage")
    
    args = parser.parse_args()
    
    project_root = Path(__file__).parent.parent
    runner = TestRunner(project_root)
    
    results = []
    
    try:
        if args.file:
            result = runner.run_specific_test_file(args.file, args.verbose)
            results.append(result)
        elif args.marker:
            result = runner.run_tests_by_marker(args.marker, args.verbose)
            results.append(result)
        elif args.type == "unit":
            result = runner.run_unit_tests(args.verbose)
            results.append(result)
        elif args.type == "integration":
            result = runner.run_integration_tests(args.verbose)
            results.append(result)
        elif args.type == "performance":
            result = runner.run_performance_tests(args.verbose)
            results.append(result)
        elif args.type == "lint":
            result = runner.lint_code()
            results.append(result)
        elif args.type == "all":
            # Run linting first
            lint_result = runner.lint_code()
            results.append(lint_result)
            
            # Run all tests
            test_result = runner.run_all_tests(args.verbose)
            results.append(test_result)
            
            # Check coverage
            runner.check_coverage_threshold(args.coverage_threshold)
        
        # Generate report
        runner.generate_test_report(results)
        
        # Exit with error if any tests failed
        if any(not result["success"] for result in results):
            sys.exit(1)
        
        print("\n🎉 All tests passed successfully!")
        
    except KeyboardInterrupt:
        print("\n⚠️  Test execution interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error running tests: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
