#!/usr/bin/env python3
"""
Test coverage analysis and reporting tool for Nexus Backend
Provides detailed coverage analysis, trends, and recommendations
"""

import os
import sys
import json
import xml.etree.ElementTree as ET
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import argparse


class CoverageAnalyzer:
    """Analyze test coverage and generate detailed reports"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.coverage_dir = project_root / "htmlcov"
        self.coverage_xml = project_root / "coverage.xml"
        self.reports_dir = project_root / "coverage-reports"
        self.reports_dir.mkdir(exist_ok=True)
    
    def parse_coverage_xml(self) -> Optional[Dict]:
        """Parse coverage XML file and extract metrics"""
        if not self.coverage_xml.exists():
            print(f"❌ Coverage XML file not found: {self.coverage_xml}")
            return None
        
        try:
            tree = ET.parse(self.coverage_xml)
            root = tree.getroot()
            
            # Extract overall coverage
            coverage_elem = root.find('.//coverage')
            if coverage_elem is None:
                print("❌ Invalid coverage XML format")
                return None
            
            overall_coverage = {
                'line_rate': float(coverage_elem.get('line-rate', 0)) * 100,
                'branch_rate': float(coverage_elem.get('branch-rate', 0)) * 100,
                'lines_covered': int(coverage_elem.get('lines-covered', 0)),
                'lines_valid': int(coverage_elem.get('lines-valid', 0)),
                'branches_covered': int(coverage_elem.get('branches-covered', 0)),
                'branches_valid': int(coverage_elem.get('branches-valid', 0)),
                'timestamp': coverage_elem.get('timestamp', datetime.now().isoformat())
            }
            
            # Extract package-level coverage
            packages = []
            for package in root.findall('.//package'):
                package_data = {
                    'name': package.get('name', ''),
                    'line_rate': float(package.get('line-rate', 0)) * 100,
                    'branch_rate': float(package.get('branch-rate', 0)) * 100,
                    'classes': []
                }
                
                # Extract class-level coverage
                for class_elem in package.findall('.//class'):
                    class_data = {
                        'name': class_elem.get('name', ''),
                        'filename': class_elem.get('filename', ''),
                        'line_rate': float(class_elem.get('line-rate', 0)) * 100,
                        'branch_rate': float(class_elem.get('branch-rate', 0)) * 100,
                        'lines': []
                    }
                    
                    # Extract line-level coverage
                    lines_elem = class_elem.find('lines')
                    if lines_elem is not None:
                        for line in lines_elem.findall('line'):
                            line_data = {
                                'number': int(line.get('number', 0)),
                                'hits': int(line.get('hits', 0)),
                                'branch': line.get('branch') == 'true',
                                'condition_coverage': line.get('condition-coverage', '')
                            }
                            class_data['lines'].append(line_data)
                    
                    package_data['classes'].append(class_data)
                
                packages.append(package_data)
            
            return {
                'overall': overall_coverage,
                'packages': packages
            }
            
        except Exception as e:
            print(f"❌ Error parsing coverage XML: {e}")
            return None
    
    def analyze_coverage_trends(self, current_coverage: Dict) -> Dict:
        """Analyze coverage trends over time"""
        trends_file = self.reports_dir / "coverage_trends.json"
        
        # Load historical data
        historical_data = []
        if trends_file.exists():
            try:
                with open(trends_file, 'r') as f:
                    historical_data = json.load(f)
            except Exception as e:
                print(f"⚠️  Error loading historical coverage data: {e}")
        
        # Add current data
        current_entry = {
            'timestamp': datetime.now().isoformat(),
            'line_coverage': current_coverage['overall']['line_rate'],
            'branch_coverage': current_coverage['overall']['branch_rate'],
            'lines_covered': current_coverage['overall']['lines_covered'],
            'lines_valid': current_coverage['overall']['lines_valid']
        }
        
        historical_data.append(current_entry)
        
        # Keep only last 30 entries
        historical_data = historical_data[-30:]
        
        # Save updated data
        try:
            with open(trends_file, 'w') as f:
                json.dump(historical_data, f, indent=2)
        except Exception as e:
            print(f"⚠️  Error saving coverage trends: {e}")
        
        # Calculate trends
        trends = {
            'entries': len(historical_data),
            'current': current_entry,
            'trend_analysis': {}
        }
        
        if len(historical_data) >= 2:
            previous = historical_data[-2]
            trends['trend_analysis'] = {
                'line_coverage_change': current_entry['line_coverage'] - previous['line_coverage'],
                'branch_coverage_change': current_entry['branch_coverage'] - previous['branch_coverage'],
                'lines_covered_change': current_entry['lines_covered'] - previous['lines_covered']
            }
        
        return trends
    
    def identify_coverage_gaps(self, coverage_data: Dict, threshold: float = 80.0) -> List[Dict]:
        """Identify files/modules with low coverage"""
        gaps = []
        
        for package in coverage_data['packages']:
            for class_data in package['classes']:
                if class_data['line_rate'] < threshold:
                    # Count uncovered lines
                    uncovered_lines = [
                        line['number'] for line in class_data['lines'] 
                        if line['hits'] == 0
                    ]
                    
                    gaps.append({
                        'file': class_data['filename'],
                        'class': class_data['name'],
                        'line_coverage': class_data['line_rate'],
                        'branch_coverage': class_data['branch_rate'],
                        'uncovered_lines': uncovered_lines,
                        'uncovered_count': len(uncovered_lines),
                        'priority': self._calculate_priority(class_data)
                    })
        
        # Sort by priority (lowest coverage first)
        gaps.sort(key=lambda x: x['line_coverage'])
        
        return gaps
    
    def _calculate_priority(self, class_data: Dict) -> str:
        """Calculate priority for coverage improvement"""
        line_rate = class_data['line_rate']
        
        if line_rate < 50:
            return "critical"
        elif line_rate < 70:
            return "high"
        elif line_rate < 80:
            return "medium"
        else:
            return "low"
    
    def generate_recommendations(self, coverage_data: Dict, gaps: List[Dict]) -> List[str]:
        """Generate coverage improvement recommendations"""
        recommendations = []
        
        overall_line_rate = coverage_data['overall']['line_rate']
        overall_branch_rate = coverage_data['overall']['branch_rate']
        
        # Overall coverage recommendations
        if overall_line_rate < 90:
            recommendations.append(
                f"📈 Increase overall line coverage from {overall_line_rate:.1f}% to 90%+"
            )
        
        if overall_branch_rate < 80:
            recommendations.append(
                f"🌿 Improve branch coverage from {overall_branch_rate:.1f}% to 80%+"
            )
        
        # File-specific recommendations
        critical_gaps = [gap for gap in gaps if gap['priority'] == 'critical']
        if critical_gaps:
            recommendations.append(
                f"🚨 Address {len(critical_gaps)} files with critical coverage gaps (<50%)"
            )
        
        high_priority_gaps = [gap for gap in gaps if gap['priority'] == 'high']
        if high_priority_gaps:
            recommendations.append(
                f"⚠️  Improve {len(high_priority_gaps)} files with high priority gaps (<70%)"
            )
        
        # Specific file recommendations
        for gap in gaps[:5]:  # Top 5 worst files
            recommendations.append(
                f"📄 {gap['file']}: {gap['line_coverage']:.1f}% coverage "
                f"({gap['uncovered_count']} uncovered lines)"
            )
        
        return recommendations
    
    def generate_detailed_report(self, coverage_data: Dict, trends: Dict, gaps: List[Dict]) -> Dict:
        """Generate comprehensive coverage report"""
        report = {
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'project': 'Nexus Backend',
                'coverage_tool': 'coverage.py'
            },
            'summary': {
                'overall_line_coverage': coverage_data['overall']['line_rate'],
                'overall_branch_coverage': coverage_data['overall']['branch_rate'],
                'lines_covered': coverage_data['overall']['lines_covered'],
                'lines_total': coverage_data['overall']['lines_valid'],
                'branches_covered': coverage_data['overall']['branches_covered'],
                'branches_total': coverage_data['overall']['branches_valid']
            },
            'trends': trends,
            'coverage_gaps': {
                'total_files_below_threshold': len(gaps),
                'critical_priority': len([g for g in gaps if g['priority'] == 'critical']),
                'high_priority': len([g for g in gaps if g['priority'] == 'high']),
                'medium_priority': len([g for g in gaps if g['priority'] == 'medium']),
                'files': gaps[:10]  # Top 10 worst files
            },
            'recommendations': self.generate_recommendations(coverage_data, gaps),
            'package_breakdown': []
        }
        
        # Package-level breakdown
        for package in coverage_data['packages']:
            package_summary = {
                'name': package['name'],
                'line_coverage': package['line_rate'],
                'branch_coverage': package['branch_rate'],
                'files_count': len(package['classes']),
                'files_below_threshold': len([
                    c for c in package['classes'] 
                    if c['line_rate'] < 80
                ])
            }
            report['package_breakdown'].append(package_summary)
        
        return report
    
    def save_report(self, report: Dict) -> Path:
        """Save coverage report to file"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = self.reports_dir / f"coverage_analysis_{timestamp}.json"
        
        try:
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            print(f"📊 Coverage report saved to: {report_file}")
            return report_file
            
        except Exception as e:
            print(f"❌ Error saving coverage report: {e}")
            return None
    
    def print_summary(self, report: Dict) -> None:
        """Print coverage summary to console"""
        summary = report['summary']
        trends = report['trends']
        gaps = report['coverage_gaps']
        
        print("\n" + "="*60)
        print("📊 COVERAGE ANALYSIS SUMMARY")
        print("="*60)
        
        # Overall metrics
        print(f"\n📈 Overall Coverage:")
        print(f"  Line Coverage:   {summary['overall_line_coverage']:.2f}%")
        print(f"  Branch Coverage: {summary['overall_branch_coverage']:.2f}%")
        print(f"  Lines Covered:   {summary['lines_covered']:,} / {summary['lines_total']:,}")
        print(f"  Branches Covered: {summary['branches_covered']:,} / {summary['branches_total']:,}")
        
        # Trends
        if 'trend_analysis' in trends and trends['trend_analysis']:
            trend_analysis = trends['trend_analysis']
            print(f"\n📊 Trends (vs previous run):")
            
            line_change = trend_analysis['line_coverage_change']
            line_emoji = "📈" if line_change > 0 else "📉" if line_change < 0 else "➡️"
            print(f"  {line_emoji} Line Coverage: {line_change:+.2f}%")
            
            branch_change = trend_analysis['branch_coverage_change']
            branch_emoji = "📈" if branch_change > 0 else "📉" if branch_change < 0 else "➡️"
            print(f"  {branch_emoji} Branch Coverage: {branch_change:+.2f}%")
        
        # Coverage gaps
        print(f"\n🎯 Coverage Gaps:")
        print(f"  Files below 80%: {gaps['total_files_below_threshold']}")
        print(f"  Critical (<50%):  {gaps['critical_priority']}")
        print(f"  High (<70%):      {gaps['high_priority']}")
        print(f"  Medium (<80%):    {gaps['medium_priority']}")
        
        # Top recommendations
        print(f"\n💡 Top Recommendations:")
        for i, rec in enumerate(report['recommendations'][:5], 1):
            print(f"  {i}. {rec}")
        
        # Package breakdown
        print(f"\n📦 Package Breakdown:")
        for pkg in report['package_breakdown'][:5]:
            print(f"  {pkg['name']}: {pkg['line_coverage']:.1f}% "
                  f"({pkg['files_below_threshold']}/{pkg['files_count']} files need improvement)")
        
        print("\n" + "="*60)


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Coverage Analysis Tool")
    parser.add_argument("--threshold", type=float, default=80.0,
                       help="Coverage threshold for gap analysis")
    parser.add_argument("--save-report", action="store_true",
                       help="Save detailed report to file")
    parser.add_argument("--trends-only", action="store_true",
                       help="Only analyze trends")
    
    args = parser.parse_args()
    
    project_root = Path(__file__).parent.parent
    analyzer = CoverageAnalyzer(project_root)
    
    try:
        # Parse coverage data
        coverage_data = analyzer.parse_coverage_xml()
        if not coverage_data:
            print("❌ Failed to parse coverage data")
            sys.exit(1)
        
        # Analyze trends
        trends = analyzer.analyze_coverage_trends(coverage_data)
        
        if args.trends_only:
            print(f"📊 Coverage trends updated ({trends['entries']} entries)")
            return
        
        # Identify gaps
        gaps = analyzer.identify_coverage_gaps(coverage_data, args.threshold)
        
        # Generate report
        report = analyzer.generate_detailed_report(coverage_data, trends, gaps)
        
        # Print summary
        analyzer.print_summary(report)
        
        # Save detailed report
        if args.save_report:
            analyzer.save_report(report)
        
        # Exit with error if coverage is below threshold
        if coverage_data['overall']['line_rate'] < 90:
            print(f"\n❌ Coverage below 90% threshold")
            sys.exit(1)
        
        print(f"\n✅ Coverage analysis completed successfully")
        
    except Exception as e:
        print(f"❌ Error during coverage analysis: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
