#!/usr/bin/env python3
"""
Performance testing script for referral pathfinding algorithms
Tests pathfinding performance with various network sizes and configurations
"""

import sys
import os
import time
import uuid
import json
from typing import List, Dict, Any
from datetime import datetime

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.core.database import SessionLocal
from app.models.person import Person
from app.models.relationship import Knows
from app.services.pathfinding_service import PathfindingService, PathfindingAlgorithm, PathOptimization
from app.services.ai_engine_service import AIEngineService


class PathfindingPerformanceTester:
    """Test pathfinding performance with various network configurations"""
    
    def __init__(self):
        self.db = SessionLocal()
        self.pathfinding_service = PathfindingService(self.db)
        self.ai_engine = AIEngineService(self.db)
        self.test_user_id = uuid.uuid4()
        self.test_results = []
    
    def cleanup(self):
        """Clean up database and close connections"""
        try:
            # Delete test data
            self.db.query(Knows).filter(Knows.user_id == self.test_user_id).delete()
            self.db.query(Person).filter(Person.user_id == self.test_user_id).delete()
            self.db.commit()
        except Exception as e:
            print(f"Cleanup error: {e}")
            self.db.rollback()
        finally:
            self.db.close()
    
    def create_test_network(self, num_persons: int, connectivity_ratio: float = 0.3) -> List[uuid.UUID]:
        """Create a test network with specified size and connectivity"""
        print(f"Creating test network with {num_persons} persons, connectivity ratio: {connectivity_ratio}")
        
        person_ids = [uuid.uuid4() for _ in range(num_persons)]
        
        # Create user person (first person)
        user_person = Person(
            person_id=person_ids[0],
            user_id=self.test_user_id,
            first_name="Test",
            last_name="User",
            is_user=True
        )
        persons = [user_person]
        
        # Create other persons
        for i in range(1, num_persons):
            person = Person(
                person_id=person_ids[i],
                user_id=self.test_user_id,
                first_name=f"Person",
                last_name=f"{i}",
                professional_info={
                    "company": f"Company{i % 10}",  # 10 different companies
                    "title": f"Title{i % 5}"       # 5 different titles
                },
                is_user=False
            )
            persons.append(person)
        
        self.db.add_all(persons)
        
        # Create relationships based on connectivity ratio
        relationships = []
        total_possible_connections = num_persons * (num_persons - 1) // 2
        target_connections = int(total_possible_connections * connectivity_ratio)
        
        # Ensure user has some connections
        user_connections = min(5, num_persons - 1)
        for i in range(1, user_connections + 1):
            rel = self._create_relationship(person_ids[0], person_ids[i], 70 + (i * 5))
            relationships.append(rel)
        
        # Create random connections between other persons
        import random
        connections_created = user_connections
        attempts = 0
        max_attempts = target_connections * 3
        
        while connections_created < target_connections and attempts < max_attempts:
            from_idx = random.randint(0, num_persons - 1)
            to_idx = random.randint(0, num_persons - 1)
            
            if from_idx != to_idx:
                # Check if relationship already exists
                existing = any(
                    (r.from_person_id == person_ids[from_idx] and r.to_person_id == person_ids[to_idx]) or
                    (r.from_person_id == person_ids[to_idx] and r.to_person_id == person_ids[from_idx])
                    for r in relationships
                )
                
                if not existing:
                    score = random.randint(40, 95)
                    rel = self._create_relationship(person_ids[from_idx], person_ids[to_idx], score)
                    relationships.append(rel)
                    connections_created += 1
            
            attempts += 1
        
        self.db.add_all(relationships)
        self.db.commit()
        
        print(f"Created {len(persons)} persons and {len(relationships)} relationships")
        return person_ids
    
    def _create_relationship(self, from_id: uuid.UUID, to_id: uuid.UUID, score: int) -> Knows:
        """Create a relationship with given score"""
        archetypes = ["colleague", "friend", "mentor", "client", "acquaintance"]
        archetype = archetypes[score // 20] if score // 20 < len(archetypes) else "colleague"
        
        return Knows(
            from_person_id=from_id,
            to_person_id=to_id,
            user_id=self.test_user_id,
            archetype=archetype,
            relationship_depth={
                "overall_score": score,
                "dimensions": {
                    "emotional_intimacy": score - 10,
                    "professional_collaboration": score,
                    "trust_level": score + 5,
                    "communication_frequency": score - 5,
                    "shared_experience_value": score,
                    "reciprocity_balance": score
                }
            }
        )
    
    def test_algorithm_performance(
        self, 
        network_size: int, 
        algorithm: PathfindingAlgorithm,
        optimization: PathOptimization,
        num_tests: int = 5
    ) -> Dict[str, Any]:
        """Test performance of a specific algorithm"""
        print(f"\nTesting {algorithm.value} with {optimization.value} optimization on {network_size} node network")
        
        # Create test network
        person_ids = self.create_test_network(network_size, connectivity_ratio=0.2)
        
        # Test pathfinding performance
        total_time = 0
        successful_tests = 0
        path_lengths = []
        success_probabilities = []
        
        for i in range(num_tests):
            # Pick random target (not the user)
            target_id = person_ids[1 + (i % (len(person_ids) - 1))]
            
            start_time = time.time()
            
            try:
                paths = self.pathfinding_service.find_referral_paths(
                    user_id=self.test_user_id,
                    target_person_id=target_id,
                    algorithm=algorithm,
                    optimization=optimization,
                    max_paths=3,
                    max_hops=5
                )
                
                end_time = time.time()
                test_time = end_time - start_time
                total_time += test_time
                
                if paths:
                    successful_tests += 1
                    path_lengths.append(paths[0].path_length)
                    success_probabilities.append(paths[0].success_probability)
                    print(f"  Test {i+1}: {test_time:.3f}s, path length: {paths[0].path_length}, success: {paths[0].success_probability:.3f}")
                else:
                    print(f"  Test {i+1}: {test_time:.3f}s, no path found")
            
            except Exception as e:
                print(f"  Test {i+1}: Error - {str(e)}")
                continue
        
        # Calculate statistics
        avg_time = total_time / num_tests if num_tests > 0 else 0
        success_rate = successful_tests / num_tests if num_tests > 0 else 0
        avg_path_length = sum(path_lengths) / len(path_lengths) if path_lengths else 0
        avg_success_prob = sum(success_probabilities) / len(success_probabilities) if success_probabilities else 0
        
        # Get network statistics
        network_stats = self.pathfinding_service.get_path_statistics(self.test_user_id)
        
        result = {
            "algorithm": algorithm.value,
            "optimization": optimization.value,
            "network_size": network_size,
            "num_tests": num_tests,
            "total_time": total_time,
            "average_time": avg_time,
            "success_rate": success_rate,
            "successful_tests": successful_tests,
            "average_path_length": avg_path_length,
            "average_success_probability": avg_success_prob,
            "network_stats": network_stats,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        print(f"Results: {avg_time:.3f}s avg time, {success_rate:.1%} success rate, {avg_path_length:.1f} avg path length")
        
        return result
    
    def test_ai_engine_integration(self, network_size: int = 20) -> Dict[str, Any]:
        """Test AI engine integration performance"""
        print(f"\nTesting AI Engine integration with {network_size} node network")
        
        person_ids = self.create_test_network(network_size, connectivity_ratio=0.25)
        target_id = person_ids[-1]  # Use last person as target
        
        start_time = time.time()
        
        # Test direct person pathfinding
        result = self.ai_engine.find_referral_path(
            user_id=self.test_user_id,
            target_person_id=target_id,
            algorithm="dijkstra",
            optimization="balanced",
            max_paths=3,
            max_hops=4
        )
        
        direct_time = time.time() - start_time
        
        # Test goal-oriented pathfinding
        start_time = time.time()
        
        goal_result = self.ai_engine.find_referral_path(
            user_id=self.test_user_id,
            target_criteria={
                "description": "Find professionals at Company5",
                "company": "Company5",
                "title_keywords": ["Title"]
            },
            algorithm="a_star",
            optimization="success_probability",
            max_paths=2
        )
        
        goal_time = time.time() - start_time
        
        return {
            "network_size": network_size,
            "direct_pathfinding": {
                "time": direct_time,
                "paths_found": len(result.get("paths", [])),
                "has_alternative_strategies": len(result.get("alternative_strategies", [])) > 0,
                "has_ai_insights": len(result.get("ai_insights", [])) > 0
            },
            "goal_oriented_pathfinding": {
                "time": goal_time,
                "paths_found": len(goal_result.get("paths", [])),
                "target": goal_result.get("target", ""),
                "has_network_stats": "network_stats" in goal_result
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    
    def run_comprehensive_tests(self):
        """Run comprehensive performance tests"""
        print("=" * 60)
        print("PATHFINDING PERFORMANCE TESTING")
        print("=" * 60)
        
        try:
            # Test different network sizes
            network_sizes = [10, 25, 50]
            algorithms = [PathfindingAlgorithm.DIJKSTRA, PathfindingAlgorithm.A_STAR]
            optimizations = [PathOptimization.SHORTEST, PathOptimization.BALANCED, PathOptimization.SUCCESS_PROBABILITY]
            
            # Algorithm performance tests
            for size in network_sizes:
                for algorithm in algorithms:
                    for optimization in optimizations:
                        result = self.test_algorithm_performance(size, algorithm, optimization, num_tests=3)
                        self.test_results.append(result)
                        
                        # Clear cache between tests
                        self.pathfinding_service.clear_cache()
                        
                        # Clean up network
                        self.db.query(Knows).filter(Knows.user_id == self.test_user_id).delete()
                        self.db.query(Person).filter(Person.user_id == self.test_user_id).delete()
                        self.db.commit()
            
            # AI Engine integration tests
            for size in [15, 30]:
                ai_result = self.test_ai_engine_integration(size)
                self.test_results.append({"test_type": "ai_integration", **ai_result})
                
                # Clean up
                self.db.query(Knows).filter(Knows.user_id == self.test_user_id).delete()
                self.db.query(Person).filter(Person.user_id == self.test_user_id).delete()
                self.db.commit()
            
            # Generate performance report
            self.generate_performance_report()
            
        except Exception as e:
            print(f"Error during testing: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.cleanup()
    
    def generate_performance_report(self):
        """Generate a comprehensive performance report"""
        print("\n" + "=" * 60)
        print("PERFORMANCE REPORT")
        print("=" * 60)
        
        # Algorithm performance summary
        algorithm_results = [r for r in self.test_results if "algorithm" in r]
        
        if algorithm_results:
            print("\nAlgorithm Performance Summary:")
            print("-" * 30)
            
            for algorithm in ["dijkstra", "a_star"]:
                algo_results = [r for r in algorithm_results if r["algorithm"] == algorithm]
                if algo_results:
                    avg_time = sum(r["average_time"] for r in algo_results) / len(algo_results)
                    avg_success = sum(r["success_rate"] for r in algo_results) / len(algo_results)
                    print(f"{algorithm.upper()}:")
                    print(f"  Average time: {avg_time:.3f}s")
                    print(f"  Average success rate: {avg_success:.1%}")
            
            # Network size impact
            print("\nNetwork Size Impact:")
            print("-" * 20)
            for size in [10, 25, 50]:
                size_results = [r for r in algorithm_results if r["network_size"] == size]
                if size_results:
                    avg_time = sum(r["average_time"] for r in size_results) / len(size_results)
                    print(f"  {size} nodes: {avg_time:.3f}s average")
        
        # AI Integration summary
        ai_results = [r for r in self.test_results if r.get("test_type") == "ai_integration"]
        if ai_results:
            print("\nAI Engine Integration:")
            print("-" * 22)
            for result in ai_results:
                direct = result["direct_pathfinding"]
                goal = result["goal_oriented_pathfinding"]
                print(f"  Network size {result['network_size']}:")
                print(f"    Direct pathfinding: {direct['time']:.3f}s, {direct['paths_found']} paths")
                print(f"    Goal-oriented: {goal['time']:.3f}s, {goal['paths_found']} paths")
        
        # Save detailed results
        report_file = f"pathfinding_performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"\nDetailed results saved to: {report_file}")


def main():
    """Main function to run performance tests"""
    tester = PathfindingPerformanceTester()
    
    try:
        tester.run_comprehensive_tests()
    except KeyboardInterrupt:
        print("\nTesting interrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        tester.cleanup()


if __name__ == "__main__":
    main()