from sqlalchemy import create_engine
from app.core.config import settings
from app.core.database import Base
from app.models import *  # Import all models

def create_tables():
    """
    Creates all database tables defined in the models.
    """
    engine = create_engine(settings.DATABASE_URL, echo=True)
    
    # Drop all tables first (optional, for a clean slate)
    # Base.metadata.drop_all(bind=engine)
    # Create all tables
    Base.metadata.create_all(bind=engine)
        
    print("Database tables created successfully.")

if __name__ == "__main__":
    print("Starting database table creation...")
    create_tables()
