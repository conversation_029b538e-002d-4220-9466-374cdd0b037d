name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: "3.11"
  POETRY_VERSION: "1.6.1"

jobs:
  lint:
    name: Code Quality & Linting
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}
    
    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --no-root
    
    - name: Install project
      run: poetry install --no-interaction
    
    - name: Run Black (Code Formatting)
      run: poetry run black --check app/
    
    - name: Run isort (Import Sorting)
      run: poetry run isort --check-only app/
    
    - name: Run flake8 (Style Guide)
      run: poetry run flake8 app/
    
    - name: Run mypy (Type Checking)
      run: poetry run mypy app/
      continue-on-error: true  # Type checking is advisory for now

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: lint
    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12"]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ matrix.python-version }}-${{ hashFiles('**/poetry.lock') }}
    
    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --no-root
    
    - name: Install project
      run: poetry install --no-interaction
    
    - name: Run Unit Tests
      run: |
        poetry run pytest \
          -m "unit" \
          --cov=app \
          --cov-report=xml:coverage-unit.xml \
          --cov-report=html:htmlcov-unit \
          --junitxml=test-results-unit.xml \
          --cov-fail-under=85
    
    - name: Upload Unit Test Coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage-unit.xml
        flags: unit
        name: unit-tests-${{ matrix.python-version }}
    
    - name: Upload Unit Test Results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: unit-test-results-${{ matrix.python-version }}
        path: |
          test-results-unit.xml
          htmlcov-unit/

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: lint
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_USER: testuser
          POSTGRES_DB: nexus_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ env.PYTHON_VERSION }}-${{ hashFiles('**/poetry.lock') }}
    
    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --no-root
    
    - name: Install project
      run: poetry install --no-interaction
    
    - name: Set up test environment
      env:
        DATABASE_URL: postgresql://testuser:testpassword@localhost:5432/nexus_test
        REDIS_URL: redis://localhost:6379/0
        SUPABASE_URL: ${{ secrets.SUPABASE_TEST_URL }}
        SUPABASE_KEY: ${{ secrets.SUPABASE_TEST_KEY }}
        OPENAI_API_KEY: ${{ secrets.OPENAI_TEST_KEY }}
      run: |
        poetry run alembic upgrade head
    
    - name: Run Integration Tests
      env:
        DATABASE_URL: postgresql://testuser:testpassword@localhost:5432/nexus_test
        REDIS_URL: redis://localhost:6379/0
        SUPABASE_URL: ${{ secrets.SUPABASE_TEST_URL }}
        SUPABASE_KEY: ${{ secrets.SUPABASE_TEST_KEY }}
        OPENAI_API_KEY: ${{ secrets.OPENAI_TEST_KEY }}
      run: |
        poetry run pytest \
          -m "integration" \
          --cov=app \
          --cov-report=xml:coverage-integration.xml \
          --cov-report=html:htmlcov-integration \
          --junitxml=test-results-integration.xml \
          --cov-fail-under=80
    
    - name: Upload Integration Test Coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage-integration.xml
        flags: integration
        name: integration-tests
    
    - name: Upload Integration Test Results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: |
          test-results-integration.xml
          htmlcov-integration/

  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ env.PYTHON_VERSION }}-${{ hashFiles('**/poetry.lock') }}
    
    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --no-root
    
    - name: Install project
      run: poetry install --no-interaction
    
    - name: Run Performance Tests
      run: |
        poetry run pytest \
          -m "performance" \
          --junitxml=test-results-performance.xml \
          --timeout=300
    
    - name: Upload Performance Test Results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-test-results
        path: test-results-performance.xml

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: lint
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Install dependencies
      run: poetry install --no-interaction --no-root
    
    - name: Run Safety (Dependency Vulnerability Scan)
      run: poetry run safety check --json --output safety-report.json
      continue-on-error: true
    
    - name: Run Bandit (Security Linting)
      run: poetry run bandit -r app/ -f json -o bandit-report.json
      continue-on-error: true
    
    - name: Upload Security Reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          safety-report.json
          bandit-report.json

  test-summary:
    name: Test Summary
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, security-scan]
    if: always()
    
    steps:
    - name: Download all test artifacts
      uses: actions/download-artifact@v3
    
    - name: Generate Test Summary
      run: |
        echo "# Test Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "## Test Results" >> $GITHUB_STEP_SUMMARY
        
        if [ "${{ needs.unit-tests.result }}" == "success" ]; then
          echo "✅ Unit Tests: PASSED" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ Unit Tests: FAILED" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ "${{ needs.integration-tests.result }}" == "success" ]; then
          echo "✅ Integration Tests: PASSED" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ Integration Tests: FAILED" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ "${{ needs.security-scan.result }}" == "success" ]; then
          echo "✅ Security Scan: PASSED" >> $GITHUB_STEP_SUMMARY
        else
          echo "⚠️ Security Scan: ISSUES FOUND" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "## Coverage Reports" >> $GITHUB_STEP_SUMMARY
        echo "Coverage reports are available in the test artifacts." >> $GITHUB_STEP_SUMMARY
