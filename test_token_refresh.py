#!/usr/bin/env python3
"""
Test token refresh functionality
"""

import requests
import json
import time

BASE_URL = "http://localhost:8010"

def test_token_refresh():
    print("Testing token refresh functionality...")
    
    # 1. Register user (if needed)
    register_data = {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "first_name": "Test",
        "last_name": "User"
    }
    
    register_response = requests.post(f"{BASE_URL}/api/v1/auth/register-local", json=register_data)
    print(f"Registration: {register_response.status_code}")
    
    # 2. Login to get initial token
    login_data = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    login_response = requests.post(f"{BASE_URL}/api/v1/auth/login-local", json=login_data)
    print(f"Login: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print(f"Login failed: {login_response.text}")
        return
    
    login_data = login_response.json()
    original_token = login_data["access_token"]
    refresh_token = login_data.get("refresh_token", f"local-refresh-token-{login_data.get('user_id', 'test')}")
    
    print(f"Original token: {original_token[:50]}...")
    print(f"Refresh token: {refresh_token[:50]}...")
    
    # 3. Wait a moment to ensure different timestamp
    time.sleep(2)
    
    # 4. Test token refresh
    refresh_data = {
        "refresh_token": refresh_token
    }
    
    refresh_response = requests.post(f"{BASE_URL}/api/v1/auth/refresh", json=refresh_data)
    print(f"Token refresh: {refresh_response.status_code}")
    
    if refresh_response.status_code == 200:
        refresh_result = refresh_response.json()
        new_token = refresh_result["access_token"]
        print(f"New token: {new_token[:50]}...")
        
        # Check if tokens are different
        if original_token != new_token:
            print("✅ Token refresh successful - tokens are different!")
            
            # Test both tokens work
            headers_old = {"Authorization": f"Bearer {original_token}"}
            headers_new = {"Authorization": f"Bearer {new_token}"}
            
            # Test old token
            old_test = requests.get(f"{BASE_URL}/api/v1/admin/test-data-status", headers=headers_old)
            print(f"Old token test: {old_test.status_code}")
            
            # Test new token
            new_test = requests.get(f"{BASE_URL}/api/v1/admin/test-data-status", headers=headers_new)
            print(f"New token test: {new_test.status_code}")
            
        else:
            print("❌ Token refresh failed - tokens are identical!")
    else:
        print(f"Token refresh failed: {refresh_response.text}")

if __name__ == "__main__":
    test_token_refresh()
