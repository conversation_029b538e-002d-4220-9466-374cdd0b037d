#!/usr/bin/env python3
"""
Quick Copilot Test - Simplified E2E test for LLM functionality
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8010"

def test_copilot_functionality():
    """Quick test of copilot functionality"""
    session = requests.Session()
    results = []
    
    def log_test(name, success, details=None):
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {name}")
        if details and not success:
            print(f"   Error: {details}")
        results.append({"name": name, "success": success, "details": details})
    
    print("🤖 Quick Copilot LLM Test")
    print("=" * 50)
    
    # 1. Health Check
    try:
        response = session.get(f"{BASE_URL}/health")
        log_test("API Health Check", response.status_code == 200)
    except Exception as e:
        log_test("API Health Check", False, str(e))
        return results
    
    # 2. Register Test User
    try:
        user_data = {
            "email": f"quicktest.{int(time.time())}@example.com",
            "password": "TestPassword123!",
            "first_name": "Quick",
            "last_name": "Test"
        }
        
        response = session.post(f"{BASE_URL}/api/v1/auth/register-local", json=user_data)
        log_test("User Registration", response.status_code in [200, 201])
        
        if response.status_code not in [200, 201]:
            print(f"   Registration response: {response.text}")
            return results
            
    except Exception as e:
        log_test("User Registration", False, str(e))
        return results
    
    # 3. Login
    try:
        login_data = {
            "email": user_data["email"],
            "password": user_data["password"]
        }
        
        response = session.post(f"{BASE_URL}/api/v1/auth/login-local", json=login_data)
        log_test("User Login", response.status_code == 200)
        
        if response.status_code != 200:
            print(f"   Login response: {response.text}")
            return results
        
        token = response.json().get("access_token")
        headers = {"Authorization": f"Bearer {token}"}
        
    except Exception as e:
        log_test("User Login", False, str(e))
        return results
    
    # 4. Test Basic Copilot Conversation
    try:
        message_data = {
            "message": "Hello! Can you help me test the copilot functionality?"
        }
        
        response = session.post(
            f"{BASE_URL}/api/v1/copilot/converse",
            json=message_data,
            headers=headers
        )
        
        success = response.status_code == 200
        details = None
        
        if success:
            data = response.json()
            has_response = bool(data.get("response"))
            has_conversation_id = bool(data.get("conversation_id"))
            success = has_response and has_conversation_id
            
            if not success:
                details = f"Missing fields - response: {has_response}, conversation_id: {has_conversation_id}"
        else:
            details = f"HTTP {response.status_code}: {response.text}"
        
        log_test("Basic Copilot Conversation", success, details)
        
        if success:
            conversation_id = data.get("conversation_id")
        
    except Exception as e:
        log_test("Basic Copilot Conversation", False, str(e))
        return results
    
    # 5. Test Function Calling (Network Search)
    try:
        message_data = {
            "message": "Can you search my network for any connections?",
            "conversation_id": conversation_id
        }
        
        response = session.post(
            f"{BASE_URL}/api/v1/copilot/converse",
            json=message_data,
            headers=headers
        )
        
        success = response.status_code == 200
        details = None
        
        if success:
            data = response.json()
            tool_calls = data.get("tool_calls", [])
            has_tool_calls = len(tool_calls) > 0
            
            if has_tool_calls:
                search_tool_called = any(tc.get("name") == "search_network" for tc in tool_calls)
                success = search_tool_called
                if not success:
                    details = f"Expected search_network tool, got: {[tc.get('name') for tc in tool_calls]}"
            else:
                # If no OpenAI key, might not have tool calls but should still respond
                has_response = bool(data.get("response"))
                success = has_response
                details = "No tool calls (possibly no OpenAI key configured)" if success else "No response"
        else:
            details = f"HTTP {response.status_code}: {response.text}"
        
        log_test("Function Calling (Network Search)", success, details)
        
    except Exception as e:
        log_test("Function Calling (Network Search)", False, str(e))
    
    # 6. Test Intent Analysis Endpoint
    try:
        message_data = {
            "message": "I want to create a task to follow up with someone"
        }
        
        response = session.post(
            f"{BASE_URL}/api/v1/copilot/analyze",
            json=message_data,
            headers=headers
        )
        
        success = response.status_code == 200
        details = None
        
        if success:
            data = response.json()
            has_intent = "intent" in data
            has_confidence = "confidence" in data
            success = has_intent and has_confidence
            
            if not success:
                details = f"Missing fields - intent: {has_intent}, confidence: {has_confidence}"
        else:
            details = f"HTTP {response.status_code}: {response.text}"
        
        log_test("Intent Analysis Endpoint", success, details)
        
    except Exception as e:
        log_test("Intent Analysis Endpoint", False, str(e))
    
    # 7. Test Enhanced Suggestions
    try:
        response = session.get(
            f"{BASE_URL}/api/v1/copilot/suggestions?limit=3",
            headers=headers
        )
        
        success = response.status_code == 200
        details = None
        
        if success:
            data = response.json()
            has_suggestions = "suggestions" in data
            success = has_suggestions
            
            if not success:
                details = "Missing suggestions field"
        else:
            details = f"HTTP {response.status_code}: {response.text}"
        
        log_test("Enhanced Suggestions", success, details)
        
    except Exception as e:
        log_test("Enhanced Suggestions", False, str(e))
    
    # 8. Test Task Creation via LLM
    try:
        message_data = {
            "message": "Please create a task for me to test the copilot functionality",
            "conversation_id": conversation_id
        }
        
        response = session.post(
            f"{BASE_URL}/api/v1/copilot/converse",
            json=message_data,
            headers=headers
        )
        
        success = response.status_code == 200
        details = None
        
        if success:
            data = response.json()
            tool_calls = data.get("tool_calls", [])
            
            if tool_calls:
                task_tool_called = any(tc.get("name") == "create_task" for tc in tool_calls)
                success = task_tool_called
                if not success:
                    details = f"Expected create_task tool, got: {[tc.get('name') for tc in tool_calls]}"
            else:
                # Check if response mentions task creation
                response_text = data.get("response", "").lower()
                mentions_task = any(word in response_text for word in ["task", "create", "remind"])
                success = mentions_task
                details = "No tool calls but response mentions task creation" if success else "No task-related response"
        else:
            details = f"HTTP {response.status_code}: {response.text}"
        
        log_test("Task Creation via LLM", success, details)
        
    except Exception as e:
        log_test("Task Creation via LLM", False, str(e))
    
    return results

def main():
    """Main test runner"""
    results = test_copilot_functionality()
    
    print("\n" + "=" * 50)
    print("📊 QUICK TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for r in results if r["success"])
    total = len(results)
    
    for result in results:
        status = "✅" if result["success"] else "❌"
        print(f"{status} {result['name']}")
        if not result["success"] and result["details"]:
            print(f"   └─ {result['details']}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    print(f"📊 Success Rate: {(passed/total*100):.1f}%")
    
    # Save results
    report = {
        "timestamp": datetime.now().isoformat(),
        "total_tests": total,
        "passed_tests": passed,
        "success_rate": passed/total*100,
        "results": results
    }
    
    filename = f"copilot_quick_test_report_{int(time.time())}.json"
    with open(filename, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"📄 Report saved: {filename}")
    
    if passed == total:
        print("\n🎉 All tests passed! Copilot LLM functionality is working!")
        return 0
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Check details above.")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
