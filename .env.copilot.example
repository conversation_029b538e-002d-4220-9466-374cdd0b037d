# Example environment configuration for LLM-powered Copilot
# Copy this file to .env and update with your actual values

# Database Configuration
DATABASE_URL=sqlite:///./nexus.db

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# OpenAI Configuration for LLM-powered Copilot
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-openai-api-key-here

# Optional: Use a different OpenAI-compatible API
# OPENAI_API_BASE=https://api.openai.com/v1

# Optional: Specify model (default: gpt-4)
# OPENAI_MODEL_NAME=gpt-4
# OPENAI_MODEL_NAME=gpt-3.5-turbo

# Application Settings
DEBUG=true
ENVIRONMENT=development

# CORS Settings
ALLOWED_HOSTS=*

# Optional: Redis for caching (improves performance)
# REDIS_URL=redis://localhost:6379

# Optional: File storage
# UPLOAD_DIR=./uploads
# MAX_FILE_SIZE=10485760

# Logging
LOG_LEVEL=INFO
