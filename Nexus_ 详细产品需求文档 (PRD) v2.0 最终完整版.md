# **Nexus：关系智能领航者 \- 详细产品需求文档 (PRD)**

---

文档版本： 2.0 (最终完整版)
日期： 2025年6月22日
作者： 领域专家团队
状态： 定稿

---

## **第一部分：战略愿景与市场机遇**

本部分旨在阐明“Nexus”项目的核心“为什么”。它将定义市场问题，阐述产品的独特愿景，并通过深入的市场分析，为产品开辟一个可防御的利基市场。

### **第一章 引言：重新定义个人关系管理（PRM）**

本章将Nexus定位为一种范式转移，而非现有工具的增量改进。它旨在改变个人战略性地管理其最宝贵资产——人际网络的方式。

#### **现代人际网络管理的困境**

在当今高度互联的商业环境中，人际网络的构建与维护被公认为个人与组织成功的关键要素 1。然而，理论上的重要性与实践中的困境形成了鲜明对比。大量的培训材料和商业文献强调了人际网络的力量，但许多专业人士在实践中仍然感到力不从心。他们普遍认为，构建人际关系的过程充满了功利性，令人不适，甚至感觉虚伪和肮脏，这导致他们本能地回避这项至关重要的活动 1。现有的技术工具，如传统的客户关系管理（CRM）系统或简单的联系人管理器，未能有效解决这一核心问题。它们大多扮演着被动数据存储库的角色，缺乏主动性、战略指导和基于成熟理论的分析能力。

#### **Nexus：您的主动式关系智能领航者**

Nexus的诞生，正是为了填补这一战略鸿沟。它不仅仅是一个应用程序，更是一位基于先进人工智能的主动式领航者（Co-Pilot），其核心理念源于经过验证的人际网络管理理论 1。Nexus的使命是将复杂的网络科学原理转化为用户个人化的、可执行的行动指南。它将超越被动的数据记录，进化为主动的分析、诊断与教练。Nexus的核心愿景是成为用户的智能合作伙伴，帮助他们不仅“记录”人际网络，更能“理解”、“诊断”并“强化”它。

#### **数据主权与混合架构：信任的基石**

在一个数据隐私日益受到关注的时代，信任是任何个人管理工具的生命线。Nexus采用创新的**混合式数据架构**，旨在平衡强大的云端智能与用户对个人隐私的终极控制权。

*   **云端智能核心：** 为了实现复杂的网络计算、大规模语言模型（LLM）集成以及高级AI分析，Nexus的核心计算引擎和关系图谱数据库将部署在服务端。这确保了用户可以随时随地获得强大、一致的智能体验，而不受本地设备性能的限制。
*   **用户控制的隐私边界：** 我们深刻理解部分个人信息的敏感性。因此，对于不直接参与核心计算的强隐私字段（如电话、邮箱、证件号码、家庭住址等），Nexus赋予用户**明确的选择权**。用户可以选择仅将这些信息保留在本地设备上，或选择性地将其通过端到端加密同步至云端，以便在多设备间访问。

这种设计哲学——**“云端赋能，本地守护”**——既保证了Nexus作为“关系智能领航者”的强大功能，又将最敏感数据的控制权牢牢交还给用户，旨在建立一种更智能、更透明的信任关系。

### **第二章 竞争格局与市场定位**

Nexus通过结合“深度战略分析”、“主动AI教练”和“数据主权”这三个关键特性，在现有个人CRM市场（如Clay, Dex, Monica）和通用生产力工具（如Notion, Airtable）之间，开创了一个全新的“战略型个人关系管理平台”（Strategic PRM）利基市场 2。

**表1：Nexus竞争特性矩阵**

| 特性 | Nexus (本项目) | Clay | Dex | Monica | Notion/Airtable (DIY) |
| :---- | :---- | :---- | :---- | :---- | :---- |
| **核心理念** | 战略性网络构建与分析 | 自动化联系人信息丰富 | 关系维护与保持联系 | 个人生活关系记录 | 高度灵活的通用数据库 |
| **主动式AI教练** | **高 (核心功能)** | 低 (研究型AI) | 低 (提醒型) | 无 | 无 |
| **网络图谱分析** | **高 (核心功能)** | 无 | 无 | 无 | 低 (需手动配置) |
| **引荐路径查找** | **高 (核心功能)** | 无 | 无 | 无 | 无 |
| **数据存储模型** | **混合架构 (云端计算核心 + 用户可选的隐私数据同步)** | 云端 | 云端 | 自托管/云端 | 云端 |
| **社交媒体监控** | 阶段性实现 (浏览器插件/API) | 自动化信息丰富 | 深度集成 (LinkedIn, Facebook) | 无 | 需手动集成 |
| **对话式输入 (LLM)** | **是 (核心交互方式)** | 否 | 否 | 否 | 否 |
| **开源选项** | **是 (计划中)** | 否 | 否 | 是 | 否 |
| **目标用户** | 战略导向的专业人士、创业者 | 注重效率的网络建设者 | 广泛的社交活跃用户 | 注重隐私的个人用户 | 技术导向的DIY用户 |

---

## **第二部分：核心产品架构与功能**

本部分将战略愿景转化为具体、详细的产品需求，为设计和工程团队提供明确的蓝图。

### **第三章 Nexus数据模型：以图为中心、本地优先的基础**

#### **3.1 核心实体与关系 (v2.0 终极重构)**

数据模型最终定型，将用户自身作为网络的中心节点，实现了概念的完全统一。

* **实体：**
  * **Person (个人):** 唯一的“人”节点类型。它既可以代表网络中的联系人，也可以代表用户本人。
  * **其他节点:** Organization, Event, Goal, Note, Topic/Interest/Skill 保持不变。
* **Person 节点 Schema (最终版):**
  * person\_id: (Primary Key) 唯一标识符。
  * is\_user: (Boolean) **核心标识**。当值为true时，表示该节点为应用的所有者，即用户本人。整个数据库中只有一个节点的该值为true。
  * first\_name, last\_name, profile\_picture 等基本信息。
  * social\_profiles, professional\_info 等非隐私的职业与社交信息。
  * personal\_details: (包含生日、星座、MBTI等非敏感个人信息)。
  * **contact\_info (隐私字段，用户可选同步):** 包含电话、邮箱、证件号、家庭住址等。
  * **以下字段仅当 is\_user 为 true 时填充：**
    * **stated\_decision\_factors (陈述型决策因子):** 这是用户在设置中明确设定的、对六个关系维度的个人权重。
      * emotional\_weight: (Float, 0-1) 情感连接权重
      * value\_weight: (Float, 0-1) 价值交换权重
      * trust\_weight: (Float, 0-1) 专业信赖权重
      * information\_weight: (Float, 0-1) 信息枢纽权重
      * role\_weight: (Float, 0-1) 角色绑定权重
      * coercive\_weight: (Float, 0-1) 负向牵制权重
    * **learned\_decision\_factors (学习型决策因子):** 这是AI通过学习用户的行为模式，动态推断出的决策因子权重。其数据结构与stated\_decision\_factors相同。
    * **last\_calibration\_date (上次校准日期):** (Timestamp)
* **边（Edges）：**
  * **Knows (认识):** Person \-\> Person。这条边的结构不变，包含relationship\_foundation (六维模型) 和 relationship\_depth (洋葱理论) 两个核心对象。
  * 其他边 (Works\_At, Participated\_In 等) 保持不变。

#### **3.2 设计哲学：后台的图谱智能与前台的体验简洁**

我们明确一个核心设计哲学：**将技术实现的复杂性留在后台，将交互体验的简洁性呈现在前台。** 我们用后台的“网络”思维，来赋能和简化用户前台的“列表”思维。用户无需理解图数据库的复杂概念，就能享受到其带来的全部智能优势 5。

### **第四章 主动式AI引擎：“Nexus领航者” (v2.0 升级)**

AI引擎的能力得到质的飞跃，它将作为一个**服务端核心**，实现了从“通用智能”到“个性化智能”，再到“学习型智能”的终极进化。所有个性化分析的依据，均来自云端图谱数据库中 `is_user: true` 的那个Person节点。

#### **4.1 核心哲学：“内在思考”框架**

Nexus的AI领航者将不会是一个简单的聊天机器人，而是采用“内在思考”框架 7。这意味着AI系统会在**服务端**持续不断地分析用户的人际网络图谱，主动发现潜在的问题、机会和信息缺口 9。

#### **4.2 关系原型应用 (Archetype Application)**

为了解决用户在添加新联系人或对某段关系不了解时的“冷启动”问题，系统内置了一套基于常见社会角色的“关系原型模板”。当用户为联系人添加特定角色标签时（如“投资人”），系统会提示用户是否应用该角色的通用关系模板作为分析起点，从而在新关系建立的最初阶段就提供基础的分析和洞察。

#### **4.3 个性化洞察与建议 (Personalized Insights & Suggestions)**

AI的所有分析和建议都将结合用户的stated\_decision\_factors（决策因子）权重。AI在评估一段关系的重要性或风险时，会对六个维度的评分进行加权计算，使得每一条建议都直击用户的核心关切点 10。

#### **4.4 动态偏好学习引擎 (Dynamic Preference Learning Engine)**

这是Nexus实现自我进化、真正理解用户的核心模块，**完全在服务端运行**。

* **目标:** 解决用户“陈述的偏好”与“实际的行为”之间的差异，通过学习用户的决策案例，动态调整其关系决策模型。
* **“决策案例”的定义 (Data Points for Learning):** AI将以下用户行为视为可学习的“决策案例”：高优先级行动、目标设定、评分模式、注意力投入等。
* **学习与推断逻辑:** 引擎在**服务端**持续、匿名地收集这些“决策案例”，运用机器学习算法识别模式，并推断出一套能更好地解释用户实际行为的决策因子权重，即learned\_decision\_factors。

#### **4.5 偏好校准建议 (Preference Calibration Suggestion)**

这是将学习引擎的成果呈现给用户，并尊重用户最终决策权的关键交互机制。

* **触发机制:** 当AI学习到的偏好与用户设定的偏好之间出现显著且持续的偏差时，系统将触发此建议。
* **交互流程:** AI领航者会推送一个特殊的建议卡片：“**AI洞察：我发现您的行为模式似乎揭示了新的优先级。**”
* **校准界面:** 点击卡片后，会进入一个专门的“偏好校准”界面，清晰地展示并排对比、AI的解释以及用户的选择（“接受新偏好”、“保持不变”或“手动微调”）。

### **第五章 数据输入与管理**

* **对话式接口：** 核心输入方式，利用**服务端LLM**进行自然语言理解 11。
* **社交媒体监控：** 采取分阶段实施策略（用户分享 \-\> 浏览器插件 \-\> API合作）18。
* **模糊搜索：** 利用字符串相似度算法，提升搜索容错性 28。

### **第六章 网络智能与分析 (v2.0 升级)**

#### **6.1-6.3 基础分析功能**

* **交互式关系图谱:** 可视化、可交互的人际网络地图 1。
* **网络健康诊断:** **服务端**自动分析图谱，诊断结构洞、过度中心化、多样性不足等问题 1。
* **引荐路径查找器:** **服务端**运用加权算法，寻找“最有效”的引荐路径 32。

#### **6.4 多维关系透视镜 (Multi-Dimensional Relationship Prism)**

* **关系诊断雷达图:** 一个六边形的雷达图，直观展示关系的六个维度（情感连接、价值交换、专业信赖、信息枢纽、角色绑定、负向牵制）。
* **非对称性分析矩阵:** 以表格形式量化并解释关系中的不平衡。AI分析文本会动态结合用户的“陈述型偏好”和“学习型偏好”进行分析。
  * **示例分析文本:** “**高度不对称 (您是主要依赖方):** 您在专业上对对方有极强的依赖。虽然您设定的‘专业信赖’权重为20%，但**AI学习发现，您的实际行动表明它对您的影响接近40%**。这种潜在的认知偏差和权力失衡需要您高度关注。”
* **关系深度分析 (洋葱理论):** 分析关系的亲密程度（话题广度和沟通深度）。

#### **6.5 目标情报仪表盘**

为每个高优先级目标自动生成一个专属的“作战指挥室”，集中展示目标档案、AI分析的需求动机、最佳引荐路径图等。

### **第七章 用户指导与任务管理**

AI可以基于对关系的深度分析，生成更具战略性的任务，例如，在检测到价值交换不平衡后，可能会自动创建任务：“思考如何为‘李四’提供一次帮助，以平衡你们的价值交换关系。”

### **第八章 数据架构与可移植性 (v2.0 混合模型)**

#### **8.1 混合数据存储模型**

*   **服务端核心数据：**
    *   **存储内容：** 关系图谱的核心结构（所有实体与边）、关系六维评分、用户决策因子、AI学习模型、以及所有不包含用户定义隐私字段的个人信息。
    *   **安全措施：** 所有数据在传输和静态存储时均采用业界最高标准加密。
*   **本地设备数据：**
    *   **存储内容：** 默认情况下，所有强隐私字段（如电话、邮箱、证件号）仅存储在用户本地设备上，并采用AES-256加密 35。
    *   **缓存机制：** 本地设备会缓存一部分非隐私数据，以保证离线时的基本可用性。

#### **8.2 用户可选的隐私数据同步**

*   **光同步（Opt-in）原则：** 用户必须主动选择，才能将本地存储的隐私字段同步到云端。
*   **端到端加密（E2EE）：** 即使用户选择同步，这些隐私数据也将采用端到端加密策略。这意味着除了用户本人，任何第三方（包括Nexus的服务器管理员）都无法解密和访问这些数据。
*   **增量同步：** 同步过程采用高效的增量同步策略，只传输发生变化的数据，以节省带宽和电量 41。

#### **8.3 设备间数据传输**

*   对于未选择云同步的用户，依然可以采用安全的Wi-Fi直连（Wi-Fi Direct）方案实现设备间的数据迁移 37。

#### **8.4 全面的数据导出**

*   用户可随时将自己的全部数据（包括云端和本地）以多种格式（CSV/XLSX, JSON, PDF/PNG, vCard）完整导出，确保数据所有权。

---

## **第三部分：设计、交互与模块化**

### **第九章 设计哲学与界面风格**

* **核心原则:** 沉静专业、信息致密、信任至上。
* **界面风格:** 默认采用深色模式，以科技蓝为强调色，选用清晰的无衬线字体，图标风格保持线性、简洁。

### **第十章 产品功能模块 (v2.0 升级)**

Nexus应用划分为**四个**核心功能模块，主导航采用底部标签栏。

#### **10.1 领航者中心 (Co-Pilot Center)**

* **功能职责:** 作为应用的默认主页和交互枢纽，提供对话式接口，并主动呈现AI的建议和洞察。
* **核心功能需求点:**
  * 支持文本与语音输入。
  * 以信息卡片形式推送AI建议。
  * 提供每日/每周人脉动态摘要。
  * 发起并处理“偏好校准”建议。
* **界面与交互设计:**
  * 界面类似现代即时通讯应用，底部为文本/语音输入框。
  * 对话流中，AI的建议卡片以特殊样式突出，包含可操作按钮（如“创建任务”、“查看详情”）。
  * 用户可对AI的建议进行反馈（如“这个建议很有用”/“不适用”）。
* **用户故事:**
  * **作为一名忙碌的销售总监，我希望能通过语音快速记录会议要点和后续任务，** 以便在会议间隙就能完成信息整理，不遗漏任何商机。
  * **作为一名创业者，我希望AI能主动告诉我网络中的新机会，比如谁换了新工作可能成为潜在客户，** 这样我就能第一时间采取行动，而不是被动等待。

#### **10.2 人脉库 (Contacts Hub)**

* **功能职责:** 作为智能通讯录，集中管理所有个人和组织信息，并提供深度关系分析入口。
* **核心功能需求点:**
  * 创建、编辑、删除个人（Person）和组织（Organization）条目。
  * 提供列表视图，支持按姓名、公司、标签等排序和筛选。
  * 提供强大的模糊搜索功能。
  * 提供个人/组织详情页，聚合所有相关信息（事件、笔记、任务等）。
  * 提供“多维关系透视镜”入口。
* **界面与交互设计:**
  * 主界面为可滚动、可搜索的联系人列表，每行显示头像、姓名、职位和公司。
  * 详情页顶部为联系人核心信息，下方为标签页或可折叠区域，分别展示“互动历史”、“相关任务”、“关系透视镜”等。
  * “多维关系透视镜”以一个醒目的雷达图为主体，下方附有可交互的分析矩阵表格。
* **用户故事:**
  * **作为一名咨询顾问，在与重要客户会面前，我希望能快速查看他的详情页，回顾我们所有的互动历史和上次的谈话要点，** 以便我能更好地准备，让对话更有深度。
  * **作为一名团队管理者，当我感觉与某位下属的关系变得紧张时，我希望能通过‘关系透视镜’分析我们关系的各个维度，** 找出问题的根源是价值交换不对等还是情感连接出了问题，从而进行有针对性的沟通。

#### **10.3 关系图谱 (Network Map)**

* **功能职责:** 作为数据可视化和探索中心，宏观审视网络结构，发现隐藏的模式和机会。
* **核心功能需求点:**
  * 以力导向图的形式可视化整个人际网络。
  * 支持平移、缩放等交互操作。
  * 提供筛选器，按标签、组织、关系强度等过滤网络。
  * 自动识别并高亮显示关键人物（如信息经纪人、超级连接者）和结构洞。
  * 交互式展示引荐路径。
* **界面与交互设计:**
  * 主视图为一个全屏的、动态的图谱。
  * 侧边栏或浮动面板提供筛选和图例说明。
  * 点击节点会弹出信息摘要卡片，包含核心信息和“查看详情”的链接。
  * 路径查找结果会以高亮和动画效果在图谱上清晰展示。
* **用户故事:**
  * **作为一名风险投资人，我希望能通过关系图谱直观地看到我的投资组合公司之间是否存在潜在的合作机会（即它们在网络中的距离），** 以便我能主动牵线搭桥，创造协同价值。
  * **作为一名求职者，我希望能可视化地看到从我到目标公司招聘经理的最短路径，并识别出路径上的关键引荐人，** 这样我就能制定出最高效的求职策略。

#### **10.4 行动中心 (Action Center)**

* **功能职责:** 作为统一的任务和目标管理中心，将所有洞察转化为可执行的行动。
* **核心功能需求点:**
  * 统一展示所有待办事项。
  * 支持创建、编辑、完成任务。
  * 支持创建“项目”（高阶目标），并为其添加层级式子任务。
  * AI对任务列表进行智能优先级排序。
  * 提供项目详情页（即目标情报仪表盘）。
* **界面与交互设计:**
  * 主界面为一个可筛选的任务列表（类似Todoist或Things），可按“今天”、“项目”、“收件箱”等视图切换。
  * 每个任务项都清晰地标明其优先级和关联的联系人或项目。
  * “项目”在列表中以特殊样式（如文件夹图标）展示，可展开查看其下的子任务。
  * 项目详情页以仪表盘形式设计，模块化展示目标信息、相关任务、情报和关系路径图。
* **用户故事:**
  * **作为一名项目经理，我希望将“拓展新市场”设为一个项目，并让AI帮我自动分解出联系关键人物、市场调研等子任务，** 这样我就能确保这个复杂的战略目标被有条不紊地推进。
  * **作为一名普通用户，我希望每天打开行动中心时，看到的都是AI已经帮我排好优先级的、今天最应该做的几件人脉维护任务，** 让我无需思考“先做什么”，直接开始行动。

#### **10.5 设置与集成 (Settings & Integrations)**

* **功能职责:** 负责应用配置、数据管理和与外部世界的连接。
* **核心功能需求点:**
  * 管理账户与安全设置。
  * 提供“关系决策偏好”设置页面。
  * **管理隐私数据同步选项。**
  * 提供数据导入/导出功能。
  * 管理设备间同步。
  * 管理与系统日历、通讯录的集成。
* **界面与交互设计:**
  * 采用标准的列表式设置菜单。
  * “关系决策偏好”页面使用六个可拖动的滑块来调整权重，并实时显示总和，确保为100%。
  * **新增“隐私与同步”设置项，允许用户对强隐私字段逐项设置同步策略（“仅本地”、“云端加密同步”）。**
  * 集成管理页面清晰地列出已连接和可连接的服务，并提供开关和状态指示。
* **用户故事:**
  * **作为一名注重情感连接的用户，我希望能在设置中调高‘情感连接’维度的权重，** 以便AI在给我建议时，能优先考虑那些能加深情感联系的行动。
  * **作为一名注重数据隐私的用户，我希望能轻松地将我的全部数据导出一份本地备份，** 让我对自己的数据有完全的控制权和安全感。

### **第十一章 整体交互设计**

#### **11.1 主导航模型**

* **结构:** 采用包含四个常驻入口（领航者、人脉、图谱、行动）的底部标签栏导航。
* **逻辑:**
  * **领航者:** 默认主页，是快速输入和获取AI主动建议的入口。
  * **人脉:** 关系的“数据库”，用于查找和深度分析单个联系人。
  * **图谱:** 关系的“望远镜”，用于宏观审视和探索网络结构。
  * **行动:** 关系的“待办清单”，用于将所有洞察转化为可执行的任务和项目。

#### **11.2 模块功能交互**

* **从“领航者”出发:**
  * **输入信息:** 用户在领航者中心输入“昨天和张三吃了饭，聊了A项目”，AI会自动在**人脉库**中找到“张三”，并创建一条与\#A项目标签关联的Event记录。
  * **接收建议:** 领航者推送“您与关键联系人‘李四’已3个月未联系”，卡片上提供“创建提醒”按钮，点击后直接在**行动中心**生成一条任务。
* **从“人脉库”出发:**
  * **分析关系:** 用户在“王五”的详情页，点击进入“多维关系透视镜”。AI在分析矩阵中提示“价值交换不对称”。用户可以点击分析文本旁的“我该怎么做？”按钮，AI会在**领航者中心**以对话形式提供具体的行动建议，并询问是否要创建相关任务到**行动中心**。
  * **关联查询:** 在“王五”的详情页，用户可以点击其所在的“XYZ公司”，页面会跳转到该公司的聚合页，显示所有在**人脉库**中任职于XYZ公司的联系人。
* **从“关系图谱”出发:**
  * **探索与行动:** 用户在图谱中发现一个孤立的“金融圈”集群。他可以框选该集群，并点击“寻找连接点”按钮。AI会分析并高亮显示用户网络中可能认识该集群成员的“桥梁”人物，并弹出建议：“联系‘赵六’可能是您进入金融圈的有效途径”，用户可一键将其设为**行动中心**的一个新“项目”。
* **从“行动中心”出发:**
  * **项目驱动:** 用户查看“结识投资人周总”的项目。项目详情页（即目标情报仪表盘）显示最佳引荐路径是“通过孙先生”。点击“孙先生”的头像，直接跳转到他在**人脉库**的详情页，以便用户在联系前快速回顾其背景和过往互动。

#### **11.3 核心用户流模型**

* **流程一：设定战略目标并分解执行**
  1. **设定项目:** 用户进入**行动中心**，创建一个新的“项目”：“为我的初创公司A轮融资”，并设为高优先级。
  2. **AI启动分析:** **服务端AI引擎**被激活，开始在后台扫描用户的整个网络，寻找与“融资”、“投资人”等标签相关的联系人。
  3. **情报与路径呈现:** 用户点击该项目进入详情页。AI已在此处呈现了几个潜在的投资人联系人，并为其中评分最高的“李总”绘制了最佳引荐路径图。
  4. **任务自动生成:** AI自动在该项目下生成数条建议的子任务，如：
     * 子任务1: “完善项目A的商业计划书” (通用任务)
     * 子任务2: “更新您与‘李总’的关系评分” (数据维护任务)
     * 子任务3: “与引荐人‘王五’喝杯咖啡，预热关系” (关系维护任务)
  5. **执行与追踪:** 这些子任务自动出现在**行动中心**的主任务列表中。用户完成任务，项目进度条随之更新。
* **流程二：关系健康度检查与维护**
  1. **AI主动提醒:** 用户在**领航者中心**看到一条AI推送的卡片：“关系洞察：您与导师‘陈教授’的互动在最近6个月主要由您发起，存在‘价值交换’不对称的风险。”
  2. **深度分析:** 用户点击卡片，直接跳转到**人脉库**中“陈教授”的“多维关系透视镜”页面。雷达图和分析矩阵直观地证实了AI的发现。
  3. **寻求建议:** 用户点击分析矩阵中的“如何改善？”按钮。
  4. **生成行动:** AI在**领航者中心**回应：“陈教授最近公开分享了关于‘量子计算’的文章。1. 您可以分享一篇相关的、有深度的文章给他，并附上您的见解。2. 您也可以向他请教一个您在该领域遇到的具体问题。需要我为您创建相关任务吗？”
  5. **任务闭环:** 用户选择“1”，AI自动在**行动中心**创建任务：“找到关于量子计算的好文章并分享给陈教授”。

### **第十二章 完整功能列表 (按模块)**

#### **12.1 领航者中心 (Co-Pilot Center)**

* \[ \] 对话式交互接口（文本与语音）
* \[ \] AI主动建议与洞察卡片推送
* \[ \] 每日/每周人脉动态摘要
* \[ \] 偏好校准建议与交互界面

#### **12.2 人脉库 (Contacts Hub)**

* \[ \] 创建/编辑/删除个人（Person）联系人
* \[ \] 创建/编辑/删除组织（Organization）信息
* \[ \] 联系人列表视图（支持排序与筛选）
* \[ \] 强大的模糊搜索功能
* \[ \] 标签系统（创建、分配、按标签筛选）
* \[ \] 个人详情页（聚合所有相关信息）
* \[ \] 组织详情页（聚合所有相关信息）
* \[ \] **多维关系透视镜**
  * \[ \] 六维关系手动评分
  * \[ \] 关系诊断雷达图可视化
  * \[ \] 非对称性分析矩阵
  * \[ \] 关系深度（洋葱理论）分析
* \[ \] 关系原型模板应用功能

#### **12.3 关系图谱 (Network Map)**

* \[ \] 完整的网络图谱可视化
* \[ \] 节点的交互式操作（拖动、点击查看摘要）
* \[ \] 网络的筛选与高亮（按标签、组织等）
* \[ \] 网络健康诊断分析（结构洞、多样性等）
* \[ \] 关键人物（连接者、中心人物）识别
* \[ \] 交互式引荐路径查找与展示

#### **12.4 行动中心 (Action Center)**

* \[ \] 统一的任务列表视图
* \[ \] 创建/编辑/完成任务
* \[ \] 创建/编辑/归档“项目”（高阶目标）
* \[ \] 在项目中创建层级式子任务
* \[ \] AI智能任务优先级排序
* \[ \] 项目详情页（即目标情报仪表盘）
  * \[ \] 关联目标信息展示
  * \[ \] 关联任务列表
  * \[ \] AI生成的下一步行动建议

#### **12.5 设置与集成 (Settings & Integrations)**

* \[ \] 用户账户与安全设置
* \[ \] **关系决策偏好（决策因子）设置与调整**
* \[ \] AI学习与校准历史回顾
* \[ \] **隐私与同步管理 (新增)**
  * \[ \] **对强隐私字段（电话/邮箱等）进行逐项同步策略设置**
* \[ \] 本地数据管理（备份提醒）
* \[ \] 数据导入/导出功能（多种格式）
* \[ \] 设备间数据传输（Wi-Fi直连）
* \[ \] 与系统日历/通讯录的集成管理

### **第十三章 最小可行产品（MVP）功能优先级 (v2.0 最终版)**

**表3：Nexus MVP功能MoSCoW优先级划分 (v2.0)**

| 功能 | 类别 | 优先级 | 理由 |
| :---- | :---- | :---- | :---- |
| **服务端核心数据库与API** | 数据架构 | **Must-have (必须有)** | 新架构的核心，所有功能依赖于此。 |
| **基础用户认证系统** | 数据架构 | **Must-have (必须有)** | 保障云端数据安全的基础。 |
| 手动进行六维关系评分 | 网络智能 | **Must-have (必须有)** | MVP阶段，允许用户为关键联系人手动输入评分。 |
| 关系诊断雷达图 (可视化) | 网络智能 | **Must-have (必须有)** | 将手动输入的分数以雷达图形式可视化，是新模型最直观的价值体现。 |
| 统一的行动中心 (基础版) | 任务与目标管理 | **Must-have (必须有)** | 提供任务列表和创建项目/目标的能力。 |
| **隐私数据本地存储与可选同步** | 数据架构 | **Must-have (必须有)** | 产品的核心哲学和信任基石。 |
| **关系决策偏好设置 (静态版)** | 用户画像 | **Should-have (应该有)** | 这是实现个性化分析的第一步，能极大提升产品价值。 |
| 非对称性分析矩阵 (基础版) | 网络智能 | **Should-have (应该有)** | 在雷达图下方用表格展示评分差异和基础的文字提示。 |
| 应用关系原型模板 (基础版) | AI引擎 | **Could-have (可以有)** | MVP阶段可以先内置几个固定的原型模板，供用户手动选择应用。 |
| **动态偏好学习引擎** | AI引擎 | **Won't-have (暂时不做)** | **(未来核心)** 这是产品的终极能力，技术复杂度和数据要求极高，是清晰的远期目标。 |
| **偏好校准建议与交互** | AI引擎 | **Won't-have (暂时不做)** | **(未来核心)** 作为学习引擎的用户接口，随学习引擎一同开发。 |

---

## **第四部分：未来愿景与伦理考量**

### **第十四章 未来路线图与扩展功能**

* **第二阶段（MVP发布后6-18个月）：智能分析深化**
  * 逐步引入Could-have功能，如关系深度分析、更复杂的AI提醒。
* **第三阶段（长期愿景）：学习型AI与平台化**
  * 启动并逐步完善\*\*“动态偏好学习引擎”**和**“偏好校准”\*\*机制的开发与上线。
  * 在此基础上，探索团队协作与全方位发展平台。

### **第十五章 Nexus伦理框架：视信任为功能**

Nexus的设计和运营将严格遵守五大伦理原则：

1. **透明性与可解释性** 44
2. **用户自主权至上** 44
3. **数据隐私与最小化原则** 44
4. **偏见缓解** 44
5. **防止有害依赖** 45

我们坚信，对于一个深度介入个人生活的应用而言，伦理不是一种束缚，而是一种核心功能。一个公开、健全、可信赖的伦理框架，将是Nexus最强大的护城河，也是其赢得用户长期信任的唯一途径。

#### **引用的著作**

1. 人际网络.pdf
2. 10 Best Personal CRM Tools: An Ultimate Review of the Top in 2025 \- Lemlist, 访问时间为 六月 14, 2025， [https://www.lemlist.com/blog/personal-crm](https://www.lemlist.com/blog/personal-crm)
3. 5 personal CRM tools to organize your work and personal life \- Softr, 访问时间为 六月 14, 2025， [https://www.softr.io/blog/personal-crm](https://www.softr.io/blog/personal-crm)
4. Best Personal CRM Tools in 2025: Complete Comparison Guide, 访问时间为 六月 14, 2025， [https://www.folk.app/articles/best-personal-crm](https://www.folk.app/articles/best-personal-crm)
5. What is a Graph Database? Use Cases and Advantages \- Decube, 访问时间为 六月 14, 2025， [https://www.decube.io/post/graph-database-concept](https://www.decube.io/post/graph-database-concept)
6. 6 Graph Database Use Cases With Examples, 访问时间为 六月 14, 2025， [https://www.puppygraph.com/blog/graph-database-use-cases](https://www.puppygraph.com/blog/graph-database-use-cases)
7. Proactive Conversational Agents with Inner Thoughts | AI Research Paper Details, 访问时间为 六月 14, 2025， [https://www.aimodels.fyi/papers/arxiv/proactive-conversational-agents-inner-thoughts](https://www.aimodels.fyi/papers/arxiv/proactive-conversational-agents-inner-thoughts)
8. \[2501.00383\] Proactive Conversational Agents with Inner Thoughts \- arXiv, 访问时间为 六月 14, 2025， [https://arxiv.org/abs/2501.00383](https://arxiv.org/abs/2501.00383)
9. Proactive Conversational AI: A Comprehensive Survey of Advancements and Opportunities (Journal Article) | NSF PAGES, 访问时间为 六月 14, 2025， [https://par.nsf.gov/biblio/10580599-proactive-conversational-ai-comprehensive-survey-advancements-opportunities](https://par.nsf.gov/biblio/10580599-proactive-conversational-ai-comprehensive-survey-advancements-opportunities)
10. Big data analytics for customer relationship management: Enhancing engagement and retention strategies \- ResearchGate, 访问时间为 六月 14, 2025， [https://www.researchgate.net/publication/387524618\_Big\_data\_analytics\_for\_customer\_relationship\_management\_Enhancing\_engagement\_and\_retention\_strategies](https://www.researchgate.net/publication/387524618_Big_data_analytics_for_customer_relationship_management_Enhancing_engagement_and_retention_strategies)
11. Conversational AI Examples, Applications & Use Cases \- IBM, 访问时间为 六月 14, 2025， [https://www.ibm.com/think/topics/conversational-ai-use-cases](https://www.ibm.com/think/topics/conversational-ai-use-cases)
12. What is Conversational AI? \- AWS, 访问时间为 六月 14, 2025， [https://aws.amazon.com/what-is/conversational-ai/](https://aws.amazon.com/what-is/conversational-ai/)
13. cloud.google.com, 访问时间为 六月 14, 2025， [https://cloud.google.com/conversational-ai\#:\~:text=Conversational%20AI%20works%20by%20using,understand%20and%20process%20human%20language.](https://cloud.google.com/conversational-ai#:~:text=Conversational%20AI%20works%20by%20using,understand%20and%20process%20human%20language.)
14. Conversational AI Use Cases: How You Can Use AI to Transform Your Business, 访问时间为 六月 14, 2025， [https://forethought.ai/blog/conversational-ai-use-cases-2](https://forethought.ai/blog/conversational-ai-use-cases-2)
15. Boost SMB CRM Adoption: The Power of Natural Language Query (NLQ) \- Vtiger CRM Blog, 访问时间为 六月 14, 2025， [https://www.vtiger.com/blog/boost-smb-crm-adoption-the-power-of-natural-language-query-nlq/](https://www.vtiger.com/blog/boost-smb-crm-adoption-the-power-of-natural-language-query-nlq/)
16. What is Natural Language Query (NLQ)? Everything You Need to Know \- Yellowfin, 访问时间为 六月 14, 2025， [https://www.yellowfinbi.com/blog/what-is-natural-language-query-nlq](https://www.yellowfinbi.com/blog/what-is-natural-language-query-nlq)
17. Natural language BI: Query your database with no-code AI solutions \- Celigo, 访问时间为 六月 14, 2025， [https://www.celigo.com/blog/text-to-sql-access-databases-no-code-ai-solutions/](https://www.celigo.com/blog/text-to-sql-access-databases-no-code-ai-solutions/)
18. Ultimate Guide to LinkedIn API: Integration, Scraping, and More \- Scrupp, 访问时间为 六月 14, 2025， [https://scrupp.com/blog/linkedin-api](https://scrupp.com/blog/linkedin-api)
19. What Is LinkedIn API? Complete Guide On How It Works \[2025\] \- Evaboot, 访问时间为 六月 14, 2025， [https://evaboot.com/blog/what-is-linkedin-api](https://evaboot.com/blog/what-is-linkedin-api)
20. Data Integrations API \- LinkedIn Developer, 访问时间为 六月 14, 2025， [https://developer.linkedin.com/product-catalog/marketing/data-integrations-api](https://developer.linkedin.com/product-catalog/marketing/data-integrations-api)
21. Increasing Access \- LinkedIn | Microsoft Learn, 访问时间为 六月 14, 2025， [https://learn.microsoft.com/en-us/linkedin/marketing/increasing-access?view=li-lms-2025-04](https://learn.microsoft.com/en-us/linkedin/marketing/increasing-access?view=li-lms-2025-04)
22. Access Levels \- Graph API \- Meta for Developers, 访问时间为 六月 14, 2025， [https://developers.facebook.com/docs/graph-api/overview/access-levels/](https://developers.facebook.com/docs/graph-api/overview/access-levels/)
23. v21.0 \- Graph API, 访问时间为 六月 14, 2025， [https://developers.facebook.com/docs/graph-api/changelog/version21.0/](https://developers.facebook.com/docs/graph-api/changelog/version21.0/)
24. Overview \- Graph API \- Meta for Developers \- Facebook, 访问时间为 六月 14, 2025， [https://developers.facebook.com/docs/graph-api/overview/](https://developers.facebook.com/docs/graph-api/overview/)
25. Track and Manage Social Media with BIGContacts CRM, 访问时间为 六月 14, 2025， [https://www.bigcontacts.com/social-media/](https://www.bigcontacts.com/social-media/)
26. 21 Best CRMs With Social Media Integration Reviewed In 2025 \- The CRO Club, 访问时间为 六月 14, 2025， [https://croclub.com/tools/best-crm-social-media-integration/](https://croclub.com/tools/best-crm-social-media-integration/)
27. What Is Social CRM? 10 Best Social Media CRM Tools Reviewed \- CRM.org, 访问时间为 六月 14, 2025， [https://crm.org/crmland/social-crm](https://crm.org/crmland/social-crm)
28. What is fuzzy search? Fuzzy search meaning. | Google Cloud, 访问时间为 六月 14, 2025， [https://cloud.google.com/discover/what-is-fuzzy-search](https://cloud.google.com/discover/what-is-fuzzy-search)
29. Fuzzy search \- Azure AI Search | Microsoft Learn, 访问时间为 六月 14, 2025， [https://learn.microsoft.com/en-us/azure/search/search-query-fuzzy](https://learn.microsoft.com/en-us/azure/search/search-query-fuzzy)
30. Fuzzy Search Core Concepts and Examples \- Akooda, 访问时间为 六月 14, 2025， [https://www.akooda.co/blog/understanding-fuzzy-search-key-concepts-and-applications](https://www.akooda.co/blog/understanding-fuzzy-search-key-concepts-and-applications)
31. How to implement Fuzzy Search in a Bubble application, 访问时间为 六月 14, 2025， [https://build.airdev.co/wiki/how-to-implement-fuzzy-search-bubble-application](https://build.airdev.co/wiki/how-to-implement-fuzzy-search-bubble-application)
32. Shortest Path In Social Network Analysis \- HeyCoach | Blogs, 访问时间为 六月 14, 2025， [https://blog.heycoach.in/shortest-path-in-social-network-analysis/](https://blog.heycoach.in/shortest-path-in-social-network-analysis/)
33. Introduction to Dijkstra's Shortest Path Algorithm \- GeeksforGeeks, 访问时间为 六月 14, 2025， [https://www.geeksforgeeks.org/introduction-to-dijkstras-shortest-path-algorithm/](https://www.geeksforgeeks.org/introduction-to-dijkstras-shortest-path-algorithm/)
34. Dijkstra's Algorithm to find Shortest Paths from a Source to all \- GeeksforGeeks, 访问时间为 六月 14, 2025， [https://www.geeksforgeeks.org/dijkstras-shortest-path-algorithm-greedy-algo-7/](https://www.geeksforgeeks.org/dijkstras-shortest-path-algorithm-greedy-algo-7/)
35. Realm vs SQLite: Which database to choose in 2025 \- Orangesoft, 访问时间为 六月 14, 2025， [https://orangesoft.co/blog/realm-vs-sqlite](https://orangesoft.co/blog/realm-vs-sqlite)
36. Covve CRM Manage your contacts 4+ \- App Store \- Apple, 访问时间为 六月 14, 2025， [https://apps.apple.com/us/app/covve-crm-manage-your-contacts/id958935377](https://apps.apple.com/us/app/covve-crm-manage-your-contacts/id958935377)
37. How to Bluetooth from Android to iPhone (NEW METHOD 2024\) \- YouTube, 访问时间为 六月 14, 2025， [https://www.youtube.com/watch?v=PpDan3CEvHo](https://www.youtube.com/watch?v=PpDan3CEvHo)
38. Is it possible to transfer files between Android and iPhone without using any apps, using only NFC or Bluetooth? \- Quora, 访问时间为 六月 14, 2025， [https://www.quora.com/Is-it-possible-to-transfer-files-between-Android-and-iPhone-without-using-any-apps-using-only-NFC-or-Bluetooth](https://www.quora.com/Is-it-possible-to-transfer-files-between-Android-and-iPhone-without-using-any-apps-using-only-NFC-or-Bluetooth)
39. Move from Android to iPhone or iPad \- Apple Support, 访问时间为 六月 14, 2025， [https://support.apple.com/en-lamr/118670](https://support.apple.com/en-lamr/118670)
40. How to Transfer Files from iPhone to Android via Bluetooth \- Wondershare MobileTrans, 访问时间为 六月 14, 2025， [https://mobiletrans.wondershare.com/iphone-transfer/transfer-files-from-iphone-to-android-via-bluethooth.html](https://mobiletrans.wondershare.com/iphone-transfer/transfer-files-from-iphone-to-android-via-bluethooth.html)
41. Handling Optimized Complex Synchronization on Mobile Devices \- OutSystems 11 Documentation, 访问时间为 六月 14, 2025， [https://success.outsystems.com/documentation/11/building\_apps/data\_management/mobile\_performance\_strategies\_and\_offline\_optimization/handling\_optimized\_complex\_synchronization\_on\_mobile\_devices/](https://success.outsystems.com/documentation/11/building_apps/data_management/mobile_performance_strategies_and_offline_optimization/handling_optimized_complex_synchronization_on_mobile_devices/)
42. Offline data synchronization \- IBM Developer, 访问时间为 六月 14, 2025， [https://developer.ibm.com/articles/offline-data-synchronization-strategies/](https://developer.ibm.com/articles/offline-data-synchronization-strategies/)
43. Best Data Synchronization Strategies for Enterprise IT Data Patterns \- ONEiO Cloud, 访问时间为 六月 14, 2025， [https://www.oneio.cloud/blog/data-sync-strategy](https://www.oneio.cloud/blog/data-sync-strategy)
44. Top 10 Ethical Considerations for AI Projects | PMI Blog, 访问时间为 六月 14, 2025， [https://www.pmi.org/blog/top-10-ethical-considerations-for-ai-projects](https://www.pmi.org/blog/top-10-ethical-considerations-for-ai-projects)
45. Psychologists Highlight Ethical Concerns in Human-AI Relationships \- Bioengineer.org, 访问时间为 六月 14, 2025， [https://bioengineer.org/psychologists-highlight-ethical-concerns-in-human-ai-relationships/](https://bioengineer.org/psychologists-highlight-ethical-concerns-in-human-ai-relationships/)
46. Psychologists explore ethical issues associated with human-AI relationships, 访问时间为 六月 14, 2025， [https://www.news-medical.net/news/20250411/Psychologists-explore-ethical-issues-associated-with-human-AI-relationships.aspx](https://www.news-medical.net/news/20250411/Psychologists-explore-ethical-issues-associated-with-human-AI-relationships.aspx)
47. Is it ethical to create an AI-based companion for the purpose of finding a romantic partner?, 访问时间为 六月 14, 2025， [https://www.quora.com/Is-it-ethical-to-create-an-AI-based-companion-for-the-purpose-of-finding-a-romantic-partner](https://www.quora.com/Is-it-ethical-to-create-an-AI-based-companion-for-the-purpose-of-finding-a-romantic-partner)
