#!/usr/bin/env python3
"""
Copilot E2E Test Suite
Comprehensive end-to-end testing of LLM-powered Copilot functionality
"""

import requests
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Any, List
import sys
import os

BASE_URL = "http://localhost:8010"

class CopilotE2ETest:
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.auth_token = None
        self.conversation_id = None
        self.test_results = []
        self.created_resources = {
            "tasks": [],
            "goals": [],
            "persons": []
        }
        
    def log_test(self, test_name: str, success: bool, details: Dict[str, Any] = None):
        """Log test result"""
        result = {
            "test_name": test_name,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details or {}
        }
        self.test_results.append(result)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details and not success:
            print(f"   Error: {details.get('error', 'Unknown error')}")

    def setup_test_environment(self) -> bool:
        """Setup test environment with authentication and test data"""
        try:
            print("🔧 Setting up test environment...")
            
            # 1. Check API health
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code != 200:
                self.log_test("API Health Check", False, {"error": f"API not healthy: {response.status_code}"})
                return False
            self.log_test("API Health Check", True)
            
            # 2. Register/Login test user
            test_email = f"copilot.e2e.{int(time.time())}@example.com"
            user_data = {
                "email": test_email,
                "password": "TestPassword123!",
                "first_name": "Copilot",
                "last_name": "E2E"
            }
            
            # Register using local endpoint
            register_response = self.session.post(f"{self.base_url}/api/v1/auth/register-local", json=user_data)

            # Login using local endpoint
            login_data = {
                "email": test_email,
                "password": "TestPassword123!"
            }
            response = self.session.post(
                f"{self.base_url}/api/v1/auth/login-local",
                json=login_data
            )
            
            if response.status_code == 200:
                self.auth_token = response.json().get("access_token")
                self.log_test("User Authentication", True)
            else:
                self.log_test("User Authentication", False, {"error": response.text})
                return False
            
            # 3. Create test persons for network
            self.create_test_network()
            
            return True
            
        except Exception as e:
            self.log_test("Environment Setup", False, {"error": str(e)})
            return False

    def create_test_network(self):
        """Create test persons for network operations"""
        test_persons = [
            {
                "first_name": "Alice",
                "last_name": "Johnson",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "company": "TechCorp",
                    "title": "Software Engineer",
                    "industry": "Technology"
                },
                "personal_details": {
                    "tags": ["colleague", "tech"],
                    "notes": "Met at tech conference"
                }
            },
            {
                "first_name": "Bob",
                "last_name": "Smith",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "company": "AI Company",
                    "title": "ML Engineer",
                    "industry": "Artificial Intelligence"
                },
                "personal_details": {
                    "tags": ["ai", "machine-learning"],
                    "notes": "AI expert"
                }
            },
            {
                "first_name": "Carol",
                "last_name": "Davis",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "company": "Startup Inc",
                    "title": "Product Manager",
                    "industry": "Technology"
                },
                "personal_details": {
                    "tags": ["product", "startup"],
                    "notes": "Product management expert"
                }
            }
        ]
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        for person_data in test_persons:
            try:
                response = self.session.post(
                    f"{self.base_url}/api/v1/persons",
                    json=person_data,
                    headers=headers
                )
                if response.status_code in [200, 201]:
                    person = response.json()
                    self.created_resources["persons"].append(person["person_id"])
            except Exception as e:
                print(f"Warning: Failed to create test person: {e}")

    def get_auth_headers(self) -> Dict[str, str]:
        """Get authorization headers"""
        return {"Authorization": f"Bearer {self.auth_token}"}

    def test_basic_conversation(self) -> bool:
        """Test basic conversation functionality"""
        try:
            message_data = {
                "message": "Hello! I'm testing the copilot functionality. Can you help me?"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/copilot/converse",
                json=message_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                self.conversation_id = data.get("conversation_id")
                
                # Verify response structure
                required_fields = ["response", "conversation_id", "timestamp"]
                missing_fields = [field for field in required_fields if field not in data]
                
                if missing_fields:
                    self.log_test("Basic Conversation", False, 
                                {"error": f"Missing fields: {missing_fields}"})
                    return False
                
                self.log_test("Basic Conversation", True, {
                    "conversation_id": self.conversation_id,
                    "response_length": len(data.get("response", ""))
                })
                return True
            else:
                self.log_test("Basic Conversation", False, 
                            {"error": f"HTTP {response.status_code}: {response.text}"})
                return False
                
        except Exception as e:
            self.log_test("Basic Conversation", False, {"error": str(e)})
            return False

    def test_network_search_with_function_calling(self) -> bool:
        """Test network search with LLM function calling"""
        try:
            message_data = {
                "message": "Can you search my network for people who work in technology companies?",
                "conversation_id": self.conversation_id
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/copilot/converse",
                json=message_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Check if function calls were made
                tool_calls = data.get("tool_calls", [])
                search_tool_called = any(tc.get("name") == "search_network" for tc in tool_calls)
                
                if not search_tool_called:
                    self.log_test("Network Search Function Calling", False, 
                                {"error": "search_network tool was not called"})
                    return False
                
                # Check tool call results
                search_results = None
                for tc in tool_calls:
                    if tc.get("name") == "search_network" and tc.get("status") == "success":
                        search_results = tc.get("result")
                        break
                
                self.log_test("Network Search Function Calling", True, {
                    "tool_calls_count": len(tool_calls),
                    "search_results_found": bool(search_results),
                    "response_includes_results": "found" in data.get("response", "").lower()
                })
                return True
            else:
                self.log_test("Network Search Function Calling", False, 
                            {"error": f"HTTP {response.status_code}: {response.text}"})
                return False
                
        except Exception as e:
            self.log_test("Network Search Function Calling", False, {"error": str(e)})
            return False

    def test_task_creation_with_llm(self) -> bool:
        """Test task creation through LLM conversation"""
        try:
            message_data = {
                "message": "I need you to create a task reminder: Follow up with Alice Johnson about the tech conference discussion next week. Set the due date for next Friday.",
                "conversation_id": self.conversation_id
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/copilot/converse",
                json=message_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Check if create_task function was called
                tool_calls = data.get("tool_calls", [])
                task_tool_called = any(tc.get("name") == "create_task" for tc in tool_calls)
                
                if not task_tool_called:
                    self.log_test("Task Creation with LLM", False, 
                                {"error": "create_task tool was not called"})
                    return False
                
                # Check if task was actually created
                task_created = False
                task_id = None
                for tc in tool_calls:
                    if tc.get("name") == "create_task" and tc.get("status") == "success":
                        result = tc.get("result", {})
                        if result.get("created"):
                            task_created = True
                            task_id = result.get("task_id")
                            self.created_resources["tasks"].append(task_id)
                            break
                
                self.log_test("Task Creation with LLM", task_created, {
                    "task_id": task_id,
                    "tool_calls_count": len(tool_calls),
                    "response_confirms_creation": "created" in data.get("response", "").lower()
                })
                return task_created
            else:
                self.log_test("Task Creation with LLM", False, 
                            {"error": f"HTTP {response.status_code}: {response.text}"})
                return False
                
        except Exception as e:
            self.log_test("Task Creation with LLM", False, {"error": str(e)})
            return False

    def test_intent_analysis_endpoint(self) -> bool:
        """Test the intent analysis endpoint"""
        try:
            message_data = {
                "message": "I need to find connections at Google and create a reminder to reach out to them"
            }

            response = self.session.post(
                f"{self.base_url}/api/v1/copilot/analyze",
                json=message_data,
                headers=self.get_auth_headers()
            )

            if response.status_code == 200:
                data = response.json()

                # Check response structure
                required_fields = ["intent", "confidence", "entities"]
                missing_fields = [field for field in required_fields if field not in data]

                if missing_fields:
                    self.log_test("Intent Analysis Endpoint", False,
                                {"error": f"Missing fields: {missing_fields}"})
                    return False

                # Check if intent was properly identified
                intent_identified = data.get("intent") != "unknown"
                confidence_reasonable = data.get("confidence", 0) > 0.3

                self.log_test("Intent Analysis Endpoint", intent_identified and confidence_reasonable, {
                    "intent": data.get("intent"),
                    "confidence": data.get("confidence"),
                    "entities_count": len(data.get("entities", []))
                })
                return intent_identified and confidence_reasonable
            else:
                self.log_test("Intent Analysis Endpoint", False,
                            {"error": f"HTTP {response.status_code}: {response.text}"})
                return False

        except Exception as e:
            self.log_test("Intent Analysis Endpoint", False, {"error": str(e)})
            return False

    def test_enhanced_suggestions_endpoint(self) -> bool:
        """Test the enhanced AI suggestions endpoint"""
        try:
            response = self.session.get(
                f"{self.base_url}/api/v1/copilot/suggestions?limit=5",
                headers=self.get_auth_headers()
            )

            if response.status_code == 200:
                data = response.json()

                # Check if we got suggestions
                suggestions = data.get("suggestions", [])
                has_suggestions = len(suggestions) > 0

                # Check if LLM recommendations are included
                has_llm_recommendations = "llm_recommendations" in data

                self.log_test("Enhanced Suggestions Endpoint", True, {
                    "suggestions_count": len(suggestions),
                    "has_llm_recommendations": has_llm_recommendations,
                    "has_network_insights": "network_insights" in data
                })
                return True
            else:
                self.log_test("Enhanced Suggestions Endpoint", False,
                            {"error": f"HTTP {response.status_code}: {response.text}"})
                return False

        except Exception as e:
            self.log_test("Enhanced Suggestions Endpoint", False, {"error": str(e)})
            return False

    def test_conversation_history_management(self) -> bool:
        """Test conversation history endpoints"""
        try:
            if not self.conversation_id:
                self.log_test("Conversation History Management", False,
                            {"error": "No conversation ID available"})
                return False

            # Test getting conversation history
            response = self.session.get(
                f"{self.base_url}/api/v1/copilot/conversation/{self.conversation_id}",
                headers=self.get_auth_headers()
            )

            if response.status_code == 200:
                data = response.json()
                messages = data.get("messages", [])

                # Should have some messages from previous tests
                has_messages = len(messages) > 0

                self.log_test("Conversation History Management", has_messages, {
                    "conversation_id": self.conversation_id,
                    "messages_count": len(messages),
                    "has_message_structure": all("role" in msg and "content" in msg for msg in messages[:3])
                })
                return has_messages
            else:
                self.log_test("Conversation History Management", False,
                            {"error": f"HTTP {response.status_code}: {response.text}"})
                return False

        except Exception as e:
            self.log_test("Conversation History Management", False, {"error": f"Exception: {str(e)}"})
            return False

    def test_multi_turn_conversation_context(self) -> bool:
        """Test multi-turn conversation with context preservation"""
        try:
            # First message - establish context
            message1_data = {
                "message": "I'm looking for people in the AI field",
                "conversation_id": self.conversation_id
            }

            response1 = self.session.post(
                f"{self.base_url}/api/v1/copilot/converse",
                json=message1_data,
                headers=self.get_auth_headers()
            )

            if response1.status_code != 200:
                self.log_test("Multi-turn Conversation Context", False,
                            {"error": f"First message failed: {response1.status_code}"})
                return False

            # Second message - reference previous context
            message2_data = {
                "message": "Can you create a task to reach out to them?",
                "conversation_id": self.conversation_id
            }

            response2 = self.session.post(
                f"{self.base_url}/api/v1/copilot/converse",
                json=message2_data,
                headers=self.get_auth_headers()
            )

            if response2.status_code == 200:
                data2 = response2.json()

                # Check if the context was understood (should create task related to AI people)
                tool_calls = data2.get("tool_calls", [])
                task_created = any(tc.get("name") == "create_task" for tc in tool_calls)

                # Check if response shows understanding of context
                response_text = data2.get("response", "").lower()
                context_understood = any(word in response_text for word in ["ai", "them", "people", "contacts"])

                self.log_test("Multi-turn Conversation Context", task_created or context_understood, {
                    "task_tool_called": task_created,
                    "context_in_response": context_understood,
                    "tool_calls_count": len(tool_calls)
                })
                return task_created or context_understood
            else:
                self.log_test("Multi-turn Conversation Context", False,
                            {"error": f"Second message failed: {response2.status_code}"})
                return False

        except Exception as e:
            self.log_test("Multi-turn Conversation Context", False, {"error": str(e)})
            return False

    def test_complex_multi_tool_request(self) -> bool:
        """Test complex request that should trigger multiple tools"""
        try:
            message_data = {
                "message": "Can you analyze my network health, show me my recent tasks, and suggest some networking goals?",
                "conversation_id": self.conversation_id
            }

            response = self.session.post(
                f"{self.base_url}/api/v1/copilot/converse",
                json=message_data,
                headers=self.get_auth_headers()
            )

            if response.status_code == 200:
                data = response.json()

                tool_calls = data.get("tool_calls", [])
                tool_names = [tc.get("name") for tc in tool_calls]

                # Should call multiple relevant tools
                expected_tools = ["get_network_health", "get_recent_tasks", "get_ai_suggestions"]
                tools_called = sum(1 for tool in expected_tools if tool in tool_names)

                # At least 2 of the 3 expected tools should be called
                success = tools_called >= 2

                self.log_test("Complex Multi-tool Request", success, {
                    "tools_called": tool_names,
                    "expected_tools_hit": tools_called,
                    "total_tool_calls": len(tool_calls)
                })
                return success
            else:
                self.log_test("Complex Multi-tool Request", False,
                            {"error": f"HTTP {response.status_code}: {response.text}"})
                return False

        except Exception as e:
            self.log_test("Complex Multi-tool Request", False, {"error": str(e)})
            return False

    def test_error_handling_and_fallback(self) -> bool:
        """Test error handling and fallback behavior"""
        try:
            # Test with invalid/nonsensical request
            message_data = {
                "message": "xyz123 invalid request with random data !@#$%",
                "conversation_id": self.conversation_id
            }

            response = self.session.post(
                f"{self.base_url}/api/v1/copilot/converse",
                json=message_data,
                headers=self.get_auth_headers()
            )

            if response.status_code == 200:
                data = response.json()

                # Should still get a response (graceful handling)
                has_response = bool(data.get("response"))
                no_errors_in_response = "error" not in data or not data.get("error")

                self.log_test("Error Handling and Fallback", has_response and no_errors_in_response, {
                    "has_response": has_response,
                    "response_length": len(data.get("response", "")),
                    "tool_calls_count": len(data.get("tool_calls", []))
                })
                return has_response and no_errors_in_response
            else:
                self.log_test("Error Handling and Fallback", False,
                            {"error": f"HTTP {response.status_code}: {response.text}"})
                return False

        except Exception as e:
            self.log_test("Error Handling and Fallback", False, {"error": str(e)})
            return False

    def cleanup_test_resources(self):
        """Clean up created test resources"""
        try:
            headers = self.get_auth_headers()

            # Clean up tasks
            for task_id in self.created_resources["tasks"]:
                try:
                    self.session.delete(f"{self.base_url}/api/v1/tasks/{task_id}", headers=headers)
                except:
                    pass

            # Clean up goals
            for goal_id in self.created_resources["goals"]:
                try:
                    self.session.delete(f"{self.base_url}/api/v1/goals/{goal_id}", headers=headers)
                except:
                    pass

            # Clean up persons
            for person_id in self.created_resources["persons"]:
                try:
                    self.session.delete(f"{self.base_url}/api/v1/persons/{person_id}", headers=headers)
                except:
                    pass

            print("🧹 Test resources cleaned up")

        except Exception as e:
            print(f"⚠️  Warning: Failed to clean up some test resources: {e}")

    def save_test_report(self):
        """Save detailed test report to file"""
        try:
            report = {
                "test_suite": "Copilot E2E Tests",
                "timestamp": datetime.now().isoformat(),
                "total_tests": len(self.test_results),
                "passed_tests": sum(1 for r in self.test_results if r["success"]),
                "failed_tests": sum(1 for r in self.test_results if not r["success"]),
                "test_results": self.test_results,
                "environment": {
                    "base_url": self.base_url,
                    "conversation_id": self.conversation_id
                },
                "created_resources": self.created_resources
            }

            filename = f"copilot_e2e_test_report_{int(time.time())}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            print(f"📊 Test report saved to: {filename}")
            return filename

        except Exception as e:
            print(f"⚠️  Failed to save test report: {e}")
            return None

    def run_all_tests(self) -> Dict[str, Any]:
        """Run complete E2E test suite"""
        print("🚀 Starting Copilot E2E Test Suite")
        print("=" * 60)

        # Setup
        if not self.setup_test_environment():
            print("❌ Failed to setup test environment")
            return {"success": False, "error": "Environment setup failed"}

        # Define test methods
        test_methods = [
            ("Basic Conversation", self.test_basic_conversation),
            ("Network Search Function Calling", self.test_network_search_with_function_calling),
            ("Task Creation with LLM", self.test_task_creation_with_llm),
            ("Network Health Analysis", self.test_network_health_analysis),
            ("Goal Creation with Context", self.test_goal_creation_with_context),
            ("Intent Analysis Endpoint", self.test_intent_analysis_endpoint),
            ("Enhanced Suggestions Endpoint", self.test_enhanced_suggestions_endpoint),
            ("Conversation History Management", self.test_conversation_history_management),
            ("Multi-turn Conversation Context", self.test_multi_turn_conversation_context),
            ("Complex Multi-tool Request", self.test_complex_multi_tool_request),
            ("Error Handling and Fallback", self.test_error_handling_and_fallback)
        ]

        # Run tests
        for test_name, test_method in test_methods:
            try:
                print(f"\n🧪 Running: {test_name}")
                test_method()
                time.sleep(1)  # Brief pause between tests
            except Exception as e:
                self.log_test(test_name, False, {"error": f"Test execution failed: {str(e)}"})

        # Cleanup and reporting
        self.cleanup_test_resources()
        report_file = self.save_test_report()

        # Summary
        passed = sum(1 for r in self.test_results if r["success"])
        total = len(self.test_results)

        print("\n" + "=" * 60)
        print("📊 E2E TEST SUMMARY")
        print("=" * 60)

        for result in self.test_results:
            status = "✅" if result["success"] else "❌"
            print(f"{status} {result['test_name']}")
            if not result["success"] and result["details"].get("error"):
                print(f"   └─ {result['details']['error']}")

        print(f"\n🎯 Results: {passed}/{total} tests passed")
        print(f"📊 Success Rate: {(passed/total*100):.1f}%")

        if report_file:
            print(f"📄 Detailed report: {report_file}")

        return {
            "success": passed == total,
            "passed": passed,
            "total": total,
            "success_rate": passed/total*100,
            "report_file": report_file,
            "test_results": self.test_results
        }

    def test_network_health_analysis(self) -> bool:
        """Test network health analysis functionality"""
        try:
            message_data = {
                "message": "Can you analyze my network health and give me insights about my professional connections?",
                "conversation_id": self.conversation_id
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/copilot/converse",
                json=message_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Check if network health function was called
                tool_calls = data.get("tool_calls", [])
                health_tool_called = any(tc.get("name") == "get_network_health" for tc in tool_calls)
                
                if not health_tool_called:
                    self.log_test("Network Health Analysis", False, 
                                {"error": "get_network_health tool was not called"})
                    return False
                
                # Check if analysis was successful
                analysis_successful = False
                for tc in tool_calls:
                    if tc.get("name") == "get_network_health" and tc.get("status") == "success":
                        result = tc.get("result", {})
                        if "overall_health_score" in result or "network_size" in result:
                            analysis_successful = True
                            break
                
                self.log_test("Network Health Analysis", analysis_successful, {
                    "tool_calls_count": len(tool_calls),
                    "analysis_in_response": any(word in data.get("response", "").lower() 
                                               for word in ["health", "network", "analysis", "score"])
                })
                return analysis_successful
            else:
                self.log_test("Network Health Analysis", False, 
                            {"error": f"HTTP {response.status_code}: {response.text}"})
                return False
                
        except Exception as e:
            self.log_test("Network Health Analysis", False, {"error": str(e)})
            return False

    def test_goal_creation_with_context(self) -> bool:
        """Test goal creation with contextual understanding"""
        try:
            message_data = {
                "message": "I want to set a goal to connect with 5 new people in the AI industry by the end of this month",
                "conversation_id": self.conversation_id
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/copilot/converse",
                json=message_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Check if create_goal function was called
                tool_calls = data.get("tool_calls", [])
                goal_tool_called = any(tc.get("name") == "create_goal" for tc in tool_calls)
                
                if not goal_tool_called:
                    self.log_test("Goal Creation with Context", False, 
                                {"error": "create_goal tool was not called"})
                    return False
                
                # Check if goal was created successfully
                goal_created = False
                goal_id = None
                for tc in tool_calls:
                    if tc.get("name") == "create_goal" and tc.get("status") == "success":
                        result = tc.get("result", {})
                        if result.get("created"):
                            goal_created = True
                            goal_id = result.get("goal_id")
                            self.created_resources["goals"].append(goal_id)
                            break
                
                self.log_test("Goal Creation with Context", goal_created, {
                    "goal_id": goal_id,
                    "tool_calls_count": len(tool_calls),
                    "context_understood": "AI" in str(tool_calls) or "5" in str(tool_calls)
                })
                return goal_created
            else:
                self.log_test("Goal Creation with Context", False, 
                            {"error": f"HTTP {response.status_code}: {response.text}"})
                return False
                
        except Exception as e:
            self.log_test("Goal Creation with Context", False, {"error": str(e)})
            return False

def main():
    """Main test runner"""
    print("🤖 Copilot LLM E2E Test Suite")
    print("Testing comprehensive LLM-powered functionality")
    print("=" * 60)

    tester = CopilotE2ETest()
    results = tester.run_all_tests()

    if results.get("success"):
        print("\n🎉 All E2E tests passed! Copilot LLM functionality is working correctly.")
        return 0
    else:
        failed_count = results.get('total', 0) - results.get('passed', 0)
        print(f"\n⚠️  {failed_count} test(s) failed.")
        print("Check the detailed report for more information.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
