#!/usr/bin/env python3
"""
Quick test to verify the authentication fixes
"""
import requests
import json

def test_auth():
    base_url = "http://localhost:8010"
    
    # Test health endpoint
    try:
        health_response = requests.get(f"{base_url}/health", timeout=5)
        print(f"Health check: {health_response.status_code} - {health_response.text}")
    except Exception as e:
        print(f"Health check failed: {e}")
        return False
    
    # Test registration
    try:
        register_data = {
            "email": "<EMAIL>",
            "password": "testpass123",
            "first_name": "Quick",
            "last_name": "Test"
        }
        register_response = requests.post(
            f"{base_url}/api/v1/auth/register-local", 
            json=register_data,
            timeout=10
        )
        print(f"Registration: {register_response.status_code}")
        if register_response.status_code not in [200, 201, 409]:
            print(f"Registration failed: {register_response.text}")
            return False
    except Exception as e:
        print(f"Registration failed: {e}")
        return False
    
    # Test login
    try:
        login_data = {
            "email": "<EMAIL>",
            "password": "testpass123"
        }
        login_response = requests.post(
            f"{base_url}/api/v1/auth/login-local",
            json=login_data,
            timeout=10
        )
        print(f"Login: {login_response.status_code}")
        if login_response.status_code == 200:
            login_result = login_response.json()
            print(f"Login successful! Token: {login_result.get('access_token', 'N/A')[:20]}...")
            return True
        else:
            print(f"Login failed: {login_response.text}")
            return False
    except Exception as e:
        print(f"Login failed: {e}")
        return False

if __name__ == "__main__":
    print("Running quick authentication test...")
    success = test_auth()
    if success:
        print("✅ Authentication test passed!")
    else:
        print("❌ Authentication test failed!")
