# App Directory - Nexus Backend Application Code

This directory contains the main application code for the Nexus relationship management platform backend.

## Architecture Overview

The Nexus backend implements a sophisticated relationship management platform with advanced AI capabilities, featuring:
- **AI-Powered Networking Intelligence**: OpenAI GPT-4 integration with fallback systems
- **Stateless LLM Service**: Database-backed conversation history for multi-instance deployment
- **Graph-Based Network Analysis**: NetworkX algorithms for pathfinding and network health
- **Advanced Search**: PostgreSQL pg_trgm fuzzy search with sub-50ms performance
- **Multi-Format Data Portability**: JSON, CSV, vCard export/import with background processing

## Directory Structure

### `api/` - FastAPI API Layer
- **`v1/endpoints/`** - RESTful API endpoint implementations
  - `auth.py` - JWT authentication and user management
  - `persons.py` - Contact management with relationship tracking
  - `organizations.py` - Organization CRUD with work relationship management
  - `relationships.py` - Six-dimensional relationship management with archetypes
  - `goals.py` - AI-powered goal tracking and achievement analysis
  - `ai_engine.py` - AI-powered insights and networking recommendations
  - `graph.py` - Network visualization and graph data generation
  - `search.py` - Advanced fuzzy search with relevance scoring
  - `data_portability.py` - Multi-format data export/import system
  - `copilot.py` - AI chat interface for networking guidance
  - `integrations.py` - External service integrations (Google Calendar/Contacts)
- **`deps.py`** - Dependency injection (authentication, database sessions)

### `core/` - Core System Components
- **`config.py`** - Environment configuration and settings management
- **`database.py`** - SQLAlchemy database connection and session management
- **`security.py`** - JWT authentication, password hashing, security utilities
- **`llm_service.py`** - Stateless LLM service with database-backed conversation history
- **`ai_utils.py`** - OpenAI integration utilities and AI service abstractions
- **`db_types.py`** - Database type definitions for cross-platform compatibility

### `models/` - SQLAlchemy Database Models
- **`user.py`** - User accounts and authentication
- **`person.py`** - Individual contacts with detailed information storage
- **`organization.py`** - Companies and groups with work relationship tracking
- **`relationship.py`** - Six-dimensional relationship model (Knows table)
- **`goal.py`** - Strategic objectives with AI analysis integration
- **`task.py`** - Action items and goal-related tasks
- **`interaction.py`** - Communication records and relationship activity
- **`note.py`** - Notes and annotations with entity associations
- **`tag.py`** - Tagging system for categorization
- **`data_job.py`** - Background job tracking for data operations
- **`conversation.py`** - Conversation and message models for stateless LLM service

### `schemas/` - Pydantic API Validation Schemas
- **`auth.py`** - Authentication schemas (login, register, tokens)
- **`user.py`** - User profile management schemas
- **`person.py`** - Contact management with validation rules
- **`organization.py`** - Organization and work relationship schemas
- **`relationship.py`** - Six-dimensional relationship validation
- **`goal.py`** - Goal tracking with AI analysis schemas
- **`task.py`** - Task management schemas
- **`data_portability.py`** - Export/import validation with format support

### `services/` - Business Logic Layer
- **`ai_engine_service.py`** - Core AI functionality for relationship analysis
- **`search_service.py`** - Advanced fuzzy search with PostgreSQL pg_trgm
- **`graph_service.py`** - Network graph generation and visualization data
- **`pathfinding_service.py`** - Dijkstra/A* algorithms for referral path discovery
- **`network_health_service.py`** - Comprehensive network health diagnosis
- **`relationship_service.py`** - Relationship management with archetype system
- **`goal_service.py`** - AI-powered goal tracking and achievement prediction
- **`organization_service.py`** - Organization management with statistical analysis
- **`data_export_service.py`** - Multi-format data export with background processing
- **`data_import_service.py`** - Data import with validation and duplicate handling
- **`background_job_service.py`** - Async job processing system
- **`person_service.py`** - Contact management business logic
- **`user_service.py`** - User account management
- **`intelligent_copilot.py`** - Stateless intelligent AI copilot service
- **`copilot_tools.py`** - Function tools for LLM with user isolation
- **`base_service.py`** - Base service class with common CRUD operations

## Key Features Implemented

### ✅ Advanced Search Engine
- **PostgreSQL pg_trgm Extension**: Fuzzy search with similarity scoring
- **GIN Indexes**: Sub-50ms query performance for large datasets
- **Multi-Field Search**: Across persons, organizations, and relationships
- **Relevance Ranking**: Intelligent result ordering with relationship weighting

### ✅ AI-Powered Relationship Analysis
- **OpenAI GPT-4 Integration**: Advanced relationship insights and strategy
- **Six-Dimensional Scoring**: Comprehensive relationship depth measurement
- **Proactive Suggestions**: AI-generated networking recommendations
- **Fallback Systems**: Rule-based algorithms when AI unavailable

### ✅ Graph-Based Network Intelligence
- **NetworkX Integration**: Sophisticated graph algorithms and analysis
- **Community Detection**: Leiden algorithm for network clustering
- **Centrality Analysis**: Betweenness, closeness, eigenvector centrality
- **Real-Time Visualization**: Dynamic graph data generation

### ✅ Strategic Pathfinding
- **Dijkstra Algorithm**: Shortest path discovery for referrals
- **A* Search**: Goal-oriented pathfinding with heuristics
- **Multiple Optimization**: Shortest, strongest, balanced path strategies
- **Success Prediction**: AI-enhanced path success probability

### ✅ Comprehensive CRUD Operations
- **Complete Entity Management**: Persons, organizations, relationships, goals
- **Archetype System**: Template-based relationship creation
- **Data Validation**: Comprehensive input validation and sanitization
- **Audit Trails**: Complete history tracking for all entities

### ✅ Network Health Diagnosis
- **15+ Health Metrics**: Activity, diversity, centrality, growth indicators
- **Shannon Entropy**: Network diversity measurement
- **Actionable Insights**: AI-generated improvement recommendations
- **Trend Analysis**: Historical health tracking

### ✅ Goal Management with AI
- **Strategy Analysis**: AI-powered goal achievement strategies
- **Person Matching**: Goal-relevant connection identification
- **Success Prediction**: Multi-factor success probability modeling
- **Progress Tracking**: Milestone management and velocity analysis

### ✅ Data Portability System
- **Multi-Format Support**: JSON, CSV, vCard export/import
- **Background Processing**: Async job system with progress tracking
- **Custom Mappings**: Flexible field mapping for data transformation
- **Validation Modes**: Strict/lenient validation with comprehensive error handling

### ✅ Stateless Conversation System
- **Database-Backed History**: Persistent conversation storage for multi-instance deployment
- **User Data Isolation**: Complete separation of conversation data between users
- **Tool Call Persistence**: Complex AI function calls stored and retrievable
- **Zero Memory State**: No in-memory conversation storage, fully stateless service
- **Horizontal Scaling**: Load balancer compatible without session affinity

## Development Patterns

### Service Layer Pattern
```python
class BaseService:
    def __init__(self, db: Session):
        self.db = db
    
    def create(self, obj_in: CreateSchema) -> Model:
        # Standardized creation pattern
    
    def get_by_user(self, user_id: UUID) -> List[Model]:
        # User-isolated data access
```

### AI Integration Pattern
```python
def get_ai_analysis(self, context: Dict[str, Any]) -> Dict[str, Any]:
    if self.openai_client:
        try:
            return self.openai_client.analyze(context)
        except Exception:
            return self._get_fallback_analysis(context)
    return self._get_fallback_analysis(context)
```

### Error Handling Pattern
```python
try:
    result = self.complex_operation()
    return {"success": True, "data": result}
except ValueError as e:
    logger.warning(f"Validation error: {e}")
    raise HTTPException(status_code=400, detail=str(e))
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    raise HTTPException(status_code=500, detail="Internal server error")
```

## Performance Characteristics

- **API Response Times**: < 100ms for most endpoints
- **Database Queries**: Sub-50ms with proper indexing
- **AI Processing**: 2-5 seconds with intelligent caching
- **Background Jobs**: Async processing with progress tracking
- **Memory Usage**: Optimized algorithms with efficient data structures

## Security Implementation

- **User Data Isolation**: Complete separation between user accounts
- **Input Validation**: Comprehensive Pydantic schema validation
- **SQL Injection Prevention**: Parameterized queries and ORM protection
- **Authentication**: JWT-based with secure token management
- **Error Sanitization**: Secure error responses without data leakage

## Usage

For complete development commands and Task Master integration, refer to the main CLAUDE.md file in the project root.