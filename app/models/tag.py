"""
Tag models - represents tags and tagging system
"""

import uuid

from sqlalchemy import (Column, DateTime, ForeignKey, Index, String,
                        UniqueConstraint)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base
from app.core.db_types import get_uuid_type, get_json_type


class Tag(Base):
    """
    Tag model representing user-defined tags
    """

    __tablename__ = "tags"

    # Primary key
    tag_id = Column(get_uuid_type(), primary_key=True, default=uuid.uuid4)

    # Foreign key to user (owner of this tag)
    user_id = Column(
        get_uuid_type(), ForeignKey("users.user_id"), nullable=False, index=True
    )

    # Tag name
    name = Column(String(100), nullable=False)

    # Optional description
    description = Column(String(500))

    # Optional color for UI
    color = Column(String(7))  # Hex color code like #FF5733

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", backref="tags")

    # Unique constraint: user can't have duplicate tag names
    __table_args__ = (
        UniqueConstraint("user_id", "name", name="uq_user_tag_name"),
        Index("idx_tag_user_name", "user_id", "name"),
    )

    def __repr__(self):
        return f"<Tag(name='{self.name}', user_id='{self.user_id}')>"


class Tagging(Base):
    """
    Polymorphic tagging association table
    Allows tagging of any entity (Person, Goal, Task, Note, etc.)
    """

    __tablename__ = "taggings"

    # Composite primary key
    tag_id = Column(get_uuid_type(), ForeignKey("tags.tag_id"), primary_key=True)
    taggable_id = Column(
        get_uuid_type(), primary_key=True
    )  # ID of the tagged entity
    taggable_type = Column(
        String(50), primary_key=True
    )  # Type of the tagged entity (e.g., 'Person', 'Goal')

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    tag = relationship("Tag", backref="taggings")

    # Indexes for performance
    __table_args__ = (
        Index("idx_tagging_taggable", "taggable_type", "taggable_id"),
        Index("idx_tagging_tag", "tag_id"),
    )

    def __repr__(self):
        return f"<Tagging(tag_id='{self.tag_id}', taggable_type='{self.taggable_type}', taggable_id='{self.taggable_id}')>"


class UserActionLog(Base):
    """
    User action log for tracking user behavior and learning preferences
    """

    __tablename__ = "user_action_logs"

    # Primary key
    log_id = Column(get_uuid_type(), primary_key=True, default=uuid.uuid4)

    # Foreign key to user
    user_id = Column(
        get_uuid_type(), ForeignKey("users.user_id"), nullable=False, index=True
    )

    # Action type (e.g., 'TASK_COMPLETED', 'RELATIONSHIP_SCORED', 'GOAL_CREATED')
    action_type = Column(String(100), nullable=False, index=True)

    # Action details (JSON with context data)
    details = Column(get_json_type(), default={})
    # Example structure:
    # {
    #   "entity_type": "task",
    #   "entity_id": "task_uuid_123",
    #   "action_data": {
    #     "priority_before": 3,
    #     "priority_after": 5,
    #     "related_people": ["person_id_1"]
    #   }
    # }

    # Timestamp
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)

    # Relationships
    user = relationship("User", backref="action_logs")

    # Indexes for performance
    __table_args__ = (
        Index("idx_action_log_user_type", "user_id", "action_type"),
        Index("idx_action_log_created", "created_at"),
    )

    def __repr__(self):
        return f"<UserActionLog(action_type='{self.action_type}', user_id='{self.user_id}')>"
