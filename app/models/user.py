"""
User model - represents authenticated users of the system
"""

import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, String, Text
from sqlalchemy.sql import func

from app.core.database import Base
from app.core.db_types import get_json_type, get_uuid_type


class User(Base):
    """User model for authentication and basic user info"""

    __tablename__ = "users"

    # Use a default UUID for the primary key
    user_id = Column(get_uuid_type(), primary_key=True, default=uuid.uuid4)

    # Authentication fields
    email = Column(String(255), unique=True, index=True, nullable=False)
    # Nullable, since password is not stored locally when using Supabase
    hashed_password = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)

    # Basic profile
    first_name = Column(String(100))
    last_name = Column(String(100))

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))

    # Settings and preferences
    settings = Column(get_json_type(), default={})

    def __repr__(self):
        return (
            f"<User(email='{self.email}', name='{self.first_name} {self.last_name}')>"
        )
