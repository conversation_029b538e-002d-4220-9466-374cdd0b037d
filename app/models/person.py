"""
Person model - represents individuals in the user's network
Based on the technical documentation ER diagram
"""

import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, Foreign<PERSON>ey, String, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base
from app.core.db_types import get_uuid_type, get_json_type


class Person(Base):
    """
    Person model representing individuals in the user's network
    This is the core entity in the relationship graph
    """

    __tablename__ = "persons"

    # Primary key
    person_id = Column(get_uuid_type(), primary_key=True, default=uuid.uuid4)

    # Foreign key to user (owner of this person record)
    user_id = Column(
        get_uuid_type(), ForeignKey("users.user_id"), nullable=False, index=True
    )

    # Basic information
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    profile_picture = Column(String(500))  # URL to profile picture

    # Contact information (privacy-sensitive, user can choose to sync)
    contact_info = Column(
        get_json_type(), default={}
    )  # {"email": "...", "phone": "...", "address": "..."}

    # Social and professional profiles (non-sensitive)
    social_profiles = Column(
        get_json_type(), default={}
    )  # {"linkedin": "...", "twitter": "...", "github": "..."}
    professional_info = Column(
        get_json_type(), default={}
    )  # {"title": "...", "company": "...", "industry": "..."}

    # Personal details (non-sensitive)
    personal_details = Column(
        get_json_type(), default={}
    )  # {"birthday": "...", "mbti": "...", "interests": [...]}

    # Special flag for user identification (only one person per user should have this as True)
    is_user = Column(Boolean, default=False, index=True)

    # User profile fields (only filled when is_user=True)
    stated_decision_factors = Column(
        get_json_type(), default={}
    )  # User's explicitly stated relationship preferences
    learned_decision_factors = Column(
        get_json_type(), default={}
    )  # AI-learned preferences from user behavior
    last_calibration_date = Column(DateTime(timezone=True))

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", backref="persons")

    def __repr__(self):
        return f"<Person(name='{self.first_name} {self.last_name}', is_user={self.is_user})>"

    @property
    def full_name(self) -> str:
        """Get full name"""
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def display_name(self) -> str:
        """Get display name for UI"""
        if self.is_user:
            return "Me"
        return self.full_name
