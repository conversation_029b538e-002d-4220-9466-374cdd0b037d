"""
Organization model - represents companies, institutions, etc.
"""

import uuid

from sqlalchemy import <PERSON>olean, Column, DateTime, ForeignKey, String, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base
from app.core.db_types import get_uuid_type, get_json_type


class Organization(Base):
    """Organization model representing companies, institutions, etc."""

    __tablename__ = "organizations"

    # Primary key
    org_id = Column(get_uuid_type(), primary_key=True, default=uuid.uuid4)

    # Foreign key to user (owner of this organization record)
    user_id = Column(
        get_uuid_type(), ForeignKey("users.user_id"), nullable=False, index=True
    )

    # Basic information
    name = Column(String(255), nullable=False)
    description = Column(Text)

    # Organization details
    details = Column(
        get_json_type(), default={}
    )  # {"industry": "...", "size": "...", "website": "...", "address": "..."}

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", backref="organizations")

    def __repr__(self):
        return f"<Organization(name='{self.name}')>"


class WorksAt(Base):
    """Association table for Person-Organization work relationships"""

    __tablename__ = "works_at"

    # Composite primary key
    person_id = Column(
        get_uuid_type(), ForeignKey("persons.person_id"), primary_key=True
    )
    org_id = Column(
        get_uuid_type(), ForeignKey("organizations.org_id"), primary_key=True
    )

    # Work relationship details
    role = Column(String(255))  # Job title/position
    start_date = Column(DateTime(timezone=True))
    end_date = Column(DateTime(timezone=True))  # NULL if current position
    is_current = Column(Boolean, default=True)

    # Additional details
    details = Column(
        get_json_type(), default={}
    )  # {"department": "...", "responsibilities": [...]}

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    person = relationship("Person", backref="work_history")
    organization = relationship("Organization", backref="employees")

    def __repr__(self):
        return f"<WorksAt(person_id='{self.person_id}', org_id='{self.org_id}', role='{self.role}')>"
