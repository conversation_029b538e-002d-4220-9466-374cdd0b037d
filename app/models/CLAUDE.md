# Models Directory - SQLAlchemy Database Models

This directory contains the comprehensive database models for the Nexus relationship management platform.

## Core Entity Models

### `user.py` - User Management
- **User**: Account management with JWT authentication
- **Features**: Secure password hashing, profile management, preferences
- **Relationships**: One-to-many with all user-owned entities

### `person.py` - Contact Management
- **Person**: Individual contacts with detailed information storage
- **Features**: Name, contact info, work history, custom details JSON field
- **Relationships**: Many-to-many relationships via Knows table, work history via WorksAt

### `organization.py` - Organization Management
- **Organization**: Companies, institutions, and groups
- **WorksAt**: Person-organization work relationships with role tracking
- **Features**: Organization details, employee associations, work history

### `relationship.py` - Six-Dimensional Relationship Model
- **Knows**: Sophisticated relationship tracking between persons
- **Six Dimensions**:
  1. **Emotional Intimacy** (1-5): Personal connection depth
  2. **Professional Collaboration** (1-5): Work relationship strength
  3. **Trust Level** (1-5): Reliability and confidence
  4. **Communication Frequency** (1-5): Interaction regularity
  5. **Shared Experience Value** (1-5): Common background importance
  6. **Reciprocity Balance** (1-5): Mutual benefit assessment

### `goal.py` - Strategic Objective Management
- **Goal**: User objectives with AI analysis integration
- **Features**: Target dates, priority levels, success criteria, AI metadata
- **Goal Metadata**: AI analysis results, related people, progress tracking

### `task.py` - Action Item Management
- **Task**: Action items linked to goals and persons
- **Features**: Due dates, completion tracking, goal associations
- **Relationships**: Many-to-one with Goal, optional Person association

## Supporting Models

### `interaction.py` - Communication Tracking
- **Interaction**: Records of communication and meetings
- **Features**: Interaction types, duration, location, notes
- **Relationships**: Linked to persons and goals for activity tracking

### `note.py` - Annotation System
- **Note**: Notes and annotations with flexible entity associations
- **Features**: Rich text content, entity linking (person, goal, task)
- **Relationships**: Polymorphic associations with multiple entity types

### `tag.py` - Categorization System
- **Tag**: Tagging system for categorization and organization
- **Features**: Color coding, descriptions, hierarchical organization
- **Usage**: Applied across persons, organizations, goals, and tasks

### `data_job.py` - Background Job Tracking
- **DataJob**: Async job processing for data operations
- **Features**: Progress tracking, error handling, file management
- **Job Types**: Export/import operations with comprehensive status tracking

### `conversation.py` - Stateless LLM Conversation System
- **Conversation**: User conversation sessions with metadata
- **ConversationMessage**: Individual messages with role-based content
- **ConversationService**: Complete CRUD operations for conversation management
- **Features**: User isolation, tool call persistence, sequence ordering
- **Multi-Instance Ready**: Database-backed storage for horizontal scaling

## Advanced Features

### JSON Field Usage
Many models use PostgreSQL JSONB fields for flexible data storage:
- **Person.details**: Custom contact information and preferences
- **Organization.details**: Company information, industry data
- **Goal.goal_metadata**: AI analysis results, success criteria
- **Task.details**: Custom task attributes and metadata

### Relationship Patterns

#### User Data Isolation
```python
# All user-owned entities include user_id foreign key
user_id = Column(get_uuid_type(), ForeignKey("users.user_id"), nullable=False, index=True)
```

#### Flexible Entity Associations
```python
# Notes can be associated with multiple entity types
person_id = Column(get_uuid_type(), ForeignKey("persons.person_id"), nullable=True)
goal_id = Column(get_uuid_type(), ForeignKey("goals.goal_id"), nullable=True)
task_id = Column(get_uuid_type(), ForeignKey("tasks.task_id"), nullable=True)
```

#### Composite Primary Keys
```python
# WorksAt uses composite primary key for person-organization relationships
person_id = Column(get_uuid_type(), ForeignKey("persons.person_id"), primary_key=True)
org_id = Column(get_uuid_type(), ForeignKey("organizations.org_id"), primary_key=True)
```

### Database Performance Features

#### Indexing Strategy
- **Primary Keys**: UUID with B-tree indexes
- **Foreign Keys**: Automatic indexing for relationship lookups
- **Search Fields**: GIN indexes for fuzzy search (pg_trgm extension)
- **User Isolation**: Compound indexes on (user_id, entity_id) for fast user queries

#### Data Types
- **UUIDs**: Cross-platform compatible UUID implementation
- **JSONB**: PostgreSQL native JSON with indexing support
- **Timestamps**: Timezone-aware datetime with automatic updates

## Model Relationships Overview

```
User (1) ──┬── (∞) Person
           ├── (∞) Organization  
           ├── (∞) Goal
           ├── (∞) Task
           ├── (∞) Note
           ├── (∞) Tag
           ├── (∞) Interaction
           └── (∞) DataJob

Person (∞) ──── (∞) Person [via Knows - Six-Dimensional Relationships]
Person (∞) ──── (∞) Organization [via WorksAt - Work History]
Goal (1) ────── (∞) Task [Goal-Task Association]
Person (1) ───── (∞) Task [Person-Task Association]
```

## AI Integration Features

### Goal Intelligence
- **AI Analysis Storage**: Goal metadata stores AI-generated insights
- **Strategy Recommendations**: AI-powered achievement strategies
- **Success Prediction**: Multi-factor success probability modeling

### Relationship Analysis
- **Archetype Templates**: Predefined relationship patterns
- **Dimension Scoring**: Automated relationship strength assessment
- **Network Health**: Connection quality and diversity analysis

### Smart Associations
- **Goal-Person Matching**: AI identifies relevant connections for goal achievement
- **Referral Path Discovery**: Graph algorithms find optimal introduction paths
- **Activity Correlation**: Interaction history informs relationship insights

## Usage Patterns

### Service Layer Integration
Models are accessed through service layer classes that provide:
- **Business Logic**: Complex operations and validations
- **User Isolation**: Automatic filtering by user_id
- **Error Handling**: Comprehensive exception management
- **Performance Optimization**: Efficient query patterns

### API Integration
Models integrate with FastAPI through:
- **Pydantic Schemas**: Type-safe API validation
- **SQLAlchemy Sessions**: Async database operations
- **Dependency Injection**: Clean separation of concerns

For complete API documentation and service implementations, refer to the `../schemas/` and `../services/` directories.