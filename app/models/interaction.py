"""
Interaction models - represents interactions between people
"""

import uuid

from sqlalchemy import (<PERSON>umn, DateTime, Foreign<PERSON>ey, Integer, String, Table,
                        Text)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base
from app.core.db_types import get_uuid_type, get_json_type

# Association table for interaction participants
interaction_participants = Table(
    "interaction_participants",
    Base.metadata,
    Column(
        "interaction_id",
        get_uuid_type(),
        ForeignKey("interactions.interaction_id"),
        primary_key=True,
    ),
    Column(
        "person_id",
        get_uuid_type(),
        ForeignKey("persons.person_id"),
        primary_key=True,
    ),
)


class Interaction(Base):
    """
    Interaction model representing communications and meetings between people
    """

    __tablename__ = "interactions"

    # Primary key
    interaction_id = Column(get_uuid_type(), primary_key=True, default=uuid.uuid4)

    # Foreign key to user (owner of this interaction record)
    user_id = Column(
        get_uuid_type(), ForeignKey("users.user_id"), nullable=False, index=True
    )

    # Interaction type (e.g., "meeting", "call", "email", "message", "event")
    type = Column(String(50), nullable=False, index=True)

    # Content summary (AI-generated or user-provided)
    content_summary = Column(Text)

    # When the interaction occurred
    occurred_at = Column(DateTime(timezone=True), nullable=False, index=True)

    # Duration in minutes (optional)
    duration_minutes = Column(Integer)

    # Location or platform (optional)
    location = Column(String(255))

    # Additional metadata
    interaction_metadata = Column("metadata", get_json_type(), default={})
    # Example structure:
    # {
    #   "sentiment": "positive",
    #   "topics": ["project_x", "career_advice"],
    #   "action_items": ["Follow up on proposal", "Schedule next meeting"],
    #   "source": "calendar_sync",  # or "manual", "email_sync", etc.
    #   "external_id": "calendar_event_123"  # for synced events
    # }

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", backref="interactions")
    participants = relationship(
        "Person", secondary=interaction_participants, backref="interactions"
    )

    def __repr__(self):
        return f"<Interaction(type='{self.type}', occurred_at='{self.occurred_at}')>"

    @property
    def participant_names(self) -> list:
        """Get list of participant names"""
        return [p.full_name for p in self.participants]

    @property
    def sentiment(self) -> str:
        """Get interaction sentiment"""
        if not self.interaction_metadata:
            return "neutral"
        return self.interaction_metadata.get("sentiment", "neutral")

    @property
    def topics(self) -> list:
        """Get interaction topics"""
        if not self.interaction_metadata:
            return []
        return self.interaction_metadata.get("topics", [])

    @property
    def action_items(self) -> list:
        """Get action items from interaction"""
        if not self.interaction_metadata:
            return []
        return self.interaction_metadata.get("action_items", [])
