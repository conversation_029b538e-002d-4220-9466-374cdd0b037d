"""
Goal model - represents user's strategic objectives
"""

import enum
import uuid

from sqlalchemy import (Column, DateTime, Enum, Foreign<PERSON>ey, Integer, String,
                        Text)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base
from app.core.db_types import get_uuid_type, get_json_type


class GoalStatus(str, enum.Enum):
    """Goal status enumeration"""

    ACTIVE = "active"
    COMPLETED = "completed"
    PAUSED = "paused"
    CANCELLED = "cancelled"


class Goal(Base):
    """
    Goal model representing user's strategic objectives
    """

    __tablename__ = "goals"

    # Primary key
    goal_id = Column(get_uuid_type(), primary_key=True, default=uuid.uuid4)

    # Foreign key to user (owner of this goal)
    user_id = Column(
        get_uuid_type(), ForeignKey("users.user_id"), nullable=False, index=True
    )

    # Basic information
    title = Column(String(255), nullable=False)
    description = Column(Text)

    # Status
    status = Column(Enum(GoalStatus), default=GoalStatus.ACTIVE, index=True)

    # Priority (1-5, where 5 is highest)
    priority = Column(Integer, default=3)

    # Dates
    target_date = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))

    # Goal metadata and AI analysis
    goal_metadata = Column("metadata", get_json_type(), default={})
    # Example structure:
    # {
    #   "category": "career",
    #   "tags": ["networking", "fundraising"],
    #   "success_criteria": ["Raise $1M", "Get 10 investor meetings"],
    #   "related_people": ["person_id_1", "person_id_2"],
    #   "ai_analysis": {
    #     "difficulty": "high",
    #     "required_connections": ["investor", "mentor"],
    #     "suggested_actions": ["Update pitch deck", "Reach out to John"]
    #   }
    # }

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", backref="goals")
    tasks = relationship("Task", back_populates="goal", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Goal(title='{self.title}', status='{self.status}')>"

    @property
    def is_active(self) -> bool:
        """Check if goal is active"""
        return self.status == GoalStatus.ACTIVE

    @property
    def is_completed(self) -> bool:
        """Check if goal is completed"""
        return self.status == GoalStatus.COMPLETED

    @property
    def tags(self) -> list:
        """Get goal tags"""
        if not self.goal_metadata:
            return []
        return self.goal_metadata.get("tags", [])

    @property
    def related_people(self) -> list:
        """Get related people IDs"""
        if not self.goal_metadata:
            return []
        return self.goal_metadata.get("related_people", [])

    @property
    def success_criteria(self) -> list:
        """Get success criteria"""
        if not self.goal_metadata:
            return []
        return self.goal_metadata.get("success_criteria", [])

    def mark_completed(self):
        """Mark goal as completed"""
        self.status = GoalStatus.COMPLETED
        self.completed_at = func.now()

    def add_related_person(self, person_id: str):
        """Add a related person to this goal"""
        if not self.goal_metadata:
            self.goal_metadata = {}
        if "related_people" not in self.goal_metadata:
            self.goal_metadata["related_people"] = []
        if person_id not in self.goal_metadata["related_people"]:
            self.goal_metadata["related_people"].append(person_id)
