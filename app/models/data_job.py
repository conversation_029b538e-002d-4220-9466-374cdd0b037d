"""
Data export/import job models
"""

import uuid
from datetime import datetime, timedelta

from sqlalchemy import <PERSON>olean, Column, DateTime, Foreign<PERSON>ey, Integer, String, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base
from app.core.db_types import get_json_type, get_uuid_type


class DataJob(Base):
    """Model for tracking data export/import jobs"""
    
    __tablename__ = "data_jobs"
    
    # Primary key
    job_id = Column(get_uuid_type(), primary_key=True, default=uuid.uuid4)
    
    # Foreign key to user
    user_id = Column(
        get_uuid_type(), ForeignKey("users.user_id"), nullable=False, index=True
    )
    
    # Job details
    job_type = Column(String(20), nullable=False)  # "export" or "import"
    status = Column(String(20), nullable=False, default="pending")  # pending, processing, completed, failed, cancelled
    format = Column(String(20), nullable=False)  # json, csv, vcard
    
    # Job configuration
    config = Column(
        get_json_type(), 
        default={}
    )  # Export/import configuration (entities, options, etc.)
    
    # File details
    filename = Column(String(255))  # Original filename for imports
    file_size = Column(Integer)  # File size in bytes
    file_path = Column(String(512))  # Path to generated/uploaded file
    
    # Processing details
    progress = Column(Integer, default=0)  # Progress percentage (0-100)
    
    # Processing results
    records_total = Column(Integer)  # Total records in file
    records_processed = Column(Integer)  # Records processed
    records_imported = Column(Integer)  # Records successfully imported (import only)
    records_failed = Column(Integer)  # Records that failed
    records_skipped = Column(Integer)  # Records skipped
    
    # Error tracking
    errors = Column(get_json_type(), default=[])  # List of error messages
    warnings = Column(get_json_type(), default=[])  # List of warning messages
    
    # Statistics
    stats = Column(get_json_type(), default={})  # Processing statistics
    
    # File expiration (for downloads)
    expires_at = Column(DateTime(timezone=True))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    started_at = Column(DateTime(timezone=True))  # When processing started
    completed_at = Column(DateTime(timezone=True))  # When processing completed
    
    # Relationships
    user = relationship("User", backref="data_jobs")
    
    def __repr__(self):
        return f"<DataJob(job_id='{self.job_id}', type='{self.job_type}', status='{self.status}')>"
    
    def is_expired(self) -> bool:
        """Check if the job/file has expired"""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at
    
    def set_default_expiration(self):
        """Set default expiration time (7 days from now)"""
        self.expires_at = datetime.utcnow() + timedelta(days=7)
    
    def update_progress(self, progress: int, processed: int = None):
        """Update job progress"""
        self.progress = min(100, max(0, progress))
        if processed is not None:
            self.records_processed = processed
        self.updated_at = datetime.utcnow()
    
    def mark_started(self):
        """Mark job as started"""
        self.status = "processing"
        self.started_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def mark_completed(self, stats: dict = None):
        """Mark job as completed"""
        self.status = "completed"
        self.progress = 100
        self.completed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        
        if stats:
            self.stats = stats
        
        # Set expiration for completed jobs
        if not self.expires_at:
            self.set_default_expiration()
    
    def mark_failed(self, error_message: str):
        """Mark job as failed"""
        self.status = "failed"
        self.completed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        
        if not self.errors:
            self.errors = []
        self.errors.append(error_message)
    
    def add_error(self, error_message: str):
        """Add an error message to the job"""
        if not self.errors:
            self.errors = []
        self.errors.append(error_message)
        self.updated_at = datetime.utcnow()
    
    def add_warning(self, warning_message: str):
        """Add a warning message to the job"""
        if not self.warnings:
            self.warnings = []
        self.warnings.append(warning_message)
        self.updated_at = datetime.utcnow()
    
    def get_download_url(self) -> str:
        """Get download URL for completed export jobs"""
        if self.job_type == "export" and self.status == "completed" and not self.is_expired():
            return f"/api/v1/data/download/{self.job_id}"
        return None
    
    def get_processing_time(self) -> float:
        """Get processing time in seconds"""
        if not self.started_at:
            return 0.0
        
        end_time = self.completed_at or datetime.utcnow()
        return (end_time - self.started_at).total_seconds()