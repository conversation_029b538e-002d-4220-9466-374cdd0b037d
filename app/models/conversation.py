"""
Conversation and Message models - for storing LLM conversation history
"""

import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Optional

from sqlalchemy import Column, DateTime, ForeignKey, String, Text, Integer
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base
from app.core.db_types import get_uuid_type, get_json_type


class Conversation(Base):
    """
    Conversation model for storing LLM conversation sessions
    """
    
    __tablename__ = "conversations"
    
    # Primary key
    conversation_id = Column(get_uuid_type(), primary_key=True, default=uuid.uuid4)
    
    # Foreign key to user (owner of this conversation)
    user_id = Column(
        get_uuid_type(), ForeignKey("users.user_id"), nullable=False, index=True
    )
    
    # Conversation metadata
    title = Column(String(255))  # Optional conversation title
    context = Column("context", get_json_type(), default={})  # Additional context
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_message_at = Column(DateTime(timezone=True))
    
    # Relationships
    user = relationship("User", backref="conversations")
    messages = relationship("ConversationMessage", back_populates="conversation", 
                          cascade="all, delete-orphan", order_by="ConversationMessage.created_at")
    
    def __repr__(self):
        return f"<Conversation(id='{self.conversation_id}', user_id='{self.user_id}')>"


class ConversationMessage(Base):
    """
    Individual message within a conversation
    """
    
    __tablename__ = "conversation_messages"
    
    # Primary key
    message_id = Column(get_uuid_type(), primary_key=True, default=uuid.uuid4)
    
    # Foreign key to conversation
    conversation_id = Column(
        get_uuid_type(), ForeignKey("conversations.conversation_id"), nullable=False, index=True
    )
    
    # Message content
    role = Column(String(20), nullable=False)  # "system", "user", "assistant", "tool"
    content = Column(Text, nullable=False)
    
    # Tool call information (for assistant messages with tool calls)
    tool_calls = Column("tool_calls", get_json_type())  # List of tool call data
    tool_call_id = Column(String(100))  # For tool response messages
    
    # Message metadata
    message_metadata = Column("metadata", get_json_type(), default={})
    # Example structure:
    # {
    #   "tokens_used": 150,
    #   "model": "gpt-4",
    #   "temperature": 0.7,
    #   "processing_time": 2.5,
    #   "error": "optional error message"
    # }
    
    # Sequence number for ordering within conversation
    sequence_number = Column(Integer, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    conversation = relationship("Conversation", back_populates="messages")
    
    def __repr__(self):
        return f"<ConversationMessage(role='{self.role}', seq={self.sequence_number})>"
    
    @property
    def is_tool_call(self) -> bool:
        """Check if this message contains tool calls"""
        return self.tool_calls is not None and len(self.tool_calls) > 0
    
    @property
    def is_tool_response(self) -> bool:
        """Check if this message is a tool response"""
        return self.role == "tool" and self.tool_call_id is not None
    
    def to_openai_format(self) -> dict:
        """Convert to OpenAI API message format"""
        message = {
            "role": self.role,
            "content": self.content
        }
        
        if self.tool_calls:
            message["tool_calls"] = self.tool_calls
            
        if self.tool_call_id:
            message["tool_call_id"] = self.tool_call_id
            
        return message


class ConversationService:
    """
    Service for managing conversation history in database
    """
    
    def __init__(self, db_session):
        self.db = db_session
    
    def get_or_create_conversation(self, conversation_id: str, user_id: str) -> Conversation:
        """Get existing conversation or create new one"""
        # Try to get existing conversation
        conversation = self.db.query(Conversation).filter(
            Conversation.conversation_id == conversation_id,
            Conversation.user_id == user_id
        ).first()
        
        if not conversation:
            # Create new conversation
            conversation = Conversation(
                conversation_id=conversation_id,
                user_id=user_id,
                created_at=datetime.utcnow(),
                last_message_at=datetime.utcnow()
            )
            self.db.add(conversation)
            self.db.commit()
            self.db.refresh(conversation)
        
        return conversation
    
    def add_message(self, conversation_id: str, user_id: str, role: str, content: str, 
                   tool_calls: List[dict] = None, tool_call_id: str = None,
                   metadata: dict = None) -> ConversationMessage:
        """Add a message to conversation"""
        # Get or create conversation
        conversation = self.get_or_create_conversation(conversation_id, user_id)
        
        # Get next sequence number
        last_message = self.db.query(ConversationMessage).filter(
            ConversationMessage.conversation_id == conversation_id
        ).order_by(ConversationMessage.sequence_number.desc()).first()
        
        sequence_number = (last_message.sequence_number + 1) if last_message else 1
        
        # Create message
        message = ConversationMessage(
            conversation_id=conversation_id,
            role=role,
            content=content,
            tool_calls=tool_calls,
            tool_call_id=tool_call_id,
            message_metadata=metadata or {},
            sequence_number=sequence_number,
            created_at=datetime.utcnow()
        )
        
        self.db.add(message)
        
        # Update conversation last message time
        conversation.last_message_at = datetime.utcnow()
        conversation.updated_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(message)
        
        return message
    
    def get_conversation_messages(self, conversation_id: str, user_id: str, 
                                limit: int = None) -> List[ConversationMessage]:
        """Get messages for a conversation"""
        query = self.db.query(ConversationMessage).join(Conversation).filter(
            Conversation.conversation_id == conversation_id,
            Conversation.user_id == user_id
        ).order_by(ConversationMessage.sequence_number)
        
        if limit:
            # Get last N messages
            query = query.order_by(ConversationMessage.sequence_number.desc()).limit(limit)
            messages = query.all()
            messages.reverse()  # Restore chronological order
            return messages
        
        return query.all()
    
    def get_recent_messages(self, conversation_id: str, user_id: str, 
                          limit: int = 10) -> List[ConversationMessage]:
        """Get recent messages for context (last N messages)"""
        return self.get_conversation_messages(conversation_id, user_id, limit)
    
    def clear_conversation(self, conversation_id: str, user_id: str):
        """Clear all messages in a conversation"""
        # Verify user owns the conversation
        conversation = self.db.query(Conversation).filter(
            Conversation.conversation_id == conversation_id,
            Conversation.user_id == user_id
        ).first()
        
        if conversation:
            # Delete all messages (cascade will handle this, but explicit is better)
            self.db.query(ConversationMessage).filter(
                ConversationMessage.conversation_id == conversation_id
            ).delete()
            
            # Delete conversation
            self.db.delete(conversation)
            self.db.commit()
    
    def cleanup_old_conversations(self, days_old: int = 30):
        """Clean up conversations older than specified days"""
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        
        old_conversations = self.db.query(Conversation).filter(
            Conversation.last_message_at < cutoff_date
        ).all()
        
        for conversation in old_conversations:
            self.db.delete(conversation)
        
        self.db.commit()
        return len(old_conversations)
    
    def get_conversation_stats(self, user_id: str) -> dict:
        """Get conversation statistics for user"""
        total_conversations = self.db.query(Conversation).filter(
            Conversation.user_id == user_id
        ).count()
        
        total_messages = self.db.query(ConversationMessage).join(Conversation).filter(
            Conversation.user_id == user_id
        ).count()
        
        return {
            "total_conversations": total_conversations,
            "total_messages": total_messages
        }