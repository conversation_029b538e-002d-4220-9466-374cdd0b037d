"""
Relationship models - represents connections between people
Based on the "Knows" relationship in the technical documentation
"""

import uuid

from sqlalchemy import Column, DateTime, ForeignKey, Index, String
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base
from app.core.db_types import get_uuid_type, get_json_type


class Knows(Base):
    """
    Knows relationship model - represents relationships between people
    This is the core relationship entity implementing the six-dimensional model
    and onion theory from the technical documentation
    """

    __tablename__ = "knows"

    # Composite primary key (from_person_id, to_person_id)
    from_person_id = Column(
        get_uuid_type(), ForeignKey("persons.person_id"), primary_key=True
    )
    to_person_id = Column(
        get_uuid_type(), Foreign<PERSON>ey("persons.person_id"), primary_key=True
    )

    # User who owns this relationship record
    user_id = Column(
        get_uuid_type(), ForeignKey("users.user_id"), nullable=False, index=True
    )

    # Relationship archetype (e.g., "朋友", "同事", "导师", "客户")
    archetype = Column(String(100))

    # Relationship foundation - how they met, common ground, etc.
    relationship_foundation = Column(get_json_type(), default={})
    # Example structure:
    # {
    #   "how_met": "Through mutual friend at conference",
    #   "common_interests": ["AI", "Startups"],
    #   "shared_experiences": ["Worked on project X together"],
    #   "introduction_context": "Professional networking event"
    # }

    # Relationship depth - six-dimensional model + onion theory
    relationship_depth = Column(get_json_type(), default={})
    # Example structure based on technical documentation:
    # {
    #   "overall_score": 85,
    #   "trend": "positive",
    #   "last_updated_by_interaction": "interaction_uuid_123",
    #   "dimensions": {
    #     "emotional_intimacy": 70,
    #     "professional_collaboration": 90,
    #     "trust_level": 80,
    #     "communication_frequency": 95,
    #     "shared_experience_value": 60,
    #     "reciprocity_balance": 75
    #   },
    #   "history": [
    #     {"timestamp": "2025-05-20T10:00:00Z", "event": "Created", "score": 50},
    #     {"timestamp": "2025-06-18T14:30:00Z", "event": "Interaction: Project Kick-off", "score_change": "+15"}
    #   ]
    # }

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    from_person = relationship(
        "Person", foreign_keys=[from_person_id], backref="outgoing_relationships"
    )
    to_person = relationship(
        "Person", foreign_keys=[to_person_id], backref="incoming_relationships"
    )
    user = relationship("User", backref="relationships")

    # Indexes for performance
    __table_args__ = (
        Index("idx_knows_user_from", "user_id", "from_person_id"),
        Index("idx_knows_user_to", "user_id", "to_person_id"),
        Index("idx_knows_archetype", "archetype"),
    )

    def __repr__(self):
        return f"<Knows(from='{self.from_person_id}', to='{self.to_person_id}', archetype='{self.archetype}')>"

    @property
    def overall_score(self) -> int:
        """Get overall relationship score"""
        if not self.relationship_depth:
            return 0
        return self.relationship_depth.get("overall_score", 0)

    @property
    def dimensions(self) -> dict:
        """Get relationship dimensions scores"""
        if not self.relationship_depth:
            return {}
        return self.relationship_depth.get("dimensions", {})

    def update_score(self, new_score: int, event: str, interaction_id: str = None):
        """Update relationship score and add to history"""
        if not self.relationship_depth:
            self.relationship_depth = {
                "overall_score": new_score,
                "trend": "neutral",
                "dimensions": {},
                "history": [],
            }
        else:
            old_score = self.relationship_depth.get("overall_score", 0)
            score_change = new_score - old_score

            # Update overall score
            self.relationship_depth["overall_score"] = new_score

            # Update trend
            if score_change > 0:
                self.relationship_depth["trend"] = "positive"
            elif score_change < 0:
                self.relationship_depth["trend"] = "negative"
            else:
                self.relationship_depth["trend"] = "stable"

            # Add to history
            history_entry = {
                "timestamp": func.now().isoformat(),
                "event": event,
                "score_change": f"{score_change:+d}" if score_change != 0 else "0",
            }

            if interaction_id:
                history_entry["interaction_id"] = interaction_id
                self.relationship_depth["last_updated_by_interaction"] = interaction_id

            if "history" not in self.relationship_depth:
                self.relationship_depth["history"] = []

            self.relationship_depth["history"].append(history_entry)

            # Keep only last 50 history entries
            if len(self.relationship_depth["history"]) > 50:
                self.relationship_depth["history"] = self.relationship_depth["history"][
                    -50:
                ]
