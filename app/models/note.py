"""
Note model - represents user notes and observations
"""

import uuid

from sqlalchemy import Column, DateTime, ForeignKey, String, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base
from app.core.db_types import get_uuid_type, get_json_type


class Note(Base):
    """
    Note model representing user notes and observations
    """

    __tablename__ = "notes"

    # Primary key
    note_id = Column(get_uuid_type(), primary_key=True, default=uuid.uuid4)

    # Foreign key to user (owner of this note)
    user_id = Column(
        get_uuid_type(), ForeignKey("users.user_id"), nullable=False, index=True
    )

    # Note content
    content = Column(Text, nullable=False)

    # Optional title
    title = Column(String(255))

    # Note metadata
    note_metadata = Column("metadata", get_json_type(), default={})
    # Example structure:
    # {
    #   "related_people": ["person_id_1", "person_id_2"],
    #   "related_goals": ["goal_id_1"],
    #   "related_interactions": ["interaction_id_1"],
    #   "tags": ["important", "follow_up"],
    #   "source": "manual",  # or "ai_generated", "interaction_summary"
    #   "visibility": "private"  # or "shared"
    # }

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", backref="notes")

    def __repr__(self):
        return f"<Note(title='{self.title}', content_length={len(self.content) if self.content else 0})>"

    @property
    def related_people(self) -> list:
        """Get related people IDs"""
        if not self.note_metadata:
            return []
        return self.note_metadata.get("related_people", [])

    @property
    def related_goals(self) -> list:
        """Get related goal IDs"""
        if not self.note_metadata:
            return []
        return self.note_metadata.get("related_goals", [])

    @property
    def related_interactions(self) -> list:
        """Get related interaction IDs"""
        if not self.note_metadata:
            return []
        return self.note_metadata.get("related_interactions", [])

    @property
    def tags(self) -> list:
        """Get note tags"""
        if not self.note_metadata:
            return []
        return self.note_metadata.get("tags", [])

    @property
    def source(self) -> str:
        """Get note source"""
        if not self.note_metadata:
            return "manual"
        return self.note_metadata.get("source", "manual")

    def add_related_person(self, person_id: str):
        """Add a related person to this note"""
        if not self.note_metadata:
            self.note_metadata = {}
        if "related_people" not in self.note_metadata:
            self.note_metadata["related_people"] = []
        if person_id not in self.note_metadata["related_people"]:
            self.note_metadata["related_people"].append(person_id)

    def add_tag(self, tag: str):
        """Add a tag to this note"""
        if not self.note_metadata:
            self.note_metadata = {}
        if "tags" not in self.note_metadata:
            self.note_metadata["tags"] = []
        if tag not in self.note_metadata["tags"]:
            self.note_metadata["tags"].append(tag)
