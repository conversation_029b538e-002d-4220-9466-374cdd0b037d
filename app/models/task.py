"""
Task model - represents actionable items and to-dos
"""

import uuid

from sqlalchemy import (<PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, Foreign<PERSON>ey, Integer, String,
                        Text)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base
from app.core.db_types import get_uuid_type, get_json_type


class Task(Base):
    """
    Task model representing actionable items and to-dos
    """

    __tablename__ = "tasks"

    # Primary key
    task_id = Column(get_uuid_type(), primary_key=True, default=uuid.uuid4)

    # Foreign key to user (owner of this task)
    user_id = Column(
        get_uuid_type(), ForeignKey("users.user_id"), nullable=False, index=True
    )

    # Optional foreign key to goal (if task is part of a goal)
    goal_id = Column(
        get_uuid_type(), ForeignKey("goals.goal_id"), nullable=True, index=True
    )

    # Basic information
    title = Column(String(255), nullable=False)
    description = Column(Text)

    # Task properties
    priority = Column(Integer, default=3)  # 1-5, where 5 is highest
    is_completed = Column(Boolean, default=False, index=True)

    # Dates
    due_date = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))

    # Task metadata
    task_metadata = Column("metadata", get_json_type(), default={})
    # Example structure:
    # {
    #   "category": "relationship_maintenance",
    #   "related_people": ["person_id_1"],
    #   "estimated_duration": 30,  # minutes
    #   "context": "Follow up after meeting",
    #   "ai_generated": true,
    #   "source": "copilot_suggestion"
    # }

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", backref="tasks")
    goal = relationship("Goal", back_populates="tasks")

    def __repr__(self):
        return f"<Task(title='{self.title}', completed={self.is_completed})>"

    @property
    def is_overdue(self) -> bool:
        """Check if task is overdue"""
        if not self.due_date or self.is_completed:
            return False
        return self.due_date < func.now()

    @property
    def related_people(self) -> list:
        """Get related people IDs"""
        if not self.task_metadata:
            return []
        return self.task_metadata.get("related_people", [])

    @property
    def category(self) -> str:
        """Get task category"""
        if not self.task_metadata:
            return "general"
        return self.task_metadata.get("category", "general")

    @property
    def estimated_duration(self) -> int:
        """Get estimated duration in minutes"""
        if not self.task_metadata:
            return 0
        return self.task_metadata.get("estimated_duration", 0)

    @property
    def is_ai_generated(self) -> bool:
        """Check if task was generated by AI"""
        if not self.task_metadata:
            return False
        return self.task_metadata.get("ai_generated", False)

    def mark_completed(self):
        """Mark task as completed"""
        self.is_completed = True
        self.completed_at = func.now()

    def add_related_person(self, person_id: str):
        """Add a related person to this task"""
        if not self.task_metadata:
            self.task_metadata = {}
        if "related_people" not in self.task_metadata:
            self.task_metadata["related_people"] = []
        if person_id not in self.task_metadata["related_people"]:
            self.task_metadata["related_people"].append(person_id)
