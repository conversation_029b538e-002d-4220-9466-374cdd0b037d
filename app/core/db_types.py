"""
Database type utilities for cross-database compatibility
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, String
from sqlalchemy.dialects.postgresql import <PERSON>SO<PERSON><PERSON>, UUID
from sqlalchemy.sql.type_api import TypeDecorator

from app.core.config import settings


class JSONBType(TypeDecorator):
    """Cross-database JSON type that uses JSONB for PostgreSQL and JSON for others"""
    
    impl = JSON
    cache_ok = True
    
    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(JSONB())
        else:
            return dialect.type_descriptor(JSON())


class UUIDType(TypeDecorator):
    """Cross-database UUID type"""

    impl = String
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(UUID(as_uuid=True))
        else:
            return dialect.type_descriptor(String(36))

    def process_bind_param(self, value, dialect):
        """Convert UUID to string for non-PostgreSQL databases"""
        if value is None:
            return value
        if dialect.name == 'postgresql':
            return value
        else:
            # Convert UUID to string for SQLite and other databases
            return str(value)

    def process_result_value(self, value, dialect):
        """Convert string back to UUID for non-PostgreSQL databases"""
        if value is None:
            return value
        if dialect.name == 'postgresql':
            return value
        else:
            # Convert string back to UUID for SQLite and other databases
            import uuid
            return uuid.UUID(value) if isinstance(value, str) else value


# Helper functions to get the right types based on current database
def get_json_type():
    """Get the appropriate JSON type for the current database"""
    return JSONBType()


def get_uuid_type():
    """Get the appropriate UUID type for the current database"""
    return UUIDType()