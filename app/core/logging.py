"""
Logging configuration for structured logging
"""

import logging
import sys
from typing import Any, Dict

import structlog
from structlog.stdlib import LoggerFactory

from app.core.config import settings


def configure_logging() -> None:
    """Configure structured logging with structlog"""

    # Configure structlog
    structlog.configure(
        processors=[
            # Add log level and timestamp
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            # Add JSON formatting for production
            (
                structlog.processors.JSONRenderer()
                if not settings.DEBUG
                else structlog.dev.ConsoleRenderer(colors=True)
            ),
        ],
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.LOG_LEVEL.upper()),
    )


class RequestContextFilter(logging.Filter):
    """Add request context to log records"""

    def filter(self, record: logging.LogRecord) -> bool:
        # Add request ID and user ID if available
        # This would be populated by middleware
        record.request_id = getattr(record, "request_id", None)
        record.user_id = getattr(record, "user_id", None)
        return True


def get_logger(name: str) -> Any:
    """Get a structured logger instance"""
    return structlog.get_logger(name)


# Application loggers
app_logger = get_logger("nexus.app")
api_logger = get_logger("nexus.api")
db_logger = get_logger("nexus.database")
ai_logger = get_logger("nexus.ai")
security_logger = get_logger("nexus.security")
