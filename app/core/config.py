"""
Application configuration settings
"""

import os
import sys
from typing import Any, List, Optional, Union

from pydantic import ValidationError, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv

# Load .env file (if exists) - similar to alltomarkdown pattern
load_dotenv(override=True)


def parse_allowed_hosts(value: Union[str, List[str], None]) -> List[str]:
    """Parse ALLOWED_HOSTS from various input formats"""
    if value is None:
        return ["*"]

    if isinstance(value, list):
        return value

    if isinstance(value, str):
        value = value.strip()
        if not value:
            return ["*"]

        # Handle comma-separated values
        if "," in value:
            return [host.strip() for host in value.split(",") if host.strip()]

        # Handle single value
        return [value]

    return ["*"]


class Settings(BaseSettings):
    """Application settings"""

    model_config = SettingsConfigDict(
        env_file=".env.test" if "pytest" in sys.modules else ".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore",
    )

    # Basic settings
    DEBUG: bool = False
    PROJECT_NAME: str = "Nexus Backend"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"

    # Security
    SECRET_KEY: str = "change-this-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    ALGORITHM: str = "HS256"

    # Database configuration - following alltomarkdown pattern
    # DB_SOURCE can be "sqlite", "postgresql", or "supabase"
    DB_SOURCE: str = os.getenv("DB_SOURCE", "sqlite")
    
    # Local PostgreSQL or SQLite
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./nexus.db")
    
    # Supabase database connection
    SUPABASE_DATABASE_URL: Optional[str] = os.getenv("SUPABASE_DATABASE_URL")

    # Supabase API configuration
    SUPABASE_URL: Optional[str] = os.getenv("SUPABASE_URL")
    SUPABASE_KEY: Optional[str] = os.getenv("SUPABASE_KEY")

    # CORS - Use string for environment variable, will be converted to list
    ALLOWED_HOSTS: Union[str, List[str]] = "*"

    # AI/LLM Settings
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_API_BASE: Optional[str] = None
    OPENAI_MODEL_NAME: Optional[str] = None

    # Redis (for caching and sessions)
    REDIS_URL: Optional[str] = None

    # File storage
    UPLOAD_DIR: str = "uploads"
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB

    # Logging
    LOG_LEVEL: str = "INFO"

    @field_validator("ALLOWED_HOSTS", mode="before")
    @classmethod
    def validate_allowed_hosts(cls, v: Any) -> List[str]:
        """Convert ALLOWED_HOSTS to list format"""
        return parse_allowed_hosts(v)



# Safe settings loading
try:
    settings = Settings()
except ValidationError as e:
    print(f"Configuration validation error: {e}")
    print("Please check your environment variables and .env file")

    # Try to provide helpful error messages
    for error in e.errors():
        field = error.get("loc", ["unknown"])[0] if error.get("loc") else "unknown"
        msg = error.get("msg", "Unknown error")
        input_val = error.get("input", "Unknown")
        print(f"  Field '{field}': {msg} (input: {input_val})")

    # Exit gracefully
    sys.exit(1)
except Exception as e:
    print(f"Unexpected configuration error: {e}")
    print("Please check your environment variables and .env file")
    sys.exit(1)
