"""
Application configuration settings - Simplified version
"""

import os
import sys
from typing import List, Optional

from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings"""

    model_config = SettingsConfigDict(
        env_file=".env.test" if "pytest" in sys.modules else ".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore",
    )

    # Basic settings
    DEBUG: bool = False
    PROJECT_NAME: str = "Nexus Backend"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"

    # Security
    SECRET_KEY: str = "change-this-in-production-very-long-secret-key"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    ALGORITHM: str = "HS256"

    # Database
    DATABASE_URL: str = "sqlite:///./test.db"

    # Supabase
    SUPABASE_URL: Optional[str] = None
    SUPABASE_KEY: Optional[str] = None

    # CORS - Keep as string, will parse manually
    ALLOWED_HOSTS_STR: str = "*"

    # AI/LLM Settings
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_API_BASE: Optional[str] = None
    OPENAI_MODEL_NAME: Optional[str] = None

    # Redis (for caching and sessions)
    REDIS_URL: Optional[str] = None

    # File storage
    UPLOAD_DIR: str = "uploads"
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB

    # Logging
    LOG_LEVEL: str = "INFO"

    @property
    def ALLOWED_HOSTS(self) -> List[str]:
        """Parse ALLOWED_HOSTS from string"""
        # Try to get from environment first, fallback to ALLOWED_HOSTS_STR
        hosts_str = os.getenv("ALLOWED_HOSTS", self.ALLOWED_HOSTS_STR)

        if not hosts_str or hosts_str.strip() == "":
            return ["*"]

        hosts_str = hosts_str.strip()

        # Handle comma-separated values
        if "," in hosts_str:
            return [host.strip() for host in hosts_str.split(",") if host.strip()]

        # Handle single value
        return [hosts_str]


# Safe settings loading with better error handling
def create_settings():
    """Create settings instance with error handling"""
    try:
        return Settings()
    except Exception as e:
        print(f"Error loading settings: {e}")
        print("Using fallback configuration...")

        # Return minimal working configuration
        class FallbackSettings:
            DEBUG = True
            PROJECT_NAME = "Nexus Backend"
            VERSION = "1.0.0"
            API_V1_STR = "/api/v1"
            SECRET_KEY = "fallback-secret-key-change-in-production"
            ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 8
            ALGORITHM = "HS256"
            DATABASE_URL = "sqlite:///./test.db"
            SUPABASE_URL = None
            SUPABASE_KEY = None
            ALLOWED_HOSTS = ["*"]
            OPENAI_API_KEY = None
            OPENAI_API_BASE = None
            OPENAI_MODEL_NAME = None
            REDIS_URL = None
            UPLOAD_DIR = "uploads"
            MAX_UPLOAD_SIZE = 10 * 1024 * 1024
            LOG_LEVEL = "INFO"

        return FallbackSettings()


settings = create_settings()
