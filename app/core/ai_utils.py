"""
AI utilities for natural language processing and analysis
"""

import json
import re
from datetime import datetime
from typing import Any, Dict, List, Optional

from openai import OpenAI

from app.core.config import settings


class OpenAIClient:
    """Client for interacting with OpenAI compatible APIs"""

    def __init__(self, api_key: str, base_url: Optional[str] = None):
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
        )

    def get_completion(self, prompt: str, model: Optional[str] = None) -> str:
        """Get a completion from the model"""
        if not model:
            model = settings.OPENAI_MODEL_NAME

        try:
            completion = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": prompt},
                ],
            )
            return completion.choices[0].message.content
        except Exception as e:
            # Handle API errors gracefully
            print(f"Error getting completion: {e}")
            return "Sorry, I couldn't process that request."


def get_openai_client() -> OpenAIClient:
    """Dependency to get OpenAI client"""
    return OpenAIClient(
        api_key=settings.OPENAI_API_KEY, base_url=settings.OPENAI_API_BASE
    )


class NLPProcessor:
    """Natural Language Processing utilities for the copilot"""

    @staticmethod
    def extract_entities(text: str) -> Dict[str, List[str]]:
        """
        Extract entities from natural language input
        This is a simplified implementation - in production, use spaCy or similar
        """
        entities = {
            "people": [],
            "organizations": [],
            "dates": [],
            "actions": [],
            "topics": [],
        }

        # Simple regex patterns for entity extraction
        # In production, use proper NER models

        # Extract potential person names (capitalized words)
        person_pattern = r"\b[A-Z][a-z]+\s+[A-Z][a-z]+\b"
        entities["people"] = re.findall(person_pattern, text)

        # Extract potential organizations (words ending with Corp, Inc, etc.)
        org_pattern = r"\b[A-Z][a-zA-Z\s]+(Corp|Inc|LLC|Ltd|Company|Group)\b"
        entities["organizations"] = re.findall(org_pattern, text)

        # Extract dates (simple patterns)
        date_patterns = [
            r"\b\d{1,2}/\d{1,2}/\d{4}\b",  # MM/DD/YYYY
            r"\b\d{4}-\d{2}-\d{2}\b",  # YYYY-MM-DD
            r"\b(today|tomorrow|yesterday)\b",
            r"\b(next|last)\s+(week|month|year)\b",
        ]
        for pattern in date_patterns:
            entities["dates"].extend(re.findall(pattern, text, re.IGNORECASE))

        # Extract action verbs
        action_words = [
            "meet",
            "call",
            "email",
            "contact",
            "schedule",
            "remind",
            "follow up",
            "connect",
            "introduce",
            "discuss",
            "review",
        ]
        for action in action_words:
            if action.lower() in text.lower():
                entities["actions"].append(action)

        return entities

    @staticmethod
    def classify_intent(text: str) -> str:
        """
        Classify the intent of user input
        Simplified rule-based classification
        """
        text_lower = text.lower()

        # Task creation intents
        if any(word in text_lower for word in ["remind", "task", "todo", "need to"]):
            return "create_task"

        # Meeting/interaction logging
        if any(
            word in text_lower
            for word in ["met with", "talked to", "called", "meeting"]
        ):
            return "log_interaction"

        # Goal setting
        if any(
            word in text_lower for word in ["goal", "want to", "plan to", "objective"]
        ):
            return "create_goal"

        # Information request
        if any(
            word in text_lower
            for word in ["who", "what", "when", "where", "how", "show me"]
        ):
            return "information_request"

        # Relationship inquiry
        if any(
            word in text_lower
            for word in ["relationship", "connection", "know", "introduce"]
        ):
            return "relationship_inquiry"

        return "general_conversation"

    @staticmethod
    def extract_sentiment(text: str) -> str:
        """
        Simple sentiment analysis
        In production, use proper sentiment analysis models
        """
        positive_words = [
            "good",
            "great",
            "excellent",
            "amazing",
            "wonderful",
            "fantastic",
            "successful",
            "productive",
            "helpful",
            "positive",
            "happy",
        ]

        negative_words = [
            "bad",
            "terrible",
            "awful",
            "disappointing",
            "frustrating",
            "difficult",
            "challenging",
            "negative",
            "unhappy",
            "sad",
        ]

        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)

        if positive_count > negative_count:
            return "positive"
        elif negative_count > positive_count:
            return "negative"
        else:
            return "neutral"


class RelationshipAnalyzer:
    """Utilities for analyzing relationships and network patterns"""

    @staticmethod
    def calculate_relationship_strength(
        interactions: List[Dict], days_window: int = 90
    ) -> float:
        """
        Calculate relationship strength based on interaction history
        """
        if not interactions:
            return 0.0

        # Filter recent interactions
        cutoff_date = datetime.now().timestamp() - (days_window * 24 * 60 * 60)
        recent_interactions = [
            i for i in interactions if i.get("timestamp", 0) > cutoff_date
        ]

        if not recent_interactions:
            return 0.1  # Very weak for old relationships

        # Calculate frequency score
        frequency_score = min(len(recent_interactions) / 10, 1.0)  # Max 10 interactions

        # Calculate recency score
        most_recent = max(i.get("timestamp", 0) for i in recent_interactions)
        days_since_last = (datetime.now().timestamp() - most_recent) / (24 * 60 * 60)
        recency_score = max(0, 1 - (days_since_last / 30))  # Decay over 30 days

        # Calculate sentiment score
        sentiments = [i.get("sentiment", "neutral") for i in recent_interactions]
        positive_ratio = sentiments.count("positive") / len(sentiments)
        sentiment_score = 0.5 + (positive_ratio - 0.5) * 0.5  # Scale to 0.25-0.75

        # Weighted combination
        strength = frequency_score * 0.4 + recency_score * 0.4 + sentiment_score * 0.2
        return min(strength, 1.0)

    @staticmethod
    def detect_relationship_patterns(relationships: List[Dict]) -> Dict[str, Any]:
        """
        Detect patterns in relationship network
        """
        if not relationships:
            return {"patterns": [], "insights": []}

        patterns = []
        insights = []

        # Analyze relationship distribution by archetype
        archetypes = {}
        for rel in relationships:
            archetype = rel.get("archetype", "unknown")
            archetypes[archetype] = archetypes.get(archetype, 0) + 1

        # Check for imbalances
        total_relationships = len(relationships)
        for archetype, count in archetypes.items():
            ratio = count / total_relationships
            if ratio > 0.6:
                patterns.append(f"Heavy concentration in {archetype} relationships")
                insights.append(f"Consider diversifying beyond {archetype} connections")

        # Check for dormant relationships
        strong_relationships = [r for r in relationships if r.get("strength", 0) > 0.7]
        weak_relationships = [r for r in relationships if r.get("strength", 0) < 0.3]

        if len(weak_relationships) > len(strong_relationships):
            patterns.append("Many dormant relationships detected")
            insights.append("Focus on reactivating dormant connections")

        return {
            "patterns": patterns,
            "insights": insights,
            "archetype_distribution": archetypes,
            "strength_distribution": {
                "strong": len(strong_relationships),
                "weak": len(weak_relationships),
                "medium": total_relationships
                - len(strong_relationships)
                - len(weak_relationships),
            },
        }


class PreferenceLearner:
    """Machine learning utilities for learning user preferences"""

    @staticmethod
    def analyze_user_behavior(action_logs: List[Dict]) -> Dict[str, float]:
        """
        Analyze user behavior patterns to infer preferences
        Simplified implementation of preference learning
        """
        if not action_logs:
            return {}

        # Initialize preference weights
        preferences = {
            "emotional_weight": 0.2,
            "value_weight": 0.2,
            "trust_weight": 0.2,
            "information_weight": 0.15,
            "role_weight": 0.15,
            "coercive_weight": 0.1,
        }

        # Analyze actions to infer preferences
        for log in action_logs:
            action_type = log.get("action_type", "")
            details = log.get("details", {})

            # If user frequently prioritizes emotional connections
            if "emotional" in action_type.lower() or "personal" in str(details).lower():
                preferences["emotional_weight"] += 0.01

            # If user focuses on professional value
            if (
                "professional" in action_type.lower()
                or "business" in str(details).lower()
            ):
                preferences["value_weight"] += 0.01

            # If user emphasizes trust-building activities
            if "trust" in action_type.lower() or "reliable" in str(details).lower():
                preferences["trust_weight"] += 0.01

        # Normalize to sum to 1.0
        total = sum(preferences.values())
        if total > 0:
            preferences = {k: v / total for k, v in preferences.items()}

        return preferences

    @staticmethod
    def detect_preference_drift(
        stated_prefs: Dict[str, float], learned_prefs: Dict[str, float]
    ) -> Dict[str, Any]:
        """
        Detect significant differences between stated and learned preferences
        """
        if not stated_prefs or not learned_prefs:
            return {"has_drift": False, "suggestions": []}

        drift_threshold = 0.1  # 10% difference threshold
        drifts = {}
        suggestions = []

        for factor in stated_prefs:
            if factor in learned_prefs:
                stated_val = stated_prefs[factor]
                learned_val = learned_prefs[factor]
                diff = abs(stated_val - learned_val)

                if diff > drift_threshold:
                    drifts[factor] = {
                        "stated": stated_val,
                        "learned": learned_val,
                        "difference": diff,
                    }

                    if learned_val > stated_val:
                        suggestions.append(
                            f"You seem to value {factor.replace('_', ' ')} more than you stated"
                        )
                    else:
                        suggestions.append(
                            f"You seem to value {factor.replace('_', ' ')} less than you stated"
                        )

        return {
            "has_drift": len(drifts) > 0,
            "drifts": drifts,
            "suggestions": suggestions,
        }
