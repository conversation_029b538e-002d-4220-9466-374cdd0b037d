# Core Directory - System Infrastructure

This directory contains the foundational infrastructure components for the Nexus relationship management platform.

## Core System Components

### `config.py` - Configuration Management
**Pydantic-based settings with environment variable support**

**Configuration Features**:
- **Environment Variables**: Automatic loading from .env files
- **Type Validation**: Pydantic-based configuration validation
- **Default Values**: Sensible defaults for development
- **Production Ready**: Environment-specific configuration support

**Key Settings**:
```python
class Settings(BaseSettings):
    # Database Configuration
    DATABASE_URL: str
    DB_SOURCE: str = "postgresql"  # postgresql, sqlite, supabase
    
    # Security
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # AI Services
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_API_BASE: str = "https://api.openai.com/v1"
    
    # External Services
    SUPABASE_URL: Optional[str] = None
    SUPABASE_KEY: Optional[str] = None
    SUPABASE_DATABASE_URL: Optional[str] = None
```

### `database.py` - Database Infrastructure
**SQLAlchemy database connection and session management**

**Database Features**:
- **Multi-Database Support**: PostgreSQL, SQLite, Supabase compatibility
- **Connection Pooling**: Efficient database connection management
- **Session Management**: Proper session lifecycle handling
- **Cross-Platform Types**: UUID and JSON type compatibility

**Database Configuration**:
```python
# Dynamic database URL selection
if settings.DB_SOURCE == "supabase":
    database_url = settings.SUPABASE_DATABASE_URL
else:
    database_url = settings.DATABASE_URL

# Engine configuration with connection pooling
engine = create_engine(database_url, pool_pre_ping=True)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
```

**Session Management**:
```python
def get_db() -> Generator[Session, None, None]:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

### `security.py` - Authentication and Security
**JWT authentication, password hashing, and security utilities**

**Security Features**:
- **Password Hashing**: Bcrypt-based secure password hashing
- **JWT Tokens**: Secure token generation and validation
- **Token Expiration**: Automatic token expiration handling
- **Security Headers**: CORS and security header management

**Authentication Implementation**:
```python
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)
```

### `llm_service.py` - Stateless LLM Service Infrastructure
**Multi-instance ready LLM service with database-backed conversation history**

**Stateless Features**:
- **Zero Memory State**: No class-level or instance-level conversation storage
- **Database Injection**: SQLAlchemy session dependency injection for persistence
- **User Isolation**: All conversation operations require user_id parameter
- **Tool Call Persistence**: Complex AI function calls stored and retrieved from database
- **Multi-Instance Compatible**: Service instances can be load balanced without session affinity

**Stateless Implementation**:
```python
class LLMService:
    def __init__(self, api_key: str = None, base_url: str = None, model: str = None, 
                 db_session: Session = None):
        # Database session for stateless operation
        self.db = db_session
        
    async def process_conversation(self, user_message: str, conversation_id: str, 
                                 user_id: str, context: Dict[str, Any] = None):
        # All operations require user_id for database isolation
        conversation = self.get_conversation(conversation_id, user_id)
        # ... process with database persistence
        
    def get_conversation(self, conversation_id: str, user_id: str = None) -> List[ConversationMessage]:
        # Retrieve from database instead of memory
        conv_service = ConversationService(self.db)
        return conv_service.get_recent_messages(conversation_id, user_id, limit=20)
```

### `ai_utils.py` - AI Integration Infrastructure
**OpenAI integration utilities and AI service abstractions**

**AI Service Features**:
- **OpenAI Client**: Wrapper for OpenAI API integration
- **Relationship Analyzer**: Specialized relationship analysis
- **Preference Learner**: User preference learning and adaptation
- **Fallback Systems**: Graceful degradation when AI unavailable

**OpenAI Client Implementation**:
```python
class OpenAIClient:
    def __init__(self, api_key: str, base_url: str = "https://api.openai.com/v1"):
        self.client = openai.OpenAI(api_key=api_key, base_url=base_url)
    
    def generate_text(self, prompt: str, max_tokens: int = 500, temperature: float = 0.7):
        # Structured text generation with error handling
        # Rate limiting and cost optimization
        # Response validation and parsing
    
    def analyze_relationships(self, context: Dict[str, Any]):
        # Specialized relationship analysis prompts
        # Structured response processing
        # Fallback logic for API failures
```

**Relationship Analyzer**:
```python
class RelationshipAnalyzer:
    def __init__(self, openai_client: OpenAIClient):
        self.client = openai_client
    
    def analyze_relationship_strength(self, relationship_data: Dict[str, Any]):
        # Six-dimensional relationship analysis
        # AI-powered insights and recommendations
        # Relationship archetype detection
    
    def suggest_networking_actions(self, network_context: Dict[str, Any]):
        # Proactive networking suggestions
        # Goal-oriented connection recommendations
        # Strategic relationship building advice
```

### `db_types.py` - Database Type Definitions
**Cross-platform database type compatibility**

**Type Features**:
- **UUID Support**: Cross-platform UUID implementation
- **JSON Fields**: PostgreSQL JSONB with SQLite JSON fallback
- **Type Safety**: Consistent type definitions across databases

**Type Implementations**:
```python
def get_uuid_type():
    # Returns UUID type for PostgreSQL, String for SQLite
    if settings.DB_SOURCE in ["postgresql", "supabase"]:
        return UUID(as_uuid=True)
    else:
        return String(36)  # SQLite fallback

def get_json_type():
    # Returns JSONB for PostgreSQL, JSON for SQLite
    if settings.DB_SOURCE in ["postgresql", "supabase"]:
        return JSONB
    else:
        return JSON
```

## Infrastructure Patterns

### Configuration Pattern
```python
# Environment-specific configuration loading
settings = Settings()

# Type-safe configuration access
database_url = settings.DATABASE_URL
openai_key = settings.OPENAI_API_KEY
```

### Database Pattern
```python
# Dependency injection for database sessions
def get_db() -> Generator[Session, None, None]:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Usage in API endpoints
@router.get("/")
def get_items(db: Session = Depends(get_db)):
    return db.query(Model).all()
```

### Security Pattern
```python
# JWT token validation
def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials"
    )
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = get_user_by_username(db, username=username)
    if user is None:
        raise credentials_exception
    
    return user
```

### AI Integration Pattern
```python
# AI service with fallback
class AIService:
    def __init__(self):
        self.openai_client = None
        if settings.OPENAI_API_KEY:
            self.openai_client = OpenAIClient(settings.OPENAI_API_KEY)
    
    def get_analysis(self, context: Dict[str, Any]):
        if self.openai_client:
            try:
                return self.openai_client.analyze(context)
            except Exception:
                return self._get_fallback_analysis(context)
        return self._get_fallback_analysis(context)
```

## Performance and Reliability

### Database Performance
- **Connection Pooling**: Efficient database connection reuse
- **Session Management**: Proper session lifecycle with automatic cleanup
- **Query Optimization**: Prepared statements and parameter binding
- **Cross-Platform Compatibility**: Consistent behavior across database types

### Security Performance
- **Password Hashing**: Bcrypt with appropriate work factor
- **JWT Validation**: Efficient token verification
- **Rate Limiting**: Ready for implementation with Redis/Memcached
- **Security Headers**: CORS and security header optimization

### AI Performance
- **Response Caching**: Intelligent caching for repeated AI requests
- **Rate Limiting**: Proper API usage management
- **Cost Optimization**: Efficient prompt engineering
- **Fallback Systems**: Sub-500ms rule-based fallbacks

## Error Handling and Logging

### Exception Management
- **Structured Exceptions**: Custom exception types for different error categories
- **HTTP Status Mapping**: Proper HTTP status code usage
- **Error Sanitization**: Secure error messages without data leakage
- **Graceful Degradation**: Service continues operation when components fail

### Logging Infrastructure
- **Structured Logging**: JSON-formatted logs for easy parsing
- **Log Levels**: Appropriate log level usage (DEBUG, INFO, WARNING, ERROR)
- **Performance Monitoring**: Request timing and performance metrics
- **Security Auditing**: Authentication and authorization event logging

For complete API implementation and business logic, refer to the `../api/` and `../services/` directories.