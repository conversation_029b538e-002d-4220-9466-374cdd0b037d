"""
Database configuration and session management
"""

from typing import Generator
from sqlalchemy import create_engine
from sqlalchemy.orm import DeclarativeBase, sessionmaker
from supabase import Client, create_client

from app.core.config import settings


class Base(DeclarativeBase):
    """Base class for all database models"""
    pass


# Choose database URL based on DB_SOURCE setting - following alltomarkdown pattern
if settings.DB_SOURCE == "supabase":
    if not settings.SUPABASE_DATABASE_URL:
        raise ValueError("DB_SOURCE is 'supabase' but SUPABASE_DATABASE_URL is not set.")
    database_url = settings.SUPABASE_DATABASE_URL
else:  # Default to "sqlite" or "postgresql"
    database_url = settings.DATABASE_URL

print(f"DB_SOURCE: {settings.DB_SOURCE}")
print(f"Selected DATABASE_URL: {database_url}")

# Set engine arguments based on database type
engine_args = {"pool_pre_ping": True}
if database_url.startswith("sqlite"):
    engine_args["connect_args"] = {"check_same_thread": False}

engine = create_engine(database_url, **engine_args)

# Create SQLAlchemy ORM "SessionLocal" class
# This will be the actual database session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Supabase client (optional)
supabase: Client = None
if settings.SUPABASE_URL and settings.SUPABASE_KEY:
    supabase = create_client(settings.SUPABASE_URL, settings.SUPABASE_KEY)


def get_db() -> Generator:
    """Database dependency for FastAPI routes"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_supabase() -> Client:
    """Dependency to get Supabase client"""
    if supabase is None:
        raise ValueError(
            "Supabase is not configured. Please set SUPABASE_URL and SUPABASE_KEY environment variables."
        )
    return supabase


def init_db() -> None:
    """Initialize database"""
    # Import all models to ensure they are registered with SQLAlchemy
    from app.models import (goal, interaction, note, organization, person, tag,
                            task, user)

    # Create all tables
    Base.metadata.create_all(bind=engine)
