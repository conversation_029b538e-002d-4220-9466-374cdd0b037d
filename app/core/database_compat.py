"""
Database compatibility layer for handling differences between PostgreSQL and SQLite
"""

import difflib
from sqlalchemy import func, case, text, Numeric
from sqlalchemy.sql import expression
from sqlalchemy.ext.compiler import compiles
from sqlalchemy.sql.functions import GenericFunction
from app.core.config import settings


class similarity(GenericFunction):
    """Custom similarity function that works with both PostgreSQL and SQLite"""
    type = Numeric
    name = 'similarity'


class word_similarity(GenericFunction):
    """Custom word_similarity function that works with both PostgreSQL and SQLite"""
    type = Numeric
    name = 'word_similarity'


@compiles(similarity)
def compile_similarity_default(element, compiler, **kw):
    """Default compilation for PostgreSQL"""
    return "similarity(%s, %s)" % (
        compiler.process(element.clauses.clauses[0], **kw),
        compiler.process(element.clauses.clauses[1], **kw)
    )


@compiles(similarity, 'sqlite')
def compile_similarity_sqlite(element, compiler, **kw):
    """SQLite implementation using simple string matching"""
    # For SQLite, we'll use a simple LIKE-based similarity
    # This is a fallback that provides basic functionality
    left = compiler.process(element.clauses.clauses[0], **kw)
    right = compiler.process(element.clauses.clauses[1], **kw)
    
    # Simple similarity based on substring matching
    # Returns 1.0 for exact match, 0.5 for partial match, 0.0 for no match
    return f"""
    CASE 
        WHEN {left} = {right} THEN 1.0
        WHEN {left} LIKE '%' || {right} || '%' OR {right} LIKE '%' || {left} || '%' THEN 0.6
        WHEN length({left}) > 0 AND length({right}) > 0 AND (
            substr({left}, 1, 3) = substr({right}, 1, 3) OR
            substr({left}, -3) = substr({right}, -3)
        ) THEN 0.4
        ELSE 0.0
    END
    """


@compiles(word_similarity)
def compile_word_similarity_default(element, compiler, **kw):
    """Default compilation for PostgreSQL"""
    return "word_similarity(%s, %s)" % (
        compiler.process(element.clauses.clauses[0], **kw),
        compiler.process(element.clauses.clauses[1], **kw)
    )


@compiles(word_similarity, 'sqlite')
def compile_word_similarity_sqlite(element, compiler, **kw):
    """SQLite implementation using word-based matching"""
    left = compiler.process(element.clauses.clauses[0], **kw)
    right = compiler.process(element.clauses.clauses[1], **kw)
    
    # Simple word similarity based on substring matching
    return f"""
    CASE 
        WHEN {left} = {right} THEN 1.0
        WHEN {left} LIKE '%' || {right} || '%' THEN 0.7
        WHEN {right} LIKE '%' || {left} || '%' THEN 0.7
        WHEN length({left}) > 2 AND length({right}) > 2 AND (
            {left} LIKE substr({right}, 1, 3) || '%' OR
            {right} LIKE substr({left}, 1, 3) || '%'
        ) THEN 0.5
        ELSE 0.0
    END
    """


def get_db_specific_similarity_func():
    """Get the appropriate similarity function based on database type"""
    if settings.DATABASE_URL and 'postgresql' in settings.DATABASE_URL:
        return func.similarity
    else:
        return similarity


def get_db_specific_word_similarity_func():
    """Get the appropriate word similarity function based on database type"""
    if settings.DATABASE_URL and 'postgresql' in settings.DATABASE_URL:
        return func.word_similarity
    else:
        return word_similarity


def get_db_specific_greatest_func(*args):
    """Get database-specific greatest function"""
    if settings.DATABASE_URL and 'postgresql' in settings.DATABASE_URL:
        return func.greatest(*args)
    else:
        # SQLite doesn't have greatest, so we'll use MAX
        return func.max(*args)


def get_db_specific_concat_func(*args):
    """Get database-specific concatenation function"""
    if settings.DATABASE_URL and 'postgresql' in settings.DATABASE_URL:
        return func.concat(*args)
    else:
        # SQLite uses || for concatenation
        if len(args) == 2:
            return args[0] + args[1]
        elif len(args) == 3:
            return args[0] + args[1] + args[2]
        else:
            # For more complex cases, use SQLite's || operator
            result = args[0]
            for arg in args[1:]:
                result = result + arg
            return result


def get_db_specific_json_extract_func(json_field, path):
    """Get database-specific JSON extraction function"""
    if settings.DATABASE_URL and 'postgresql' in settings.DATABASE_URL:
        return func.jsonb_extract_path_text(json_field, path)
    else:
        # SQLite uses json_extract
        return func.json_extract(json_field, f'$.{path}')


# Compatibility functions for search
def build_compatible_search_query(query_lower, person_model, similarity_threshold=0.3):
    """Build a search query that works with both PostgreSQL and SQLite"""
    
    # Get database-specific functions
    similarity_func = get_db_specific_similarity_func()
    word_similarity_func = get_db_specific_word_similarity_func()
    greatest_func = get_db_specific_greatest_func
    concat_func = get_db_specific_concat_func
    json_extract_func = get_db_specific_json_extract_func
    
    # Build the search filters
    search_filters = []
    
    # Name-based similarity filters
    first_name_sim = similarity_func(func.lower(person_model.first_name), query_lower)
    last_name_sim = similarity_func(func.lower(person_model.last_name), query_lower)
    full_name_sim = similarity_func(
        func.lower(concat_func(person_model.first_name, ' ', person_model.last_name)), 
        query_lower
    )
    
    # Word similarity filters
    first_name_word_sim = word_similarity_func(query_lower, func.lower(person_model.first_name))
    last_name_word_sim = word_similarity_func(query_lower, func.lower(person_model.last_name))
    full_name_word_sim = word_similarity_func(
        query_lower, 
        func.lower(concat_func(person_model.first_name, ' ', person_model.last_name))
    )
    
    # Add similarity-based filters
    search_filters.extend([
        (first_name_sim >= similarity_threshold),
        (last_name_sim >= similarity_threshold),
        (full_name_sim >= similarity_threshold),
        (first_name_word_sim >= similarity_threshold),
        (last_name_word_sim >= similarity_threshold),
        (full_name_word_sim >= similarity_threshold),
    ])
    
    # Company matching (works on both databases)
    if query_lower:
        company_field = json_extract_func(person_model.professional_info, 'company')
        search_filters.append(
            func.lower(func.coalesce(company_field, '')).like(f'%{query_lower}%')
        )
    
    return {
        'filters': search_filters,
        'similarity_scores': {
            'first_name_similarity': first_name_sim,
            'last_name_similarity': last_name_sim,
            'full_name_similarity': full_name_sim,
            'first_name_word_similarity': first_name_word_sim,
            'last_name_word_similarity': last_name_word_sim,
            'full_name_word_similarity': full_name_word_sim,
        },
        'base_similarity': greatest_func(
            first_name_sim, last_name_sim, full_name_sim,
            first_name_word_sim, last_name_word_sim, full_name_word_sim
        )
    }
