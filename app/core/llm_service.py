"""
LLM Service with Function Calling Support
Provides intelligent conversation and tool execution capabilities for the Copilot
Now stateless with database-backed conversation history for multi-instance deployment
"""

import json
import logging
from typing import Any, Dict, List, Optional, Callable, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from openai import OpenAI
from sqlalchemy.orm import Session
from app.core.config import settings

logger = logging.getLogger(__name__)


class ToolCallStatus(Enum):
    SUCCESS = "success"
    ERROR = "error"
    PENDING = "pending"


@dataclass
class ToolCall:
    """Represents a function call made by the LLM"""
    id: str
    name: str
    arguments: Dict[str, Any]
    status: ToolCallStatus = ToolCallStatus.PENDING
    result: Optional[Any] = None
    error: Optional[str] = None


@dataclass
class ConversationMessage:
    """Represents a message in the conversation"""
    role: str  # "system", "user", "assistant", "tool"
    content: str
    tool_calls: Optional[List[ToolCall]] = None
    tool_call_id: Optional[str] = None
    timestamp: datetime = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


class LLMService:
    """
    Advanced LLM service with function calling capabilities
    Supports OpenAI-compatible APIs and tool execution
    Now stateless with database-backed conversation history for multi-instance deployment
    """

    def __init__(self, api_key: str = None, base_url: str = None, model: str = None, 
                 db_session: Session = None):
        self.api_key = api_key or settings.OPENAI_API_KEY
        self.base_url = base_url or settings.OPENAI_API_BASE
        self.model = model or settings.OPENAI_MODEL_NAME or "gpt-4"
        self.db = db_session
        
        if not self.api_key:
            raise ValueError("OpenAI API key is required")
        
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
        
        # Registry for available tools
        self.tools: Dict[str, Callable] = {}
        self.tool_schemas: List[Dict[str, Any]] = []

    def register_tool(self, name: str, func: Callable, schema: Dict[str, Any]):
        """Register a tool function that can be called by the LLM"""
        self.tools[name] = func
        self.tool_schemas.append({
            "type": "function",
            "function": {
                "name": name,
                **schema
            }
        })
        logger.info(f"Registered tool: {name}")

    def get_conversation(self, conversation_id: str, user_id: str = None) -> List[ConversationMessage]:
        """Get conversation history from database"""
        if not self.db or not user_id:
            return []
        
        from app.models.conversation import ConversationService
        conv_service = ConversationService(self.db)
        db_messages = conv_service.get_recent_messages(conversation_id, user_id, limit=20)
        
        # Convert database messages to ConversationMessage objects
        messages = []
        for db_msg in db_messages:
            # Convert tool calls with proper status handling
            tool_calls = []
            if db_msg.tool_calls:
                for tc_data in db_msg.tool_calls:
                    # Handle status conversion from string to enum
                    status = ToolCallStatus.SUCCESS
                    if isinstance(tc_data.get('status'), str):
                        try:
                            status = ToolCallStatus(tc_data['status'])
                        except ValueError:
                            status = ToolCallStatus.SUCCESS
                    elif hasattr(tc_data.get('status'), 'value'):
                        status = tc_data['status']
                    
                    tool_call = ToolCall(
                        id=tc_data.get('id', ''),
                        name=tc_data.get('name', ''),
                        arguments=tc_data.get('arguments', {}),
                        status=status,
                        result=tc_data.get('result'),
                        error=tc_data.get('error')
                    )
                    tool_calls.append(tool_call)
            
            msg = ConversationMessage(
                role=db_msg.role,
                content=db_msg.content,
                tool_calls=tool_calls,
                tool_call_id=db_msg.tool_call_id,
                timestamp=db_msg.created_at
            )
            messages.append(msg)
        
        return messages

    def add_message(self, conversation_id: str, message: ConversationMessage, user_id: str = None):
        """Add message to conversation history in database"""
        if not self.db or not user_id:
            return
        
        from app.models.conversation import ConversationService
        conv_service = ConversationService(self.db)
        
        # Convert tool calls to dict format for storage
        tool_calls_data = None
        if message.tool_calls:
            tool_calls_data = [
                {
                    "id": tc.id,
                    "name": tc.name,
                    "arguments": tc.arguments,
                    "status": tc.status.value,
                    "result": tc.result,
                    "error": tc.error
                }
                for tc in message.tool_calls
            ]
        
        conv_service.add_message(
            conversation_id=conversation_id,
            user_id=user_id,
            role=message.role,
            content=message.content,
            tool_calls=tool_calls_data,
            tool_call_id=message.tool_call_id,
            metadata={"timestamp": message.timestamp.isoformat()}
        )

    def clear_conversation(self, conversation_id: str, user_id: str = None):
        """Clear conversation history in database"""
        if not self.db or not user_id:
            return
        
        from app.models.conversation import ConversationService
        conv_service = ConversationService(self.db)
        conv_service.clear_conversation(conversation_id, user_id)

    async def chat_completion(
        self,
        messages: List[Dict[str, Any]],
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: str = "auto",
        max_tokens: int = 1000,
        temperature: float = 0.7
    ) -> Dict[str, Any]:
        """
        Get chat completion with optional tool calling
        """
        try:
            kwargs = {
                "model": self.model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature
            }
            
            if tools:
                kwargs["tools"] = tools
                kwargs["tool_choice"] = tool_choice
            
            response = self.client.chat.completions.create(**kwargs)
            return response
            
        except Exception as e:
            logger.error(f"Error in chat completion: {e}")
            raise

    async def execute_tool_call(self, tool_call: ToolCall, context: Dict[str, Any] = None) -> ToolCall:
        """Execute a tool call and return the result"""
        try:
            if tool_call.name not in self.tools:
                tool_call.status = ToolCallStatus.ERROR
                tool_call.error = f"Tool '{tool_call.name}' not found"
                return tool_call
            
            func = self.tools[tool_call.name]
            
            # Add context to arguments if provided
            if context:
                tool_call.arguments.update(context)
            
            # Execute the function
            if hasattr(func, '__call__'):
                if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # Check if async
                    result = await func(**tool_call.arguments)
                else:
                    result = func(**tool_call.arguments)
            else:
                raise ValueError(f"Tool '{tool_call.name}' is not callable")
            
            tool_call.result = result
            tool_call.status = ToolCallStatus.SUCCESS
            
        except Exception as e:
            logger.error(f"Error executing tool '{tool_call.name}': {e}")
            tool_call.status = ToolCallStatus.ERROR
            tool_call.error = str(e)
        
        return tool_call

    async def process_conversation(
        self,
        user_message: str,
        conversation_id: str,
        user_id: str,
        context: Dict[str, Any] = None,
        system_prompt: str = None
    ) -> Dict[str, Any]:
        """
        Process a conversation turn with potential tool calling
        """
        try:
            # Get conversation history
            conversation = self.get_conversation(conversation_id, user_id)
            
            # Build messages for API call
            messages = []
            
            # Add system prompt if provided
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            elif not conversation:  # Default system prompt for new conversations
                messages.append({
                    "role": "system", 
                    "content": self._get_default_system_prompt()
                })
            
            # Add conversation history
            for msg in conversation[-10:]:  # Keep last 10 messages for context
                if msg.role == "tool":
                    messages.append({
                        "role": "tool",
                        "content": msg.content,
                        "tool_call_id": msg.tool_call_id
                    })
                else:
                    message_dict = {"role": msg.role, "content": msg.content}
                    if msg.tool_calls:
                        message_dict["tool_calls"] = [
                            {
                                "id": tc.id,
                                "type": "function",
                                "function": {
                                    "name": tc.name,
                                    "arguments": json.dumps(tc.arguments)
                                }
                            }
                            for tc in msg.tool_calls
                        ]
                    messages.append(message_dict)
            
            # Add current user message
            messages.append({"role": "user", "content": user_message})
            
            # Add user message to conversation
            user_msg = ConversationMessage(role="user", content=user_message)
            self.add_message(conversation_id, user_msg, user_id)
            
            # Get completion with tools
            response = await self.chat_completion(
                messages=messages,
                tools=self.tool_schemas if self.tool_schemas else None
            )
            
            assistant_message = response.choices[0].message
            
            # Process tool calls if any
            tool_calls = []
            if hasattr(assistant_message, 'tool_calls') and assistant_message.tool_calls:
                for tc in assistant_message.tool_calls:
                    tool_call = ToolCall(
                        id=tc.id,
                        name=tc.function.name,
                        arguments=json.loads(tc.function.arguments)
                    )
                    
                    # Execute tool call
                    executed_call = await self.execute_tool_call(tool_call, context)
                    tool_calls.append(executed_call)
                    
                    # Add tool result to conversation
                    tool_result_msg = ConversationMessage(
                        role="tool",
                        content=json.dumps(executed_call.result) if executed_call.result else str(executed_call.error),
                        tool_call_id=executed_call.id
                    )
                    self.add_message(conversation_id, tool_result_msg, user_id)
            
            # Add assistant message to conversation
            assistant_msg = ConversationMessage(
                role="assistant",
                content=assistant_message.content or "",
                tool_calls=tool_calls
            )
            self.add_message(conversation_id, assistant_msg, user_id)
            
            # If there were tool calls, get a follow-up response
            final_response = assistant_message.content or ""
            if tool_calls:
                # Build messages including tool results
                follow_up_messages = messages + [
                    {
                        "role": "assistant",
                        "content": assistant_message.content,
                        "tool_calls": [
                            {
                                "id": tc.id,
                                "type": "function",
                                "function": {
                                    "name": tc.name,
                                    "arguments": json.dumps(tc.arguments)
                                }
                            }
                            for tc in tool_calls
                        ]
                    }
                ]
                
                # Add tool results
                for tc in tool_calls:
                    follow_up_messages.append({
                        "role": "tool",
                        "content": json.dumps(tc.result) if tc.result else str(tc.error),
                        "tool_call_id": tc.id
                    })
                
                # Get final response
                follow_up_response = await self.chat_completion(
                    messages=follow_up_messages,
                    tools=None  # No more tool calls needed
                )
                
                final_response = follow_up_response.choices[0].message.content
                
                # Add final response to conversation
                final_msg = ConversationMessage(
                    role="assistant",
                    content=final_response
                )
                self.add_message(conversation_id, final_msg, user_id)
            
            return {
                "response": final_response,
                "tool_calls": [
                    {
                        "name": tc.name,
                        "arguments": tc.arguments,
                        "result": tc.result,
                        "status": tc.status.value,
                        "error": tc.error
                    }
                    for tc in tool_calls
                ],
                "conversation_id": conversation_id,
                "message_count": len(self.get_conversation(conversation_id, user_id))
            }
            
        except Exception as e:
            logger.error(f"Error processing conversation: {e}")
            return {
                "response": "I apologize, but I encountered an error processing your request. Please try again.",
                "error": str(e),
                "conversation_id": conversation_id
            }

    def _get_default_system_prompt(self) -> str:
        """Get the default system prompt for the Nexus Copilot"""
        return """You are Nexus Copilot, an AI assistant specialized in helping users manage their professional networks and relationships.

Your capabilities include:
- Analyzing network health and providing insights
- Finding connections and suggesting introductions
- Creating and managing tasks and goals
- Searching through the user's network
- Providing relationship advice and networking strategies

You have access to various tools to help users with their networking needs. Always be helpful, professional, and focused on building meaningful connections.

When users ask for help, try to understand their intent and use the appropriate tools to provide actionable assistance."""
