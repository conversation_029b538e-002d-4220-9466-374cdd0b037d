"""
Custom middleware for security and logging
"""

import time
import uuid
from typing import Callable

import structlog
from fastapi import Request, Response
from fastapi.responses import JSONResponse

logger = structlog.get_logger()


class SecurityHeadersMiddleware:
    """Add security headers to all responses"""

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":

            async def send_wrapper(message):
                if message["type"] == "http.response.start":
                    headers = dict(message.get("headers", []))

                    # Add security headers
                    security_headers = {
                        b"x-content-type-options": b"nosniff",
                        b"x-frame-options": b"DENY",
                        b"x-xss-protection": b"1; mode=block",
                        b"strict-transport-security": b"max-age=31536000; includeSubDomains",
                        b"referrer-policy": b"strict-origin-when-cross-origin",
                        b"permissions-policy": b"geolocation=(), microphone=(), camera=()",
                    }

                    headers.update(security_headers)
                    message["headers"] = list(headers.items())

                await send(message)

            await self.app(scope, receive, send_wrapper)
        else:
            await self.app(scope, receive, send)


class RequestLoggingMiddleware:
    """Log all requests for monitoring and debugging"""

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            request_id = str(uuid.uuid4())
            start_time = time.time()

            # Log request start
            logger.info(
                "Request started",
                request_id=request_id,
                method=scope["method"],
                path=scope["path"],
                query_string=scope.get("query_string", b"").decode(),
                client=scope.get("client", ["unknown", 0])[0],
            )

            async def send_wrapper(message):
                if message["type"] == "http.response.start":
                    process_time = time.time() - start_time
                    status_code = message["status"]

                    # Log request completion
                    logger.info(
                        "Request completed",
                        request_id=request_id,
                        status_code=status_code,
                        process_time=round(process_time, 4),
                    )

                await send(message)

            await self.app(scope, receive, send_wrapper)
        else:
            await self.app(scope, receive, send)


class RateLimitMiddleware:
    """Simple rate limiting middleware"""

    def __init__(self, app, requests_per_minute: int = 60):
        self.app = app
        self.requests_per_minute = requests_per_minute
        self.request_counts = {}  # In production, use Redis

    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            client_ip = scope.get("client", ["unknown", 0])[0]
            current_minute = int(time.time() // 60)

            # Clean old entries
            self.request_counts = {
                key: count
                for key, count in self.request_counts.items()
                if key[1] >= current_minute - 1
            }

            # Check rate limit
            key = (client_ip, current_minute)
            current_count = self.request_counts.get(key, 0)

            if current_count >= self.requests_per_minute:
                # Rate limit exceeded
                response = JSONResponse(
                    status_code=429, content={"detail": "Rate limit exceeded"}
                )
                await response(scope, receive, send)
                return

            # Increment counter
            self.request_counts[key] = current_count + 1

        await self.app(scope, receive, send)
