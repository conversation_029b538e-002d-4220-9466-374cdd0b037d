"""
Data export service for generating data exports in various formats
"""

import csv
import json
import os
import uuid
from datetime import datetime
from io import String<PERSON>
from typing import Any, Dict, List, Optional

from sqlalchemy import and_
from sqlalchemy.orm import Session, joinedload

from app.models.data_job import DataJob
from app.models.goal import Goal
from app.models.interaction import Interaction
from app.models.note import Note
from app.models.organization import Organization, WorksAt
from app.models.person import Person
from app.models.relationship import Knows
from app.models.tag import Tag
from app.models.task import Task
from app.schemas.data_portability import ExportFormat, ExportRequest, ExportStats


class DataExportService:
    """Service for handling data exports"""
    
    def __init__(self, db: Session):
        self.db = db
        self.export_dir = "/tmp/nexus_exports"  # In production, use proper storage
        os.makedirs(self.export_dir, exist_ok=True)
    
    def create_export_job(
        self, 
        export_request: ExportRequest, 
        user_id: uuid.UUID
    ) -> DataJob:
        """Create a new export job"""
        # Create job record
        job = DataJob(
            job_id=uuid.uuid4(),
            user_id=user_id,
            job_type="export",
            status="pending",
            format=export_request.format.value,
            config={
                "include_privacy_data": export_request.include_privacy_data,
                "entities": export_request.entities,
                "custom_fields": export_request.custom_fields or {}
            }
        )
        
        self.db.add(job)
        self.db.commit()
        self.db.refresh(job)
        
        return job
    
    def process_export_job(self, job_id: uuid.UUID) -> bool:
        """Process an export job"""
        job = self.db.query(DataJob).filter(DataJob.job_id == job_id).first()
        
        if not job:
            return False
        
        try:
            job.mark_started()
            self.db.commit()
            
            # Get export configuration
            config = job.config or {}
            entities = config.get("entities", [])
            include_privacy_data = config.get("include_privacy_data", False)
            custom_fields = config.get("custom_fields", {})
            
            # Collect data
            export_data = self._collect_export_data(
                job.user_id, 
                entities, 
                include_privacy_data
            )
            
            # Generate file based on format
            if job.format == ExportFormat.JSON:
                file_path = self._generate_json_export(job, export_data)
            elif job.format == ExportFormat.CSV:
                file_path = self._generate_csv_export(job, export_data, custom_fields)
            elif job.format == ExportFormat.VCARD:
                file_path = self._generate_vcard_export(job, export_data)
            else:
                raise ValueError(f"Unsupported export format: {job.format}")
            
            # Update job with results
            job.file_path = file_path
            job.file_size = os.path.getsize(file_path)
            
            # Calculate statistics
            stats = self._calculate_export_stats(export_data, job)
            
            job.mark_completed(stats)
            self.db.commit()
            
            return True
            
        except Exception as e:
            job.mark_failed(str(e))
            self.db.commit()
            return False
    
    def _collect_export_data(
        self, 
        user_id: uuid.UUID, 
        entities: List[str], 
        include_privacy_data: bool
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Collect data for export"""
        export_data = {}
        
        # Persons
        if "persons" in entities:
            persons = (
                self.db.query(Person)
                .filter(Person.user_id == user_id)
                .options(joinedload(Person.work_history))
                .all()
            )
            
            export_data["persons"] = []
            for person in persons:
                person_data = {
                    "person_id": str(person.person_id),
                    "first_name": person.first_name,
                    "last_name": person.last_name,
                    "email": person.email,
                    "phone": person.phone,
                    "details": person.details or {},
                    "created_at": person.created_at.isoformat() if person.created_at else None,
                    "updated_at": person.updated_at.isoformat() if person.updated_at else None
                }
                
                # Add work history
                if person.work_history:
                    person_data["work_history"] = []
                    for work in person.work_history:
                        work_data = {
                            "org_id": str(work.org_id),
                            "role": work.role,
                            "start_date": work.start_date.isoformat() if work.start_date else None,
                            "end_date": work.end_date.isoformat() if work.end_date else None,
                            "is_current": work.is_current,
                            "details": work.details or {}
                        }
                        person_data["work_history"].append(work_data)
                
                export_data["persons"].append(person_data)
        
        # Organizations
        if "organizations" in entities:
            organizations = (
                self.db.query(Organization)
                .filter(Organization.user_id == user_id)
                .all()
            )
            
            export_data["organizations"] = []
            for org in organizations:
                org_data = {
                    "org_id": str(org.org_id),
                    "name": org.name,
                    "description": org.description,
                    "details": org.details or {},
                    "created_at": org.created_at.isoformat() if org.created_at else None,
                    "updated_at": org.updated_at.isoformat() if org.updated_at else None
                }
                export_data["organizations"].append(org_data)
        
        # Relationships
        if "relationships" in entities:
            relationships = (
                self.db.query(Knows)
                .join(Person, Knows.person_id == Person.person_id)
                .filter(Person.user_id == user_id)
                .all()
            )
            
            export_data["relationships"] = []
            for rel in relationships:
                rel_data = {
                    "person_id": str(rel.person_id),
                    "known_person_id": str(rel.known_person_id),
                    "relationship_type": rel.relationship_type,
                    "emotional_intimacy": rel.emotional_intimacy,
                    "professional_collaboration": rel.professional_collaboration,
                    "trust_level": rel.trust_level,
                    "communication_frequency": rel.communication_frequency,
                    "shared_experience_value": rel.shared_experience_value,
                    "reciprocity_balance": rel.reciprocity_balance,
                    "notes": rel.notes,
                    "created_at": rel.created_at.isoformat() if rel.created_at else None,
                    "updated_at": rel.updated_at.isoformat() if rel.updated_at else None
                }
                export_data["relationships"].append(rel_data)
        
        # Goals
        if "goals" in entities:
            goals = (
                self.db.query(Goal)
                .filter(Goal.user_id == user_id)
                .all()
            )
            
            export_data["goals"] = []
            for goal in goals:
                goal_data = {
                    "goal_id": str(goal.goal_id),
                    "title": goal.title,
                    "description": goal.description,
                    "status": goal.status,
                    "priority": goal.priority,
                    "target_date": goal.target_date.isoformat() if goal.target_date else None,
                    "completion_date": goal.completion_date.isoformat() if goal.completion_date else None,
                    "details": goal.details or {},
                    "created_at": goal.created_at.isoformat() if goal.created_at else None,
                    "updated_at": goal.updated_at.isoformat() if goal.updated_at else None
                }
                export_data["goals"].append(goal_data)
        
        # Tasks
        if "tasks" in entities:
            tasks = (
                self.db.query(Task)
                .filter(Task.user_id == user_id)
                .all()
            )
            
            export_data["tasks"] = []
            for task in tasks:
                task_data = {
                    "task_id": str(task.task_id),
                    "title": task.title,
                    "description": task.description,
                    "status": task.status,
                    "priority": task.priority,
                    "due_date": task.due_date.isoformat() if task.due_date else None,
                    "completion_date": task.completion_date.isoformat() if task.completion_date else None,
                    "person_id": str(task.person_id) if task.person_id else None,
                    "goal_id": str(task.goal_id) if task.goal_id else None,
                    "details": task.details or {},
                    "created_at": task.created_at.isoformat() if task.created_at else None,
                    "updated_at": task.updated_at.isoformat() if task.updated_at else None
                }
                export_data["tasks"].append(task_data)
        
        # Interactions
        if "interactions" in entities:
            interactions = (
                self.db.query(Interaction)
                .filter(Interaction.user_id == user_id)
                .all()
            )
            
            export_data["interactions"] = []
            for interaction in interactions:
                interaction_data = {
                    "interaction_id": str(interaction.interaction_id),
                    "interaction_type": interaction.interaction_type,
                    "date": interaction.date.isoformat() if interaction.date else None,
                    "duration": interaction.duration,
                    "location": interaction.location,
                    "notes": interaction.notes,
                    "person_id": str(interaction.person_id) if interaction.person_id else None,
                    "details": interaction.details or {},
                    "created_at": interaction.created_at.isoformat() if interaction.created_at else None,
                    "updated_at": interaction.updated_at.isoformat() if interaction.updated_at else None
                }
                export_data["interactions"].append(interaction_data)
        
        # Notes
        if "notes" in entities:
            notes = (
                self.db.query(Note)
                .filter(Note.user_id == user_id)
                .all()
            )
            
            export_data["notes"] = []
            for note in notes:
                note_data = {
                    "note_id": str(note.note_id),
                    "title": note.title,
                    "content": note.content,
                    "person_id": str(note.person_id) if note.person_id else None,
                    "goal_id": str(note.goal_id) if note.goal_id else None,
                    "task_id": str(note.task_id) if note.task_id else None,
                    "details": note.details or {},
                    "created_at": note.created_at.isoformat() if note.created_at else None,
                    "updated_at": note.updated_at.isoformat() if note.updated_at else None
                }
                export_data["notes"].append(note_data)
        
        # Tags
        if "tags" in entities:
            tags = (
                self.db.query(Tag)
                .filter(Tag.user_id == user_id)
                .all()
            )
            
            export_data["tags"] = []
            for tag in tags:
                tag_data = {
                    "tag_id": str(tag.tag_id),
                    "name": tag.name,
                    "color": tag.color,
                    "description": tag.description,
                    "created_at": tag.created_at.isoformat() if tag.created_at else None,
                    "updated_at": tag.updated_at.isoformat() if tag.updated_at else None
                }
                export_data["tags"].append(tag_data)
        
        return export_data
    
    def _generate_json_export(self, job: DataJob, export_data: Dict[str, List[Dict[str, Any]]]) -> str:
        """Generate JSON export file"""
        filename = f"nexus_export_{job.job_id}.json"
        file_path = os.path.join(self.export_dir, filename)
        
        # Add metadata
        export_package = {
            "metadata": {
                "export_id": str(job.job_id),
                "export_date": datetime.utcnow().isoformat(),
                "format": "json",
                "version": "1.0",
                "user_id": str(job.user_id)
            },
            "data": export_data
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(export_package, f, indent=2, ensure_ascii=False)
        
        return file_path
    
    def _generate_csv_export(
        self, 
        job: DataJob, 
        export_data: Dict[str, List[Dict[str, Any]]], 
        custom_fields: Dict[str, Any]
    ) -> str:
        """Generate CSV export file (creates separate CSV files for each entity)"""
        filename = f"nexus_export_{job.job_id}.csv"
        file_path = os.path.join(self.export_dir, filename)
        
        # For CSV, we'll focus on persons as the main entity
        # This is a simplified version - in production, you might want separate CSVs per entity
        if "persons" in export_data and export_data["persons"]:
            persons_data = export_data["persons"]
            
            # Define CSV headers
            headers = ["person_id", "first_name", "last_name", "email", "phone", "created_at"]
            
            # Apply custom field mappings if provided
            if custom_fields:
                headers = [custom_fields.get(field, field) for field in headers]
            
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=headers)
                writer.writeheader()
                
                for person in persons_data:
                    row = {
                        "person_id": person.get("person_id", ""),
                        "first_name": person.get("first_name", ""),
                        "last_name": person.get("last_name", ""),
                        "email": person.get("email", ""),
                        "phone": person.get("phone", ""),
                        "created_at": person.get("created_at", "")
                    }
                    
                    # Apply custom field mappings
                    if custom_fields:
                        mapped_row = {}
                        for old_field, new_field in custom_fields.items():
                            if old_field in row:
                                mapped_row[new_field] = row[old_field]
                        row = mapped_row
                    
                    writer.writerow(row)
        else:
            # Create empty CSV if no persons data
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(["No data to export"])
        
        return file_path
    
    def _generate_vcard_export(self, job: DataJob, export_data: Dict[str, List[Dict[str, Any]]]) -> str:
        """Generate vCard export file"""
        filename = f"nexus_export_{job.job_id}.vcf"
        file_path = os.path.join(self.export_dir, filename)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            if "persons" in export_data:
                for person in export_data["persons"]:
                    # vCard format
                    f.write("BEGIN:VCARD\n")
                    f.write("VERSION:3.0\n")
                    
                    # Name
                    first_name = person.get("first_name", "")
                    last_name = person.get("last_name", "")
                    if first_name or last_name:
                        f.write(f"FN:{first_name} {last_name}\n".strip())
                        f.write(f"N:{last_name};{first_name};;;\n")
                    
                    # Email
                    if person.get("email"):
                        f.write(f"EMAIL:{person['email']}\n")
                    
                    # Phone
                    if person.get("phone"):
                        f.write(f"TEL:{person['phone']}\n")
                    
                    # Organization (from work history)
                    if person.get("work_history"):
                        for work in person["work_history"]:
                            if work.get("is_current") and work.get("role"):
                                f.write(f"TITLE:{work['role']}\n")
                                break
                    
                    # Notes
                    if person.get("details") and person["details"].get("notes"):
                        f.write(f"NOTE:{person['details']['notes']}\n")
                    
                    f.write("END:VCARD\n")
        
        return file_path
    
    def _calculate_export_stats(self, export_data: Dict[str, List[Dict[str, Any]]], job: DataJob) -> Dict[str, Any]:
        """Calculate export statistics"""
        stats = {
            "total_persons": len(export_data.get("persons", [])),
            "total_organizations": len(export_data.get("organizations", [])),
            "total_relationships": len(export_data.get("relationships", [])),
            "total_goals": len(export_data.get("goals", [])),
            "total_tasks": len(export_data.get("tasks", [])),
            "total_interactions": len(export_data.get("interactions", [])),
            "total_notes": len(export_data.get("notes", [])),
            "total_tags": len(export_data.get("tags", [])),
            "export_size_bytes": job.file_size or 0,
            "processing_time_seconds": job.get_processing_time()
        }
        
        return stats
    
    def get_export_job(self, job_id: uuid.UUID, user_id: uuid.UUID) -> Optional[DataJob]:
        """Get export job by ID"""
        return (
            self.db.query(DataJob)
            .filter(
                and_(
                    DataJob.job_id == job_id,
                    DataJob.user_id == user_id,
                    DataJob.job_type == "export"
                )
            )
            .first()
        )
    
    def get_export_jobs(self, user_id: uuid.UUID, limit: int = 50) -> List[DataJob]:
        """Get user's export jobs"""
        return (
            self.db.query(DataJob)
            .filter(
                and_(
                    DataJob.user_id == user_id,
                    DataJob.job_type == "export"
                )
            )
            .order_by(DataJob.created_at.desc())
            .limit(limit)
            .all()
        )
    
    def get_file_path(self, job_id: uuid.UUID, user_id: uuid.UUID) -> Optional[str]:
        """Get file path for download"""
        job = self.get_export_job(job_id, user_id)
        
        if not job or job.status != "completed" or job.is_expired():
            return None
        
        return job.file_path
    
    def cleanup_expired_jobs(self):
        """Clean up expired export jobs and files"""
        expired_jobs = (
            self.db.query(DataJob)
            .filter(
                and_(
                    DataJob.job_type == "export",
                    DataJob.expires_at < datetime.utcnow()
                )
            )
            .all()
        )
        
        for job in expired_jobs:
            # Delete file if exists
            if job.file_path and os.path.exists(job.file_path):
                try:
                    os.remove(job.file_path)
                except OSError:
                    pass
            
            # Delete job record
            self.db.delete(job)
        
        self.db.commit()
        return len(expired_jobs)