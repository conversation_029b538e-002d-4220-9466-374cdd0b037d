"""
Data import service for processing uploaded data files
"""

import csv
import json
import uuid
from datetime import datetime
from io import String<PERSON>
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import and_
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.models.data_job import DataJob
from app.models.organization import Organization
from app.models.person import Person
from app.schemas.data_portability import ImportFormat, ImportRequest, DataPreview


class DataImportService:
    """Service for handling data imports"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_import_job(
        self, 
        import_request: ImportRequest,
        filename: str,
        file_size: int,
        user_id: uuid.UUID
    ) -> DataJob:
        """Create a new import job"""
        job = DataJob(
            job_id=uuid.uuid4(),
            user_id=user_id,
            job_type="import",
            status="pending",
            format=import_request.format.value,
            filename=filename,
            file_size=file_size,
            config={
                "duplicate_handling": import_request.duplicate_handling,
                "validation_strict": import_request.validation_strict,
                "custom_mappings": import_request.custom_mappings or {}
            }
        )
        
        self.db.add(job)
        self.db.commit()
        self.db.refresh(job)
        
        return job
    
    def preview_import_data(
        self, 
        file_content: str, 
        format: ImportFormat
    ) -> DataPreview:
        """Preview import data and suggest mappings"""
        try:
            if format == ImportFormat.CSV:
                return self._preview_csv_data(file_content)
            elif format == ImportFormat.JSON:
                return self._preview_json_data(file_content)
            elif format == ImportFormat.VCARD:
                return self._preview_vcard_data(file_content)
            else:
                raise ValueError(f"Unsupported format for preview: {format}")
        except Exception as e:
            return DataPreview(
                sample_records=[],
                detected_columns=[],
                suggested_mappings={},
                record_count=0,
                validation_errors=[f"Error parsing file: {str(e)}"]
            )
    
    def process_import_job(self, job_id: uuid.UUID, file_content: str) -> bool:
        """Process an import job"""
        job = self.db.query(DataJob).filter(DataJob.job_id == job_id).first()
        
        if not job:
            return False
        
        try:
            job.mark_started()
            self.db.commit()
            
            # Get import configuration
            config = job.config or {}
            duplicate_handling = config.get("duplicate_handling", "skip")
            validation_strict = config.get("validation_strict", True)
            custom_mappings = config.get("custom_mappings", {})
            
            # Process data based on format
            if job.format == ImportFormat.JSON:
                results = self._process_json_import(
                    file_content, job.user_id, duplicate_handling, validation_strict, custom_mappings
                )
            elif job.format == ImportFormat.CSV:
                results = self._process_csv_import(
                    file_content, job.user_id, duplicate_handling, validation_strict, custom_mappings
                )
            elif job.format == ImportFormat.VCARD:
                results = self._process_vcard_import(
                    file_content, job.user_id, duplicate_handling, validation_strict
                )
            else:
                raise ValueError(f"Unsupported import format: {job.format}")
            
            # Update job with results
            job.records_total = results["total"]
            job.records_processed = results["processed"]
            job.records_imported = results["imported"]
            job.records_failed = results["failed"]
            job.records_skipped = results["skipped"]
            job.errors = results["errors"]
            job.warnings = results["warnings"]
            
            # Calculate statistics
            stats = self._calculate_import_stats(results, job)
            
            job.mark_completed(stats)
            self.db.commit()
            
            return True
            
        except Exception as e:
            job.mark_failed(str(e))
            self.db.commit()
            return False
    
    def _preview_csv_data(self, file_content: str) -> DataPreview:
        """Preview CSV data"""
        reader = csv.DictReader(StringIO(file_content))
        
        # Get first few rows as samples
        sample_records = []
        for i, row in enumerate(reader):
            if i >= 5:  # Limit to 5 sample records
                break
            sample_records.append(dict(row))
        
        # Reset to count total records
        reader = csv.DictReader(StringIO(file_content))
        record_count = sum(1 for _ in reader)
        
        # Get detected columns
        detected_columns = list(reader.fieldnames) if reader.fieldnames else []
        
        # Suggest mappings based on common field names
        suggested_mappings = self._suggest_csv_mappings(detected_columns)
        
        # Basic validation
        validation_errors = []
        if not detected_columns:
            validation_errors.append("No columns detected in CSV file")
        
        return DataPreview(
            sample_records=sample_records,
            detected_columns=detected_columns,
            suggested_mappings=suggested_mappings,
            record_count=record_count,
            validation_errors=validation_errors
        )
    
    def _preview_json_data(self, file_content: str) -> DataPreview:
        """Preview JSON data"""
        data = json.loads(file_content)
        
        # Handle different JSON structures
        if isinstance(data, list):
            records = data
        elif isinstance(data, dict):
            if "data" in data:
                # Nexus export format
                if isinstance(data["data"], dict) and "persons" in data["data"]:
                    records = data["data"]["persons"]
                else:
                    records = data["data"] if isinstance(data["data"], list) else [data["data"]]
            else:
                records = [data]
        else:
            records = [data]
        
        # Get sample records
        sample_records = records[:5] if len(records) > 5 else records
        
        # Get detected columns from first record
        detected_columns = []
        if records:
            detected_columns = list(records[0].keys()) if isinstance(records[0], dict) else []
        
        # Suggest mappings
        suggested_mappings = self._suggest_json_mappings(detected_columns)
        
        validation_errors = []
        if not records:
            validation_errors.append("No records found in JSON file")
        
        return DataPreview(
            sample_records=sample_records,
            detected_columns=detected_columns,
            suggested_mappings=suggested_mappings,
            record_count=len(records),
            validation_errors=validation_errors
        )
    
    def _preview_vcard_data(self, file_content: str) -> DataPreview:
        """Preview vCard data"""
        # Simple vCard parsing for preview
        vcards = []
        current_vcard = {}
        
        for line in file_content.split('\n'):
            line = line.strip()
            if line == "BEGIN:VCARD":
                current_vcard = {}
            elif line == "END:VCARD":
                if current_vcard:
                    vcards.append(current_vcard)
            elif ':' in line:
                key, value = line.split(':', 1)
                current_vcard[key] = value
        
        # Get sample records
        sample_records = vcards[:5] if len(vcards) > 5 else vcards
        
        # Common vCard fields
        detected_columns = ["FN", "N", "EMAIL", "TEL", "ORG", "TITLE"]
        
        # Map vCard fields to person fields
        suggested_mappings = {
            "FN": "full_name",
            "EMAIL": "email",
            "TEL": "phone",
            "ORG": "organization",
            "TITLE": "role"
        }
        
        validation_errors = []
        if not vcards:
            validation_errors.append("No valid vCard records found")
        
        return DataPreview(
            sample_records=sample_records,
            detected_columns=detected_columns,
            suggested_mappings=suggested_mappings,
            record_count=len(vcards),
            validation_errors=validation_errors
        )
    
    def _suggest_csv_mappings(self, columns: List[str]) -> Dict[str, str]:
        """Suggest field mappings for CSV columns"""
        mappings = {}
        
        # Common field mappings
        field_map = {
            # Name fields
            "first_name": ["first_name", "firstname", "first", "fname", "given_name"],
            "last_name": ["last_name", "lastname", "last", "lname", "surname", "family_name"],
            "full_name": ["full_name", "fullname", "name", "display_name"],
            
            # Contact fields
            "email": ["email", "email_address", "e_mail", "mail"],
            "phone": ["phone", "telephone", "tel", "phone_number", "mobile"],
            
            # Organization fields
            "organization": ["organization", "company", "org", "employer", "workplace"],
            "role": ["role", "title", "position", "job_title", "job"],
            
            # Location fields
            "address": ["address", "street", "location"],
            "city": ["city", "town"],
            "state": ["state", "province", "region"],
            "country": ["country"],
            "zip_code": ["zip", "zipcode", "postal_code", "postcode"]
        }
        
        for column in columns:
            column_lower = column.lower().strip()
            for field, variations in field_map.items():
                if column_lower in variations:
                    mappings[column] = field
                    break
        
        return mappings
    
    def _suggest_json_mappings(self, fields: List[str]) -> Dict[str, str]:
        """Suggest field mappings for JSON fields"""
        # For JSON, we assume fields are already in the correct format
        # but provide mappings for common variations
        mappings = {}
        
        standard_fields = {
            "person_id", "first_name", "last_name", "email", "phone",
            "org_id", "name", "description", "details"
        }
        
        for field in fields:
            if field in standard_fields:
                mappings[field] = field
            # Handle common variations
            elif field == "id":
                mappings[field] = "person_id"
            elif field == "organization_name":
                mappings[field] = "name"
        
        return mappings
    
    def _process_csv_import(
        self, 
        file_content: str, 
        user_id: uuid.UUID, 
        duplicate_handling: str,
        validation_strict: bool,
        custom_mappings: Dict[str, str]
    ) -> Dict[str, Any]:
        """Process CSV import"""
        results = {
            "total": 0,
            "processed": 0,
            "imported": 0,
            "failed": 0,
            "skipped": 0,
            "errors": [],
            "warnings": []
        }
        
        try:
            reader = csv.DictReader(StringIO(file_content))
            
            # Count total records first
            total_records = sum(1 for _ in csv.DictReader(StringIO(file_content)))
            results["total"] = total_records
            
            # Process each record
            for row_num, row in enumerate(reader, 1):
                try:
                    results["processed"] += 1
                    
                    # Apply custom mappings
                    mapped_row = {}
                    for csv_field, value in row.items():
                        db_field = custom_mappings.get(csv_field, csv_field.lower().replace(' ', '_'))
                        mapped_row[db_field] = value.strip() if value else None
                    
                    # Create person from CSV row
                    person_data = {
                        "first_name": mapped_row.get("first_name", ""),
                        "last_name": mapped_row.get("last_name", ""),
                        "email": mapped_row.get("email"),
                        "phone": mapped_row.get("phone"),
                        "user_id": user_id
                    }
                    
                    # Validation
                    if validation_strict and not (person_data["first_name"] or person_data["last_name"]):
                        results["failed"] += 1
                        results["errors"].append(f"Row {row_num}: First name or last name required")
                        continue
                    
                    # Check for duplicates
                    existing_person = None
                    if person_data["email"]:
                        existing_person = (
                            self.db.query(Person)
                            .filter(
                                and_(
                                    Person.user_id == user_id,
                                    Person.email == person_data["email"]
                                )
                            )
                            .first()
                        )
                    
                    if existing_person:
                        if duplicate_handling == "skip":
                            results["skipped"] += 1
                            results["warnings"].append(f"Row {row_num}: Skipped duplicate email {person_data['email']}")
                            continue
                        elif duplicate_handling == "update":
                            # Update existing person
                            for key, value in person_data.items():
                                if key != "user_id" and value:
                                    setattr(existing_person, key, value)
                            self.db.commit()
                            results["imported"] += 1
                            continue
                    
                    # Create new person
                    person = Person(**person_data)
                    self.db.add(person)
                    self.db.commit()
                    results["imported"] += 1
                    
                except Exception as e:
                    results["failed"] += 1
                    results["errors"].append(f"Row {row_num}: {str(e)}")
                    self.db.rollback()
            
        except Exception as e:
            results["errors"].append(f"CSV processing error: {str(e)}")
        
        return results
    
    def _process_json_import(
        self, 
        file_content: str, 
        user_id: uuid.UUID, 
        duplicate_handling: str,
        validation_strict: bool,
        custom_mappings: Dict[str, str]
    ) -> Dict[str, Any]:
        """Process JSON import"""
        results = {
            "total": 0,
            "processed": 0,
            "imported": 0,
            "failed": 0,
            "skipped": 0,
            "errors": [],
            "warnings": []
        }
        
        try:
            data = json.loads(file_content)
            
            # Handle Nexus export format
            if isinstance(data, dict) and "data" in data:
                import_data = data["data"]
            else:
                import_data = {"persons": data if isinstance(data, list) else [data]}
            
            # Import persons
            if "persons" in import_data:
                persons = import_data["persons"]
                results["total"] += len(persons)
                
                for person_data in persons:
                    try:
                        results["processed"] += 1
                        
                        # Apply custom mappings
                        mapped_data = {}
                        for key, value in person_data.items():
                            mapped_key = custom_mappings.get(key, key)
                            mapped_data[mapped_key] = value
                        
                        # Create person
                        person_fields = {
                            "first_name": mapped_data.get("first_name", ""),
                            "last_name": mapped_data.get("last_name", ""),
                            "email": mapped_data.get("email"),
                            "phone": mapped_data.get("phone"),
                            "details": mapped_data.get("details", {}),
                            "user_id": user_id
                        }
                        
                        # Validation
                        if validation_strict and not (person_fields["first_name"] or person_fields["last_name"]):
                            results["failed"] += 1
                            results["errors"].append(f"Person: First name or last name required")
                            continue
                        
                        # Check for duplicates
                        existing_person = None
                        if person_fields["email"]:
                            existing_person = (
                                self.db.query(Person)
                                .filter(
                                    and_(
                                        Person.user_id == user_id,
                                        Person.email == person_fields["email"]
                                    )
                                )
                                .first()
                            )
                        
                        if existing_person:
                            if duplicate_handling == "skip":
                                results["skipped"] += 1
                                continue
                            elif duplicate_handling == "update":
                                for key, value in person_fields.items():
                                    if key != "user_id" and value:
                                        setattr(existing_person, key, value)
                                self.db.commit()
                                results["imported"] += 1
                                continue
                        
                        # Create new person
                        person = Person(**person_fields)
                        self.db.add(person)
                        self.db.commit()
                        results["imported"] += 1
                        
                    except Exception as e:
                        results["failed"] += 1
                        results["errors"].append(f"Person import error: {str(e)}")
                        self.db.rollback()
            
            # Import organizations
            if "organizations" in import_data:
                organizations = import_data["organizations"]
                results["total"] += len(organizations)
                
                for org_data in organizations:
                    try:
                        results["processed"] += 1
                        
                        org_fields = {
                            "name": org_data.get("name", ""),
                            "description": org_data.get("description"),
                            "details": org_data.get("details", {}),
                            "user_id": user_id
                        }
                        
                        if validation_strict and not org_fields["name"]:
                            results["failed"] += 1
                            results["errors"].append("Organization: Name is required")
                            continue
                        
                        # Check for duplicates
                        existing_org = (
                            self.db.query(Organization)
                            .filter(
                                and_(
                                    Organization.user_id == user_id,
                                    Organization.name == org_fields["name"]
                                )
                            )
                            .first()
                        )
                        
                        if existing_org:
                            if duplicate_handling == "skip":
                                results["skipped"] += 1
                                continue
                            elif duplicate_handling == "update":
                                for key, value in org_fields.items():
                                    if key != "user_id" and value:
                                        setattr(existing_org, key, value)
                                self.db.commit()
                                results["imported"] += 1
                                continue
                        
                        # Create new organization
                        organization = Organization(**org_fields)
                        self.db.add(organization)
                        self.db.commit()
                        results["imported"] += 1
                        
                    except Exception as e:
                        results["failed"] += 1
                        results["errors"].append(f"Organization import error: {str(e)}")
                        self.db.rollback()
        
        except Exception as e:
            results["errors"].append(f"JSON processing error: {str(e)}")
        
        return results
    
    def _process_vcard_import(
        self, 
        file_content: str, 
        user_id: uuid.UUID, 
        duplicate_handling: str,
        validation_strict: bool
    ) -> Dict[str, Any]:
        """Process vCard import"""
        results = {
            "total": 0,
            "processed": 0,
            "imported": 0,
            "failed": 0,
            "skipped": 0,
            "errors": [],
            "warnings": []
        }
        
        try:
            # Parse vCard data
            vcards = []
            current_vcard = {}
            
            for line in file_content.split('\n'):
                line = line.strip()
                if line == "BEGIN:VCARD":
                    current_vcard = {}
                elif line == "END:VCARD":
                    if current_vcard:
                        vcards.append(current_vcard)
                elif ':' in line:
                    key, value = line.split(':', 1)
                    current_vcard[key] = value
            
            results["total"] = len(vcards)
            
            for vcard in vcards:
                try:
                    results["processed"] += 1
                    
                    # Parse name
                    first_name = ""
                    last_name = ""
                    
                    if "FN" in vcard:
                        # Full name
                        full_name = vcard["FN"]
                        name_parts = full_name.split()
                        if len(name_parts) >= 1:
                            first_name = name_parts[0]
                        if len(name_parts) >= 2:
                            last_name = " ".join(name_parts[1:])
                    elif "N" in vcard:
                        # Structured name (Last;First;Middle;Prefix;Suffix)
                        name_parts = vcard["N"].split(';')
                        if len(name_parts) >= 2:
                            last_name = name_parts[0]
                            first_name = name_parts[1]
                    
                    person_data = {
                        "first_name": first_name,
                        "last_name": last_name,
                        "email": vcard.get("EMAIL"),
                        "phone": vcard.get("TEL"),
                        "user_id": user_id
                    }
                    
                    # Validation
                    if validation_strict and not (first_name or last_name):
                        results["failed"] += 1
                        results["errors"].append("vCard: Name is required")
                        continue
                    
                    # Check for duplicates
                    existing_person = None
                    if person_data["email"]:
                        existing_person = (
                            self.db.query(Person)
                            .filter(
                                and_(
                                    Person.user_id == user_id,
                                    Person.email == person_data["email"]
                                )
                            )
                            .first()
                        )
                    
                    if existing_person:
                        if duplicate_handling == "skip":
                            results["skipped"] += 1
                            continue
                        elif duplicate_handling == "update":
                            for key, value in person_data.items():
                                if key != "user_id" and value:
                                    setattr(existing_person, key, value)
                            self.db.commit()
                            results["imported"] += 1
                            continue
                    
                    # Create new person
                    person = Person(**person_data)
                    self.db.add(person)
                    self.db.commit()
                    results["imported"] += 1
                    
                except Exception as e:
                    results["failed"] += 1
                    results["errors"].append(f"vCard import error: {str(e)}")
                    self.db.rollback()
        
        except Exception as e:
            results["errors"].append(f"vCard processing error: {str(e)}")
        
        return results
    
    def _calculate_import_stats(self, results: Dict[str, Any], job: DataJob) -> Dict[str, Any]:
        """Calculate import statistics"""
        stats = {
            "file_size_bytes": job.file_size or 0,
            "processing_time_seconds": job.get_processing_time(),
            "persons_imported": results.get("imported", 0),
            "organizations_imported": 0,  # Would need to track separately
            "relationships_imported": 0,
            "goals_imported": 0,
            "tasks_imported": 0,
            "interactions_imported": 0,
            "notes_imported": 0,
            "tags_imported": 0
        }
        
        return stats
    
    def get_import_job(self, job_id: uuid.UUID, user_id: uuid.UUID) -> Optional[DataJob]:
        """Get import job by ID"""
        return (
            self.db.query(DataJob)
            .filter(
                and_(
                    DataJob.job_id == job_id,
                    DataJob.user_id == user_id,
                    DataJob.job_type == "import"
                )
            )
            .first()
        )
    
    def get_import_jobs(self, user_id: uuid.UUID, limit: int = 50) -> List[DataJob]:
        """Get user's import jobs"""
        return (
            self.db.query(DataJob)
            .filter(
                and_(
                    DataJob.user_id == user_id,
                    DataJob.job_type == "import"
                )
            )
            .order_by(DataJob.created_at.desc())
            .limit(limit)
            .all()
        )