"""
Relationship Service - Advanced relationship management with archetypes and validation
Enhances the relationship functionality with templates, validation, and advanced features
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Set
from uuid import UUID
from enum import Enum

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from sqlalchemy.orm.attributes import flag_modified

from app.models.relationship import Knows
from app.models.person import Person
from app.models.user import User


class RelationshipArchetype(str, Enum):
    """Predefined relationship archetypes with their characteristics"""
    COLLEAGUE = "colleague"
    FRIEND = "friend"
    MENTOR = "mentor"
    MENTEE = "mentee"
    CLIENT = "client"
    VENDOR = "vendor"
    FAMILY = "family"
    ACQUAINTANCE = "acquaintance"
    PARTNER = "partner"
    NEIGHBOR = "neighbor"
    CLASSMATE = "classmate"
    TEAMMATE = "teammate"


class RelationshipFoundationType(str, Enum):
    """How relationships are typically founded"""
    WORK = "work"
    EDUCATION = "education"
    SOCIAL = "social"
    FAMILY = "family"
    PROFESSIONAL_EVENT = "professional_event"
    INTRODUCTION = "introduction"
    ONLINE = "online"
    NEIGHBORHOOD = "neighborhood"
    HOBBY = "hobby"
    TRAVEL = "travel"


class RelationshipService:
    """
    Advanced relationship management service with archetype templates and validation
    """

    def __init__(self, db: Session):
        self.db = db
        self.archetype_templates = self._initialize_archetype_templates()

    def _initialize_archetype_templates(self) -> Dict[str, Dict[str, Any]]:
        """Initialize predefined archetype templates with default dimensions and characteristics"""
        return {
            RelationshipArchetype.COLLEAGUE.value: {
                "default_dimensions": {
                    "emotional_intimacy": 30,
                    "professional_collaboration": 80,
                    "trust_level": 60,
                    "communication_frequency": 70,
                    "shared_experience_value": 50,
                    "reciprocity_balance": 65
                },
                "foundation_types": [
                    RelationshipFoundationType.WORK.value,
                    RelationshipFoundationType.PROFESSIONAL_EVENT.value
                ],
                "typical_interactions": ["meeting", "email", "collaboration", "presentation"],
                "growth_opportunities": [
                    "Increase collaboration on projects",
                    "Share professional knowledge",
                    "Provide mutual support in work challenges"
                ],
                "maintenance_frequency": "weekly",
                "expected_evolution": "Can develop into mentor/mentee or friend relationships"
            },
            
            RelationshipArchetype.FRIEND.value: {
                "default_dimensions": {
                    "emotional_intimacy": 75,
                    "professional_collaboration": 30,
                    "trust_level": 80,
                    "communication_frequency": 60,
                    "shared_experience_value": 85,
                    "reciprocity_balance": 75
                },
                "foundation_types": [
                    RelationshipFoundationType.SOCIAL.value,
                    RelationshipFoundationType.EDUCATION.value,
                    RelationshipFoundationType.HOBBY.value
                ],
                "typical_interactions": ["social_event", "phone_call", "text", "hangout"],
                "growth_opportunities": [
                    "Increase frequency of personal interactions",
                    "Share deeper personal experiences",
                    "Provide emotional support"
                ],
                "maintenance_frequency": "weekly",
                "expected_evolution": "Can deepen into close friend or life partner"
            },
            
            RelationshipArchetype.MENTOR.value: {
                "default_dimensions": {
                    "emotional_intimacy": 50,
                    "professional_collaboration": 70,
                    "trust_level": 85,
                    "communication_frequency": 40,
                    "shared_experience_value": 60,
                    "reciprocity_balance": 30  # Mentor gives more than receives
                },
                "foundation_types": [
                    RelationshipFoundationType.WORK.value,
                    RelationshipFoundationType.PROFESSIONAL_EVENT.value,
                    RelationshipFoundationType.INTRODUCTION.value
                ],
                "typical_interactions": ["mentoring_session", "advice", "feedback", "guidance"],
                "growth_opportunities": [
                    "Regular mentoring sessions",
                    "Career guidance and advice",
                    "Professional network introductions"
                ],
                "maintenance_frequency": "monthly",
                "expected_evolution": "May evolve into colleague or long-term advisor relationship"
            },
            
            RelationshipArchetype.MENTEE.value: {
                "default_dimensions": {
                    "emotional_intimacy": 45,
                    "professional_collaboration": 65,
                    "trust_level": 75,
                    "communication_frequency": 50,
                    "shared_experience_value": 55,
                    "reciprocity_balance": 70  # Mentee receives more than gives
                },
                "foundation_types": [
                    RelationshipFoundationType.WORK.value,
                    RelationshipFoundationType.PROFESSIONAL_EVENT.value
                ],
                "typical_interactions": ["learning_session", "question", "update", "progress_report"],
                "growth_opportunities": [
                    "Regular learning sessions",
                    "Skill development",
                    "Career progression support"
                ],
                "maintenance_frequency": "weekly",
                "expected_evolution": "May evolve into colleague or peer relationship"
            },
            
            RelationshipArchetype.CLIENT.value: {
                "default_dimensions": {
                    "emotional_intimacy": 25,
                    "professional_collaboration": 85,
                    "trust_level": 70,
                    "communication_frequency": 80,
                    "shared_experience_value": 40,
                    "reciprocity_balance": 60
                },
                "foundation_types": [
                    RelationshipFoundationType.WORK.value,
                    RelationshipFoundationType.PROFESSIONAL_EVENT.value
                ],
                "typical_interactions": ["meeting", "presentation", "email", "proposal"],
                "growth_opportunities": [
                    "Deliver exceptional service",
                    "Build long-term partnership",
                    "Expand collaboration scope"
                ],
                "maintenance_frequency": "bi-weekly",
                "expected_evolution": "Can develop into long-term business partnership"
            },
            
            RelationshipArchetype.FAMILY.value: {
                "default_dimensions": {
                    "emotional_intimacy": 90,
                    "professional_collaboration": 20,
                    "trust_level": 95,
                    "communication_frequency": 70,
                    "shared_experience_value": 95,
                    "reciprocity_balance": 80
                },
                "foundation_types": [RelationshipFoundationType.FAMILY.value],
                "typical_interactions": ["family_gathering", "phone_call", "visit", "celebration"],
                "growth_opportunities": [
                    "Increase quality time together",
                    "Share important life events",
                    "Provide emotional support"
                ],
                "maintenance_frequency": "weekly",
                "expected_evolution": "Lifelong relationship with varying intensity"
            },
            
            RelationshipArchetype.ACQUAINTANCE.value: {
                "default_dimensions": {
                    "emotional_intimacy": 20,
                    "professional_collaboration": 30,
                    "trust_level": 40,
                    "communication_frequency": 20,
                    "shared_experience_value": 25,
                    "reciprocity_balance": 50
                },
                "foundation_types": [
                    RelationshipFoundationType.SOCIAL.value,
                    RelationshipFoundationType.NEIGHBORHOOD.value,
                    RelationshipFoundationType.INTRODUCTION.value
                ],
                "typical_interactions": ["greeting", "small_talk", "brief_encounter"],
                "growth_opportunities": [
                    "Find common interests",
                    "Increase interaction frequency",
                    "Develop deeper conversations"
                ],
                "maintenance_frequency": "monthly",
                "expected_evolution": "Can develop into friend or colleague relationship"
            }
        }

    def create_relationship(
        self,
        from_person_id: UUID,
        to_person_id: UUID,
        user_id: UUID,
        archetype: str,
        relationship_foundation: Optional[Dict[str, Any]] = None,
        custom_dimensions: Optional[Dict[str, int]] = None,
        initial_interaction: Optional[Dict[str, Any]] = None
    ) -> Knows:
        """
        Create a new relationship with enhanced archetype application and validation
        """
        # Validate archetype
        if archetype not in self.archetype_templates:
            raise ValueError(f"Invalid archetype: {archetype}")
        
        # Check if relationship already exists
        existing = self._get_relationship_between(from_person_id, to_person_id, user_id)
        if existing:
            raise ValueError("Relationship already exists between these persons")
        
        # Validate persons exist and belong to user
        self._validate_persons_exist(from_person_id, to_person_id, user_id)
        
        # Get archetype template
        template = self.archetype_templates[archetype]
        
        # Build relationship foundation
        foundation = self._build_relationship_foundation(
            archetype, relationship_foundation, template
        )
        
        # Build relationship dimensions
        dimensions = custom_dimensions or template["default_dimensions"].copy()
        
        # Create relationship depth structure
        relationship_depth = {
            "overall_score": self._calculate_overall_score(dimensions),
            "trend": "neutral",
            "dimensions": dimensions,
            "history": [
                {
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                    "event": "Relationship created",
                    "score_change": "+0",
                    "archetype_applied": archetype
                }
            ]
        }
        
        # Add initial interaction if provided
        if initial_interaction:
            relationship_depth["history"].append({
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "event": f"Initial interaction: {initial_interaction.get('type', 'unknown')}",
                "score_change": "+0",
                "interaction_details": initial_interaction
            })
        
        # Create relationship
        relationship = Knows(
            from_person_id=from_person_id,
            to_person_id=to_person_id,
            user_id=user_id,
            archetype=archetype,
            relationship_foundation=foundation,
            relationship_depth=relationship_depth
        )
        
        self.db.add(relationship)
        self.db.commit()
        self.db.refresh(relationship)
        
        return relationship

    def update_relationship(
        self,
        from_person_id: UUID,
        to_person_id: UUID,
        user_id: UUID,
        updates: Dict[str, Any]
    ) -> Optional[Knows]:
        """
        Update an existing relationship with validation
        """
        relationship = self._get_relationship_between(from_person_id, to_person_id, user_id)
        if not relationship:
            raise ValueError("Relationship not found")
        
        # Update archetype if provided
        if "archetype" in updates:
            new_archetype = updates["archetype"]
            if new_archetype not in self.archetype_templates:
                raise ValueError(f"Invalid archetype: {new_archetype}")
            
            # Apply new archetype template
            old_archetype = relationship.archetype
            relationship.archetype = new_archetype
            
            # Update foundation if archetype changed
            if old_archetype != new_archetype:
                template = self.archetype_templates[new_archetype]
                relationship.relationship_foundation = self._build_relationship_foundation(
                    new_archetype, relationship.relationship_foundation, template
                )
                
                # Add to history
                self._add_to_history(relationship, f"Archetype changed from {old_archetype} to {new_archetype}")
        
        # Update foundation if provided
        if "relationship_foundation" in updates:
            foundation_updates = updates["relationship_foundation"]
            current_foundation = relationship.relationship_foundation or {}
            current_foundation.update(foundation_updates)
            relationship.relationship_foundation = current_foundation
            
            self._add_to_history(relationship, "Relationship foundation updated")
        
        # Update dimensions if provided
        if "dimensions" in updates:
            dimension_updates = updates["dimensions"]
            current_depth = relationship.relationship_depth or {}
            current_dimensions = current_depth.get("dimensions", {})
            
            # Validate dimension values
            for dim_name, value in dimension_updates.items():
                if not isinstance(value, int) or not (0 <= value <= 100):
                    raise ValueError(f"Invalid dimension value for {dim_name}: must be integer 0-100")
            
            current_dimensions.update(dimension_updates)
            current_depth["dimensions"] = current_dimensions

            # Recalculate overall score
            new_overall_score = self._calculate_overall_score(current_dimensions)
            old_score = current_depth.get("overall_score", 50)
            current_depth["overall_score"] = new_overall_score


            
            # Update trend
            if new_overall_score > old_score:
                current_depth["trend"] = "positive"
            elif new_overall_score < old_score:
                current_depth["trend"] = "negative"
            else:
                current_depth["trend"] = "stable"
            
            relationship.relationship_depth = current_depth
            flag_modified(relationship, "relationship_depth")

            # Add to history
            score_change = new_overall_score - old_score
            self._add_to_history(
                relationship, 
                f"Dimensions updated",
                score_change=score_change
            )
        
        self.db.commit()
        self.db.refresh(relationship)
        return relationship

    def delete_relationship(
        self,
        from_person_id: UUID,
        to_person_id: UUID,
        user_id: UUID
    ) -> bool:
        """
        Delete a relationship
        """
        relationship = self._get_relationship_between(from_person_id, to_person_id, user_id)
        if not relationship:
            return False
        
        self.db.delete(relationship)
        self.db.commit()
        return True

    def get_relationship_details(
        self,
        from_person_id: UUID,
        to_person_id: UUID,
        user_id: UUID
    ) -> Optional[Dict[str, Any]]:
        """
        Get detailed relationship information including template insights
        """
        relationship = self._get_relationship_between(from_person_id, to_person_id, user_id)
        if not relationship:
            return None
        
        # Get archetype template
        template = self.archetype_templates.get(relationship.archetype, {})
        
        # Get related persons
        from_person = self.db.query(Person).filter(Person.person_id == from_person_id).first()
        to_person = self.db.query(Person).filter(Person.person_id == to_person_id).first()
        
        return {
            "relationship": {
                "from_person": {
                    "id": str(from_person.person_id),
                    "name": f"{from_person.first_name} {from_person.last_name}"
                } if from_person else None,
                "to_person": {
                    "id": str(to_person.person_id),
                    "name": f"{to_person.first_name} {to_person.last_name}"
                } if to_person else None,
                "archetype": relationship.archetype,
                "overall_score": relationship.overall_score,
                "foundation": relationship.relationship_foundation,
                "dimensions": relationship.dimensions,
                "history": relationship.relationship_depth.get("history", []) if relationship.relationship_depth else []
            },
            "archetype_template": {
                "growth_opportunities": template.get("growth_opportunities", []),
                "typical_interactions": template.get("typical_interactions", []),
                "maintenance_frequency": template.get("maintenance_frequency", "monthly"),
                "expected_evolution": template.get("expected_evolution", "")
            },
            "insights": self._generate_relationship_insights(relationship, template)
        }

    def get_archetype_templates(self) -> Dict[str, Dict[str, Any]]:
        """Get all available archetype templates"""
        return self.archetype_templates.copy()

    def get_archetype_template(self, archetype: str) -> Optional[Dict[str, Any]]:
        """Get specific archetype template"""
        return self.archetype_templates.get(archetype)

    def validate_relationship_data(self, relationship_data: Dict[str, Any]) -> List[str]:
        """
        Validate relationship data and return list of validation errors
        """
        errors = []
        
        # Validate archetype
        archetype = relationship_data.get("archetype")
        if not archetype:
            errors.append("Archetype is required")
        elif archetype not in self.archetype_templates:
            errors.append(f"Invalid archetype: {archetype}. Must be one of: {list(self.archetype_templates.keys())}")
        
        # Validate dimensions if provided
        dimensions = relationship_data.get("dimensions", {})
        for dim_name, value in dimensions.items():
            if not isinstance(value, int):
                errors.append(f"Dimension {dim_name} must be an integer")
            elif not (0 <= value <= 100):
                errors.append(f"Dimension {dim_name} must be between 0 and 100")
        
        # Validate foundation type if provided
        foundation = relationship_data.get("relationship_foundation", {})
        foundation_type = foundation.get("foundation_type")
        if foundation_type:
            valid_types = [ft.value for ft in RelationshipFoundationType]
            if foundation_type not in valid_types:
                errors.append(f"Invalid foundation type: {foundation_type}. Must be one of: {valid_types}")
        
        return errors

    def get_relationship_recommendations(
        self, user_id: UUID, person_id: UUID
    ) -> List[Dict[str, Any]]:
        """
        Get archetype recommendations for a person based on their profile
        """
        person = self.db.query(Person).filter(
            Person.person_id == person_id,
            Person.user_id == user_id
        ).first()
        
        if not person:
            return []
        
        recommendations = []
        prof_info = person.professional_info or {}
        
        # Analyze person's profile to suggest appropriate archetypes
        if prof_info.get("company"):
            recommendations.append({
                "archetype": RelationshipArchetype.COLLEAGUE.value,
                "confidence": 0.8,
                "reason": "Professional background suggests colleague relationship",
                "template": self.archetype_templates[RelationshipArchetype.COLLEAGUE.value]
            })
        
        if prof_info.get("title") and any(word in prof_info.get("title", "").lower() 
                                        for word in ["senior", "director", "manager", "lead"]):
            recommendations.append({
                "archetype": RelationshipArchetype.MENTOR.value,
                "confidence": 0.7,
                "reason": "Senior role suggests potential mentor relationship",
                "template": self.archetype_templates[RelationshipArchetype.MENTOR.value]
            })
        
        # Default recommendations
        if not recommendations:
            recommendations.extend([
                {
                    "archetype": RelationshipArchetype.ACQUAINTANCE.value,
                    "confidence": 0.6,
                    "reason": "Safe default for new connections",
                    "template": self.archetype_templates[RelationshipArchetype.ACQUAINTANCE.value]
                },
                {
                    "archetype": RelationshipArchetype.COLLEAGUE.value,
                    "confidence": 0.5,
                    "reason": "Common professional relationship type",
                    "template": self.archetype_templates[RelationshipArchetype.COLLEAGUE.value]
                }
            ])
        
        return recommendations

    # Private helper methods
    
    def _get_relationship_between(
        self, from_person_id: UUID, to_person_id: UUID, user_id: UUID
    ) -> Optional[Knows]:
        """Get relationship between two persons"""
        return self.db.query(Knows).filter(
            and_(
                Knows.user_id == user_id,
                or_(
                    and_(Knows.from_person_id == from_person_id, Knows.to_person_id == to_person_id),
                    and_(Knows.from_person_id == to_person_id, Knows.to_person_id == from_person_id)
                )
            )
        ).first()

    def _validate_persons_exist(
        self, from_person_id: UUID, to_person_id: UUID, user_id: UUID
    ) -> None:
        """Validate that both persons exist and belong to the user"""
        person1 = self.db.query(Person).filter(
            Person.person_id == from_person_id,
            Person.user_id == user_id
        ).first()
        
        person2 = self.db.query(Person).filter(
            Person.person_id == to_person_id,
            Person.user_id == user_id
        ).first()
        
        if not person1:
            raise ValueError(f"Person {from_person_id} not found")
        if not person2:
            raise ValueError(f"Person {to_person_id} not found")

    def _build_relationship_foundation(
        self,
        archetype: str,
        provided_foundation: Optional[Dict[str, Any]],
        template: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Build relationship foundation with template defaults"""
        foundation = provided_foundation or {}
        
        # Add template defaults if not provided
        if "foundation_types" not in foundation:
            foundation["foundation_types"] = template.get("foundation_types", [])
        
        if "how_met" not in foundation:
            foundation["how_met"] = f"Connected through {archetype} context"
        
        if "created_date" not in foundation:
            foundation["created_date"] = datetime.utcnow().isoformat() + "Z"
        
        return foundation

    def _calculate_overall_score(self, dimensions: Dict[str, int]) -> int:
        """Calculate overall score from dimensions"""
        if not dimensions:
            return 50
        
        scores = list(dimensions.values())
        return int(sum(scores) / len(scores))

    def _add_to_history(
        self,
        relationship: Knows,
        event: str,
        score_change: int = 0
    ) -> None:
        """Add entry to relationship history"""
        if not relationship.relationship_depth:
            relationship.relationship_depth = {"history": []}
        
        if "history" not in relationship.relationship_depth:
            relationship.relationship_depth["history"] = []
        
        history_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "event": event,
            "score_change": f"{score_change:+d}" if score_change != 0 else "0"
        }
        
        relationship.relationship_depth["history"].append(history_entry)
        
        # Keep only last 50 entries
        if len(relationship.relationship_depth["history"]) > 50:
            relationship.relationship_depth["history"] = relationship.relationship_depth["history"][-50:]

    def _generate_relationship_insights(
        self, relationship: Knows, template: Dict[str, Any]
    ) -> List[str]:
        """Generate insights about the relationship"""
        insights = []
        dimensions = relationship.dimensions or {}
        
        # Analyze dimension strengths and weaknesses
        if dimensions:
            max_dim = max(dimensions.items(), key=lambda x: x[1])
            min_dim = min(dimensions.items(), key=lambda x: x[1])
            
            if max_dim[1] > 80:
                insights.append(f"Particularly strong in {max_dim[0].replace('_', ' ')}")
            
            if min_dim[1] < 40:
                insights.append(f"Could improve {min_dim[0].replace('_', ' ')}")
        
        # Compare with template expectations
        template_dims = template.get("default_dimensions", {})
        for dim_name, expected_score in template_dims.items():
            actual_score = dimensions.get(dim_name, 50)
            if actual_score > expected_score + 20:
                insights.append(f"Above typical {relationship.archetype} level in {dim_name.replace('_', ' ')}")
            elif actual_score < expected_score - 20:
                insights.append(f"Below typical {relationship.archetype} level in {dim_name.replace('_', ' ')}")
        
        # Overall health assessment
        overall_score = relationship.overall_score
        if overall_score > 80:
            insights.append("Very strong and healthy relationship")
        elif overall_score < 40:
            insights.append("Relationship needs attention and nurturing")
        
        return insights