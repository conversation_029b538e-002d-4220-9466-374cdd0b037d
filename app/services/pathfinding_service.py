"""
Pathfinding Service - Intelligent referral path discovery using graph algorithms
Implements Dijkstra and A* algorithms for finding optimal connection paths
"""

import heapq
import math
from typing import Any, Dict, List, Optional, Set, Tuple, Union
from uuid import UUID
from dataclasses import dataclass
from enum import Enum

import networkx as nx
from sqlalchemy.orm import Session

from app.models.person import Person
from app.models.relationship import Knows
from app.services.graph_service import GraphService


class PathfindingAlgorithm(str, Enum):
    """Available pathfinding algorithms"""
    DIJKSTRA = "dijkstra"
    A_STAR = "a_star"
    BREADTH_FIRST = "breadth_first"


class PathOptimization(str, Enum):
    """Path optimization strategies"""
    SHORTEST = "shortest"  # Minimize number of hops
    STRONGEST = "strongest"  # Maximize relationship strength
    BALANCED = "balanced"  # Balance length and strength
    SUCCESS_PROBABILITY = "success_probability"  # Maximize success likelihood


@dataclass
class PathNode:
    """Represents a node in the pathfinding algorithm"""
    person_id: UUID
    person_name: str
    company: Optional[str] = None
    title: Optional[str] = None
    archetype: Optional[str] = None
    
    def __hash__(self):
        return hash(self.person_id)
    
    def __eq__(self, other):
        return isinstance(other, PathNode) and self.person_id == other.person_id


@dataclass
class PathEdge:
    """Represents an edge (relationship) in the pathfinding algorithm"""
    from_node: UUID
    to_node: UUID
    weight: float
    strength: float
    archetype: str
    overall_score: int
    success_probability: float


@dataclass
class ReferralPath:
    """Represents a complete referral path"""
    path_id: str
    algorithm: str
    total_weight: float
    success_probability: float
    path_length: int
    optimization_strategy: str
    nodes: List[PathNode]
    edges: List[PathEdge]
    estimated_time_days: int
    confidence_score: float
    alternative_rank: int = 1


class PathfindingService:
    """
    Advanced pathfinding service for referral discovery
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.graph_service = GraphService(db)
        self._path_cache = {}  # Simple in-memory cache
        
        # Pathfinding weights and parameters
        self.archetype_weights = {
            "colleague": 0.8,      # Good for professional referrals
            "friend": 0.9,         # High trust factor
            "mentor": 0.95,        # Excellent for advice/introductions
            "client": 0.7,         # Professional but transactional
            "family": 0.6,         # Personal connections may not help professionally
            "acquaintance": 0.5,   # Weak connection
            "partner": 0.85,       # High trust but context dependent
        }
        
        # Success probability factors
        self.success_factors = {
            "strong_relationship": 0.3,      # 70+ relationship score
            "recent_interaction": 0.2,       # Interaction within 30 days
            "same_company": 0.25,           # Work at same company
            "mutual_connections": 0.15,      # Share connections
            "archetype_bonus": 0.1          # Bonus for good archetype
        }
    
    def find_referral_paths(
        self,
        user_id: UUID,
        target_person_id: UUID,
        algorithm: PathfindingAlgorithm = PathfindingAlgorithm.DIJKSTRA,
        optimization: PathOptimization = PathOptimization.BALANCED,
        max_paths: int = 3,
        max_hops: int = 4,
        min_success_probability: float = 0.3
    ) -> List[ReferralPath]:
        """
        Find optimal referral paths to target person
        """
        cache_key = f"{user_id}_{target_person_id}_{algorithm}_{optimization}_{max_hops}"
        
        # Check cache first
        if cache_key in self._path_cache:
            cached_paths = self._path_cache[cache_key]
            return cached_paths[:max_paths]
        
        # Build network graph
        graph = self._build_pathfinding_graph(user_id)
        
        # Find paths using specified algorithm
        if algorithm == PathfindingAlgorithm.DIJKSTRA:
            paths = self._dijkstra_pathfinding(graph, user_id, target_person_id, optimization, max_hops)
        elif algorithm == PathfindingAlgorithm.A_STAR:
            paths = self._a_star_pathfinding(graph, user_id, target_person_id, optimization, max_hops)
        else:
            paths = self._breadth_first_pathfinding(graph, user_id, target_person_id, max_hops)
        
        # Filter by success probability
        valid_paths = [p for p in paths if p.success_probability >= min_success_probability]
        
        # Sort by optimization criteria
        sorted_paths = self._sort_paths_by_optimization(valid_paths, optimization)
        
        # Assign rankings and limit results
        for i, path in enumerate(sorted_paths):
            path.alternative_rank = i + 1
        
        result_paths = sorted_paths[:max_paths]
        
        # Cache results
        self._path_cache[cache_key] = result_paths
        
        return result_paths
    
    def find_goal_oriented_paths(
        self,
        user_id: UUID,
        goal_description: str,
        target_criteria: Dict[str, Any],
        max_paths: int = 5
    ) -> List[ReferralPath]:
        """
        Find paths to people who can help achieve specific goals
        """
        # Find potential target persons based on criteria
        potential_targets = self._find_goal_relevant_persons(user_id, target_criteria)
        
        all_paths = []
        
        for target_person in potential_targets:
            paths = self.find_referral_paths(
                user_id=user_id,
                target_person_id=target_person.person_id,
                algorithm=PathfindingAlgorithm.A_STAR,
                optimization=PathOptimization.SUCCESS_PROBABILITY,
                max_paths=2
            )
            
            # Add goal relevance scoring
            for path in paths:
                path.confidence_score *= self._calculate_goal_relevance(target_person, target_criteria)
            
            all_paths.extend(paths)
        
        # Sort by confidence and return top paths
        all_paths.sort(key=lambda x: x.confidence_score, reverse=True)
        return all_paths[:max_paths]
    
    def _build_pathfinding_graph(self, user_id: UUID) -> nx.DiGraph:
        """Build weighted directed graph for pathfinding"""
        graph = nx.DiGraph()
        
        # Get all relationships for user
        relationships = self.db.query(Knows).filter(Knows.user_id == user_id).all()
        persons = self.db.query(Person).filter(Person.user_id == user_id).all()
        
        # Create person lookup
        person_lookup = {p.person_id: p for p in persons}
        
        # Add nodes
        for person in persons:
            node = PathNode(
                person_id=person.person_id,
                person_name=f"{person.first_name} {person.last_name}",
                company=person.professional_info.get("company") if person.professional_info else None,
                title=person.professional_info.get("title") if person.professional_info else None
            )
            graph.add_node(person.person_id, data=node)
        
        # Add edges with weights
        for rel in relationships:
            if rel.from_person_id in person_lookup and rel.to_person_id in person_lookup:
                weight = self._calculate_edge_weight(rel)
                success_prob = self._calculate_success_probability(rel, person_lookup)
                
                edge = PathEdge(
                    from_node=rel.from_person_id,
                    to_node=rel.to_person_id,
                    weight=weight,
                    strength=rel.overall_score / 100.0,
                    archetype=rel.archetype or "acquaintance",
                    overall_score=rel.overall_score,
                    success_probability=success_prob
                )
                
                graph.add_edge(
                    rel.from_person_id,
                    rel.to_person_id,
                    weight=weight,
                    edge_data=edge
                )
        
        return graph
    
    def _dijkstra_pathfinding(
        self,
        graph: nx.DiGraph,
        start: UUID,
        target: UUID,
        optimization: PathOptimization,
        max_hops: int
    ) -> List[ReferralPath]:
        """Implement Dijkstra's algorithm for shortest path finding"""
        if start not in graph or target not in graph:
            return []
        
        try:
            # Find shortest path using NetworkX
            path_nodes = nx.shortest_path(graph, start, target, weight='weight')
            
            if len(path_nodes) > max_hops + 1:  # +1 because path includes start and end
                return []
            
            # Build ReferralPath object
            path = self._build_referral_path(
                graph, path_nodes, "dijkstra", optimization.value
            )
            
            return [path] if path else []
            
        except nx.NetworkXNoPath:
            return []
    
    def _a_star_pathfinding(
        self,
        graph: nx.DiGraph,
        start: UUID,
        target: UUID,
        optimization: PathOptimization,
        max_hops: int
    ) -> List[ReferralPath]:
        """Implement A* algorithm with heuristic for goal-oriented pathfinding"""
        def heuristic(node):
            # Simple heuristic: prefer nodes with higher relationship scores
            node_data = graph.nodes[node].get('data')
            if not node_data:
                return 1.0
            
            # Could be enhanced with more sophisticated heuristics
            return 0.5  # Placeholder heuristic
        
        try:
            path_nodes = nx.astar_path(graph, start, target, heuristic=heuristic, weight='weight')
            
            if len(path_nodes) > max_hops + 1:
                return []
            
            path = self._build_referral_path(
                graph, path_nodes, "a_star", optimization.value
            )
            
            return [path] if path else []
            
        except nx.NetworkXNoPath:
            return []
    
    def _breadth_first_pathfinding(
        self,
        graph: nx.DiGraph,
        start: UUID,
        target: UUID,
        max_hops: int
    ) -> List[ReferralPath]:
        """Simple breadth-first search for multiple paths"""
        try:
            # Find all simple paths up to max_hops
            all_paths = list(nx.all_simple_paths(graph, start, target, cutoff=max_hops))
            
            referral_paths = []
            for i, path_nodes in enumerate(all_paths[:5]):  # Limit to 5 paths
                path = self._build_referral_path(
                    graph, path_nodes, "breadth_first", "shortest"
                )
                if path:
                    referral_paths.append(path)
            
            return referral_paths
            
        except nx.NetworkXNoPath:
            return []
    
    def _build_referral_path(
        self,
        graph: nx.DiGraph,
        path_nodes: List[UUID],
        algorithm: str,
        optimization: str
    ) -> Optional[ReferralPath]:
        """Build ReferralPath object from node sequence"""
        if len(path_nodes) < 2:
            return None
        
        nodes = []
        edges = []
        total_weight = 0.0
        total_success_prob = 1.0
        
        # Build nodes
        for node_id in path_nodes:
            node_data = graph.nodes[node_id].get('data')
            if node_data:
                nodes.append(node_data)
        
        # Build edges
        for i in range(len(path_nodes) - 1):
            from_node = path_nodes[i]
            to_node = path_nodes[i + 1]
            
            if graph.has_edge(from_node, to_node):
                edge_data = graph[from_node][to_node].get('edge_data')
                if edge_data:
                    edges.append(edge_data)
                    total_weight += edge_data.weight
                    total_success_prob *= edge_data.success_probability
        
        # Calculate confidence score
        confidence_score = self._calculate_path_confidence(nodes, edges, optimization)
        
        # Estimate time to complete referral
        estimated_days = len(path_nodes) * 3  # Rough estimate: 3 days per hop
        
        return ReferralPath(
            path_id=f"path_{algorithm}_{len(path_nodes)}",
            algorithm=algorithm,
            total_weight=total_weight,
            success_probability=total_success_prob,
            path_length=len(path_nodes) - 1,  # Number of hops
            optimization_strategy=optimization,
            nodes=nodes,
            edges=edges,
            estimated_time_days=estimated_days,
            confidence_score=confidence_score
        )
    
    def _calculate_edge_weight(self, relationship: Knows) -> float:
        """Calculate edge weight based on relationship strength"""
        base_weight = 1.0
        
        # Lower weight = better path (shorter distance)
        strength_factor = (100 - relationship.overall_score) / 100.0
        archetype_factor = 1.0 - self.archetype_weights.get(relationship.archetype or "acquaintance", 0.5)
        
        return base_weight * strength_factor * archetype_factor
    
    def _calculate_success_probability(
        self, relationship: Knows, person_lookup: Dict[UUID, Person]
    ) -> float:
        """Calculate probability of successful referral through this relationship"""
        base_probability = 0.5
        
        # Relationship strength factor
        if relationship.overall_score >= 70:
            base_probability += self.success_factors["strong_relationship"]
        
        # Archetype bonus
        archetype_weight = self.archetype_weights.get(relationship.archetype or "acquaintance", 0.5)
        if archetype_weight > 0.8:
            base_probability += self.success_factors["archetype_bonus"]
        
        return min(base_probability, 1.0)
    
    def _calculate_path_confidence(
        self, nodes: List[PathNode], edges: List[PathEdge], optimization: str
    ) -> float:
        """Calculate overall confidence score for the path"""
        if not edges:
            return 0.0
        
        base_confidence = sum(edge.success_probability for edge in edges) / len(edges)
        
        # Apply optimization-specific adjustments
        if optimization == "shortest":
            # Prefer shorter paths
            length_penalty = max(0, (len(nodes) - 2) * 0.1)
            base_confidence = max(0, base_confidence - length_penalty)
        elif optimization == "strongest":
            # Prefer stronger relationships
            avg_strength = sum(edge.strength for edge in edges) / len(edges)
            base_confidence = (base_confidence + avg_strength) / 2
        
        return min(base_confidence, 1.0)
    
    def _find_goal_relevant_persons(
        self, user_id: UUID, criteria: Dict[str, Any]
    ) -> List[Person]:
        """Find persons relevant to achieving specific goals"""
        query = self.db.query(Person).filter(
            Person.user_id == user_id,
            Person.is_user == False
        )
        
        # Filter by criteria
        if "company" in criteria:
            # PostgreSQL JSON query for company
            query = query.filter(
                Person.professional_info.op('->')('company').astext.ilike(f"%{criteria['company']}%")
            )
        
        if "title_keywords" in criteria:
            # Filter by title containing keywords
            title_filter = None
            for keyword in criteria["title_keywords"]:
                condition = Person.professional_info.op('->')('title').astext.ilike(f"%{keyword}%")
                title_filter = condition if title_filter is None else title_filter | condition
            
            if title_filter is not None:
                query = query.filter(title_filter)
        
        if "tags" in criteria:
            # Filter by personal tags
            for tag in criteria["tags"]:
                query = query.filter(
                    Person.personal_details.op('->')('tags').astext.like(f"%{tag}%")
                )
        
        return query.limit(10).all()
    
    def _calculate_goal_relevance(
        self, person: Person, criteria: Dict[str, Any]
    ) -> float:
        """Calculate how relevant a person is for achieving the goal"""
        relevance_score = 0.5  # Base score
        
        prof_info = person.professional_info or {}
        personal_info = person.personal_details or {}
        
        # Company match
        if "company" in criteria and prof_info.get("company"):
            if criteria["company"].lower() in prof_info["company"].lower():
                relevance_score += 0.3
        
        # Title keyword matches
        if "title_keywords" in criteria and prof_info.get("title"):
            title = prof_info["title"].lower()
            matches = sum(1 for keyword in criteria["title_keywords"] if keyword.lower() in title)
            relevance_score += min(matches * 0.15, 0.3)
        
        # Tag matches
        if "tags" in criteria and personal_info.get("tags"):
            person_tags = [tag.lower() for tag in personal_info["tags"]]
            matches = sum(1 for tag in criteria["tags"] if tag.lower() in person_tags)
            relevance_score += min(matches * 0.1, 0.2)
        
        return min(relevance_score, 1.0)
    
    def _sort_paths_by_optimization(
        self, paths: List[ReferralPath], optimization: PathOptimization
    ) -> List[ReferralPath]:
        """Sort paths based on optimization strategy"""
        if optimization == PathOptimization.SHORTEST:
            return sorted(paths, key=lambda p: (p.path_length, -p.success_probability))
        elif optimization == PathOptimization.STRONGEST:
            return sorted(paths, key=lambda p: (-p.confidence_score, p.path_length))
        elif optimization == PathOptimization.SUCCESS_PROBABILITY:
            return sorted(paths, key=lambda p: (-p.success_probability, p.path_length))
        else:  # BALANCED
            return sorted(paths, key=lambda p: (
                -p.confidence_score * 0.6 - p.success_probability * 0.4 + p.path_length * 0.1
            ))
    
    def clear_cache(self):
        """Clear the path cache"""
        self._path_cache.clear()
    
    def get_path_statistics(self, user_id: UUID) -> Dict[str, Any]:
        """Get pathfinding statistics for user's network"""
        graph = self._build_pathfinding_graph(user_id)
        
        return {
            "total_nodes": graph.number_of_nodes(),
            "total_edges": graph.number_of_edges(),
            "network_density": nx.density(graph),
            "average_clustering": nx.average_clustering(graph.to_undirected()),
            "connected_components": nx.number_weakly_connected_components(graph),
            "diameter": self._safe_diameter(graph),
            "cache_size": len(self._path_cache)
        }
    
    def _safe_diameter(self, graph: nx.DiGraph) -> Optional[int]:
        """Safely calculate graph diameter"""
        try:
            if nx.is_weakly_connected(graph):
                return nx.diameter(graph.to_undirected())
            return None
        except:
            return None