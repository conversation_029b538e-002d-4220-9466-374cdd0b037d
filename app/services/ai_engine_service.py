"""
AI Engine service - handles AI-powered analysis and suggestions
Based on technical documentation AI engine requirements
"""

from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from uuid import UUID
import uuid
import json
import logging

import networkx as nx
from sqlalchemy.orm import Session

from app.models.goal import Goal
from app.models.person import Person
from app.models.relationship import Knows
from app.models.task import Task
from app.models.user import User
from app.models.interaction import Interaction
from app.services.goal_service import GoalService
from app.services.person_service import PersonService
from app.services.task_service import TaskService
from app.core.ai_utils import OpenAIClient, RelationshipAnalyzer, PreferenceLearner
from app.core.config import settings
from app.services.pathfinding_service import PathfindingService, PathfindingAlgorithm, PathOptimization
from app.services.network_health_service import NetworkHealthService

logger = logging.getLogger(__name__)


class AIEngineService:
    """
    AI Engine service implementing the core AI functionality from technical documentation
    Provides AI-powered relationship analysis, suggestions, and insights
    """

    def __init__(self, db: Session):
        self.db = db
        self.person_service = PersonService(db)
        self.goal_service = GoalService(db)
        self.task_service = TaskService(db)
        self.pathfinding_service = PathfindingService(db)
        self.network_health_service = NetworkHealthService(db)
        
        # Initialize OpenAI client if API key is available
        self.openai_client = None
        if settings.OPENAI_API_KEY:
            try:
                self.openai_client = OpenAIClient(
                    api_key=settings.OPENAI_API_KEY,
                    base_url=settings.OPENAI_API_BASE
                )
                logger.info("OpenAI client initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize OpenAI client: {e}")
        else:
            logger.warning("OpenAI API key not configured, AI features will use fallback logic")

    async def get_active_suggestions(self, user_id: UUID) -> List[Dict[str, Any]]:
        """
        Generate AI-powered proactive suggestions
        Implements the suggestion generation algorithm from technical documentation
        """
        suggestions = []

        # Get user's network and goals
        persons = self.person_service.get_all_by_user(user_id)
        # goals = await self.goal_service.get_active_goals(user_id)  # TODO: Implement when goal service is ready

        # 1. Relationship maintenance suggestions
        relationship_suggestions = await self._generate_relationship_suggestions(
            user_id, persons
        )
        suggestions.extend(relationship_suggestions)

        # 2. Goal-oriented networking suggestions (disabled for now)
        # goal_suggestions = await self._generate_goal_networking_suggestions(
        #     user_id, goals, persons
        # )
        # suggestions.extend(goal_suggestions)

        # 3. Network expansion suggestions
        network_suggestions = await self._generate_network_expansion_suggestions(
            user_id, persons
        )
        suggestions.extend(network_suggestions)

        # Sort by priority and return top suggestions
        suggestions.sort(key=lambda x: x.get("priority_score", 0), reverse=True)
        return suggestions[:10]  # Return top 10 suggestions

    async def _generate_relationship_suggestions(
        self, user_id: UUID, persons: List[Person]
    ) -> List[Dict[str, Any]]:
        """Generate AI-powered relationship maintenance suggestions"""
        suggestions = []
        
        try:
            # Get all relationships for the user
            relationships = self.db.query(Knows).filter(
                Knows.user_id == user_id
            ).all()
            
            # Analyze relationships for dormant connections
            dormant_relationships = await self._find_dormant_relationships(user_id, relationships)
            if dormant_relationships:
                suggestions.append({
                    "id": str(uuid.uuid4()),
                    "type": "reconnect",
                    "title": f"Reconnect with {len(dormant_relationships)} dormant relationships",
                    "description": "You have strong relationships that haven't been active recently",
                    "priority": "high",
                    "priority_score": 85,
                    "actions": ["view_dormant_relationships", "send_message", "schedule_catch_up"],
                    "related_people": [str(rel.to_person_id) for rel in dormant_relationships[:3]],
                    "created_at": datetime.utcnow().isoformat() + "Z",
                    "ai_reasoning": "Based on relationship strength and interaction history analysis"
                })
            
            # Analyze for relationship imbalances
            imbalanced_relationships = await self._find_relationship_imbalances(user_id, relationships)
            if imbalanced_relationships:
                suggestions.append({
                    "id": str(uuid.uuid4()),
                    "type": "balance_relationship",
                    "title": "Address relationship asymmetry",
                    "description": f"Found {len(imbalanced_relationships)} relationships that may need attention",
                    "priority": "medium",
                    "priority_score": 70,
                    "actions": ["view_relationship_analysis", "plan_reciprocal_action"],
                    "related_people": [str(rel.to_person_id) for rel in imbalanced_relationships[:2]],
                    "created_at": datetime.utcnow().isoformat() + "Z",
                    "ai_reasoning": "Detected asymmetry in relationship dimensions"
                })
            
            # Generate AI-powered insights if OpenAI is available
            if self.openai_client and relationships:
                ai_suggestions = await self._generate_ai_relationship_insights(user_id, relationships, persons)
                suggestions.extend(ai_suggestions)
                
        except Exception as e:
            logger.error(f"Error generating relationship suggestions: {e}")
            # Fallback to basic suggestion
            suggestions.append({
                "id": str(uuid.uuid4()),
                "type": "network_review",
                "title": "Review your network",
                "description": "Consider reviewing and updating your professional network",
                "priority": "medium",
                "priority_score": 60,
                "actions": ["view_network_map", "update_relationships"],
                "related_people": [],
                "created_at": datetime.utcnow().isoformat() + "Z"
            })

        return suggestions

    async def _find_dormant_relationships(self, user_id: UUID, relationships: List[Knows]) -> List[Knows]:
        """Find relationships that are strong but haven't had recent activity"""
        dormant = []
        
        for relationship in relationships:
            # Check if relationship has strong score but no recent interactions
            if relationship.overall_score > 70:  # Strong relationship threshold
                # Get recent interactions for this person
                from app.models.interaction import interaction_participants
                recent_interactions = self.db.query(Interaction).join(
                    interaction_participants,
                    Interaction.interaction_id == interaction_participants.c.interaction_id
                ).filter(
                    interaction_participants.c.person_id == relationship.to_person_id,
                    Interaction.user_id == user_id,
                    Interaction.occurred_at >= datetime.utcnow() - timedelta(days=90)
                ).count()
                
                if recent_interactions == 0:
                    dormant.append(relationship)
        
        return dormant

    async def _find_relationship_imbalances(self, user_id: UUID, relationships: List[Knows]) -> List[Knows]:
        """Find relationships with significant dimensional imbalances"""
        imbalanced = []
        
        for relationship in relationships:
            dimensions = relationship.dimensions
            if not dimensions:
                continue
                
            # Calculate standard deviation of dimension scores
            scores = list(dimensions.values())
            if len(scores) >= 3:  # Need at least 3 dimensions to analyze
                mean_score = sum(scores) / len(scores)
                variance = sum((s - mean_score) ** 2 for s in scores) / len(scores)
                std_dev = variance ** 0.5
                
                # Flag relationships with high variance (> 25 points)
                if std_dev > 25:
                    imbalanced.append(relationship)
        
        return imbalanced

    async def _generate_ai_relationship_insights(
        self, user_id: UUID, relationships: List[Knows], persons: List[Person]
    ) -> List[Dict[str, Any]]:
        """Generate AI-powered relationship insights using OpenAI"""
        suggestions = []
        
        try:
            # Prepare relationship data for AI analysis
            relationship_summary = self._prepare_relationship_summary(relationships, persons)
            
            # Create prompt for AI analysis
            prompt = f"""
            Analyze the following professional network data and provide 2-3 specific, actionable relationship suggestions:

            Network Summary:
            - Total relationships: {len(relationships)}
            - Relationship distribution: {relationship_summary.get('archetype_distribution', {})}
            - Recent interaction patterns: {relationship_summary.get('interaction_patterns', {})}
            
            Please provide suggestions in the following format:
            1. [Suggestion type]: [Brief title]
            Description: [2-3 sentence explanation]
            Priority: [high/medium/low]
            Actions: [specific actions to take]
            
            Focus on:
            - Relationship maintenance opportunities
            - Network expansion gaps
            - Strategic connection recommendations
            """
            
            response = self.openai_client.get_completion(prompt)
            
            # Parse AI response and convert to structured suggestions
            ai_suggestions = self._parse_ai_suggestions(response)
            suggestions.extend(ai_suggestions)
            
        except Exception as e:
            logger.error(f"Error generating AI insights: {e}")
        
        return suggestions

    def _prepare_relationship_summary(self, relationships: List[Knows], persons: List[Person]) -> Dict[str, Any]:
        """Prepare relationship data summary for AI analysis"""
        archetype_counts = {}
        interaction_patterns = {"recent": 0, "dormant": 0}
        
        for rel in relationships:
            # Count archetypes
            archetype = rel.archetype or "unspecified"
            archetype_counts[archetype] = archetype_counts.get(archetype, 0) + 1
            
            # Analyze interaction recency (simplified)
            if rel.overall_score > 60:
                interaction_patterns["recent"] += 1
            else:
                interaction_patterns["dormant"] += 1
        
        return {
            "archetype_distribution": archetype_counts,
            "interaction_patterns": interaction_patterns,
            "total_count": len(relationships)
        }

    def _parse_ai_suggestions(self, ai_response: str) -> List[Dict[str, Any]]:
        """Parse AI response into structured suggestions"""
        suggestions = []
        
        try:
            # Simple parsing logic - in production, use more robust parsing
            lines = ai_response.split('\n')
            current_suggestion = None
            
            for line in lines:
                line = line.strip()
                if line.startswith(('1.', '2.', '3.')):
                    if current_suggestion:
                        suggestions.append(current_suggestion)
                    
                    # Extract suggestion type and title
                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        suggestion_type = parts[0].split('.', 1)[1].strip().lower().replace(' ', '_')
                        title = parts[1].strip()
                        
                        current_suggestion = {
                            "id": str(uuid.uuid4()),
                            "type": suggestion_type,
                            "title": title,
                            "description": "",
                            "priority": "medium",
                            "priority_score": 65,
                            "actions": [],
                            "related_people": [],
                            "created_at": datetime.utcnow().isoformat() + "Z",
                            "ai_reasoning": "Generated by AI analysis"
                        }
                elif line.startswith('Description:') and current_suggestion:
                    current_suggestion["description"] = line.replace('Description:', '').strip()
                elif line.startswith('Priority:') and current_suggestion:
                    priority = line.replace('Priority:', '').strip().lower()
                    current_suggestion["priority"] = priority
                    # Set priority score based on priority level
                    score_map = {"high": 80, "medium": 65, "low": 50}
                    current_suggestion["priority_score"] = score_map.get(priority, 65)
                elif line.startswith('Actions:') and current_suggestion:
                    actions_text = line.replace('Actions:', '').strip()
                    current_suggestion["actions"] = [action.strip() for action in actions_text.split(',')]
            
            # Add the last suggestion
            if current_suggestion:
                suggestions.append(current_suggestion)
                
        except Exception as e:
            logger.error(f"Error parsing AI suggestions: {e}")
        
        return suggestions

    async def _generate_goal_networking_suggestions(
        self, user_id: UUID, goals: List[Goal], persons: List[Person]
    ) -> List[Dict[str, Any]]:
        """Generate goal-oriented networking suggestions"""
        suggestions = []

        for goal in goals:
            # TODO: Implement goal-person matching logic
            suggestions.append(
                {
                    "id": f"goal_networking_{goal.goal_id}",
                    "type": "goal_networking",
                    "title": f"Expand network for: {goal.title}",
                    "description": f"Find connections that could help with '{goal.title}'",
                    "priority_score": 70,
                    "actions": ["find_referral_path", "view_goal_dashboard"],
                    "related_goal": str(goal.goal_id),
                    "related_people": [],
                }
            )

        return suggestions

    async def _generate_network_expansion_suggestions(
        self, user_id: UUID, persons: List[Person]
    ) -> List[Dict[str, Any]]:
        """Generate network expansion suggestions"""
        suggestions = []

        # TODO: Implement network analysis to find structural holes
        suggestions.append(
            {
                "id": str(uuid.uuid4()),
                "type": "introduction",
                "title": "Bridge network gaps",
                "description": "Consider connecting people in your network who could benefit from knowing each other",
                "priority": "medium",
                "priority_score": 60,
                "actions": ["view_network_map", "suggest_introductions"],
                "related_people": [],
                "created_at": datetime.utcnow().isoformat() + "Z"
            }
        )

        return suggestions

    async def get_network_diagnosis(self, user_id: UUID) -> Dict[str, Any]:
        """
        Perform comprehensive network health diagnosis
        Implements advanced network analysis algorithms and real metrics
        """
        try:
            # Use the dedicated network health service for comprehensive analysis
            diagnosis = await self.network_health_service.diagnose_network_health(user_id)
            
            # Add AI-powered insights if OpenAI is available
            if self.openai_client and diagnosis["network_size"] > 0:
                ai_insights = await self._generate_ai_network_insights(user_id, diagnosis)
                diagnosis["ai_insights"] = ai_insights
            else:
                diagnosis["ai_insights"] = []
            
            # Add personalized next steps based on user's specific situation
            next_steps = self._generate_personalized_next_steps(diagnosis)
            diagnosis["next_steps"] = next_steps
            
            # Add network comparison to peer benchmarks
            peer_comparison = await self._generate_peer_comparison(user_id, diagnosis)
            diagnosis["peer_comparison"] = peer_comparison
            
            return diagnosis
            
        except Exception as e:
            logger.error(f"Error in network diagnosis: {str(e)}")
            # Return fallback diagnosis
            return {
                "overall_health_score": 0,
                "health_grade": "N/A",
                "network_size": 0,
                "error": f"Unable to complete diagnosis: {str(e)}",
                "last_analyzed": datetime.utcnow().isoformat() + "Z",
                "recommendations": [
                    {
                        "category": "System",
                        "priority": "high",
                        "title": "Contact support",
                        "description": "There was an issue analyzing your network. Please try again or contact support.",
                        "actions": ["Refresh the page", "Contact support if issue persists"],
                        "expected_impact": "Restore network analysis functionality",
                        "time_investment": "5 minutes",
                        "target_metrics": []
                    }
                ]
            }

    async def find_referral_path(
        self,
        user_id: UUID,
        target_person_id: UUID = None,
        target_criteria: Dict[str, Any] = None,
        algorithm: str = "dijkstra",
        optimization: str = "balanced",
        max_paths: int = 3,
        max_hops: int = 4
    ) -> Dict[str, Any]:
        """
        Find referral paths using advanced graph algorithms
        Implements Dijkstra/A* algorithms with intelligent optimization
        """
        try:
            # Get user person record
            user_person = self.person_service.get_user_person(user_id)
            if not user_person:
                return {
                    "paths": [],
                    "message": "User person record not found. Please create your profile first.",
                    "alternative_strategies": [
                        "Complete your profile setup",
                        "Add personal information and contacts"
                    ]
                }

            # Convert string parameters to enums
            try:
                pathfinding_algo = PathfindingAlgorithm(algorithm)
                path_optimization = PathOptimization(optimization)
            except ValueError as e:
                return {
                    "paths": [],
                    "message": f"Invalid algorithm or optimization parameter: {str(e)}",
                    "available_algorithms": [algo.value for algo in PathfindingAlgorithm],
                    "available_optimizations": [opt.value for opt in PathOptimization]
                }

            # Handle different types of pathfinding requests
            if target_person_id:
                # Direct person-to-person pathfinding
                paths = self.pathfinding_service.find_referral_paths(
                    user_id=user_id,
                    target_person_id=target_person_id,
                    algorithm=pathfinding_algo,
                    optimization=path_optimization,
                    max_paths=max_paths,
                    max_hops=max_hops
                )
                
                # Get target person details
                target_person = self.person_service.get(target_person_id)
                target_name = f"{target_person.first_name} {target_person.last_name}" if target_person else "Unknown"
                
            elif target_criteria:
                # Goal-oriented pathfinding
                paths = self.pathfinding_service.find_goal_oriented_paths(
                    user_id=user_id,
                    goal_description=target_criteria.get("description", "Professional goal"),
                    target_criteria=target_criteria,
                    max_paths=max_paths
                )
                target_name = target_criteria.get("description", "Goal-relevant contacts")
                
            else:
                return {
                    "paths": [],
                    "message": "Either target_person_id or target_criteria must be provided"
                }

            if not paths:
                return {
                    "paths": [],
                    "message": f"No viable referral paths found to {target_name}",
                    "alternative_strategies": [
                        "Expand your network by attending relevant events",
                        "Connect with people in your target industry",
                        "Ask existing connections for introductions",
                        "Use professional networking platforms",
                        "Consider reaching out directly if appropriate"
                    ],
                    "network_stats": self.pathfinding_service.get_path_statistics(user_id)
                }

            # Convert paths to API response format
            formatted_paths = []
            for i, path in enumerate(paths):
                formatted_path = {
                    "path_id": f"{path.path_id}_{i+1}",
                    "algorithm": path.algorithm,
                    "optimization_strategy": path.optimization_strategy,
                    "path_length": path.path_length,
                    "success_probability": round(path.success_probability, 3),
                    "confidence_score": round(path.confidence_score, 3),
                    "estimated_time_days": path.estimated_time_days,
                    "alternative_rank": path.alternative_rank,
                    "total_weight": round(path.total_weight, 3),
                    "steps": [],
                    "connections": []
                }

                # Add path steps (nodes)
                for j, node in enumerate(path.nodes):
                    step = {
                        "step_number": j + 1,
                        "person_id": str(node.person_id),
                        "person_name": node.person_name,
                        "company": node.company,
                        "title": node.title,
                        "is_user": j == 0,  # First node is always the user
                        "is_target": j == len(path.nodes) - 1  # Last node is the target
                    }
                    formatted_path["steps"].append(step)

                # Add connection details (edges)
                for k, edge in enumerate(path.edges):
                    connection = {
                        "connection_number": k + 1,
                        "from_person": str(edge.from_node),
                        "to_person": str(edge.to_node),
                        "relationship_archetype": edge.archetype,
                        "relationship_strength": round(edge.strength, 3),
                        "overall_score": edge.overall_score,
                        "success_probability": round(edge.success_probability, 3),
                        "weight": round(edge.weight, 3),
                        "recommended_approach": self._get_approach_recommendation(edge.archetype, edge.strength)
                    }
                    formatted_path["connections"].append(connection)

                formatted_paths.append(formatted_path)

            # Generate alternative strategies based on analysis
            alternative_strategies = self._generate_alternative_strategies(
                user_id, target_criteria or {"target_person_id": target_person_id}, paths
            )

            # Generate AI-powered insights if available
            ai_insights = []
            if self.openai_client and len(formatted_paths) > 0:
                ai_insights = await self._generate_ai_path_insights(formatted_paths, target_name)

            return {
                "target": target_name,
                "total_paths_found": len(formatted_paths),
                "search_parameters": {
                    "algorithm": algorithm,
                    "optimization": optimization,
                    "max_paths": max_paths,
                    "max_hops": max_hops
                },
                "paths": formatted_paths,
                "alternative_strategies": alternative_strategies,
                "ai_insights": ai_insights,
                "network_stats": self.pathfinding_service.get_path_statistics(user_id),
                "generated_at": datetime.utcnow().isoformat() + "Z"
            }

        except Exception as e:
            logger.error(f"Error in find_referral_path: {str(e)}")
            return {
                "paths": [],
                "message": f"Error finding referral paths: {str(e)}",
                "alternative_strategies": [
                    "Try again with different parameters",
                    "Check your network connections",
                    "Contact support if the issue persists"
                ]
            }

    async def get_relationship_prism(
        self, user_id: UUID, person1_id: UUID, person2_id: UUID
    ) -> Dict[str, Any]:
        """
        Get multi-dimensional relationship analysis
        Implements the six-dimensional model from technical documentation
        """
        # Get the relationship between the two persons
        relationship = self.db.query(Knows).filter(
            Knows.user_id == user_id,
            Knows.from_person_id == person1_id,
            Knows.to_person_id == person2_id
        ).first()

        if not relationship:
            return {
                "error": "No relationship found between these persons",
                "relationship_exists": False,
            }

        # Extract relationship dimensions
        dimensions = relationship.dimensions or {}
        overall_score = relationship.overall_score

        # Get user preferences for personalized analysis
        user_preferences = await self._get_user_decision_factors(user_id)

        # Calculate enhanced relationship metrics
        enhanced_analysis = await self._calculate_enhanced_relationship_metrics(
            relationship, user_preferences
        )

        analysis = {
            "relationship_exists": True,
            "overall_score": overall_score,
            "dimensions": dimensions,
            "radar_chart_data": [
                {
                    "dimension": "Emotional Intimacy",
                    "score": dimensions.get("emotional_intimacy", 0),
                    "weight": user_preferences.get("emotional_weight", 0.2),
                    "weighted_score": dimensions.get("emotional_intimacy", 0) * user_preferences.get("emotional_weight", 0.2),
                },
                {
                    "dimension": "Professional Collaboration",
                    "score": dimensions.get("professional_collaboration", 0),
                    "weight": user_preferences.get("value_weight", 0.2),
                    "weighted_score": dimensions.get("professional_collaboration", 0) * user_preferences.get("value_weight", 0.2),
                },
                {
                    "dimension": "Trust Level",
                    "score": dimensions.get("trust_level", 0),
                    "weight": user_preferences.get("trust_weight", 0.2),
                    "weighted_score": dimensions.get("trust_level", 0) * user_preferences.get("trust_weight", 0.2),
                },
                {
                    "dimension": "Communication Frequency",
                    "score": dimensions.get("communication_frequency", 0),
                    "weight": user_preferences.get("information_weight", 0.15),
                    "weighted_score": dimensions.get("communication_frequency", 0) * user_preferences.get("information_weight", 0.15),
                },
                {
                    "dimension": "Shared Experience",
                    "score": dimensions.get("shared_experience_value", 0),
                    "weight": user_preferences.get("role_weight", 0.15),
                    "weighted_score": dimensions.get("shared_experience_value", 0) * user_preferences.get("role_weight", 0.15),
                },
                {
                    "dimension": "Reciprocity Balance",
                    "score": dimensions.get("reciprocity_balance", 0),
                    "weight": user_preferences.get("coercive_weight", 0.1),
                    "weighted_score": dimensions.get("reciprocity_balance", 0) * user_preferences.get("coercive_weight", 0.1),
                },
            ],
            "asymmetry_analysis": await self._analyze_relationship_asymmetry_advanced(relationship),
            "personalized_insights": await self._generate_personalized_insights_ai(
                dimensions, user_preferences, relationship
            ),
            "recommendations": await self._generate_relationship_recommendations_ai(relationship),
            "enhanced_metrics": enhanced_analysis,
            "trend_analysis": self._analyze_relationship_trends(relationship),
        }

        return analysis

    async def _get_user_decision_factors(self, user_id: UUID) -> Dict[str, float]:
        """Get user's relationship decision factors/preferences"""
        # For now, return default weights
        # TODO: Implement user preference learning from interaction patterns
        default_preferences = {
            "emotional_weight": 0.2,
            "value_weight": 0.2,
            "trust_weight": 0.2,
            "information_weight": 0.15,
            "role_weight": 0.15,
            "coercive_weight": 0.1,
        }
        
        # Try to learn preferences from user's action history
        try:
            # Get user's interaction patterns to infer preferences
            interactions = self.db.query(Interaction).filter(
                Interaction.user_id == user_id
            ).limit(100).all()
            
            interaction_data = [
                {
                    "action_type": interaction.interaction_type,
                    "details": interaction.notes or {},
                    "timestamp": interaction.interaction_date.timestamp() if interaction.interaction_date else 0
                }
                for interaction in interactions
            ]
            
            if interaction_data:
                learned_preferences = PreferenceLearner.analyze_user_behavior(interaction_data)
                # Merge with defaults, giving preference to learned weights if significant
                for key, value in learned_preferences.items():
                    if abs(value - default_preferences.get(key, 0)) > 0.05:  # 5% threshold
                        default_preferences[key] = value
                        
        except Exception as e:
            logger.warning(f"Could not learn user preferences: {e}")
        
        return default_preferences

    async def _calculate_enhanced_relationship_metrics(
        self, relationship: Knows, user_preferences: Dict[str, float]
    ) -> Dict[str, Any]:
        """Calculate enhanced relationship metrics using AI"""
        metrics = {
            "personalized_score": 0,
            "compatibility_index": 0,
            "growth_potential": 0,
            "maintenance_effort": 0,
            "strategic_value": 0
        }
        
        dimensions = relationship.dimensions or {}
        
        # Calculate personalized score based on user preferences
        weighted_scores = []
        for dim_name, weight_key in [
            ("emotional_intimacy", "emotional_weight"),
            ("professional_collaboration", "value_weight"),
            ("trust_level", "trust_weight"),
            ("communication_frequency", "information_weight"),
            ("shared_experience_value", "role_weight"),
            ("reciprocity_balance", "coercive_weight")
        ]:
            score = dimensions.get(dim_name, 0)
            weight = user_preferences.get(weight_key, 0)
            weighted_scores.append(score * weight)
        
        metrics["personalized_score"] = sum(weighted_scores)
        
        # Calculate compatibility index (how aligned the relationship is with user preferences)
        variance = sum((score - metrics["personalized_score"]) ** 2 for score in dimensions.values())
        metrics["compatibility_index"] = max(0, 1 - (variance / 1000))  # Normalize
        
        # Calculate growth potential (identify areas for improvement)
        improvement_scores = [max(0, 100 - score) for score in dimensions.values()]
        metrics["growth_potential"] = sum(improvement_scores) / len(improvement_scores) if improvement_scores else 0
        
        # Calculate maintenance effort (based on interaction frequency and relationship complexity)
        comm_freq = dimensions.get("communication_frequency", 50)
        metrics["maintenance_effort"] = max(0, 100 - comm_freq) / 100
        
        # Calculate strategic value using AI if available
        if self.openai_client:
            try:
                strategic_value = await self._calculate_strategic_value_ai(relationship)
                metrics["strategic_value"] = strategic_value
            except Exception as e:
                logger.warning(f"Could not calculate AI strategic value: {e}")
                metrics["strategic_value"] = relationship.overall_score / 100
        else:
            metrics["strategic_value"] = relationship.overall_score / 100
        
        return metrics

    async def _calculate_strategic_value_ai(self, relationship: Knows) -> float:
        """Calculate strategic value of relationship using AI analysis"""
        try:
            # Get person details for analysis
            person = self.db.query(Person).filter(
                Person.person_id == relationship.to_person_id
            ).first()
            
            if not person:
                return 0.5
            
            # Prepare data for AI analysis
            person_context = {
                "professional_info": person.professional_info or {},
                "archetype": relationship.archetype,
                "dimensions": relationship.dimensions or {},
                "overall_score": relationship.overall_score
            }
            
            prompt = f"""
            Analyze the strategic value of this professional relationship on a scale of 0-1:
            
            Person Context: {json.dumps(person_context, indent=2)}
            
            Consider:
            - Professional position and industry influence
            - Network connections and referral potential
            - Knowledge sharing and learning opportunities
            - Long-term career advancement potential
            
            Respond with only a number between 0 and 1 representing strategic value.
            """
            
            response = self.openai_client.get_completion(prompt)
            
            # Extract numeric value from response
            try:
                value = float(response.strip())
                return max(0, min(1, value))  # Ensure within 0-1 range
            except ValueError:
                return 0.5  # Default if parsing fails
                
        except Exception as e:
            logger.error(f"Error calculating strategic value: {e}")
            return 0.5

    async def _analyze_relationship_asymmetry_advanced(self, relationship: Knows) -> Dict[str, Any]:
        """Advanced relationship asymmetry analysis"""
        dimensions = relationship.dimensions or {}
        
        if len(dimensions) < 3:
            return {
                "is_asymmetric": False,
                "asymmetry_score": 0.0,
                "dominant_dimensions": [],
                "weak_dimensions": [],
                "analysis": "Insufficient dimension data for asymmetry analysis"
            }
        
        scores = list(dimensions.values())
        mean_score = sum(scores) / len(scores)
        variance = sum((s - mean_score) ** 2 for s in scores) / len(scores)
        std_dev = variance ** 0.5
        
        # Identify dominant and weak dimensions
        dominant_dimensions = []
        weak_dimensions = []
        
        for dim_name, score in dimensions.items():
            if score > mean_score + std_dev:
                dominant_dimensions.append({"dimension": dim_name, "score": score})
            elif score < mean_score - std_dev:
                weak_dimensions.append({"dimension": dim_name, "score": score})
        
        asymmetry_score = min(1.0, std_dev / 50)  # Normalize to 0-1 scale
        is_asymmetric = asymmetry_score > 0.3
        
        # Generate analysis text
        if is_asymmetric:
            analysis = f"Relationship shows significant asymmetry (score: {asymmetry_score:.2f}). "
            if dominant_dimensions:
                dom_names = [d["dimension"] for d in dominant_dimensions]
                analysis += f"Strong in: {', '.join(dom_names)}. "
            if weak_dimensions:
                weak_names = [d["dimension"] for d in weak_dimensions]
                analysis += f"Needs attention: {', '.join(weak_names)}."
        else:
            analysis = "Relationship appears well-balanced across all dimensions."
        
        return {
            "is_asymmetric": is_asymmetric,
            "asymmetry_score": asymmetry_score,
            "dominant_dimensions": dominant_dimensions,
            "weak_dimensions": weak_dimensions,
            "mean_score": mean_score,
            "standard_deviation": std_dev,
            "analysis": analysis
        }

    async def _generate_personalized_insights_ai(
        self, dimensions: Dict[str, int], user_preferences: Dict[str, float], relationship: Knows
    ) -> List[str]:
        """Generate personalized insights using AI"""
        insights = []
        
        try:
            if self.openai_client:
                # Prepare data for AI analysis
                analysis_data = {
                    "dimensions": dimensions,
                    "user_preferences": user_preferences,
                    "overall_score": relationship.overall_score,
                    "archetype": relationship.archetype
                }
                
                prompt = f"""
                Generate 2-3 personalized insights about this relationship based on the user's preferences:
                
                {json.dumps(analysis_data, indent=2)}
                
                Focus on:
                - How this relationship aligns with the user's stated preferences
                - Specific strengths that match user priorities
                - Areas that might matter most to this user
                
                Provide concise, actionable insights in a bullet point format.
                """
                
                response = self.openai_client.get_completion(prompt)
                
                # Parse response into individual insights
                lines = [line.strip() for line in response.split('\n') if line.strip()]
                insights = [line.lstrip('- ').lstrip('• ').lstrip('* ') for line in lines if line][:3]
            
            if not insights:
                # Fallback to rule-based insights
                insights = self._generate_fallback_insights(dimensions, user_preferences)
                
        except Exception as e:
            logger.error(f"Error generating personalized insights: {e}")
            insights = self._generate_fallback_insights(dimensions, user_preferences)
        
        return insights

    def _generate_fallback_insights(self, dimensions: Dict[str, int], user_preferences: Dict[str, float]) -> List[str]:
        """Generate fallback insights when AI is not available"""
        insights = []
        
        # Find user's top preference
        top_preference = max(user_preferences.items(), key=lambda x: x[1])
        pref_name = top_preference[0].replace('_weight', '')
        
        # Map preference to dimension
        pref_to_dim = {
            "emotional": "emotional_intimacy",
            "value": "professional_collaboration", 
            "trust": "trust_level",
            "information": "communication_frequency",
            "role": "shared_experience_value",
            "coercive": "reciprocity_balance"
        }
        
        relevant_dim = pref_to_dim.get(pref_name)
        if relevant_dim and relevant_dim in dimensions:
            score = dimensions[relevant_dim]
            if score > 70:
                insights.append(f"This relationship aligns well with your preference for {pref_name.replace('_', ' ')}")
            else:
                insights.append(f"Consider developing the {pref_name.replace('_', ' ')} aspect of this relationship")
        
        # General insights based on scores
        strong_dims = [name for name, score in dimensions.items() if score > 80]
        if strong_dims:
            insights.append(f"Particularly strong in {strong_dims[0].replace('_', ' ')}")
        
        return insights[:3]

    async def _generate_relationship_recommendations_ai(self, relationship: Knows) -> List[str]:
        """Generate AI-powered relationship recommendations"""
        recommendations = []
        
        try:
            if self.openai_client:
                dimensions = relationship.dimensions or {}
                
                prompt = f"""
                Based on this relationship's dimensional scores, provide 3 specific, actionable recommendations for improvement:
                
                Relationship Data:
                - Overall Score: {relationship.overall_score}
                - Archetype: {relationship.archetype}
                - Dimensions: {json.dumps(dimensions, indent=2)}
                
                Provide specific, practical recommendations that focus on the lowest-scoring dimensions.
                Format as brief action items.
                """
                
                response = self.openai_client.get_completion(prompt)
                
                # Parse recommendations
                lines = [line.strip() for line in response.split('\n') if line.strip()]
                recommendations = [line.lstrip('- ').lstrip('• ').lstrip('* ').lstrip('1. ').lstrip('2. ').lstrip('3. ') 
                                 for line in lines if line][:3]
            
            if not recommendations:
                recommendations = self._generate_relationship_recommendations(relationship.dimensions or {})
                
        except Exception as e:
            logger.error(f"Error generating AI recommendations: {e}")
            recommendations = self._generate_relationship_recommendations(relationship.dimensions or {})
        
        return recommendations

    def _analyze_relationship_trends(self, relationship: Knows) -> Dict[str, Any]:
        """Analyze relationship trends over time"""
        history = relationship.relationship_depth.get("history", []) if relationship.relationship_depth else []
        
        if len(history) < 2:
            return {
                "trend": "insufficient_data",
                "trend_strength": 0,
                "recent_changes": [],
                "prediction": "Not enough historical data for trend analysis"
            }
        
        # Calculate trend from recent history
        recent_scores = []
        for entry in history[-5:]:  # Last 5 entries
            if "score_change" in entry:
                try:
                    change = int(entry["score_change"].replace('+', ''))
                    recent_scores.append(change)
                except ValueError:
                    continue
        
        if recent_scores:
            avg_change = sum(recent_scores) / len(recent_scores)
            if avg_change > 2:
                trend = "improving"
                trend_strength = min(1.0, avg_change / 10)
            elif avg_change < -2:
                trend = "declining"
                trend_strength = min(1.0, abs(avg_change) / 10)
            else:
                trend = "stable"
                trend_strength = 0.1
        else:
            trend = "stable"
            trend_strength = 0.1
        
        return {
            "trend": trend,
            "trend_strength": trend_strength,
            "recent_changes": history[-3:],
            "prediction": f"Relationship appears to be {trend}" + (f" with {trend_strength:.1f} strength" if trend_strength > 0.1 else "")
        }

    def _analyze_relationship_asymmetry(
        self, dimensions: Dict[str, int]
    ) -> Dict[str, Any]:
        """Analyze relationship asymmetry"""
        # TODO: Implement proper asymmetry analysis
        return {
            "is_asymmetric": False,
            "asymmetry_score": 0.1,
            "dominant_party": "balanced",
            "analysis": "Relationship appears balanced across all dimensions",
        }

    def _generate_personalized_insights(
        self, dimensions: Dict[str, int], decision_factors: Dict[str, float]
    ) -> List[str]:
        """Generate personalized insights based on user's decision factors"""
        insights = []

        # TODO: Implement personalized analysis based on user preferences
        insights.append("This relationship aligns well with your stated preferences")

        return insights

    def _generate_relationship_recommendations(
        self, dimensions: Dict[str, int]
    ) -> List[str]:
        """Generate recommendations for improving the relationship"""
        recommendations = []

        # Find dimensions that could be improved
        for dimension, score in dimensions.items():
            if score < 60:  # Threshold for improvement
                recommendations.append(
                    f"Consider improving {dimension.replace('_', ' ')}"
                )

        if not recommendations:
            recommendations.append("Relationship is strong across all dimensions")

        return recommendations

    async def calculate_relationship_score(
        self, user_id: UUID, person_id: UUID, interaction_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Calculate six-dimensional relationship score
        Core algorithm for relationship scoring system
        """
        try:
            # Get existing relationship or create new scoring
            relationship = self.db.query(Knows).filter(
                Knows.user_id == user_id,
                Knows.to_person_id == person_id
            ).first()
            
            # Get person details for context
            person = self.db.query(Person).filter(
                Person.person_id == person_id
            ).first()
            
            if not person:
                return {"error": "Person not found"}
            
            # Initialize or get current dimensions
            current_dimensions = {}
            if relationship and relationship.dimensions:
                current_dimensions = relationship.dimensions.copy()
            else:
                current_dimensions = {
                    "emotional_intimacy": 30,
                    "professional_collaboration": 30,
                    "trust_level": 40,
                    "communication_frequency": 20,
                    "shared_experience_value": 20,
                    "reciprocity_balance": 50
                }
            
            # Calculate new scores based on interaction or context
            if interaction_data:
                updated_dimensions = await self._update_dimensions_from_interaction(
                    current_dimensions, interaction_data, person
                )
            else:
                updated_dimensions = current_dimensions
            
            # Calculate overall score using weighted algorithm
            user_preferences = await self._get_user_decision_factors(user_id)
            overall_score = await self._calculate_weighted_overall_score(
                updated_dimensions, user_preferences
            )
            
            # Generate AI insights for the scoring
            scoring_insights = []
            if self.openai_client:
                try:
                    scoring_insights = await self._generate_scoring_insights(
                        updated_dimensions, interaction_data, person
                    )
                except Exception as e:
                    logger.warning(f"Could not generate AI scoring insights: {e}")
            
            return {
                "person_id": str(person_id),
                "overall_score": overall_score,
                "dimensions": updated_dimensions,
                "previous_dimensions": current_dimensions,
                "score_change": overall_score - (relationship.overall_score if relationship else 50),
                "user_preferences": user_preferences,
                "scoring_insights": scoring_insights,
                "calculated_at": datetime.utcnow().isoformat() + "Z"
            }
            
        except Exception as e:
            logger.error(f"Error calculating relationship score: {e}")
            return {"error": str(e)}

    async def _update_dimensions_from_interaction(
        self, current_dimensions: Dict[str, int], interaction_data: Dict[str, Any], person: Person
    ) -> Dict[str, int]:
        """Update relationship dimensions based on new interaction"""
        updated = current_dimensions.copy()
        
        interaction_type = interaction_data.get("interaction_type", "")
        sentiment = interaction_data.get("sentiment", "neutral")
        context = interaction_data.get("context", "")
        
        # Base adjustment factors based on interaction type
        adjustment_map = {
            "meeting": {"professional_collaboration": 5, "communication_frequency": 3},
            "email": {"communication_frequency": 2, "professional_collaboration": 1},
            "phone_call": {"communication_frequency": 4, "emotional_intimacy": 2},
            "social_event": {"emotional_intimacy": 6, "shared_experience_value": 4},
            "collaboration": {"professional_collaboration": 8, "trust_level": 3},
            "favor_request": {"reciprocity_balance": -3, "trust_level": 2},
            "favor_given": {"reciprocity_balance": 5, "trust_level": 3},
            "conflict": {"emotional_intimacy": -5, "trust_level": -3, "reciprocity_balance": -2},
            "introduction": {"professional_collaboration": 4, "reciprocity_balance": 2}
        }
        
        # Apply base adjustments
        adjustments = adjustment_map.get(interaction_type, {})
        for dimension, adjustment in adjustments.items():
            if dimension in updated:
                updated[dimension] = max(0, min(100, updated[dimension] + adjustment))
        
        # Apply sentiment modifiers
        sentiment_multiplier = {"positive": 1.5, "neutral": 1.0, "negative": 0.5}.get(sentiment, 1.0)
        for dimension in adjustments:
            if dimension in updated and adjustments[dimension] > 0:
                bonus = int((adjustments[dimension] * sentiment_multiplier) - adjustments[dimension])
                updated[dimension] = max(0, min(100, updated[dimension] + bonus))
        
        # Apply frequency boost for communication frequency
        updated["communication_frequency"] = min(100, updated["communication_frequency"] + 1)
        
        # Use AI to refine scoring if available
        if self.openai_client:
            try:
                ai_adjustments = await self._get_ai_dimension_adjustments(
                    interaction_data, current_dimensions, person
                )
                for dimension, adjustment in ai_adjustments.items():
                    if dimension in updated:
                        updated[dimension] = max(0, min(100, updated[dimension] + adjustment))
            except Exception as e:
                logger.warning(f"Could not get AI dimension adjustments: {e}")
        
        return updated

    async def _get_ai_dimension_adjustments(
        self, interaction_data: Dict[str, Any], current_dimensions: Dict[str, int], person: Person
    ) -> Dict[str, int]:
        """Get AI-recommended dimension adjustments based on interaction"""
        try:
            prompt = f"""
            Analyze this interaction and recommend score adjustments for relationship dimensions (scale: -10 to +10):
            
            Interaction: {json.dumps(interaction_data, indent=2)}
            Current Dimensions: {json.dumps(current_dimensions, indent=2)}
            Person Context: {json.dumps(person.professional_info or {}, indent=2)}
            
            Provide adjustments for these dimensions:
            - emotional_intimacy
            - professional_collaboration  
            - trust_level
            - communication_frequency
            - shared_experience_value
            - reciprocity_balance
            
            Format: dimension_name: adjustment_value (between -10 and +10)
            Consider the interaction type, sentiment, and professional context.
            """
            
            response = self.openai_client.get_completion(prompt)
            
            # Parse AI response
            adjustments = {}
            for line in response.split('\n'):
                if ':' in line:
                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        dim_name = parts[0].strip().lower()
                        try:
                            adjustment = int(parts[1].strip())
                            adjustment = max(-10, min(10, adjustment))  # Clamp to range
                            adjustments[dim_name] = adjustment
                        except ValueError:
                            continue
            
            return adjustments
            
        except Exception as e:
            logger.error(f"Error getting AI dimension adjustments: {e}")
            return {}

    async def _calculate_weighted_overall_score(
        self, dimensions: Dict[str, int], user_preferences: Dict[str, float]
    ) -> int:
        """Calculate weighted overall score based on user preferences"""
        weighted_sum = 0
        total_weight = 0
        
        dimension_weights = {
            "emotional_intimacy": user_preferences.get("emotional_weight", 0.2),
            "professional_collaboration": user_preferences.get("value_weight", 0.2),
            "trust_level": user_preferences.get("trust_weight", 0.2),
            "communication_frequency": user_preferences.get("information_weight", 0.15),
            "shared_experience_value": user_preferences.get("role_weight", 0.15),
            "reciprocity_balance": user_preferences.get("coercive_weight", 0.1)
        }
        
        for dimension, score in dimensions.items():
            weight = dimension_weights.get(dimension, 0.1)
            weighted_sum += score * weight
            total_weight += weight
        
        # Normalize to 0-100 scale
        overall_score = int(weighted_sum / total_weight) if total_weight > 0 else 50
        return max(0, min(100, overall_score))

    async def _generate_scoring_insights(
        self, dimensions: Dict[str, int], interaction_data: Dict[str, Any], person: Person
    ) -> List[str]:
        """Generate AI insights about the relationship scoring"""
        try:
            prompt = f"""
            Provide 2-3 insights about this relationship scoring update:
            
            Updated Dimensions: {json.dumps(dimensions, indent=2)}
            Recent Interaction: {json.dumps(interaction_data or {}, indent=2)}
            Person: {person.first_name} {person.last_name}
            
            Focus on:
            - Key strengths and growth areas
            - Impact of recent interaction
            - Specific recommendations for relationship development
            
            Keep insights concise and actionable.
            """
            
            response = self.openai_client.get_completion(prompt)
            
            # Parse insights
            lines = [line.strip() for line in response.split('\n') if line.strip()]
            insights = [line.lstrip('- ').lstrip('• ').lstrip('* ') for line in lines if line][:3]
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating scoring insights: {e}")
            return []

    def _get_approach_recommendation(self, archetype: str, strength: float) -> str:
        """Generate approach recommendation based on relationship type and strength"""
        if strength >= 0.8:
            approaches = {
                "colleague": "Direct professional request with context",
                "friend": "Casual mention during personal conversation",
                "mentor": "Seek advice and guidance on the connection",
                "client": "Frame as business opportunity or mutual benefit",
                "family": "Direct request with personal context",
                "acquaintance": "Warm introduction request with clear purpose"
            }
        elif strength >= 0.6:
            approaches = {
                "colleague": "Email with professional context and mutual benefit",
                "friend": "Text or call to catch up, then mention the ask",
                "mentor": "Schedule meeting to discuss networking goals",
                "client": "Professional outreach with clear business case",
                "family": "Phone call with personal context",
                "acquaintance": "LinkedIn message with clear mutual benefit"
            }
        else:
            approaches = {
                "colleague": "Reconnect first, then make request",
                "friend": "Catch up on recent developments first",
                "mentor": "Send update on progress, then seek guidance",
                "client": "Professional check-in with valuable insight",
                "family": "Family gathering or personal call first",
                "acquaintance": "Gentle reconnection with industry update"
            }
        
        return approaches.get(archetype, "Professional introduction request")

    def _generate_alternative_strategies(
        self, user_id: UUID, target_criteria: Dict[str, Any], found_paths: List[Any]
    ) -> List[str]:
        """Generate alternative strategies based on pathfinding results"""
        strategies = []
        
        # Get network statistics
        network_stats = self.pathfinding_service.get_path_statistics(user_id)
        
        # Base strategies
        if not found_paths:
            strategies.extend([
                "Expand your network by attending industry events",
                "Join relevant professional associations",
                "Engage more actively on professional social platforms",
                "Ask existing connections for introductions to new people"
            ])
        else:
            # Path-specific strategies
            avg_path_length = sum(p.path_length for p in found_paths) / len(found_paths)
            if avg_path_length > 3:
                strategies.append("Consider building more direct connections in your target area")
            
            avg_success_prob = sum(p.success_probability for p in found_paths) / len(found_paths)
            if avg_success_prob < 0.6:
                strategies.append("Strengthen existing relationships before making referral requests")
        
        # Network density-based strategies
        if network_stats.get("network_density", 0) < 0.1:
            strategies.append("Focus on creating connections between your existing contacts")
        
        # Industry-specific strategies
        if "company" in target_criteria:
            strategies.append(f"Research industry events and conferences related to {target_criteria['company']}")
        
        if "title_keywords" in target_criteria:
            keywords = target_criteria["title_keywords"]
            strategies.append(f"Connect with professionals in roles containing: {', '.join(keywords)}")
        
        return strategies[:5]  # Limit to 5 strategies

    async def _generate_ai_path_insights(
        self, formatted_paths: List[Dict[str, Any]], target_name: str
    ) -> List[str]:
        """Generate AI-powered insights about the referral paths"""
        if not self.openai_client:
            return []
        
        try:
            # Prepare path summary for AI analysis
            path_summary = {
                "target": target_name,
                "total_paths": len(formatted_paths),
                "path_details": []
            }
            
            for path in formatted_paths[:2]:  # Analyze top 2 paths
                path_info = {
                    "length": path["path_length"],
                    "success_probability": path["success_probability"],
                    "confidence_score": path["confidence_score"],
                    "key_connections": [
                        {"archetype": conn["relationship_archetype"], "strength": conn["relationship_strength"]}
                        for conn in path["connections"]
                    ]
                }
                path_summary["path_details"].append(path_info)
            
            prompt = f"""
            Analyze these referral paths and provide 2-3 strategic insights:
            
            Path Analysis: {json.dumps(path_summary, indent=2)}
            
            Consider:
            - Optimal path selection strategy
            - Relationship strengths and potential concerns
            - Timing and approach recommendations
            - Risk mitigation suggestions
            
            Provide concise, actionable insights for successful referral execution.
            """
            
            response = self.openai_client.get_completion(prompt)
            
            # Parse insights
            lines = [line.strip() for line in response.split('\n') if line.strip()]
            insights = [line.lstrip('- ').lstrip('• ').lstrip('* ') for line in lines if line][:3]
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating AI path insights: {e}")
            return []

    async def _generate_ai_network_insights(self, user_id: UUID, diagnosis: Dict[str, Any]) -> List[str]:
        """Generate AI-powered insights about network health"""
        if not self.openai_client:
            return []
        
        try:
            # Prepare network summary for AI analysis
            network_summary = {
                "health_score": diagnosis["overall_health_score"],
                "network_size": diagnosis["network_size"],
                "key_metrics": {
                    "diversity_score": diagnosis["metrics"]["diversity_metrics"]["overall_diversity"],
                    "activity_rate": diagnosis["metrics"]["network_metrics"]["active_relationships"] / max(diagnosis["network_size"], 1),
                    "centrality_score": diagnosis["metrics"]["network_metrics"]["centrality_score"],
                    "dormant_relationships": diagnosis["metrics"]["network_metrics"]["dormant_relationships"]
                },
                "strengths": diagnosis["analysis"]["strengths"],
                "weaknesses": diagnosis["analysis"]["weaknesses"]
            }
            
            prompt = f"""
            Analyze this professional network health data and provide 2-3 strategic insights:
            
            Network Health Summary: {json.dumps(network_summary, indent=2)}
            
            Focus on:
            - Hidden patterns and opportunities
            - Strategic network development advice
            - Competitive advantages or vulnerabilities
            - Long-term network sustainability
            
            Provide concise, actionable insights that go beyond the basic analysis.
            """
            
            response = self.openai_client.get_completion(prompt)
            
            # Parse insights
            lines = [line.strip() for line in response.split('\n') if line.strip()]
            insights = [line.lstrip('- ').lstrip('• ').lstrip('* ') for line in lines if line][:3]
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating AI network insights: {e}")
            return []

    def _generate_personalized_next_steps(self, diagnosis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate personalized next steps based on diagnosis results"""
        next_steps = []
        
        # Get top priority recommendation
        recommendations = diagnosis.get("recommendations", [])
        high_priority_recs = [r for r in recommendations if r.get("priority") == "high"]
        
        if high_priority_recs:
            top_rec = high_priority_recs[0]
            next_steps.append({
                "action": "immediate",
                "title": f"Start with: {top_rec['title']}",
                "description": top_rec.get("description", "High priority recommendation"),
                "first_step": top_rec["actions"][0] if top_rec.get("actions") else "Begin implementation",
                "timeline": "This week"
            })
        
        # Add network-size specific steps
        network_size = diagnosis.get("network_size", 0)
        health_score = diagnosis.get("overall_health_score", 0)
        
        if network_size < 10:
            next_steps.append({
                "action": "foundation",
                "title": "Build your network foundation",
                "description": "Focus on establishing initial professional connections",
                "first_step": "Add 5 current colleagues to your network",
                "timeline": "Next 2 weeks"
            })
        elif network_size < 50 and health_score < 60:
            next_steps.append({
                "action": "expansion",
                "title": "Expand and activate your network",
                "description": "Grow your network while increasing engagement",
                "first_step": "Reach out to 3 dormant connections this week",
                "timeline": "Next month"
            })
        elif health_score >= 70:
            next_steps.append({
                "action": "optimization",
                "title": "Optimize your network leverage",
                "description": "Focus on maximizing value from your strong network",
                "first_step": "Identify 2 strategic introduction opportunities",
                "timeline": "This month"
            })
        
        # Add activity-based steps
        metrics = diagnosis.get("metrics", {})
        network_metrics = metrics.get("network_metrics", {})
        
        if network_metrics.get("dormant_relationships", 0) > 5:
            next_steps.append({
                "action": "reactivation",
                "title": "Reactivate dormant relationships",
                "description": "Reconnect with strong but inactive connections",
                "first_step": "Send a personal message to your top dormant contact",
                "timeline": "This week"
            })
        
        return next_steps[:3]  # Limit to 3 next steps

    async def _generate_peer_comparison(self, user_id: UUID, diagnosis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comparison to peer networks (simulated for now)"""
        # In a real implementation, this would compare to anonymized peer data
        # For now, provide general benchmarks
        
        network_size = diagnosis.get("network_size", 0)
        health_score = diagnosis.get("overall_health_score", 0)
        
        # Simulate peer data based on network size ranges
        if network_size < 25:
            peer_group = "emerging_professionals"
            avg_network_size = 18
            avg_health_score = 45
            percentile = min(95, (network_size / 25) * 50 + (health_score / 100) * 50)
        elif network_size < 100:
            peer_group = "established_professionals"
            avg_network_size = 60
            avg_health_score = 65
            percentile = min(95, (network_size / 100) * 40 + (health_score / 100) * 60)
        else:
            peer_group = "senior_professionals"
            avg_network_size = 120
            avg_health_score = 75
            percentile = min(95, (network_size / 200) * 30 + (health_score / 100) * 70)
        
        comparison_insights = []
        
        if percentile > 75:
            comparison_insights.append("Your network performs above average in your peer group")
        elif percentile < 25:
            comparison_insights.append("Your network has significant growth opportunities compared to peers")
        else:
            comparison_insights.append("Your network performance is typical for your peer group")
        
        # Add specific comparisons
        metrics = diagnosis.get("metrics", {})
        diversity_score = metrics.get("diversity_metrics", {}).get("overall_diversity", 0)
        
        if diversity_score > 0.7:
            comparison_insights.append("Your network diversity exceeds typical professional networks")
        elif diversity_score < 0.4:
            comparison_insights.append("Consider increasing diversity compared to peer benchmarks")
        
        return {
            "peer_group": peer_group,
            "percentile": round(percentile, 1),
            "network_size_comparison": {
                "your_size": network_size,
                "peer_average": avg_network_size,
                "performance": "above" if network_size > avg_network_size else "below"
            },
            "health_score_comparison": {
                "your_score": health_score,
                "peer_average": avg_health_score,
                "performance": "above" if health_score > avg_health_score else "below"
            },
            "insights": comparison_insights
        }
