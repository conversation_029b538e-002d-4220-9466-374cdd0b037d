"""
Organization service for managing organization-related business logic
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import and_, desc, func, or_, text
from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy.exc import IntegrityError

from app.models.organization import Organization, WorksAt
from app.models.person import Person
from app.models.user import User
from app.schemas.organization import (
    OrganizationCreate,
    OrganizationUpdate,
    OrganizationResponse,
    OrganizationSummary,
    OrganizationWithEmployees,
    OrganizationSearch,
    OrganizationStats,
    WorksAtCreate,
    WorksAtUpdate,
    WorksAtResponse
)


class OrganizationService:
    """Service for organization-related operations"""

    def __init__(self, db: Session):
        self.db = db

    def create_organization(
        self, organization_data: OrganizationCreate, user_id: uuid.UUID
    ) -> OrganizationResponse:
        """Create a new organization"""
        try:
            # Create organization record
            db_organization = Organization(
                name=organization_data.name,
                description=organization_data.description,
                details=organization_data.details or {},
                user_id=user_id
            )
            
            self.db.add(db_organization)
            self.db.commit()
            self.db.refresh(db_organization)
            
            # Get organization with computed fields
            return self._build_organization_response(db_organization)
            
        except IntegrityError as e:
            self.db.rollback()
            raise ValueError(f"Organization creation failed: {str(e)}")

    def get_organization_by_id(
        self, org_id: uuid.UUID, user_id: uuid.UUID
    ) -> Optional[OrganizationResponse]:
        """Get organization by ID"""
        organization = (
            self.db.query(Organization)
            .filter(
                and_(
                    Organization.org_id == org_id,
                    Organization.user_id == user_id
                )
            )
            .first()
        )
        
        if not organization:
            return None
            
        return self._build_organization_response(organization)

    def get_organization_with_employees(
        self, org_id: uuid.UUID, user_id: uuid.UUID
    ) -> Optional[OrganizationWithEmployees]:
        """Get organization with its employees"""
        organization = (
            self.db.query(Organization)
            .options(
                selectinload(Organization.employees)
                .selectinload(WorksAt.person)
            )
            .filter(
                and_(
                    Organization.org_id == org_id,
                    Organization.user_id == user_id
                )
            )
            .first()
        )
        
        if not organization:
            return None
        
        # Build response with employees
        org_response = self._build_organization_response(organization)
        employees = []
        
        for work_relationship in organization.employees:
            employee_data = WorksAtResponse(
                person_id=work_relationship.person_id,
                org_id=work_relationship.org_id,
                role=work_relationship.role,
                start_date=work_relationship.start_date,
                end_date=work_relationship.end_date,
                is_current=work_relationship.is_current,
                details=work_relationship.details or {},
                created_at=work_relationship.created_at,
                updated_at=work_relationship.updated_at,
                person_name=f"{work_relationship.person.first_name} {work_relationship.person.last_name}".strip(),
                organization_name=organization.name
            )
            employees.append(employee_data)
        
        return OrganizationWithEmployees(
            **org_response.dict(),
            employees=employees
        )

    def update_organization(
        self, org_id: uuid.UUID, organization_data: OrganizationUpdate, user_id: uuid.UUID
    ) -> Optional[OrganizationResponse]:
        """Update an organization"""
        organization = (
            self.db.query(Organization)
            .filter(
                and_(
                    Organization.org_id == org_id,
                    Organization.user_id == user_id
                )
            )
            .first()
        )
        
        if not organization:
            return None
        
        try:
            # Update fields if provided
            if organization_data.name is not None:
                organization.name = organization_data.name
            if organization_data.description is not None:
                organization.description = organization_data.description
            if organization_data.details is not None:
                organization.details = organization_data.details
            
            organization.updated_at = datetime.utcnow()
            
            self.db.commit()
            self.db.refresh(organization)
            
            return self._build_organization_response(organization)
            
        except IntegrityError as e:
            self.db.rollback()
            raise ValueError(f"Organization update failed: {str(e)}")

    def delete_organization(
        self, org_id: uuid.UUID, user_id: uuid.UUID
    ) -> bool:
        """Delete an organization and its work relationships"""
        organization = (
            self.db.query(Organization)
            .filter(
                and_(
                    Organization.org_id == org_id,
                    Organization.user_id == user_id
                )
            )
            .first()
        )
        
        if not organization:
            return False
        
        try:
            # Delete work relationships first (cascade)
            self.db.query(WorksAt).filter(
                WorksAt.org_id == org_id
            ).delete()
            
            # Delete organization
            self.db.delete(organization)
            self.db.commit()
            
            return True
            
        except IntegrityError as e:
            self.db.rollback()
            raise ValueError(f"Organization deletion failed: {str(e)}")

    def get_organizations(
        self,
        user_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        search_params: Optional[OrganizationSearch] = None
    ) -> List[OrganizationSummary]:
        """Get organizations with optional search/filtering"""
        query = (
            self.db.query(Organization)
            .filter(Organization.user_id == user_id)
        )
        
        # Apply search filters if provided
        if search_params:
            if search_params.query:
                # Use PostgreSQL fuzzy search if available
                try:
                    search_term = f"%{search_params.query}%"
                    query = query.filter(
                        or_(
                            Organization.name.ilike(search_term),
                            Organization.description.ilike(search_term)
                        )
                    )
                except Exception:
                    # Fallback to simple LIKE search
                    search_term = f"%{search_params.query}%"
                    query = query.filter(
                        or_(
                            Organization.name.like(search_term),
                            Organization.description.like(search_term)
                        )
                    )
            
            if search_params.industry:
                query = query.filter(
                    Organization.details['industry'].astext == search_params.industry
                )
            
            if search_params.size:
                query = query.filter(
                    Organization.details['size'].astext == search_params.size
                )
            
            if search_params.location:
                location_term = f"%{search_params.location}%"
                query = query.filter(
                    or_(
                        Organization.details['address'].astext.ilike(location_term),
                        Organization.details['headquarters'].astext.ilike(location_term)
                    )
                )
            
            if search_params.has_connections is not None:
                if search_params.has_connections:
                    # Only organizations with work relationships
                    query = query.join(WorksAt).distinct()
                else:
                    # Organizations without work relationships
                    query = query.outerjoin(WorksAt).filter(WorksAt.org_id.is_(None))
        
        # Apply pagination and ordering
        organizations = (
            query
            .order_by(Organization.name)
            .offset(skip)
            .limit(limit)
            .all()
        )
        
        # Build summary responses
        results = []
        for org in organizations:
            # Count employees for this organization
            employee_count = (
                self.db.query(func.count(WorksAt.person_id))
                .filter(WorksAt.org_id == org.org_id)
                .scalar()
            ) or 0
            
            # Count connections (persons associated with this user)
            connection_count = (
                self.db.query(func.count(WorksAt.person_id))
                .join(Person, WorksAt.person_id == Person.person_id)
                .filter(
                    and_(
                        WorksAt.org_id == org.org_id,
                        Person.user_id == user_id
                    )
                )
                .scalar()
            ) or 0
            
            summary = OrganizationSummary(
                org_id=org.org_id,
                name=org.name,
                description=org.description,
                industry=org.details.get('industry') if org.details else None,
                employee_count=employee_count,
                connection_count=connection_count
            )
            results.append(summary)
        
        return results

    def get_organization_stats(self, user_id: uuid.UUID) -> OrganizationStats:
        """Get organization statistics for the user"""
        # Total organizations
        total_organizations = (
            self.db.query(func.count(Organization.org_id))
            .filter(Organization.user_id == user_id)
            .scalar()
        ) or 0
        
        # Organizations with connections
        organizations_with_connections = (
            self.db.query(func.count(func.distinct(Organization.org_id)))
            .join(WorksAt, Organization.org_id == WorksAt.org_id)
            .join(Person, WorksAt.person_id == Person.person_id)
            .filter(
                and_(
                    Organization.user_id == user_id,
                    Person.user_id == user_id
                )
            )
            .scalar()
        ) or 0
        
        # Top industries by connection count
        top_industries_query = (
            self.db.query(
                Organization.details['industry'].astext.label('industry'),
                func.count(Person.person_id).label('count')
            )
            .join(WorksAt, Organization.org_id == WorksAt.org_id)
            .join(Person, WorksAt.person_id == Person.person_id)
            .filter(
                and_(
                    Organization.user_id == user_id,
                    Person.user_id == user_id,
                    Organization.details['industry'].astext.isnot(None)
                )
            )
            .group_by(Organization.details['industry'].astext)
            .order_by(desc('count'))
            .limit(5)
            .all()
        )
        
        total_connections = sum(row.count for row in top_industries_query)
        top_industries = []
        for row in top_industries_query:
            percentage = (row.count / total_connections * 100) if total_connections > 0 else 0
            top_industries.append({
                'industry': row.industry,
                'count': row.count,
                'percentage': round(percentage, 1)
            })
        
        # Top organizations by connection count
        top_organizations_query = (
            self.db.query(
                Organization.name,
                func.count(Person.person_id).label('count')
            )
            .join(WorksAt, Organization.org_id == WorksAt.org_id)
            .join(Person, WorksAt.person_id == Person.person_id)
            .filter(
                and_(
                    Organization.user_id == user_id,
                    Person.user_id == user_id
                )
            )
            .group_by(Organization.org_id, Organization.name)
            .order_by(desc('count'))
            .limit(5)
            .all()
        )
        
        top_organizations = []
        for row in top_organizations_query:
            percentage = (row.count / total_connections * 100) if total_connections > 0 else 0
            top_organizations.append({
                'name': row.name,
                'count': row.count,
                'percentage': round(percentage, 1)
            })
        
        # Connection distribution
        distribution_query = (
            self.db.query(
                func.count(Person.person_id).label('connection_count'),
                func.count(Organization.org_id).label('org_count')
            )
            .select_from(Organization)
            .join(WorksAt, Organization.org_id == WorksAt.org_id)
            .join(Person, WorksAt.person_id == Person.person_id)
            .filter(
                and_(
                    Organization.user_id == user_id,
                    Person.user_id == user_id
                )
            )
            .group_by(Organization.org_id)
            .all()
        )
        
        connection_distribution = {"1-5 connections": 0, "6-10 connections": 0, "11+ connections": 0}
        for row in distribution_query:
            count = row.connection_count
            if count <= 5:
                connection_distribution["1-5 connections"] += 1
            elif count <= 10:
                connection_distribution["6-10 connections"] += 1
            else:
                connection_distribution["11+ connections"] += 1
        
        return OrganizationStats(
            total_organizations=total_organizations,
            organizations_with_connections=organizations_with_connections,
            top_industries=top_industries,
            top_organizations=top_organizations,
            connection_distribution=connection_distribution
        )

    # Work relationship management methods
    
    def create_work_relationship(
        self, work_data: WorksAtCreate, user_id: uuid.UUID
    ) -> WorksAtResponse:
        """Create a work relationship between person and organization"""
        # Verify person belongs to user
        person = (
            self.db.query(Person)
            .filter(
                and_(
                    Person.person_id == work_data.person_id,
                    Person.user_id == user_id
                )
            )
            .first()
        )
        
        if not person:
            raise ValueError("Person not found or access denied")
        
        # Verify organization belongs to user
        organization = (
            self.db.query(Organization)
            .filter(
                and_(
                    Organization.org_id == work_data.org_id,
                    Organization.user_id == user_id
                )
            )
            .first()
        )
        
        if not organization:
            raise ValueError("Organization not found or access denied")
        
        try:
            # Create work relationship
            db_work_relationship = WorksAt(
                person_id=work_data.person_id,
                org_id=work_data.org_id,
                role=work_data.role,
                start_date=work_data.start_date,
                end_date=work_data.end_date,
                is_current=work_data.is_current,
                details=work_data.details or {}
            )
            
            self.db.add(db_work_relationship)
            self.db.commit()
            self.db.refresh(db_work_relationship)
            
            return WorksAtResponse(
                person_id=db_work_relationship.person_id,
                org_id=db_work_relationship.org_id,
                role=db_work_relationship.role,
                start_date=db_work_relationship.start_date,
                end_date=db_work_relationship.end_date,
                is_current=db_work_relationship.is_current,
                details=db_work_relationship.details or {},
                created_at=db_work_relationship.created_at,
                updated_at=db_work_relationship.updated_at,
                person_name=f"{person.first_name} {person.last_name}".strip(),
                organization_name=organization.name
            )
            
        except IntegrityError as e:
            self.db.rollback()
            if "duplicate key" in str(e).lower():
                raise ValueError("Work relationship already exists")
            raise ValueError(f"Work relationship creation failed: {str(e)}")

    def update_work_relationship(
        self,
        person_id: uuid.UUID,
        org_id: uuid.UUID,
        work_data: WorksAtUpdate,
        user_id: uuid.UUID
    ) -> Optional[WorksAtResponse]:
        """Update a work relationship"""
        # Get work relationship with person and organization info
        work_relationship = (
            self.db.query(WorksAt)
            .join(Person, WorksAt.person_id == Person.person_id)
            .join(Organization, WorksAt.org_id == Organization.org_id)
            .filter(
                and_(
                    WorksAt.person_id == person_id,
                    WorksAt.org_id == org_id,
                    Person.user_id == user_id,
                    Organization.user_id == user_id
                )
            )
            .first()
        )
        
        if not work_relationship:
            return None
        
        try:
            # Update fields if provided
            if work_data.role is not None:
                work_relationship.role = work_data.role
            if work_data.start_date is not None:
                work_relationship.start_date = work_data.start_date
            if work_data.end_date is not None:
                work_relationship.end_date = work_data.end_date
            if work_data.is_current is not None:
                work_relationship.is_current = work_data.is_current
            if work_data.details is not None:
                work_relationship.details = work_data.details
            
            work_relationship.updated_at = datetime.utcnow()
            
            self.db.commit()
            self.db.refresh(work_relationship)
            
            # Get related objects for response
            person = work_relationship.person
            organization = work_relationship.organization
            
            return WorksAtResponse(
                person_id=work_relationship.person_id,
                org_id=work_relationship.org_id,
                role=work_relationship.role,
                start_date=work_relationship.start_date,
                end_date=work_relationship.end_date,
                is_current=work_relationship.is_current,
                details=work_relationship.details or {},
                created_at=work_relationship.created_at,
                updated_at=work_relationship.updated_at,
                person_name=f"{person.first_name} {person.last_name}".strip(),
                organization_name=organization.name
            )
            
        except IntegrityError as e:
            self.db.rollback()
            raise ValueError(f"Work relationship update failed: {str(e)}")

    def delete_work_relationship(
        self, person_id: uuid.UUID, org_id: uuid.UUID, user_id: uuid.UUID
    ) -> bool:
        """Delete a work relationship"""
        # Verify ownership through person and organization
        work_relationship = (
            self.db.query(WorksAt)
            .join(Person, WorksAt.person_id == Person.person_id)
            .join(Organization, WorksAt.org_id == Organization.org_id)
            .filter(
                and_(
                    WorksAt.person_id == person_id,
                    WorksAt.org_id == org_id,
                    Person.user_id == user_id,
                    Organization.user_id == user_id
                )
            )
            .first()
        )
        
        if not work_relationship:
            return False
        
        try:
            self.db.delete(work_relationship)
            self.db.commit()
            return True
            
        except IntegrityError as e:
            self.db.rollback()
            raise ValueError(f"Work relationship deletion failed: {str(e)}")

    def get_person_work_history(
        self, person_id: uuid.UUID, user_id: uuid.UUID
    ) -> List[WorksAtResponse]:
        """Get work history for a specific person"""
        work_relationships = (
            self.db.query(WorksAt)
            .join(Person, WorksAt.person_id == Person.person_id)
            .join(Organization, WorksAt.org_id == Organization.org_id)
            .filter(
                and_(
                    WorksAt.person_id == person_id,
                    Person.user_id == user_id,
                    Organization.user_id == user_id
                )
            )
            .order_by(desc(WorksAt.is_current), desc(WorksAt.start_date))
            .all()
        )
        
        results = []
        for work in work_relationships:
            results.append(WorksAtResponse(
                person_id=work.person_id,
                org_id=work.org_id,
                role=work.role,
                start_date=work.start_date,
                end_date=work.end_date,
                is_current=work.is_current,
                details=work.details or {},
                created_at=work.created_at,
                updated_at=work.updated_at,
                person_name=f"{work.person.first_name} {work.person.last_name}".strip(),
                organization_name=work.organization.name
            ))
        
        return results

    def _build_organization_response(self, organization: Organization) -> OrganizationResponse:
        """Build organization response with computed fields"""
        # Count employees
        employee_count = (
            self.db.query(func.count(WorksAt.person_id))
            .filter(WorksAt.org_id == organization.org_id)
            .scalar()
        ) or 0
        
        # Count connections (persons associated with this user)
        connection_count = (
            self.db.query(func.count(WorksAt.person_id))
            .join(Person, WorksAt.person_id == Person.person_id)
            .filter(
                and_(
                    WorksAt.org_id == organization.org_id,
                    Person.user_id == organization.user_id
                )
            )
            .scalar()
        ) or 0
        
        return OrganizationResponse(
            org_id=organization.org_id,
            user_id=organization.user_id,
            name=organization.name,
            description=organization.description,
            details=organization.details or {},
            created_at=organization.created_at,
            updated_at=organization.updated_at,
            employee_count=employee_count,
            connection_count=connection_count
        )