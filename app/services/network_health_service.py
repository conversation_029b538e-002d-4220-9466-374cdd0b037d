"""
Network Health Service - Comprehensive network analysis and health diagnosis
Provides real metrics for network health, diversity, and relationship activity
"""

import math
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Tuple
from uuid import UUID
from collections import defaultdict, Counter
from dataclasses import dataclass

import networkx as nx
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from app.models.person import Person
from app.models.relationship import Knows
from app.models.interaction import Interaction
from app.services.graph_service import GraphService


@dataclass
class NetworkMetrics:
    """Container for network health metrics"""
    total_connections: int
    active_relationships: int
    dormant_relationships: int
    diversity_score: float
    centrality_score: float
    density: float
    clustering_coefficient: float
    network_efficiency: float
    structural_holes: int
    bridge_connections: int
    influence_score: float


@dataclass
class DiversityMetrics:
    """Container for network diversity analysis"""
    industry_diversity: float
    role_diversity: float
    company_diversity: float
    archetype_diversity: float
    geographic_diversity: float
    total_diversity_score: float
    diversity_gaps: List[str]


@dataclass
class ActivityMetrics:
    """Container for relationship activity analysis"""
    interactions_last_30_days: int
    interactions_last_90_days: int
    average_interaction_frequency: float
    most_active_relationships: List[str]
    least_active_relationships: List[str]
    communication_momentum: float


@dataclass
class HealthRecommendation:
    """Container for actionable health recommendations"""
    category: str
    priority: str  # high, medium, low
    title: str
    description: str
    specific_actions: List[str]
    expected_impact: str
    time_investment: str
    target_metrics: List[str]


class NetworkHealthService:
    """
    Comprehensive network health analysis service
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.graph_service = GraphService(db)
        
        # Health scoring weights
        self.health_weights = {
            "network_size": 0.15,
            "activity_level": 0.25,
            "diversity": 0.20,
            "relationship_quality": 0.25,
            "network_structure": 0.15
        }
        
        # Benchmark values for scoring
        self.benchmarks = {
            "optimal_network_size": 150,  # Dunbar's number
            "min_network_size": 20,
            "optimal_diversity_score": 0.8,
            "optimal_activity_rate": 0.3,  # 30% of relationships active monthly
            "strong_relationship_threshold": 70,
            "dormant_threshold_days": 90
        }
    
    def diagnose_network_health(self, user_id: UUID) -> Dict[str, Any]:
        """
        Perform comprehensive network health diagnosis
        """
        # Get basic network data
        persons = self.db.query(Person).filter(Person.user_id == user_id).all()
        relationships = self.db.query(Knows).filter(Knows.user_id == user_id).all()
        
        if not persons or not relationships:
            return self._generate_empty_network_diagnosis()
        
        # Calculate comprehensive metrics
        network_metrics = self._calculate_network_metrics(user_id, persons, relationships)
        diversity_metrics = self._calculate_diversity_metrics(user_id, persons, relationships)
        activity_metrics = self._calculate_activity_metrics(user_id, relationships)
        
        # Calculate overall health score
        overall_health = self._calculate_overall_health_score(
            network_metrics, diversity_metrics, activity_metrics
        )
        
        # Generate insights and recommendations
        strengths = self._identify_network_strengths(network_metrics, diversity_metrics, activity_metrics)
        weaknesses = self._identify_network_weaknesses(network_metrics, diversity_metrics, activity_metrics)
        recommendations = self._generate_health_recommendations(
            user_id, network_metrics, diversity_metrics, activity_metrics, weaknesses
        )

        # Calculate trend data
        trend_data = self._calculate_health_trends(user_id)
        
        return {
            "overall_health_score": round(overall_health, 1),
            "health_grade": self._get_health_grade(overall_health),
            "network_size": network_metrics.total_connections,
            "last_analyzed": datetime.utcnow().isoformat() + "Z",
            "metrics": {
                "network_metrics": {
                    "total_connections": network_metrics.total_connections,
                    "active_relationships": network_metrics.active_relationships,
                    "dormant_relationships": network_metrics.dormant_relationships,
                    "network_density": round(network_metrics.density, 3),
                    "clustering_coefficient": round(network_metrics.clustering_coefficient, 3),
                    "network_efficiency": round(network_metrics.network_efficiency, 3),
                    "centrality_score": round(network_metrics.centrality_score, 3),
                    "influence_score": round(network_metrics.influence_score, 3),
                    "structural_holes": network_metrics.structural_holes,
                    "bridge_connections": network_metrics.bridge_connections
                },
                "diversity_metrics": {
                    "overall_diversity": round(diversity_metrics.total_diversity_score, 3),
                    "industry_diversity": round(diversity_metrics.industry_diversity, 3),
                    "role_diversity": round(diversity_metrics.role_diversity, 3),
                    "company_diversity": round(diversity_metrics.company_diversity, 3),
                    "archetype_diversity": round(diversity_metrics.archetype_diversity, 3),
                    "diversity_gaps": diversity_metrics.diversity_gaps
                },
                "activity_metrics": {
                    "interactions_last_30_days": activity_metrics.interactions_last_30_days,
                    "interactions_last_90_days": activity_metrics.interactions_last_90_days,
                    "average_interaction_frequency": round(activity_metrics.average_interaction_frequency, 2),
                    "communication_momentum": round(activity_metrics.communication_momentum, 3),
                    "most_active_relationships": activity_metrics.most_active_relationships[:5],
                    "least_active_relationships": activity_metrics.least_active_relationships[:5]
                }
            },
            "analysis": {
                "strengths": strengths,
                "weaknesses": weaknesses,
                "critical_gaps": self._identify_critical_gaps(diversity_metrics, network_metrics),
                "growth_opportunities": self._identify_growth_opportunities(network_metrics, diversity_metrics)
            },
            "recommendations": [
                {
                    "category": rec.category,
                    "priority": rec.priority,
                    "title": rec.title,
                    "description": rec.description,
                    "actions": rec.specific_actions,
                    "expected_impact": rec.expected_impact,
                    "time_investment": rec.time_investment,
                    "target_metrics": rec.target_metrics
                }
                for rec in recommendations
            ],
            "trends": trend_data,
            "benchmark_comparison": self._generate_benchmark_comparison(network_metrics, diversity_metrics, activity_metrics)
        }
    
    def _calculate_network_metrics(
        self, user_id: UUID, persons: List[Person], relationships: List[Knows]
    ) -> NetworkMetrics:
        """Calculate comprehensive network structural metrics"""
        
        # Build network graph
        graph = self._build_network_graph(persons, relationships)
        
        total_connections = len(persons)
        
        # Calculate activity-based metrics
        active_relationships = self._count_active_relationships(user_id, relationships)
        dormant_relationships = self._count_dormant_relationships(user_id, relationships)
        
        # Calculate graph structure metrics
        if total_connections > 1:
            density = nx.density(graph)
            clustering_coefficient = nx.average_clustering(graph)
            
            # Network efficiency (average inverse shortest path length)
            try:
                efficiency = nx.global_efficiency(graph)
            except:
                efficiency = 0.5
            
            # Centrality score (how central is the user in their network)
            centrality_score = self._calculate_user_centrality(graph, user_id)
            
            # Structural analysis
            structural_holes = self._count_structural_holes(graph, user_id)
            bridge_connections = self._count_bridge_connections(graph, user_id)
            
            # Influence score based on network position
            influence_score = self._calculate_influence_score(graph, user_id, relationships)
        else:
            density = 0
            clustering_coefficient = 0
            efficiency = 0
            centrality_score = 0
            structural_holes = 0
            bridge_connections = 0
            influence_score = 0
        
        return NetworkMetrics(
            total_connections=total_connections,
            active_relationships=active_relationships,
            dormant_relationships=dormant_relationships,
            diversity_score=0,  # Will be calculated separately
            centrality_score=centrality_score,
            density=density,
            clustering_coefficient=clustering_coefficient,
            network_efficiency=efficiency,
            structural_holes=structural_holes,
            bridge_connections=bridge_connections,
            influence_score=influence_score
        )
    
    def _calculate_diversity_metrics(
        self, user_id: UUID, persons: List[Person], relationships: List[Knows]
    ) -> DiversityMetrics:
        """Calculate network diversity across multiple dimensions"""
        
        # Extract diversity data
        industries = []
        roles = []
        companies = []
        archetypes = []
        
        for person in persons:
            prof_info = person.professional_info or {}
            industries.append(prof_info.get("industry", "unknown"))
            roles.append(prof_info.get("title", "unknown"))
            companies.append(prof_info.get("company", "unknown"))
        
        for relationship in relationships:
            archetypes.append(relationship.archetype or "unknown")
        
        # Calculate diversity scores using Shannon diversity index
        industry_diversity = self._calculate_shannon_diversity(industries)
        role_diversity = self._calculate_shannon_diversity(roles)
        company_diversity = self._calculate_shannon_diversity(companies)
        archetype_diversity = self._calculate_shannon_diversity(archetypes)
        
        # Geographic diversity (if location data available)
        geographic_diversity = 0.5  # Placeholder - would need location data
        
        # Calculate total diversity score (weighted average)
        total_diversity_score = (
            industry_diversity * 0.3 +
            role_diversity * 0.25 +
            company_diversity * 0.2 +
            archetype_diversity * 0.15 +
            geographic_diversity * 0.1
        )
        
        # Identify diversity gaps
        diversity_gaps = []
        if industry_diversity < 0.4:
            diversity_gaps.append("Low industry diversity")
        if role_diversity < 0.4:
            diversity_gaps.append("Limited role diversity")
        if company_diversity < 0.3:
            diversity_gaps.append("Concentrated in few companies")
        if archetype_diversity < 0.5:
            diversity_gaps.append("Limited relationship types")
        
        return DiversityMetrics(
            industry_diversity=industry_diversity,
            role_diversity=role_diversity,
            company_diversity=company_diversity,
            archetype_diversity=archetype_diversity,
            geographic_diversity=geographic_diversity,
            total_diversity_score=total_diversity_score,
            diversity_gaps=diversity_gaps
        )
    
    def _calculate_activity_metrics(
        self, user_id: UUID, relationships: List[Knows]
    ) -> ActivityMetrics:
        """Calculate relationship activity and engagement metrics"""
        
        now = datetime.utcnow()
        thirty_days_ago = now - timedelta(days=30)
        ninety_days_ago = now - timedelta(days=90)
        
        # Get interaction counts
        interactions_30 = self.db.query(func.count(Interaction.interaction_id)).filter(
            Interaction.user_id == user_id,
            Interaction.interaction_date >= thirty_days_ago
        ).scalar() or 0
        
        interactions_90 = self.db.query(func.count(Interaction.interaction_id)).filter(
            Interaction.user_id == user_id,
            Interaction.interaction_date >= ninety_days_ago
        ).scalar() or 0
        
        # Calculate average interaction frequency per relationship
        total_relationships = len(relationships)
        avg_frequency = interactions_90 / (total_relationships * 3) if total_relationships > 0 else 0  # Per month
        
        # Find most and least active relationships
        most_active, least_active = self._analyze_relationship_activity(user_id, relationships)
        
        # Calculate communication momentum (trend)
        momentum = self._calculate_communication_momentum(interactions_30, interactions_90)
        
        return ActivityMetrics(
            interactions_last_30_days=interactions_30,
            interactions_last_90_days=interactions_90,
            average_interaction_frequency=avg_frequency,
            most_active_relationships=most_active,
            least_active_relationships=least_active,
            communication_momentum=momentum
        )
    
    def _calculate_overall_health_score(
        self, network: NetworkMetrics, diversity: DiversityMetrics, activity: ActivityMetrics
    ) -> float:
        """Calculate overall network health score (0-100)"""
        
        # Network size score (optimal around 150, Dunbar's number)
        size_score = min(100, (network.total_connections / self.benchmarks["optimal_network_size"]) * 100)
        if network.total_connections < self.benchmarks["min_network_size"]:
            size_score *= 0.5  # Penalty for very small networks
        
        # Activity score (based on active vs total relationships)
        activity_rate = network.active_relationships / max(network.total_connections, 1)
        activity_score = min(100, (activity_rate / self.benchmarks["optimal_activity_rate"]) * 100)
        
        # Diversity score
        diversity_score = diversity.total_diversity_score * 100
        
        # Relationship quality score (based on strong relationships)
        strong_relationships = sum(1 for r in self.db.query(Knows).filter(
            Knows.user_id == network.total_connections,  # This needs to be fixed with actual user_id
            Knows.relationship_depth.op('->>')('overall_score').cast(func.INTEGER) >= self.benchmarks["strong_relationship_threshold"]
        ).all() if hasattr(r, 'relationship_depth') and r.relationship_depth)
        
        quality_score = min(100, (strong_relationships / max(network.total_connections, 1)) * 150)
        
        # Network structure score (combination of density, efficiency, centrality)
        structure_score = (
            network.density * 30 +
            network.network_efficiency * 30 +
            network.centrality_score * 40
        )
        
        # Calculate weighted final score
        overall_score = (
            size_score * self.health_weights["network_size"] +
            activity_score * self.health_weights["activity_level"] +
            diversity_score * self.health_weights["diversity"] +
            quality_score * self.health_weights["relationship_quality"] +
            structure_score * self.health_weights["network_structure"]
        )
        
        return min(100, max(0, overall_score))
    
    def _build_network_graph(self, persons: List[Person], relationships: List[Knows]) -> nx.Graph:
        """Build NetworkX graph from persons and relationships"""
        graph = nx.Graph()
        
        # Add nodes
        for person in persons:
            graph.add_node(str(person.person_id), person_data=person)
        
        # Add edges
        for relationship in relationships:
            from_id = str(relationship.from_person_id)
            to_id = str(relationship.to_person_id)
            
            if from_id in graph and to_id in graph:
                weight = relationship.overall_score / 100.0 if relationship.overall_score else 0.5
                graph.add_edge(from_id, to_id, weight=weight, relationship_data=relationship)
        
        return graph
    
    def _calculate_shannon_diversity(self, items: List[str]) -> float:
        """Calculate Shannon diversity index for a list of categories"""
        if not items:
            return 0
        
        # Count occurrences
        counts = Counter(items)
        total = len(items)
        
        # Calculate Shannon index
        shannon_index = 0
        for count in counts.values():
            proportion = count / total
            if proportion > 0:
                shannon_index -= proportion * math.log2(proportion)
        
        # Normalize to 0-1 scale (divide by max possible diversity)
        max_diversity = math.log2(len(counts)) if len(counts) > 1 else 1
        return shannon_index / max_diversity if max_diversity > 0 else 0
    
    def _calculate_user_centrality(self, graph: nx.Graph, user_id: UUID) -> float:
        """Calculate user's centrality in their network"""
        user_id_str = str(user_id)
        
        if user_id_str not in graph:
            return 0
        
        try:
            # Calculate multiple centrality measures
            betweenness = nx.betweenness_centrality(graph)
            closeness = nx.closeness_centrality(graph)
            degree = nx.degree_centrality(graph)
            
            # Combine centrality measures
            user_betweenness = betweenness.get(user_id_str, 0)
            user_closeness = closeness.get(user_id_str, 0)
            user_degree = degree.get(user_id_str, 0)
            
            # Weighted combination
            centrality_score = (
                user_betweenness * 0.4 +  # How often user is on shortest paths
                user_closeness * 0.3 +    # How close user is to all others
                user_degree * 0.3         # How connected user is
            )
            
            return centrality_score
            
        except:
            return 0
    
    def _count_structural_holes(self, graph: nx.Graph, user_id: UUID) -> int:
        """Count structural holes that user could bridge"""
        user_id_str = str(user_id)
        
        if user_id_str not in graph:
            return 0
        
        user_neighbors = set(graph.neighbors(user_id_str))
        structural_holes = 0
        
        # Count pairs of user's connections that aren't connected to each other
        for neighbor1 in user_neighbors:
            for neighbor2 in user_neighbors:
                if neighbor1 != neighbor2 and not graph.has_edge(neighbor1, neighbor2):
                    structural_holes += 1
        
        return structural_holes // 2  # Divide by 2 since we count each pair twice
    
    def _count_bridge_connections(self, graph: nx.Graph, user_id: UUID) -> int:
        """Count how many bridge connections user has"""
        user_id_str = str(user_id)
        
        if user_id_str not in graph:
            return 0
        
        bridges = list(nx.bridges(graph))
        user_bridges = 0
        
        for bridge in bridges:
            if user_id_str in bridge:
                user_bridges += 1
        
        return user_bridges
    
    def _calculate_influence_score(
        self, graph: nx.Graph, user_id: UUID, relationships: List[Knows]
    ) -> float:
        """Calculate user's potential influence in the network"""
        user_id_str = str(user_id)
        
        if user_id_str not in graph:
            return 0
        
        # Factors for influence:
        # 1. Network position (centrality)
        centrality_score = self._calculate_user_centrality(graph, user_id)
        
        # 2. Relationship strength (average of user's relationship scores)
        user_relationships = [r for r in relationships if str(r.from_person_id) == user_id_str]
        avg_relationship_strength = sum(r.overall_score for r in user_relationships) / len(user_relationships) if user_relationships else 0
        avg_relationship_strength /= 100  # Normalize to 0-1
        
        # 3. Network reach (2-hop connections)
        two_hop_reach = 0
        for neighbor in graph.neighbors(user_id_str):
            two_hop_reach += len(list(graph.neighbors(neighbor)))
        two_hop_reach = min(1.0, two_hop_reach / 100)  # Normalize
        
        # 4. Structural holes (brokerage potential)
        structural_holes = self._count_structural_holes(graph, user_id)
        brokerage_potential = min(1.0, structural_holes / 20)  # Normalize
        
        # Combine factors
        influence_score = (
            centrality_score * 0.3 +
            avg_relationship_strength * 0.25 +
            two_hop_reach * 0.25 +
            brokerage_potential * 0.2
        )
        
        return influence_score
    
    def _count_active_relationships(self, user_id: UUID, relationships: List[Knows]) -> int:
        """Count relationships with recent activity"""
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        
        active_count = 0
        for relationship in relationships:
            recent_interactions = self.db.query(func.count(Interaction.interaction_id)).filter(
                Interaction.user_id == user_id,
                Interaction.person_id == relationship.to_person_id,
                Interaction.interaction_date >= thirty_days_ago
            ).scalar() or 0
            
            if recent_interactions > 0:
                active_count += 1
        
        return active_count
    
    def _count_dormant_relationships(self, user_id: UUID, relationships: List[Knows]) -> int:
        """Count strong relationships without recent activity"""
        ninety_days_ago = datetime.utcnow() - timedelta(days=90)
        
        dormant_count = 0
        for relationship in relationships:
            if relationship.overall_score >= 70:  # Strong relationship
                recent_interactions = self.db.query(func.count(Interaction.interaction_id)).filter(
                    Interaction.user_id == user_id,
                    Interaction.person_id == relationship.to_person_id,
                    Interaction.interaction_date >= ninety_days_ago
                ).scalar() or 0
                
                if recent_interactions == 0:
                    dormant_count += 1
        
        return dormant_count
    
    def _analyze_relationship_activity(
        self, user_id: UUID, relationships: List[Knows]
    ) -> Tuple[List[str], List[str]]:
        """Analyze most and least active relationships"""
        ninety_days_ago = datetime.utcnow() - timedelta(days=90)
        
        activity_scores = []
        
        for relationship in relationships:
            person = self.db.query(Person).filter(
                Person.person_id == relationship.to_person_id
            ).first()
            
            if person:
                interaction_count = self.db.query(func.count(Interaction.interaction_id)).filter(
                    Interaction.user_id == user_id,
                    Interaction.person_id == relationship.to_person_id,
                    Interaction.interaction_date >= ninety_days_ago
                ).scalar() or 0
                
                activity_scores.append({
                    "name": f"{person.first_name} {person.last_name}",
                    "interactions": interaction_count,
                    "relationship_score": relationship.overall_score
                })
        
        # Sort by interaction count
        activity_scores.sort(key=lambda x: x["interactions"], reverse=True)
        
        most_active = [item["name"] for item in activity_scores[:5] if item["interactions"] > 0]
        least_active = [item["name"] for item in activity_scores[-5:] if item["relationship_score"] >= 60]
        
        return most_active, least_active
    
    def _calculate_communication_momentum(self, interactions_30: int, interactions_90: int) -> float:
        """Calculate communication momentum (trending up/down)"""
        if interactions_90 == 0:
            return 0
        
        # Calculate monthly rates
        monthly_rate_recent = interactions_30  # Last 30 days
        monthly_rate_previous = (interactions_90 - interactions_30) / 2  # Previous 60 days average
        
        if monthly_rate_previous == 0:
            return 1.0 if monthly_rate_recent > 0 else 0
        
        momentum = monthly_rate_recent / monthly_rate_previous
        return min(2.0, momentum)  # Cap at 2.0 for very high momentum
    
    def _identify_network_strengths(
        self, network: NetworkMetrics, diversity: DiversityMetrics, activity: ActivityMetrics
    ) -> List[str]:
        """Identify network strengths"""
        strengths = []
        
        if network.total_connections >= 50:
            strengths.append("Strong network size")
        
        if diversity.total_diversity_score > 0.7:
            strengths.append("High network diversity")
        
        if network.active_relationships / max(network.total_connections, 1) > 0.3:
            strengths.append("High relationship activity")
        
        if network.centrality_score > 0.7:
            strengths.append("Strong network position")
        
        if network.structural_holes > 10:
            strengths.append("Good brokerage opportunities")
        
        if activity.communication_momentum > 1.2:
            strengths.append("Positive communication momentum")
        
        if network.influence_score > 0.6:
            strengths.append("High network influence potential")
        
        return strengths
    
    def _identify_network_weaknesses(
        self, network: NetworkMetrics, diversity: DiversityMetrics, activity: ActivityMetrics
    ) -> List[str]:
        """Identify network weaknesses"""
        weaknesses = []
        
        if network.total_connections < 20:
            weaknesses.append("Small network size")
        
        if diversity.total_diversity_score < 0.4:
            weaknesses.append("Low network diversity")
        
        if network.active_relationships / max(network.total_connections, 1) < 0.15:
            weaknesses.append("Low relationship activity")
        
        if network.dormant_relationships > network.active_relationships:
            weaknesses.append("Many dormant relationships")
        
        if network.centrality_score < 0.3:
            weaknesses.append("Weak network position")
        
        if activity.communication_momentum < 0.8:
            weaknesses.append("Declining communication")
        
        if network.structural_holes < 5:
            weaknesses.append("Limited brokerage opportunities")
        
        return weaknesses
    
    def _identify_critical_gaps(
        self, diversity: DiversityMetrics, network: NetworkMetrics
    ) -> List[str]:
        """Identify critical gaps in the network"""
        gaps = []
        
        if diversity.industry_diversity < 0.3:
            gaps.append("Lack of industry diversity")
        
        if diversity.role_diversity < 0.3:
            gaps.append("Limited role diversity")
        
        if network.bridge_connections == 0:
            gaps.append("No bridge connections to other clusters")
        
        if network.structural_holes < 3:
            gaps.append("Limited structural hole opportunities")
        
        return gaps
    
    def _identify_growth_opportunities(
        self, network: NetworkMetrics, diversity: DiversityMetrics
    ) -> List[str]:
        """Identify network growth opportunities"""
        opportunities = []
        
        if network.structural_holes > 5:
            opportunities.append("Leverage structural holes for introductions")
        
        if diversity.industry_diversity < 0.6:
            opportunities.append("Expand into new industries")
        
        if network.centrality_score > 0.5:
            opportunities.append("Use network position to broker connections")
        
        if network.dormant_relationships > 3:
            opportunities.append("Reactivate dormant relationships")
        
        return opportunities
    
    def _generate_health_recommendations(
        self,
        user_id: UUID,
        network: NetworkMetrics,
        diversity: DiversityMetrics,
        activity: ActivityMetrics,
        weaknesses: List[str]
    ) -> List[HealthRecommendation]:
        """Generate specific actionable recommendations"""
        recommendations = []
        
        # Network size recommendations
        if network.total_connections < 20:
            recommendations.append(HealthRecommendation(
                category="Network Growth",
                priority="high",
                title="Expand your professional network",
                description="Your network is smaller than optimal. Focus on building new connections.",
                specific_actions=[
                    "Attend 2-3 industry events per month",
                    "Join relevant professional associations",
                    "Engage actively on LinkedIn",
                    "Ask existing connections for introductions"
                ],
                expected_impact="Increase network size by 50% in 6 months",
                time_investment="2-3 hours per week",
                target_metrics=["total_connections", "diversity_score"]
            ))
        
        # Activity recommendations
        if activity.communication_momentum < 0.8:
            recommendations.append(HealthRecommendation(
                category="Relationship Maintenance",
                priority="high",
                title="Increase relationship activity",
                description="Your communication with your network is declining. Regular touchpoints are crucial.",
                specific_actions=[
                    "Schedule monthly check-ins with top 20 contacts",
                    "Share relevant articles or opportunities",
                    "Send personalized messages on special occasions",
                    "Organize quarterly networking gatherings"
                ],
                expected_impact="Double interaction frequency in 3 months",
                time_investment="1 hour per week",
                target_metrics=["active_relationships", "communication_momentum"]
            ))
        
        # Dormant relationship recommendations
        if network.dormant_relationships > 5:
            recommendations.append(HealthRecommendation(
                category="Relationship Revival",
                priority="medium",
                title="Reactivate dormant relationships",
                description="You have strong relationships that haven't been active recently.",
                specific_actions=[
                    "Identify top 5 dormant relationships to reactivate",
                    "Send personalized catch-up messages",
                    "Propose coffee meetings or virtual calls",
                    "Share relevant opportunities or insights"
                ],
                expected_impact="Reactivate 50% of dormant relationships",
                time_investment="30 minutes per week",
                target_metrics=["active_relationships", "dormant_relationships"]
            ))
        
        # Diversity recommendations
        if diversity.total_diversity_score < 0.5:
            recommendations.append(HealthRecommendation(
                category="Network Diversity",
                priority="medium",
                title="Increase network diversity",
                description="Your network lacks diversity across industries and roles.",
                specific_actions=[
                    "Connect with professionals in different industries",
                    "Attend cross-industry networking events",
                    "Join diverse professional groups",
                    "Seek mentors from different backgrounds"
                ],
                expected_impact="Improve diversity score by 40%",
                time_investment="1-2 hours per month",
                target_metrics=["diversity_score", "industry_diversity"]
            ))
        
        # Structural recommendations
        if network.structural_holes > 10:
            recommendations.append(HealthRecommendation(
                category="Network Leverage",
                priority="low",
                title="Leverage brokerage opportunities",
                description="You're well-positioned to make strategic introductions.",
                specific_actions=[
                    "Identify mutually beneficial connections",
                    "Facilitate strategic introductions",
                    "Host networking events to bring groups together",
                    "Position yourself as a connector in your industry"
                ],
                expected_impact="Increase influence and reciprocity",
                time_investment="1 hour per month",
                target_metrics=["influence_score", "centrality_score"]
            ))
        
        return recommendations[:5]  # Limit to top 5 recommendations
    
    def _calculate_health_trends(self, user_id: UUID) -> Dict[str, Any]:
        """Calculate health trends over time (placeholder for now)"""
        # This would typically look at historical data
        # For now, return a placeholder structure
        return {
            "health_score_trend": "stable",
            "network_size_change": "+5% (last 90 days)",
            "activity_trend": "improving",
            "diversity_trend": "stable",
            "last_major_change": None
        }
    
    def _generate_benchmark_comparison(
        self, network: NetworkMetrics, diversity: DiversityMetrics, activity: ActivityMetrics
    ) -> Dict[str, Any]:
        """Compare user's metrics to benchmarks"""
        return {
            "network_size": {
                "value": network.total_connections,
                "benchmark": self.benchmarks["optimal_network_size"],
                "percentile": min(100, (network.total_connections / self.benchmarks["optimal_network_size"]) * 100)
            },
            "diversity": {
                "value": diversity.total_diversity_score,
                "benchmark": self.benchmarks["optimal_diversity_score"],
                "percentile": (diversity.total_diversity_score / self.benchmarks["optimal_diversity_score"]) * 100
            },
            "activity_rate": {
                "value": network.active_relationships / max(network.total_connections, 1),
                "benchmark": self.benchmarks["optimal_activity_rate"],
                "percentile": (network.active_relationships / max(network.total_connections, 1) / self.benchmarks["optimal_activity_rate"]) * 100
            }
        }
    
    def _get_health_grade(self, health_score: float) -> str:
        """Convert health score to letter grade"""
        if health_score >= 90:
            return "A"
        elif health_score >= 80:
            return "B"
        elif health_score >= 70:
            return "C"
        elif health_score >= 60:
            return "D"
        else:
            return "F"
    
    def _generate_empty_network_diagnosis(self) -> Dict[str, Any]:
        """Generate diagnosis for empty network"""
        return {
            "overall_health_score": 0,
            "health_grade": "F",
            "network_size": 0,
            "last_analyzed": datetime.utcnow().isoformat() + "Z",
            "metrics": {
                "network_metrics": {
                    "total_connections": 0,
                    "active_relationships": 0,
                    "dormant_relationships": 0,
                    "network_density": 0,
                    "clustering_coefficient": 0,
                    "network_efficiency": 0,
                    "centrality_score": 0,
                    "influence_score": 0,
                    "structural_holes": 0,
                    "bridge_connections": 0
                },
                "diversity_metrics": {
                    "overall_diversity": 0,
                    "industry_diversity": 0,
                    "role_diversity": 0,
                    "company_diversity": 0,
                    "archetype_diversity": 0,
                    "diversity_gaps": ["No network connections"]
                },
                "activity_metrics": {
                    "interactions_last_30_days": 0,
                    "interactions_last_90_days": 0,
                    "average_interaction_frequency": 0,
                    "communication_momentum": 0,
                    "most_active_relationships": [],
                    "least_active_relationships": []
                }
            },
            "analysis": {
                "strengths": [],
                "weaknesses": ["No network connections"],
                "critical_gaps": ["Build initial network"],
                "growth_opportunities": ["Start building professional connections"]
            },
            "recommendations": [
                {
                    "category": "Network Foundation",
                    "priority": "high",
                    "title": "Build your first connections",
                    "description": "Start building your professional network from scratch.",
                    "actions": [
                        "Add current colleagues and peers",
                        "Connect with industry professionals",
                        "Join professional associations",
                        "Attend networking events"
                    ],
                    "expected_impact": "Establish network foundation",
                    "time_investment": "2-3 hours per week",
                    "target_metrics": ["total_connections"]
                }
            ],
            "trends": {
                "health_score_trend": "new",
                "network_size_change": "starting",
                "activity_trend": "new",
                "diversity_trend": "new"
            },
            "benchmark_comparison": {
                "network_size": {"value": 0, "benchmark": 150, "percentile": 0},
                "diversity": {"value": 0, "benchmark": 0.8, "percentile": 0},
                "activity_rate": {"value": 0, "benchmark": 0.3, "percentile": 0}
            }
        }