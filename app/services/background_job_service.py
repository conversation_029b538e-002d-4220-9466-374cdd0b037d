"""
Background job service for handling async data processing tasks
Simple implementation using threading (for production, consider Celery/RQ)
"""

import threading
import time
import uuid
from typing import Callable, Dict, Any
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

from app.core.database import SessionLocal
from app.services.data_export_service import DataExportService
from app.services.data_import_service import DataImportService


class BackgroundJobService:
    """Service for managing background jobs"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.running_jobs: Dict[str, Dict[str, Any]] = {}
    
    def submit_export_job(self, job_id: uuid.UUID) -> None:
        """Submit an export job for background processing"""
        job_key = str(job_id)
        
        if job_key in self.running_jobs:
            return  # Job already running
        
        self.running_jobs[job_key] = {
            "type": "export",
            "status": "running",
            "started_at": datetime.utcnow()
        }
        
        # Submit to thread pool
        future = self.executor.submit(self._process_export_job, job_id)
        
        # Add callback to clean up when done
        future.add_done_callback(lambda f: self._cleanup_job(job_key))
    
    def submit_import_job(self, job_id: uuid.UUID, file_content: str) -> None:
        """Submit an import job for background processing"""
        job_key = str(job_id)
        
        if job_key in self.running_jobs:
            return  # Job already running
        
        self.running_jobs[job_key] = {
            "type": "import",
            "status": "running",
            "started_at": datetime.utcnow()
        }
        
        # Submit to thread pool
        future = self.executor.submit(self._process_import_job, job_id, file_content)
        
        # Add callback to clean up when done
        future.add_done_callback(lambda f: self._cleanup_job(job_key))
    
    def _process_export_job(self, job_id: uuid.UUID) -> bool:
        """Process export job in background thread"""
        try:
            # Create new database session for this thread
            db = SessionLocal()
            try:
                service = DataExportService(db)
                result = service.process_export_job(job_id)
                return result
            finally:
                db.close()
        except Exception as e:
            print(f"Export job {job_id} failed: {str(e)}")
            return False
    
    def _process_import_job(self, job_id: uuid.UUID, file_content: str) -> bool:
        """Process import job in background thread"""
        try:
            # Create new database session for this thread
            db = SessionLocal()
            try:
                service = DataImportService(db)
                result = service.process_import_job(job_id, file_content)
                return result
            finally:
                db.close()
        except Exception as e:
            print(f"Import job {job_id} failed: {str(e)}")
            return False
    
    def _cleanup_job(self, job_key: str) -> None:
        """Clean up job from running jobs dict"""
        if job_key in self.running_jobs:
            del self.running_jobs[job_key]
    
    def get_job_status(self, job_id: uuid.UUID) -> Dict[str, Any]:
        """Get background job status"""
        job_key = str(job_id)
        if job_key in self.running_jobs:
            return self.running_jobs[job_key]
        return {"status": "not_found"}
    
    def is_job_running(self, job_id: uuid.UUID) -> bool:
        """Check if job is currently running"""
        job_key = str(job_id)
        return job_key in self.running_jobs
    
    def shutdown(self):
        """Shutdown the executor"""
        self.executor.shutdown(wait=True)


# Global instance
background_job_service = BackgroundJobService()