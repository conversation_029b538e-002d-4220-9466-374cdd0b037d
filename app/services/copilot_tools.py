"""
Copilot Tools - Function tools that can be called by the LLM
These tools provide the actual functionality for the Nexus Copilot
"""

import json
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
from uuid import UUID
import uuid

from sqlalchemy.orm import Session
from app.models.user import User
from app.models.person import Person
from app.models.relationship import Knows
from app.models.task import Task
from app.models.goal import Goal
from app.models.organization import Organization
from app.services.person_service import PersonService
from app.services.task_service import TaskService
from app.services.goal_service import GoalService
from app.services.relationship_service import RelationshipService
from app.services.network_health_service import NetworkHealthService
from app.services.ai_engine_service import AIEngineService

logger = logging.getLogger(__name__)


class CopilotTools:
    """
    Collection of tools that the LLM can call to perform actions
    """
    
    def __init__(self, db: Session, user: User):
        self.db = db
        self.user = user
        self.person_service = PersonService(db)
        self.task_service = TaskService(db)
        self.goal_service = GoalService(db)
        self.relationship_service = RelationshipService(db)
        self.network_health_service = NetworkHealthService(db)
        self.ai_engine_service = AIEngineService(db)

    def search_network(self, query: str, limit: int = 10, filters: Dict[str, Any] = None, **kwargs) -> Dict[str, Any]:
        """
        Search the user's network for people, companies, or other entities
        
        Args:
            query: Search query string
            limit: Maximum number of results to return
            filters: Additional filters (company, tags, etc.)
        """
        try:
            # Use the existing search functionality
            from app.api.v1.endpoints.search_simple import search_persons
            from fastapi import Request
            
            # Create a mock request object for the search
            class MockRequest:
                def __init__(self):
                    self.query_params = {}
            
            # Perform search
            results = []
            persons = self.db.query(Person).filter(
                Person.user_id == self.user.user_id,
                Person.is_user == False
            ).all()
            
            query_lower = query.lower()
            for person in persons:
                # Simple matching logic
                if (query_lower in person.first_name.lower() or 
                    query_lower in person.last_name.lower() or
                    (person.professional_info and 
                     query_lower in str(person.professional_info).lower())):
                    
                    professional_info = person.professional_info or {}
                    results.append({
                        "person_id": str(person.person_id),
                        "name": f"{person.first_name} {person.last_name}",
                        "company": professional_info.get("company", ""),
                        "title": professional_info.get("title", ""),
                        "email": person.contact_info.get("email", "") if person.contact_info else ""
                    })
                    
                    if len(results) >= limit:
                        break
            
            return {
                "results": results,
                "total_found": len(results),
                "query": query
            }
            
        except Exception as e:
            logger.error(f"Error searching network: {e}")
            return {"error": str(e), "results": []}

    def get_network_health(self, **kwargs) -> Dict[str, Any]:
        """
        Get the user's network health analysis
        """
        try:
            diagnosis = self.network_health_service.diagnose_network_health(self.user.user_id)
            return diagnosis
        except Exception as e:
            logger.error(f"Error getting network health: {e}")
            return {"error": str(e)}

    def create_task(self, title: str, description: str = "", due_date: str = None, 
                   priority: str = "medium", related_person_id: str = None, **kwargs) -> Dict[str, Any]:
        """
        Create a new task for the user
        
        Args:
            title: Task title
            description: Task description
            due_date: Due date in YYYY-MM-DD format
            priority: Task priority (low, medium, high)
            related_person_id: ID of related person if applicable
        """
        try:
            # Convert priority to integer
            priority_map = {"low": 1, "medium": 3, "high": 5}
            priority_int = priority_map.get(priority.lower(), 3) if isinstance(priority, str) else priority
            
            task_data = {
                "title": title,
                "description": description,
                "priority": priority_int,
                "is_completed": False,
                "user_id": self.user.user_id,
                "task_metadata": {
                    "ai_generated": True,
                    "source": "copilot_suggestion"
                }
            }
            
            if due_date:
                try:
                    # Convert to datetime for SQLite compatibility
                    parsed_date = datetime.strptime(due_date, "%Y-%m-%d")
                    task_data["due_date"] = parsed_date
                except ValueError:
                    pass  # Invalid date format, skip
            
            # Handle related person via task metadata
            if related_person_id:
                try:
                    person_uuid = UUID(related_person_id)
                    # Verify person exists and belongs to user
                    person = self.db.query(Person).filter(
                        Person.person_id == person_uuid,
                        Person.user_id == self.user.user_id
                    ).first()
                    if person:
                        task_data["task_metadata"]["related_people"] = [str(person_uuid)]
                except ValueError:
                    pass  # Invalid UUID format
            
            # Create task directly to avoid schema validation issues  
            from app.models.task import Task
            import uuid
            task_data["task_id"] = uuid.uuid4()
            task = Task(**task_data)
            self.db.add(task)
            self.db.commit()
            self.db.refresh(task)
            
            return {
                "task_id": str(task.task_id),
                "title": task.title,
                "description": task.description,
                "due_date": task.due_date.isoformat() if task.due_date else None,
                "priority": task.priority,
                "status": "completed" if task.is_completed else "pending",
                "created": True
            }
            
        except Exception as e:
            logger.error(f"Error creating task: {e}")
            return {"error": str(e), "created": False}

    def create_goal(self, title: str, description: str = "", target_date: str = None,
                   category: str = "networking", metrics: Dict[str, Any] = None, **kwargs) -> Dict[str, Any]:
        """
        Create a new goal for the user
        
        Args:
            title: Goal title
            description: Goal description
            target_date: Target date in YYYY-MM-DD format
            category: Goal category
            metrics: Goal metrics (target_value, current_value, unit)
        """
        try:
            goal_metadata = {
                "category": category
            }
            
            if metrics:
                goal_metadata["metrics"] = metrics
            
            goal_data = {
                "title": title,
                "description": description,
                "status": "active",
                "user_id": self.user.user_id,
                "goal_metadata": goal_metadata
            }
            
            if target_date:
                try:
                    # Convert to datetime for SQLite compatibility
                    parsed_date = datetime.strptime(target_date, "%Y-%m-%d")
                    goal_data["target_date"] = parsed_date
                except ValueError:
                    pass
            
            # Create goal directly to avoid schema validation issues
            from app.models.goal import Goal
            import uuid
            goal_data["goal_id"] = uuid.uuid4()
            goal = Goal(**goal_data)
            self.db.add(goal)
            self.db.commit()
            self.db.refresh(goal)
            
            return {
                "goal_id": str(goal.goal_id),
                "title": goal.title,
                "description": goal.description,
                "target_date": goal.target_date.isoformat() if goal.target_date else None,
                "category": goal.goal_metadata.get("category", category) if goal.goal_metadata else category,
                "status": goal.status,
                "created": True
            }
            
        except Exception as e:
            logger.error(f"Error creating goal: {e}")
            return {"error": str(e), "created": False}

    def get_recent_tasks(self, limit: int = 10, **kwargs) -> Dict[str, Any]:
        """
        Get the user's recent tasks
        """
        try:
            tasks = self.db.query(Task).filter(
                Task.user_id == self.user.user_id
            ).order_by(Task.created_at.desc()).limit(limit).all()
            
            task_list = []
            for task in tasks:
                task_list.append({
                    "task_id": str(task.task_id),
                    "title": task.title,
                    "description": task.description,
                    "status": "completed" if task.is_completed else "pending",
                    "priority": task.priority,
                    "due_date": task.due_date.isoformat() if task.due_date else None,
                    "created_at": task.created_at.isoformat()
                })
            
            return {"tasks": task_list, "total": len(task_list)}
            
        except Exception as e:
            logger.error(f"Error getting recent tasks: {e}")
            return {"error": str(e), "tasks": []}

    def get_goals(self, status: str = None, limit: int = 10, **kwargs) -> Dict[str, Any]:
        """
        Get the user's goals
        
        Args:
            status: Filter by status (active, completed, paused)
            limit: Maximum number of goals to return
        """
        try:
            query = self.db.query(Goal).filter(Goal.user_id == self.user.user_id)
            
            if status:
                query = query.filter(Goal.status == status)
            
            goals = query.order_by(Goal.created_at.desc()).limit(limit).all()
            
            goal_list = []
            for goal in goals:
                goal_list.append({
                    "goal_id": str(goal.goal_id),
                    "title": goal.title,
                    "description": goal.description,
                    "category": goal.category,
                    "status": goal.status,
                    "target_date": goal.target_date.isoformat() if goal.target_date else None,
                    "progress": goal.progress,
                    "metrics": goal.metrics
                })
            
            return {"goals": goal_list, "total": len(goal_list)}
            
        except Exception as e:
            logger.error(f"Error getting goals: {e}")
            return {"error": str(e), "goals": []}

    def find_mutual_connections(self, person_id: str, **kwargs) -> Dict[str, Any]:
        """
        Find mutual connections with a specific person
        
        Args:
            person_id: ID of the person to find mutual connections with
        """
        try:
            person_uuid = UUID(person_id)
            
            # Get the person's connections (this would require more complex logic)
            # For now, return a simplified response
            person = self.db.query(Person).filter(
                Person.person_id == person_uuid,
                Person.user_id == self.user.user_id
            ).first()
            
            if not person:
                return {"error": "Person not found", "mutual_connections": []}
            
            # This is a simplified implementation
            # In a real system, you'd need to implement proper mutual connection logic
            return {
                "person_name": f"{person.first_name} {person.last_name}",
                "mutual_connections": [],
                "message": "Mutual connection analysis requires additional relationship data"
            }
            
        except Exception as e:
            logger.error(f"Error finding mutual connections: {e}")
            return {"error": str(e), "mutual_connections": []}

    def get_ai_suggestions(self, suggestion_type: str = None, limit: int = 5, **kwargs) -> Dict[str, Any]:
        """
        Get AI-powered suggestions for the user

        Args:
            suggestion_type: Type of suggestions to get
            limit: Maximum number of suggestions
        """
        try:
            # Use a simple fallback since we can't await in sync context
            # In a real implementation, this would be refactored to be async
            suggestions = [
                {
                    "suggestion_id": "fallback-001",
                    "type": "networking",
                    "title": "Expand your network",
                    "description": "Consider reaching out to new connections in your industry",
                    "confidence": 0.7,
                    "priority": "medium"
                }
            ]

            if suggestion_type:
                suggestions = [s for s in suggestions if s.get("type") == suggestion_type]

            return {
                "suggestions": suggestions[:limit],
                "total": len(suggestions)
            }

        except Exception as e:
            logger.error(f"Error getting AI suggestions: {e}")
            return {"error": str(e), "suggestions": []}

    def analyze_relationship_strength(self, person_id: str, **kwargs) -> Dict[str, Any]:
        """
        Analyze the strength of relationship with a specific person
        
        Args:
            person_id: ID of the person to analyze
        """
        try:
            person_uuid = UUID(person_id)
            
            # Get relationship data
            relationship = self.db.query(Knows).filter(
                Knows.to_person_id == person_uuid,
                Knows.user_id == self.user.user_id
            ).first()
            
            if not relationship:
                return {"error": "Relationship not found", "strength": None}
            
            person = self.db.query(Person).filter(Person.person_id == person_uuid).first()
            
            return {
                "person_name": f"{person.first_name} {person.last_name}" if person else "Unknown",
                "relationship_strength": relationship.relationship_depth,
                "archetype": relationship.archetype,
                "context": relationship.context
            }
            
        except Exception as e:
            logger.error(f"Error analyzing relationship strength: {e}")
            return {"error": str(e), "strength": None}


# Tool schemas for LLM function calling
TOOL_SCHEMAS = {
    "search_network": {
        "description": "Search the user's professional network for people, companies, or other entities",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query string (person name, company, role, etc.)"
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of results to return",
                    "default": 10
                },
                "filters": {
                    "type": "object",
                    "description": "Additional filters for the search",
                    "properties": {
                        "company": {"type": "string"},
                        "tags": {"type": "string"}
                    }
                }
            },
            "required": ["query"]
        }
    },

    "get_network_health": {
        "description": "Get comprehensive analysis of the user's network health and metrics",
        "parameters": {
            "type": "object",
            "properties": {}
        }
    },

    "create_task": {
        "description": "Create a new task for the user",
        "parameters": {
            "type": "object",
            "properties": {
                "title": {
                    "type": "string",
                    "description": "Task title"
                },
                "description": {
                    "type": "string",
                    "description": "Detailed task description"
                },
                "due_date": {
                    "type": "string",
                    "description": "Due date in YYYY-MM-DD format"
                },
                "priority": {
                    "type": "string",
                    "enum": ["low", "medium", "high"],
                    "description": "Task priority level"
                },
                "related_person_id": {
                    "type": "string",
                    "description": "ID of related person if task is person-specific"
                }
            },
            "required": ["title"]
        }
    },

    "create_goal": {
        "description": "Create a new networking or professional goal",
        "parameters": {
            "type": "object",
            "properties": {
                "title": {
                    "type": "string",
                    "description": "Goal title"
                },
                "description": {
                    "type": "string",
                    "description": "Detailed goal description"
                },
                "target_date": {
                    "type": "string",
                    "description": "Target completion date in YYYY-MM-DD format"
                },
                "category": {
                    "type": "string",
                    "enum": ["networking", "career", "business", "personal"],
                    "description": "Goal category"
                },
                "metrics": {
                    "type": "object",
                    "description": "Goal metrics and targets",
                    "properties": {
                        "target_value": {"type": "number"},
                        "current_value": {"type": "number"},
                        "unit": {"type": "string"}
                    }
                }
            },
            "required": ["title"]
        }
    },

    "get_recent_tasks": {
        "description": "Get the user's recent tasks",
        "parameters": {
            "type": "object",
            "properties": {
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of tasks to return",
                    "default": 10
                }
            }
        }
    },

    "get_goals": {
        "description": "Get the user's goals with optional filtering",
        "parameters": {
            "type": "object",
            "properties": {
                "status": {
                    "type": "string",
                    "enum": ["active", "completed", "paused"],
                    "description": "Filter goals by status"
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of goals to return",
                    "default": 10
                }
            }
        }
    },

    "find_mutual_connections": {
        "description": "Find mutual connections with a specific person",
        "parameters": {
            "type": "object",
            "properties": {
                "person_id": {
                    "type": "string",
                    "description": "ID of the person to find mutual connections with"
                }
            },
            "required": ["person_id"]
        }
    },

    "get_ai_suggestions": {
        "description": "Get AI-powered suggestions for networking and relationship building",
        "parameters": {
            "type": "object",
            "properties": {
                "suggestion_type": {
                    "type": "string",
                    "enum": ["person", "task", "goal", "reconnect"],
                    "description": "Type of suggestions to get"
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of suggestions",
                    "default": 5
                }
            }
        }
    },

    "analyze_relationship_strength": {
        "description": "Analyze the strength and quality of relationship with a specific person",
        "parameters": {
            "type": "object",
            "properties": {
                "person_id": {
                    "type": "string",
                    "description": "ID of the person to analyze relationship with"
                }
            },
            "required": ["person_id"]
        }
    }
}
