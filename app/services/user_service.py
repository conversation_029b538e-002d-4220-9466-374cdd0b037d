"""
User service - handles user-related business logic
"""

from typing import Any, Dict, Optional, Union
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.orm import Session
from sqlalchemy.sql import func

from app.core.security import get_password_hash, verify_password
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.services.base_service import BaseService


class UserService(BaseService[User, UserCreate, UserUpdate]):
    """User service with authentication and user management logic"""

    def __init__(self, db: Session):
        super().__init__(User, db)

    def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        stmt = select(self.model).where(self.model.email == email)
        result = self.db.execute(stmt)
        return result.scalar_one_or_none()

    def create(self, obj_in: Union[UserCreate, Dict[str, Any]]) -> User:
        """Create a new user, handling both standard and Supabase registration."""
        if isinstance(obj_in, dict):
            # Coming from Supabase registration
            create_data = obj_in
            # Ensure placeholder for non-nullable password
            if "hashed_password" not in create_data:
                create_data["hashed_password"] = "from_supabase"
            # Map "id" from Supabase to "user_id"
            if "id" in create_data:
                create_data["user_id"] = create_data.pop("id")
        else:
            # Standard creation from UserCreate schema
            create_data = obj_in.dict()
            create_data["hashed_password"] = get_password_hash(create_data.pop("password"))

        db_obj = self.model(**create_data)
        self.db.add(db_obj)
        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj

    def authenticate(self, email: str, password: str) -> Optional[User]:
        """Authenticate user with email and password"""
        user = self.get_by_email(email)
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user

    def update(self, id: UUID, obj_in: Union[UserUpdate, Dict[str, Any]]) -> Optional[User]:
        """Update a user, explicitly using user_id."""
        db_obj = self.get(id)
        if not db_obj:
            return None
        
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
            
        for field, value in update_data.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)
                
        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj

    def update_last_login(self, user_id: UUID) -> None:
        """Update user's last login timestamp"""
        self.update(user_id, {"last_login": func.now()})

    def activate_user(self, user_id: UUID) -> Optional[User]:
        """Activate a user account"""
        return self.update(user_id, {"is_active": True})

    def deactivate_user(self, user_id: UUID) -> Optional[User]:
        """Deactivate a user account"""
        return self.update(user_id, {"is_active": False})

    def verify_user(self, user_id: UUID) -> Optional[User]:
        """Verify a user account"""
        return self.update(user_id, {"is_verified": True})

    def update_settings(self, user_id: UUID, settings: dict) -> Optional[User]:
        """Update user settings"""
        return self.update(user_id, {"settings": settings})

    def get(self, id: UUID) -> Optional[User]:
        """Override base get method to use user_id as primary key"""
        stmt = select(self.model).where(self.model.user_id == id)
        result = self.db.execute(stmt)
        return result.scalar_one_or_none()
