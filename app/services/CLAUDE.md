# Services Directory - Business Logic Layer

This directory contains the comprehensive business logic layer for the Nexus relationship management platform, implementing sophisticated AI-powered networking intelligence.

## Core Service Architecture

### Service Layer Pattern
All services follow a consistent pattern:
- **Database Session Management**: Efficient SQLAlchemy session handling
- **User Data Isolation**: Automatic filtering by user_id for security
- **Error Handling**: Comprehensive exception management with proper HTTP responses
- **AI Integration**: Seamless OpenAI integration with intelligent fallbacks
- **Stateless Design**: Database-backed state for multi-instance deployment
- **Performance Optimization**: Efficient query patterns and caching strategies

## AI-Powered Services

### `ai_engine_service.py` - Core AI Intelligence Engine
**Comprehensive AI-powered relationship analysis and networking intelligence**

**Key Features**:
- **Proactive Suggestions**: AI-generated networking recommendations
- **Relationship Analysis**: Six-dimensional relationship insights
- **Network Expansion**: Strategic connection identification
- **Goal-Person Matching**: AI-powered matching for achievement
- **Fallback Systems**: Rule-based algorithms when AI unavailable

**AI Capabilities**:
```python
async def get_active_suggestions(self, user_id: UUID) -> List[Dict[str, Any]]:
    # 1. Relationship maintenance suggestions
    # 2. Goal-oriented networking suggestions  
    # 3. Network expansion recommendations
    # 4. AI-powered prioritization and ranking
```

### `goal_service.py` - AI-Enhanced Goal Management
**Sophisticated goal tracking with AI-powered achievement strategies**

**Advanced Features**:
- **AI Strategy Analysis**: OpenAI-powered goal achievement insights
- **Success Prediction**: Multi-factor success probability modeling
- **Goal-Person Matching**: Intelligent connection recommendations
- **Referral Path Integration**: Strategic networking for goal achievement
- **Milestone Tracking**: Automated progress measurement and velocity analysis

**AI Analysis Pipeline**:
```python
def get_goal_ai_analysis(self, goal_id: UUID, user_id: UUID) -> Dict[str, Any]:
    # OpenAI analysis with comprehensive fallback
    # Context gathering and prompt engineering
    # Structured JSON response processing
    # Intelligent caching for performance
```

## Stateless Conversation Services

### `intelligent_copilot.py` - Stateless AI Copilot Service
**Multi-instance ready AI copilot with database-backed conversation history**

**Stateless Features**:
- **Zero Memory State**: No in-memory conversation storage
- **Database Integration**: Complete conversation persistence in database
- **User Isolation**: Conversations completely separated by user_id
- **Tool Call Persistence**: Complex AI function calls stored and retrievable
- **Context Preservation**: Multi-turn conversations work across service instances

**Stateless Architecture**:
```python
def __init__(self, db: Session, user: User):
    # LLM service with database session injection
    self.llm = LLMService(db_session=db)
    
async def process_message(self, message: str, conversation_id: str, ...):
    # All conversation operations require user_id for isolation
    response = await self.llm.process_conversation(
        user_message=message,
        conversation_id=conversation_id,
        user_id=str(self.user.user_id),  # Required for database operations
        context=full_context,
        system_prompt=self._get_system_prompt()
    )
```

### `copilot_tools.py` - Function Tools with User Isolation
**AI function calling tools with complete user data isolation**

**Tools Features**:
- **User-Scoped Operations**: All tools automatically filter by user_id
- **Database Persistence**: Tool results stored for conversation history
- **Multi-Instance Safe**: No shared state between service instances
- **Parameter Flexibility**: `**kwargs` support for evolving LLM interfaces

## Network Analysis Services

### `network_health_service.py` - Comprehensive Network Diagnosis
**850+ lines of sophisticated network health analysis**

**Advanced Metrics**:
- **Shannon Entropy**: Network diversity measurement using information theory
- **Centrality Analysis**: Betweenness, closeness, eigenvector centrality
- **Activity Assessment**: Relationship engagement and dormancy detection
- **Structural Analysis**: Network gaps and connectivity optimization
- **Actionable Insights**: AI-generated improvement recommendations

**Health Calculation Example**:
```python
def _calculate_shannon_diversity(self, distribution: Dict[str, int]) -> float:
    total = sum(distribution.values())
    diversity = 0.0
    for count in distribution.values():
        if count > 0:
            proportion = count / total
            diversity -= proportion * math.log2(proportion)
    return diversity
```

### `pathfinding_service.py` - Strategic Referral Discovery
**700+ lines implementing sophisticated graph algorithms**

**Algorithm Implementations**:
- **Dijkstra's Algorithm**: Shortest path discovery for referrals
- **A* Search**: Goal-oriented pathfinding with intelligent heuristics
- **Breadth-First Search**: Comprehensive network exploration
- **Multiple Optimization Strategies**: Shortest, strongest, balanced paths
- **Success Probability Calculation**: AI-enhanced path success prediction

**Pathfinding Example**:
```python
def dijkstra_shortest_path(self, graph: nx.Graph, source: str, target: str):
    # Inverse weight mapping (higher trust = lower cost)
    # NetworkX Dijkstra implementation
    # Success probability calculation
    # Path optimization and caching
```

### `graph_service.py` - Network Visualization Engine
**Real-time graph data generation and network analysis**

**Graph Features**:
- **Dynamic Construction**: Real-time network graph from database
- **Layout Algorithms**: Force-directed, hierarchical positioning
- **Community Detection**: Leiden algorithm for network clustering
- **Filtering System**: Tag-based, company-based, relationship-type filtering
- **Performance Optimization**: Handles 1000+ node networks efficiently

## Advanced Search Services

### `search_service.py` - PostgreSQL Fuzzy Search Engine
**High-performance search with pg_trgm extension**

**Search Capabilities**:
- **Fuzzy Matching**: PostgreSQL trigram similarity with configurable thresholds
- **Multi-Field Search**: Across persons, organizations, relationships
- **Relevance Scoring**: Intelligent ranking with relationship strength weighting
- **Performance**: Sub-50ms queries with GIN indexes
- **Advanced Pagination**: Efficient large dataset handling

**Search Implementation**:
```python
def fuzzy_search_persons(self, query: str, similarity_threshold: float = 0.3):
    return self.db.query(Person).filter(
        func.similarity(
            func.concat(Person.first_name, ' ', Person.last_name), 
            query
        ) > similarity_threshold
    ).order_by(similarity_score.desc())
```

## Entity Management Services

### `relationship_service.py` - Six-Dimensional Relationship Management
**662-line comprehensive relationship management with archetype system**

**Relationship Features**:
- **Archetype Templates**: 8 predefined relationship types with default dimensions
- **Six-Dimensional Scoring**: Comprehensive relationship depth measurement
- **CRUD Operations**: Complete relationship lifecycle management
- **Validation System**: Comprehensive data integrity and constraint checking
- **History Tracking**: Complete audit trail for relationship changes

**Archetype System**:
```python
RELATIONSHIP_ARCHETYPES = {
    "mentor": {
        "emotional_intimacy": 3,
        "professional_collaboration": 4,
        "trust_level": 4,
        # ... complete dimension mapping
    }
}
```

### `organization_service.py` - Organization Management System
**Complete organizational CRUD with work relationship tracking**

**Organization Features**:
- **Complete CRUD**: Full organizational entity lifecycle management
- **Work Relationships**: Person-organization association management
- **Advanced Search**: Multi-criteria organization discovery
- **Statistical Analysis**: Organization network analytics and insights
- **Employee Tracking**: Comprehensive work history management

### `person_service.py` - Contact Management
**Comprehensive contact management with relationship integration**

**Contact Features**:
- **CRUD Operations**: Complete person lifecycle management
- **Relationship Integration**: Seamless connection with relationship system
- **Work History**: Employment tracking and organizational associations
- **Custom Attributes**: Flexible person detail storage with JSONB fields

## Data Portability Services

### `data_export_service.py` - Multi-Format Export Engine
**Comprehensive data export with background processing**

**Export Features**:
- **Multi-Format Support**: JSON, CSV, vCard generation
- **Custom Field Mapping**: User-defined field mappings for CSV
- **Background Processing**: Async job processing with progress tracking
- **Comprehensive Coverage**: All entities with complete data integrity

### `data_import_service.py` - Intelligent Data Import
**Advanced data import with validation and duplicate handling**

**Import Features**:
- **Format Detection**: Automatic format validation and processing
- **Data Preview**: File preview with intelligent field mapping suggestions
- **Duplicate Handling**: Skip, update, merge strategies for existing data
- **Validation Modes**: Strict/lenient validation with comprehensive error reporting

### `background_job_service.py` - Async Processing System
**Production-ready background job processing**

**Job Features**:
- **ThreadPoolExecutor**: Scalable async processing
- **Progress Tracking**: Real-time job status and progress monitoring
- **Error Recovery**: Comprehensive error handling and retry logic
- **Resource Management**: Automatic cleanup and optimization

## Supporting Services

### `base_service.py` - Foundation Service Class
**Base class providing common CRUD operations and patterns**

**Base Features**:
- **Standardized CRUD**: Consistent create, read, update, delete operations
- **User Isolation**: Automatic user_id filtering for security
- **Error Handling**: Standardized exception management
- **Session Management**: Proper database transaction handling

### `user_service.py` - User Account Management
**User authentication and profile management**

### `task_service.py` - Task Management
**Action item tracking with goal integration**

## Performance Characteristics

### Database Performance
- **Query Optimization**: Sub-50ms response times for most operations
- **Indexing Strategy**: Comprehensive indexing for optimal performance
- **Connection Pooling**: Efficient database connection management
- **User Isolation**: Optimized queries with proper user_id filtering

### AI Performance
- **Response Caching**: Intelligent caching for repeated AI operations
- **Fallback Systems**: Sub-500ms rule-based fallbacks
- **Rate Limiting**: Proper API usage management
- **Cost Optimization**: Efficient prompt engineering for cost control

### Memory and CPU
- **Efficient Algorithms**: Optimized NetworkX and NumPy operations
- **Streaming Processing**: Memory-efficient large dataset handling
- **Async Operations**: Non-blocking I/O for improved throughput
- **Resource Cleanup**: Automatic resource management and cleanup

## Integration Patterns

### AI Integration Pattern
```python
def get_ai_analysis(self, context: Dict[str, Any]) -> Dict[str, Any]:
    if self.openai_client:
        try:
            return self.openai_client.analyze(context)
        except Exception:
            return self._get_fallback_analysis(context)
    return self._get_fallback_analysis(context)
```

### Error Handling Pattern
```python
try:
    result = self.complex_operation()
    return {"success": True, "data": result}
except ValueError as e:
    logger.warning(f"Validation error: {e}")
    raise HTTPException(status_code=400, detail=str(e))
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    raise HTTPException(status_code=500, detail="Internal server error")
```

### User Data Isolation Pattern
```python
def get_by_user(self, user_id: UUID) -> List[Model]:
    return self.db.query(self.model).filter(
        self.model.user_id == user_id
    ).all()
```

For complete API documentation and model definitions, refer to the `../api/` and `../models/` directories.