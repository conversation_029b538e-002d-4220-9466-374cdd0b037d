"""
Graph Service - Network visualization data generation
Handles graph data creation, filtering, and layout algorithms
"""

import math
import random
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Tuple
from uuid import UUID

import networkx as nx
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.models.person import Person
from app.models.relationship import Knows
from app.models.user import User


class GraphService:
    """
    Service for generating network graph visualization data
    """

    def __init__(self, db: Session):
        self.db = db

    async def generate_full_network_graph(
        self, user_id: UUID, layout_type: str = "force_directed"
    ) -> Dict[str, Any]:
        """
        Generate complete network graph for user
        """
        # Get all persons and relationships for the user
        persons = self.db.query(Person).filter(
            Person.user_id == user_id,
            Person.is_user == False
        ).all()

        relationships = self.db.query(Knows).filter(
            Knows.user_id == user_id
        ).all()

        # Get user person for center node
        user_person = self.db.query(Person).filter(
            Person.user_id == user_id,
            Person.is_user == True
        ).first()

        # Build NetworkX graph for layout calculations
        nx_graph = self._build_networkx_graph(user_person, persons, relationships)

        # Generate node positions using layout algorithm
        positions = self._calculate_node_positions(nx_graph, layout_type)

        # Generate nodes data
        nodes = self._generate_nodes_data(user_person, persons, relationships, positions)

        # Generate edges data
        edges = self._generate_edges_data(relationships, user_person)

        # Calculate graph metrics
        metrics = self._calculate_graph_metrics(nx_graph, relationships)

        return {
            "nodes": nodes,
            "edges": edges,
            "layout": {
                "type": layout_type,
                "positions": positions
            },
            "metadata": {
                "total_nodes": len(nodes),
                "total_edges": len(edges),
                "density": metrics["density"],
                "clustering_coefficient": metrics["clustering"],
                "average_path_length": metrics["avg_path_length"],
                "generated_at": datetime.utcnow().isoformat() + "Z"
            }
        }

    async def generate_filtered_network_graph(
        self, 
        user_id: UUID,
        tags: Optional[List[str]] = None,
        archetype: Optional[str] = None,
        organization: Optional[str] = None,
        min_relationship_score: Optional[int] = None,
        max_nodes: Optional[int] = None,
        layout_type: str = "force_directed"
    ) -> Dict[str, Any]:
        """
        Generate filtered network graph based on criteria
        """
        # Build base query for persons
        persons_query = self.db.query(Person).filter(
            Person.user_id == user_id,
            Person.is_user == False
        )

        # Apply filters
        if organization:
            persons_query = persons_query.filter(
                func.lower(func.coalesce(
                    func.jsonb_extract_path_text(Person.professional_info, 'company'), ''
                )).like(f'%{organization.lower()}%')
            )

        if tags:
            for tag in tags:
                persons_query = persons_query.filter(
                    func.jsonb_extract_path_text(Person.personal_details, 'tags').like(f'%"{tag}"%')
                )

        persons = persons_query.all()
        person_ids = {person.person_id for person in persons}

        # Filter relationships
        relationships_query = self.db.query(Knows).filter(
            Knows.user_id == user_id,
            Knows.to_person_id.in_(person_ids)
        )

        if archetype:
            relationships_query = relationships_query.filter(
                func.lower(Knows.archetype).like(f'%{archetype.lower()}%')
            )

        if min_relationship_score:
            relationships_query = relationships_query.filter(
                func.cast(func.jsonb_extract_path_text(Knows.relationship_depth, 'overall_score'), 
                         func.INTEGER) >= min_relationship_score
            )

        relationships = relationships_query.all()

        # Apply max_nodes limit if specified
        if max_nodes and len(persons) > max_nodes:
            # Sort by relationship score and take top N
            person_scores = {}
            for rel in relationships:
                person_scores[rel.to_person_id] = rel.overall_score

            persons = sorted(persons, 
                           key=lambda p: person_scores.get(p.person_id, 0), 
                           reverse=True)[:max_nodes]
            
            # Filter relationships to only include remaining persons
            person_ids = {person.person_id for person in persons}
            relationships = [rel for rel in relationships if rel.to_person_id in person_ids]

        # Get user person
        user_person = self.db.query(Person).filter(
            Person.user_id == user_id,
            Person.is_user == True
        ).first()

        # Generate graph data
        nx_graph = self._build_networkx_graph(user_person, persons, relationships)
        positions = self._calculate_node_positions(nx_graph, layout_type)
        nodes = self._generate_nodes_data(user_person, persons, relationships, positions)
        edges = self._generate_edges_data(relationships, user_person)
        metrics = self._calculate_graph_metrics(nx_graph, relationships)

        return {
            "nodes": nodes,
            "edges": edges,
            "layout": {
                "type": layout_type,
                "positions": positions
            },
            "filters_applied": {
                "tags": tags,
                "archetype": archetype,
                "organization": organization,
                "min_relationship_score": min_relationship_score,
                "max_nodes": max_nodes
            },
            "metadata": {
                "total_nodes": len(nodes),
                "total_edges": len(edges),
                "density": metrics["density"],
                "clustering_coefficient": metrics["clustering"],
                "average_path_length": metrics["avg_path_length"],
                "generated_at": datetime.utcnow().isoformat() + "Z"
            }
        }

    def _build_networkx_graph(
        self, user_person: Person, persons: List[Person], relationships: List[Knows]
    ) -> nx.Graph:
        """
        Build NetworkX graph for layout calculations
        """
        G = nx.Graph()

        # Add user node
        if user_person:
            G.add_node(str(user_person.person_id), 
                      label="You", 
                      type="user",
                      person_obj=user_person)

        # Add person nodes
        for person in persons:
            G.add_node(str(person.person_id),
                      label=f"{person.first_name} {person.last_name}",
                      type="person",
                      person_obj=person)

        # Add relationship edges
        for relationship in relationships:
            # Add edge with relationship strength as weight
            weight = relationship.overall_score / 100.0 if relationship.overall_score else 0.5
            G.add_edge(str(relationship.from_person_id),
                      str(relationship.to_person_id),
                      weight=weight,
                      relationship_obj=relationship)

        return G

    def _calculate_node_positions(
        self, graph: nx.Graph, layout_type: str
    ) -> Dict[str, Dict[str, float]]:
        """
        Calculate node positions using specified layout algorithm
        """
        if len(graph.nodes()) == 0:
            return {}

        positions = {}
        
        try:
            if layout_type == "force_directed":
                pos = nx.spring_layout(graph, k=2, iterations=50, seed=42)
            elif layout_type == "circular":
                pos = nx.circular_layout(graph)
            elif layout_type == "hierarchical":
                pos = nx.nx_agraph.graphviz_layout(graph, prog='dot') if hasattr(nx, 'nx_agraph') else nx.spring_layout(graph)
            elif layout_type == "radial":
                # Custom radial layout with user at center
                pos = self._radial_layout(graph)
            else:
                # Default to spring layout
                pos = nx.spring_layout(graph, k=2, iterations=50, seed=42)

            # Convert to our format and scale appropriately
            for node_id, (x, y) in pos.items():
                positions[node_id] = {
                    "x": float(x) * 500,  # Scale for frontend
                    "y": float(y) * 500
                }

        except Exception as e:
            # Fallback to random positions if layout fails
            print(f"Layout calculation failed: {e}")
            positions = self._random_layout(graph)

        return positions

    def _radial_layout(self, graph: nx.Graph) -> Dict[str, Tuple[float, float]]:
        """
        Custom radial layout with user at center
        """
        positions = {}
        nodes = list(graph.nodes())
        
        # Find user node
        user_node = None
        for node in nodes:
            if graph.nodes[node].get("type") == "user":
                user_node = node
                break
        
        if user_node:
            # Place user at center
            positions[user_node] = (0.0, 0.0)
            other_nodes = [n for n in nodes if n != user_node]
        else:
            other_nodes = nodes

        # Arrange other nodes in concentric circles
        if other_nodes:
            radius = 0.5
            angle_step = 2 * math.pi / len(other_nodes)
            
            for i, node in enumerate(other_nodes):
                angle = i * angle_step
                x = radius * math.cos(angle)
                y = radius * math.sin(angle)
                positions[node] = (x, y)

        return positions

    def _random_layout(self, graph: nx.Graph) -> Dict[str, Dict[str, float]]:
        """
        Fallback random layout
        """
        positions = {}
        for node_id in graph.nodes():
            positions[node_id] = {
                "x": random.uniform(-250, 250),
                "y": random.uniform(-250, 250)
            }
        return positions

    def _generate_nodes_data(
        self, 
        user_person: Person, 
        persons: List[Person], 
        relationships: List[Knows],
        positions: Dict[str, Dict[str, float]]
    ) -> List[Dict[str, Any]]:
        """
        Generate nodes data for visualization
        """
        nodes = []
        
        # Relationship lookup for quick access
        rel_lookup = {rel.to_person_id: rel for rel in relationships}

        # Add user node
        if user_person:
            user_pos = positions.get(str(user_person.person_id), {"x": 0, "y": 0})
            nodes.append({
                "id": str(user_person.person_id),
                "label": "You",
                "type": "user",
                "size": 25,
                "color": "#4CAF50",
                "x": user_pos["x"],
                "y": user_pos["y"],
                "data": {
                    "name": f"{user_person.first_name} {user_person.last_name}",
                    "is_user": True
                }
            })

        # Add person nodes
        for person in persons:
            relationship = rel_lookup.get(person.person_id)
            
            # Calculate node size based on relationship strength
            base_size = 15
            if relationship and relationship.overall_score:
                size_bonus = (relationship.overall_score / 100.0) * 10
                node_size = base_size + size_bonus
            else:
                node_size = base_size

            # Determine color based on archetype
            color = self._get_archetype_color(relationship.archetype if relationship else "unknown")

            # Get position
            pos = positions.get(str(person.person_id), {"x": 0, "y": 0})

            # Extract professional info
            prof_info = person.professional_info or {}
            personal_info = person.personal_details or {}

            nodes.append({
                "id": str(person.person_id),
                "label": f"{person.first_name} {person.last_name}",
                "type": "person",
                "size": node_size,
                "color": color,
                "x": pos["x"],
                "y": pos["y"],
                "data": {
                    "name": f"{person.first_name} {person.last_name}",
                    "archetype": relationship.archetype if relationship else "unknown",
                    "relationship_score": relationship.overall_score if relationship else 0,
                    "company": prof_info.get("company", ""),
                    "title": prof_info.get("title", ""),
                    "tags": personal_info.get("tags", []),
                    "is_user": False
                }
            })

        return nodes

    def _generate_edges_data(
        self, relationships: List[Knows], user_person: Person
    ) -> List[Dict[str, Any]]:
        """
        Generate edges data for visualization
        """
        edges = []

        for relationship in relationships:
            # Calculate edge properties based on relationship strength
            strength = relationship.overall_score / 100.0 if relationship.overall_score else 0.5
            
            # Edge thickness based on strength
            width = max(1, strength * 5)
            
            # Edge color based on archetype
            color = self._get_edge_color(relationship.archetype, strength)

            edges.append({
                "from": str(relationship.from_person_id),
                "to": str(relationship.to_person_id),
                "strength": strength,
                "width": width,
                "color": color,
                "type": relationship.archetype or "unknown",
                "data": {
                    "archetype": relationship.archetype,
                    "overall_score": relationship.overall_score,
                    "dimensions": relationship.dimensions or {}
                }
            })

        return edges

    def _get_archetype_color(self, archetype: str) -> str:
        """
        Get color based on relationship archetype
        """
        color_map = {
            "colleague": "#2196F3",      # Blue
            "friend": "#FF9800",         # Orange
            "mentor": "#9C27B0",         # Purple
            "client": "#4CAF50",         # Green
            "family": "#F44336",         # Red
            "acquaintance": "#607D8B",   # Blue Grey
            "partner": "#E91E63",        # Pink
            "unknown": "#9E9E9E"         # Grey
        }
        return color_map.get(archetype.lower() if archetype else "unknown", "#9E9E9E")

    def _get_edge_color(self, archetype: str, strength: float) -> str:
        """
        Get edge color based on archetype and strength
        """
        base_color = self._get_archetype_color(archetype)
        # Use alpha channel based on strength
        alpha = max(0.3, strength)
        return f"{base_color}{int(alpha * 255):02x}"

    def _calculate_graph_metrics(
        self, graph: nx.Graph, relationships: List[Knows]
    ) -> Dict[str, float]:
        """
        Calculate graph metrics for analysis
        """
        metrics = {
            "density": 0.0,
            "clustering": 0.0,
            "avg_path_length": 0.0
        }

        try:
            if len(graph.nodes()) > 1:
                # Graph density
                metrics["density"] = nx.density(graph)
                
                # Average clustering coefficient
                metrics["clustering"] = nx.average_clustering(graph)
                
                # Average path length (only for connected components)
                if nx.is_connected(graph):
                    metrics["avg_path_length"] = nx.average_shortest_path_length(graph)
                else:
                    # Calculate for largest connected component
                    largest_cc = max(nx.connected_components(graph), key=len)
                    if len(largest_cc) > 1:
                        subgraph = graph.subgraph(largest_cc)
                        metrics["avg_path_length"] = nx.average_shortest_path_length(subgraph)

        except Exception as e:
            print(f"Error calculating graph metrics: {e}")

        return metrics

    async def get_node_details(self, user_id: UUID, person_id: UUID) -> Dict[str, Any]:
        """
        Get detailed information about a specific node
        """
        person = self.db.query(Person).filter(
            Person.person_id == person_id,
            Person.user_id == user_id
        ).first()

        if not person:
            return {"error": "Person not found"}

        # Get relationship if exists
        relationship = self.db.query(Knows).filter(
            Knows.user_id == user_id,
            Knows.to_person_id == person_id
        ).first()

        # Get connections (mutual connections)
        mutual_connections = self.db.query(Person).join(
            Knows, Person.person_id == Knows.to_person_id
        ).filter(
            Knows.user_id == user_id,
            Person.person_id != person_id,
            Person.is_user == False
        ).limit(10).all()

        return {
            "person": {
                "id": str(person.person_id),
                "name": f"{person.first_name} {person.last_name}",
                "professional_info": person.professional_info or {},
                "personal_details": person.personal_details or {},
                "contact_info": person.contact_info or {}
            },
            "relationship": {
                "archetype": relationship.archetype if relationship else None,
                "overall_score": relationship.overall_score if relationship else 0,
                "dimensions": relationship.dimensions if relationship else {},
                "foundation": relationship.relationship_foundation if relationship else {}
            },
            "mutual_connections": [
                {
                    "id": str(conn.person_id),
                    "name": f"{conn.first_name} {conn.last_name}",
                    "company": conn.professional_info.get("company", "") if conn.professional_info else ""
                }
                for conn in mutual_connections
            ]
        }

    async def get_graph_clusters(self, user_id: UUID) -> Dict[str, Any]:
        """
        Detect and return graph clusters/communities
        """
        # Get all data for clustering
        persons = self.db.query(Person).filter(
            Person.user_id == user_id,
            Person.is_user == False
        ).all()

        relationships = self.db.query(Knows).filter(
            Knows.user_id == user_id
        ).all()

        user_person = self.db.query(Person).filter(
            Person.user_id == user_id,
            Person.is_user == True
        ).first()

        # Build graph
        graph = self._build_networkx_graph(user_person, persons, relationships)

        clusters = []
        try:
            # Use community detection algorithm
            import networkx.algorithms.community as nx_comm
            communities = nx_comm.greedy_modularity_communities(graph)
            
            for i, community in enumerate(communities):
                cluster_nodes = []
                for node_id in community:
                    node_data = graph.nodes[node_id]
                    cluster_nodes.append({
                        "id": node_id,
                        "label": node_data.get("label", "Unknown"),
                        "type": node_data.get("type", "person")
                    })
                
                clusters.append({
                    "cluster_id": i,
                    "size": len(community),
                    "nodes": cluster_nodes
                })

        except Exception as e:
            print(f"Error detecting clusters: {e}")

        return {
            "clusters": clusters,
            "metadata": {
                "total_clusters": len(clusters),
                "algorithm": "greedy_modularity",
                "generated_at": datetime.utcnow().isoformat() + "Z"
            }
        }