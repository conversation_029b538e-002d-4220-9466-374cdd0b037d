"""
Task service - handles task-related business logic
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>, Date, and_, or_, select
from sqlalchemy.orm import Session

from app.models.task import Task
from app.schemas.task import TaskCreate, TaskUpdate
from app.services.base_service import BaseService


class TaskService(BaseService[Task, TaskCreate, TaskUpdate]):
    """Task service with priority and completion logic"""

    def __init__(self, db: Session):
        super().__init__(Task, db)

    def get_by_user(
        self,
        user_id: UUID,
        completed: bool = None,
        goal_id: UUID = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[Task]:
        """Get tasks for a specific user with optional filters"""
        stmt = select(self.model).where(self.model.user_id == user_id)

        if completed is not None:
            stmt = stmt.where(self.model.is_completed == completed)

        if goal_id:
            stmt = stmt.where(self.model.goal_id == goal_id)

        stmt = (
            stmt.offset(skip)
            .limit(limit)
            .order_by(
                self.model.priority.desc(),
                self.model.due_date.asc().nullslast(),
                self.model.created_at.desc(),
            )
        )

        result = self.db.execute(stmt)
        return result.scalars().all()

    def get_pending_tasks(self, user_id: UUID, limit: int = 50) -> List[Task]:
        """Get all pending (not completed) tasks for a user"""
        return self.get_by_user(user_id, completed=False, limit=limit)

    def get_overdue_tasks(self, user_id: UUID) -> List[Task]:
        """Get overdue tasks for a user"""
        now = datetime.utcnow()
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.user_id == user_id,
                    self.model.is_completed == False,
                    self.model.due_date < now,
                )
            )
            .order_by(self.model.due_date.asc())
        )
        result = self.db.execute(stmt)
        return result.scalars().all()

    def get_today_tasks(self, user_id: UUID) -> List[Task]:
        """Get tasks due today"""
        today = datetime.utcnow().date()
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.user_id == user_id,
                    self.model.is_completed == False,
                    self.model.due_date.cast(Date) == today,
                )
            )
            .order_by(self.model.priority.desc())
        )
        result = self.db.execute(stmt)
        return result.scalars().all()

    def get_by_goal(self, goal_id: UUID, user_id: UUID) -> List[Task]:
        """Get all tasks for a specific goal"""
        return self.get_by_user(user_id, goal_id=goal_id)

    def get_by_id_and_user(self, task_id: UUID, user_id: UUID) -> Optional[Task]:
        """Get task by ID and user ID"""
        stmt = select(self.model).where(
            and_(self.model.task_id == task_id, self.model.user_id == user_id)
        )
        result = self.db.execute(stmt)
        return result.scalar_one_or_none()

    def mark_completed(self, task_id: UUID, user_id: UUID) -> Optional[Task]:
        """Mark task as completed"""
        task = self.get_by_id_and_user(task_id, user_id)
        if not task:
            return None

        task.mark_completed()
        self.db.commit()
        self.db.refresh(task)
        return task

    def create_for_goal(
        self, goal_id: UUID, user_id: UUID, task_data: TaskCreate
    ) -> Task:
        """Create a task associated with a goal"""
        task = Task(
            user_id=user_id,
            goal_id=goal_id,
            title=task_data.title,
            description=task_data.description,
            priority=task_data.priority,
            due_date=task_data.due_date,
            metadata=task_data.metadata or {},
        )

        self.db.add(task)
        self.db.commit()
        self.db.refresh(task)
        return task

    def add_related_person(
        self, task_id: UUID, user_id: UUID, person_id: UUID
    ) -> Optional[Task]:
        """Add a person as related to this task"""
        task = self.get_by_id_and_user(task_id, user_id)
        if not task:
            return None

        task.add_related_person(str(person_id))
        self.db.commit()
        self.db.refresh(task)
        return task

    def get_ai_generated_tasks(self, user_id: UUID) -> List[Task]:
        """Get tasks that were generated by AI"""
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.user_id == user_id,
                    self.model.metadata["ai_generated"].astext.cast(Boolean) == True,
                )
            )
            .order_by(self.model.created_at.desc())
        )
        result = self.db.execute(stmt)
        return result.scalars().all()

    def get_tasks_by_category(self, user_id: UUID, category: str) -> List[Task]:
        """Get tasks by category"""
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.user_id == user_id,
                    self.model.metadata["category"].astext == category,
                )
            )
            .order_by(self.model.priority.desc())
        )
        result = self.db.execute(stmt)
        return result.scalars().all()

    def get(self, id: UUID) -> Optional[Task]:
        """Override base get method to use task_id as primary key"""
        stmt = select(self.model).where(self.model.task_id == id)
        result = self.db.execute(stmt)
        return result.scalar_one_or_none()
