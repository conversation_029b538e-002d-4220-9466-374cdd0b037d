"""
Person service - handles person-related business logic
"""

import uuid
from typing import List, Optional
from uuid import UUID

from sqlalchemy import and_, or_, select, func
from sqlalchemy.orm import Session, selectinload

from app.models.person import Person
from app.models.relationship import Knows
from app.schemas.person import PersonCreate, PersonUpdate
from app.services.base_service import BaseService


class PersonService(BaseService[Person, PersonCreate, PersonUpdate]):
    """Person service with relationship management logic"""

    def __init__(self, db: Session):
        super().__init__(Person, db)

    # Core CRUD operations
    def get_all_by_user(self, user_id: UUID, skip: int = 0, limit: int = 100) -> List[Person]:
        """Get all persons for a specific user"""
        stmt = (
            select(self.model)
            .where(self.model.user_id == user_id)
            .offset(skip)
            .limit(limit)
            .order_by(self.model.first_name, self.model.last_name)
        )
        result = self.db.execute(stmt)
        return result.scalars().all()

    def count_by_user(self, user_id: UUID) -> int:
        """Count total persons for a specific user"""
        stmt = select(func.count(self.model.person_id)).where(self.model.user_id == user_id)
        result = self.db.execute(stmt)
        return result.scalar() or 0

    def search_persons(
        self,
        user_id: UUID,
        search: Optional[str] = None,
        company: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Person]:
        """Search persons by name or company"""
        stmt = select(self.model).where(self.model.user_id == user_id)

        # Add search conditions
        conditions = []

        if search:
            search_term = f"%{search.lower()}%"
            conditions.append(
                or_(
                    self.model.first_name.ilike(search_term),
                    self.model.last_name.ilike(search_term)
                )
            )

        if company:
            company_term = f"%{company.lower()}%"
            # For SQLite, we need to use JSON_EXTRACT instead of astext
            conditions.append(
                func.lower(func.json_extract(self.model.professional_info, '$.company')).like(company_term.lower())
            )

        if conditions:
            stmt = stmt.where(and_(*conditions))

        stmt = stmt.offset(skip).limit(limit).order_by(self.model.first_name, self.model.last_name)

        result = self.db.execute(stmt)
        return result.scalars().all()

    def count_search_results(
        self,
        user_id: UUID,
        search: Optional[str] = None,
        company: Optional[str] = None
    ) -> int:
        """Count search results"""
        stmt = select(func.count(self.model.person_id)).where(self.model.user_id == user_id)

        # Add search conditions
        conditions = []

        if search:
            search_term = f"%{search.lower()}%"
            conditions.append(
                or_(
                    self.model.first_name.ilike(search_term),
                    self.model.last_name.ilike(search_term)
                )
            )

        if company:
            company_term = f"%{company.lower()}%"
            # For SQLite, we need to use JSON_EXTRACT instead of astext
            conditions.append(
                func.lower(func.json_extract(self.model.professional_info, '$.company')).like(company_term.lower())
            )

        if conditions:
            stmt = stmt.where(and_(*conditions))

        result = self.db.execute(stmt)
        return result.scalar() or 0

    def get_all_tags(self, user_id: UUID) -> List[str]:
        """Get all unique tags used by the user"""
        stmt = select(self.model.personal_details).where(self.model.user_id == user_id)
        result = self.db.execute(stmt)

        all_tags = set()
        for personal_details in result.scalars():
            if personal_details and isinstance(personal_details, dict):
                tags = personal_details.get("tags", [])
                if isinstance(tags, list):
                    all_tags.update(tags)

        return list(all_tags)



    def get_person_relationships(self, person_id: str, user_id: UUID) -> List[dict]:
        """Get all relationships for a person"""
        # Get outgoing relationships (from this person)
        outgoing = self.db.query(Knows).filter(
            and_(
                Knows.from_person_id == person_id,
                Knows.user_id == user_id
            )
        ).all()

        # Get incoming relationships (to this person)
        incoming = self.db.query(Knows).filter(
            and_(
                Knows.to_person_id == person_id,
                Knows.user_id == user_id
            )
        ).all()

        relationships = []

        # Process outgoing relationships
        for rel in outgoing:
            relationships.append({
                "relationship_id": f"{rel.from_person_id}-{rel.to_person_id}",
                "from_person_id": str(rel.from_person_id),
                "to_person_id": str(rel.to_person_id),
                "direction": "outgoing",
                "archetype": rel.archetype,
                "relationship_foundation": rel.relationship_foundation,
                "relationship_depth": rel.relationship_depth,
                "created_at": rel.created_at.isoformat() if rel.created_at else None
            })

        # Process incoming relationships
        for rel in incoming:
            relationships.append({
                "relationship_id": f"{rel.from_person_id}-{rel.to_person_id}",
                "from_person_id": str(rel.from_person_id),
                "to_person_id": str(rel.to_person_id),
                "direction": "incoming",
                "archetype": rel.archetype,
                "relationship_foundation": rel.relationship_foundation,
                "relationship_depth": rel.relationship_depth,
                "created_at": rel.created_at.isoformat() if rel.created_at else None
            })

        return relationships

    def get_user_person(self, user_id: UUID) -> Optional[Person]:
        """Get the person record that represents the user (is_user=True)"""
        stmt = select(self.model).where(
            and_(self.model.user_id == user_id, self.model.is_user == True)
        )
        result = self.db.execute(stmt)
        return result.scalar_one_or_none()

    def create_user_person(
        self, user_id: UUID, first_name: str, last_name: str
    ) -> Person:
        """Create a person record representing the user"""
        # Check if user person already exists
        existing = self.get_user_person(user_id)
        if existing:
            return existing

        user_person = Person(
            user_id=user_id,
            first_name=first_name,
            last_name=last_name,
            is_user=True,
            stated_decision_factors={
                "emotional_weight": 0.2,
                "value_weight": 0.2,
                "trust_weight": 0.2,
                "information_weight": 0.15,
                "role_weight": 0.15,
                "coercive_weight": 0.1,
            },
            learned_decision_factors={},
        )

        self.db.add(user_person)
        self.db.commit()
        self.db.refresh(user_person)
        return user_person



    def get_with_relationships(
        self, person_id: UUID, user_id: UUID
    ) -> Optional[Person]:
        """Get person with their relationships loaded"""
        stmt = (
            select(self.model)
            .options(
                selectinload(self.model.outgoing_relationships),
                selectinload(self.model.incoming_relationships),
            )
            .where(
                and_(self.model.person_id == person_id, self.model.user_id == user_id)
            )
        )
        result = self.db.execute(stmt)
        return result.scalar_one_or_none()

    def get_relationship_between(
        self, from_person_id: UUID, to_person_id: UUID, user_id: UUID
    ) -> Optional[Knows]:
        """Get relationship between two persons"""
        stmt = select(Knows).where(
            and_(
                Knows.from_person_id == from_person_id,
                Knows.to_person_id == to_person_id,
                Knows.user_id == user_id,
            )
        )
        result = self.db.execute(stmt)
        return result.scalar_one_or_none()

    def create_relationship(
        self,
        from_person_id: UUID,
        to_person_id: UUID,
        user_id: UUID,
        archetype: str = None,
        relationship_foundation: dict = None,
        relationship_depth: dict = None,
    ) -> Knows:
        """Create a relationship between two persons"""
        # Check if relationship already exists
        existing = self.get_relationship_between(
            from_person_id, to_person_id, user_id
        )
        if existing:
            return existing

        relationship = Knows(
            from_person_id=from_person_id,
            to_person_id=to_person_id,
            user_id=user_id,
            archetype=archetype,
            relationship_foundation=relationship_foundation or {},
            relationship_depth=relationship_depth
            or {
                "overall_score": 50,
                "trend": "neutral",
                "dimensions": {
                    "emotional_intimacy": 50,
                    "professional_collaboration": 50,
                    "trust_level": 50,
                    "communication_frequency": 50,
                    "shared_experience_value": 50,
                    "reciprocity_balance": 50,
                },
                "history": [],
            },
        )

        self.db.add(relationship)
        self.db.commit()
        self.db.refresh(relationship)
        return relationship

    def update_relationship_score(
        self,
        from_person_id: UUID,
        to_person_id: UUID,
        user_id: UUID,
        new_score: int,
        event: str,
        interaction_id: str = None,
    ) -> Optional[Knows]:
        """Update relationship score and add to history"""
        relationship = self.get_relationship_between(
            from_person_id, to_person_id, user_id
        )
        if not relationship:
            return None

        relationship.update_score(new_score, event, interaction_id)
        self.db.commit()
        self.db.refresh(relationship)
        return relationship

    def get(self, id: UUID) -> Optional[Person]:
        """Override base get method to use person_id as primary key"""
        stmt = select(self.model).where(self.model.person_id == id)
        result = self.db.execute(stmt)
        return result.scalar_one_or_none()

    def check_duplicate_relationship(
        self, user_id: UUID, first_name: str, last_name: str
    ) -> Optional[Person]:
        """Check if a person with the same name already exists for this user"""
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.user_id == user_id,
                    self.model.first_name.ilike(first_name.strip()),
                    self.model.last_name.ilike(last_name.strip()),
                    self.model.is_user == False,  # Don't match the user's own record
                )
            )
        )
        result = self.db.execute(stmt)
        return result.scalar_one_or_none()

    def create_person(
        self, user_id: UUID, person_data: dict
    ) -> Person:
        """Create a new person for a user"""
        # Generate a new person_id
        person_id = str(uuid.uuid4())

        # Create person record
        new_person = Person(
            person_id=person_id,
            user_id=user_id,
            first_name=person_data.get("first_name", ""),
            last_name=person_data.get("last_name", ""),
            contact_info=person_data.get("contact_info", {}),
            social_profiles=person_data.get("social_profiles", {}),
            professional_info=person_data.get("professional_info", {}),
            personal_details=person_data.get("personal_details", {}),
            stated_decision_factors=person_data.get("stated_decision_factors", {}),
            learned_decision_factors=person_data.get("learned_decision_factors", {}),
            is_user=False,
        )

        self.db.add(new_person)
        self.db.commit()
        self.db.refresh(new_person)
        return new_person
