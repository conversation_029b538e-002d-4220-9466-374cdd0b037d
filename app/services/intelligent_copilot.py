"""
Intelligent Copilot Service
Combines LLM capabilities with function calling to provide intelligent assistance
"""

import json
import logging
import uuid
from typing import Any, Dict, List, Optional
from datetime import datetime

from sqlalchemy.orm import Session
from app.models.user import User
from app.core.llm_service import LLMService
from app.services.copilot_tools import CopilotTools, TOOL_SCHEMAS
from app.core.config import settings

logger = logging.getLogger(__name__)


class IntelligentCopilot:
    """
    Intelligent copilot that uses LLM with function calling to provide
    context-aware assistance for network management
    """
    
    def __init__(self, db: Session, user: User):
        self.db = db
        self.user = user
        self.tools = CopilotTools(db, user)
        
        # Initialize LLM service with database session
        self.llm = LLMService(db_session=db)
        
        # Register all available tools
        self._register_tools()
        
        # Conversation context
        self.user_context = self._build_user_context()

    def _register_tools(self):
        """Register all available tools with the LLM service"""
        tool_methods = {
            "search_network": self.tools.search_network,
            "get_network_health": self.tools.get_network_health,
            "create_task": self.tools.create_task,
            "create_goal": self.tools.create_goal,
            "get_recent_tasks": self.tools.get_recent_tasks,
            "get_goals": self.tools.get_goals,
            "find_mutual_connections": self.tools.find_mutual_connections,
            "get_ai_suggestions": self.tools.get_ai_suggestions,
            "analyze_relationship_strength": self.tools.analyze_relationship_strength
        }
        
        for tool_name, tool_func in tool_methods.items():
            if tool_name in TOOL_SCHEMAS:
                self.llm.register_tool(tool_name, tool_func, TOOL_SCHEMAS[tool_name])

    def _build_user_context(self) -> Dict[str, Any]:
        """Build context about the user for better assistance"""
        try:
            # Get basic user info
            context = {
                "user_id": str(self.user.user_id),
                "user_name": f"{self.user.first_name} {self.user.last_name}",
                "email": self.user.email
            }
            
            # Get network size
            from app.models.person import Person
            network_size = self.db.query(Person).filter(
                Person.user_id == self.user.user_id,
                Person.is_user == False
            ).count()
            context["network_size"] = network_size
            
            # Get recent activity counts
            from app.models.task import Task
            from app.models.goal import Goal
            
            active_tasks = self.db.query(Task).filter(
                Task.user_id == self.user.user_id,
                Task.is_completed == False
            ).count()
            
            active_goals = self.db.query(Goal).filter(
                Goal.user_id == self.user.user_id,
                Goal.status == "active"
            ).count()
            
            context["active_tasks"] = active_tasks
            context["active_goals"] = active_goals
            
            return context
            
        except Exception as e:
            logger.error(f"Error building user context: {e}")
            return {"user_id": str(self.user.user_id)}

    def _get_system_prompt(self) -> str:
        """Get personalized system prompt based on user context"""
        return f"""You are Nexus Copilot, an AI assistant specialized in helping {self.user_context.get('user_name', 'the user')} manage their professional network and relationships.

Current user context:
- Network size: {self.user_context.get('network_size', 0)} connections
- Active tasks: {self.user_context.get('active_tasks', 0)}
- Active goals: {self.user_context.get('active_goals', 0)}

Your capabilities include:
1. **Network Analysis**: Analyze network health, identify gaps, and suggest improvements
2. **Connection Search**: Find specific people or companies in the user's network
3. **Task Management**: Create and manage networking tasks and follow-ups
4. **Goal Setting**: Help set and track networking and professional goals
5. **Relationship Insights**: Analyze relationship strength and suggest actions
6. **AI Suggestions**: Provide intelligent recommendations for network growth

Guidelines:
- Always be helpful, professional, and focused on building meaningful connections
- Use the available tools to provide actionable assistance
- When creating tasks or goals, be specific and actionable
- Provide context and reasoning for your suggestions
- Ask clarifying questions when needed to better understand user intent
- Focus on quality relationships over quantity

Remember: You're helping build a professional network that creates real value and meaningful connections."""

    async def process_message(
        self, 
        message: str, 
        conversation_id: str = None,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Process a user message and return an intelligent response
        
        Args:
            message: User's message
            conversation_id: Conversation ID for context
            context: Additional context information
        """
        try:
            # Generate conversation ID if not provided
            if not conversation_id:
                conversation_id = str(uuid.uuid4())
            
            # Merge user context with provided context
            full_context = {**self.user_context}
            if context:
                full_context.update(context)
            
            # Process the conversation with LLM
            response = await self.llm.process_conversation(
                user_message=message,
                conversation_id=conversation_id,
                user_id=str(self.user.user_id),
                context=full_context,
                system_prompt=self._get_system_prompt()
            )
            
            # Enhance response with additional metadata
            response.update({
                "user_id": str(self.user.user_id),
                "timestamp": datetime.utcnow().isoformat(),
                "context": {
                    "network_size": self.user_context.get("network_size", 0),
                    "active_tasks": self.user_context.get("active_tasks", 0),
                    "active_goals": self.user_context.get("active_goals", 0)
                }
            })
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return {
                "response": "I apologize, but I encountered an error processing your request. Please try again.",
                "error": str(e),
                "conversation_id": conversation_id or str(uuid.uuid4()),
                "timestamp": datetime.utcnow().isoformat()
            }

    async def get_proactive_suggestions(
        self, 
        suggestion_type: str = None,
        limit: int = 5
    ) -> Dict[str, Any]:
        """
        Get proactive suggestions based on user's network and activity
        
        Args:
            suggestion_type: Type of suggestions to focus on
            limit: Maximum number of suggestions
        """
        try:
            # Get AI suggestions using the tool
            ai_suggestions = self.tools.get_ai_suggestions(suggestion_type, limit)
            
            # Get network health for additional insights
            network_health = self.tools.get_network_health()
            
            # Generate contextual suggestions using LLM
            context_prompt = f"""Based on the user's current network state and AI analysis, provide {limit} actionable suggestions for improving their professional network.

Network Health Summary:
{json.dumps(network_health, indent=2)}

AI Suggestions:
{json.dumps(ai_suggestions, indent=2)}

User Context:
- Network size: {self.user_context.get('network_size', 0)}
- Active tasks: {self.user_context.get('active_tasks', 0)}
- Active goals: {self.user_context.get('active_goals', 0)}

Please provide specific, actionable suggestions that would help the user improve their networking effectiveness. Focus on quality connections and meaningful relationship building."""

            llm_response = await self.llm.chat_completion(
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": context_prompt}
                ],
                max_tokens=800
            )
            
            return {
                "suggestions": ai_suggestions.get("suggestions", []),
                "network_insights": network_health,
                "llm_recommendations": llm_response.choices[0].message.content,
                "context": self.user_context,
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting proactive suggestions: {e}")
            return {
                "error": str(e),
                "suggestions": [],
                "generated_at": datetime.utcnow().isoformat()
            }

    async def analyze_conversation_intent(self, message: str) -> Dict[str, Any]:
        """
        Analyze the intent and entities in a user message
        
        Args:
            message: User's message to analyze
        """
        try:
            analysis_prompt = f"""Analyze the following user message and extract:
1. Primary intent (what the user wants to do)
2. Entities mentioned (people, companies, dates, etc.)
3. Suggested actions
4. Confidence level

Message: "{message}"

Respond in JSON format with the following structure:
{{
    "intent": "primary_intent",
    "confidence": 0.0-1.0,
    "entities": [
        {{"type": "entity_type", "value": "entity_value", "confidence": 0.0-1.0}}
    ],
    "suggested_actions": [
        {{"action": "action_name", "description": "action_description"}}
    ],
    "requires_clarification": true/false,
    "clarification_questions": ["question1", "question2"]
}}"""

            response = await self.llm.chat_completion(
                messages=[
                    {"role": "system", "content": "You are an expert at analyzing user intent for networking and relationship management tasks."},
                    {"role": "user", "content": analysis_prompt}
                ],
                max_tokens=500
            )
            
            try:
                analysis = json.loads(response.choices[0].message.content)
                return analysis
            except json.JSONDecodeError:
                # Fallback to simple analysis
                return {
                    "intent": "general_query",
                    "confidence": 0.5,
                    "entities": [],
                    "suggested_actions": [],
                    "requires_clarification": False,
                    "clarification_questions": []
                }
            
        except Exception as e:
            logger.error(f"Error analyzing conversation intent: {e}")
            return {
                "intent": "unknown",
                "confidence": 0.0,
                "entities": [],
                "suggested_actions": [],
                "error": str(e)
            }

    def get_conversation_history(self, conversation_id: str) -> List[Dict[str, Any]]:
        """Get conversation history for a specific conversation"""
        try:
            messages = self.llm.get_conversation(conversation_id, str(self.user.user_id))
            return [
                {
                    "role": msg.role,
                    "content": msg.content,
                    "timestamp": msg.timestamp.isoformat(),
                    "tool_calls": [
                        {
                            "name": tc.name,
                            "arguments": tc.arguments,
                            "result": tc.result,
                            "status": tc.status.value
                        }
                        for tc in (msg.tool_calls or [])
                    ]
                }
                for msg in messages
            ]
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []

    def clear_conversation(self, conversation_id: str):
        """Clear conversation history"""
        try:
            self.llm.clear_conversation(conversation_id, str(self.user.user_id))
        except Exception as e:
            logger.error(f"Error clearing conversation: {e}")
