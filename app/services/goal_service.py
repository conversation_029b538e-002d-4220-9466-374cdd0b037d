"""
Goal service - handles goal-related business logic with AI integration
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from uuid import UUID

from sqlalchemy import and_, desc, func, or_, select
from sqlalchemy.orm import Session, selectinload

from app.models.goal import Goal, GoalStatus
from app.models.task import Task
from app.models.person import Person
from app.models.relationship import Knows
from app.models.interaction import Interaction
from app.schemas.goal import GoalCreate, GoalUpdate
from app.services.base_service import BaseService
from app.core.ai_utils import OpenAIClient
from app.core.config import settings

logger = logging.getLogger(__name__)


class GoalService(BaseService[Goal, GoalCreate, GoalUpdate]):
    """Goal service with task management logic"""

    def __init__(self, db: Session):
        super().__init__(Goal, db)

    def get_by_user(
        self, user_id: UUID, status: GoalStatus = None, skip: int = 0, limit: int = 100
    ) -> List[Goal]:
        """Get goals for a specific user, optionally filtered by status"""
        stmt = select(self.model).where(self.model.user_id == user_id)

        if status:
            stmt = stmt.where(self.model.status == status)

        stmt = stmt.offset(skip).limit(limit).order_by(self.model.created_at.desc())

        result = self.db.execute(stmt)
        return result.scalars().all()

    def get_active_goals(self, user_id: UUID) -> List[Goal]:
        """Get all active goals for a user"""
        return self.get_by_user(user_id, status=GoalStatus.ACTIVE)

    def get_with_tasks(self, goal_id: UUID, user_id: UUID) -> Optional[Goal]:
        """Get goal with its tasks loaded"""
        stmt = (
            select(self.model)
            .options(selectinload(self.model.tasks))
            .where(and_(self.model.goal_id == goal_id, self.model.user_id == user_id))
        )
        result = self.db.execute(stmt)
        return result.scalar_one_or_none()

    def get_goal_dashboard_data(self, goal_id: UUID, user_id: UUID) -> dict:
        """
        Get comprehensive dashboard data for a goal
        This implements the "goal intelligence dashboard" from the technical documentation
        """
        goal = self.get_with_tasks(goal_id, user_id)
        if not goal:
            return None

        # Calculate task statistics
        total_tasks = len(goal.tasks)
        completed_tasks = len([t for t in goal.tasks if t.is_completed])
        pending_tasks = total_tasks - completed_tasks

        # Calculate progress percentage
        progress = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0

        # Get related people from goal metadata
        related_people = goal.related_people

        # Get AI analysis for goal
        ai_analysis = self.get_goal_ai_analysis(goal_id, user_id)
        
        # Get referral paths for goal achievement
        referral_paths = self.find_goal_referral_paths(goal_id, user_id)
        ai_analysis["referral_paths"] = referral_paths

        # Convert SQLAlchemy object to dict for serialization
        from app.schemas.goal import Goal as GoalSchema
        goal_dict = GoalSchema.model_validate(goal).model_dump()

        return {
            "goal": goal_dict,
            "progress": {
                "percentage": round(progress, 1),
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "pending_tasks": pending_tasks,
            },
            "related_people": related_people,
            "ai_analysis": ai_analysis,
            "recent_activity": self.get_goal_recent_activity(goal_id, user_id),
            "milestone_progress": self.get_milestone_progress(goal_id, user_id),
            "recommended_connections": self.get_recommended_connections(goal_id, user_id),
            "success_prediction": self.predict_goal_success(goal_id, user_id)
        }

    def add_related_person(
        self, goal_id: UUID, user_id: UUID, person_id: UUID
    ) -> Optional[Goal]:
        """Add a person as related to this goal"""
        goal = self.get_by_id_and_user(goal_id, user_id)
        if not goal:
            return None

        goal.add_related_person(str(person_id))
        self.db.commit()
        self.db.refresh(goal)
        return goal

    def get_by_id_and_user(self, goal_id: UUID, user_id: UUID) -> Optional[Goal]:
        """Get goal by ID and user ID"""
        stmt = select(self.model).where(
            and_(self.model.goal_id == goal_id, self.model.user_id == user_id)
        )
        result = self.db.execute(stmt)
        return result.scalar_one_or_none()

    def mark_completed(self, goal_id: UUID, user_id: UUID) -> Optional[Goal]:
        """Mark goal as completed"""
        goal = self.get_by_id_and_user(goal_id, user_id)
        if not goal:
            return None

        goal.mark_completed()
        self.db.commit()
        self.db.refresh(goal)
        return goal

    def get_goals_by_priority(self, user_id: UUID, limit: int = 10) -> List[Goal]:
        """Get goals ordered by priority (highest first)"""
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.user_id == user_id,
                    self.model.status == GoalStatus.ACTIVE,
                )
            )
            .order_by(self.model.priority.desc(), self.model.created_at.desc())
            .limit(limit)
        )
        result = self.db.execute(stmt)
        return result.scalars().all()

    def get(self, id: UUID) -> Optional[Goal]:
        """Override base get method to use goal_id as primary key"""
        stmt = select(self.model).where(self.model.goal_id == id)
        result = self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    def get_goal_ai_analysis(self, goal_id: UUID, user_id: UUID) -> Dict[str, Any]:
        """Generate AI-powered analysis for goal achievement strategies"""
        goal = self.get_by_id_and_user(goal_id, user_id)
        if not goal:
            return {"error": "Goal not found"}
        
        try:
            # Initialize OpenAI client if available
            openai_client = None
            if settings.OPENAI_API_KEY:
                try:
                    openai_client = OpenAIClient(
                        api_key=settings.OPENAI_API_KEY,
                        base_url=settings.OPENAI_API_BASE
                    )
                except Exception as e:
                    logger.warning(f"Failed to initialize OpenAI client: {e}")
            
            # Get goal context
            goal_context = {
                "title": goal.title,
                "description": goal.description,
                "target_date": goal.target_date.isoformat() if goal.target_date else None,
                "priority": goal.priority,
                "tags": goal.tags,
                "success_criteria": goal.success_criteria
            }
            
            # Get related tasks
            tasks = (
                self.db.query(Task)
                .filter(Task.goal_id == goal_id)
                .all()
            )
            
            task_context = [
                {
                    "title": task.title,
                    "status": task.status,
                    "due_date": task.due_date.isoformat() if task.due_date else None
                }
                for task in tasks
            ]
            
            # Get user's network context
            network_size = (
                self.db.query(func.count(Person.person_id))
                .filter(Person.user_id == user_id)
                .scalar()
            ) or 0
            
            analysis = {}
            
            if openai_client:
                # Use AI for analysis
                try:
                    prompt = f"""
                    Analyze the following goal and provide strategic insights:
                    
                    Goal: {goal_context}
                    Tasks: {task_context}
                    Network Size: {network_size} connections
                    
                    Provide analysis in JSON format with:
                    - difficulty_assessment (easy/medium/hard)
                    - success_probability (0.0-1.0)
                    - required_connections (list of connection types needed)
                    - next_steps (list of 3-5 actionable next steps)
                    - timeline_assessment (realistic/optimistic/pessimistic)
                    - key_risks (list of potential obstacles)
                    - recommended_approach (brief strategy description)
                    """
                    
                    response = openai_client.generate_text(
                        prompt=prompt,
                        max_tokens=500,
                        temperature=0.7
                    )
                    
                    # Try to parse JSON response
                    try:
                        analysis = json.loads(response)
                    except json.JSONDecodeError:
                        # Fallback if JSON parsing fails
                        analysis = self._get_fallback_analysis(goal, tasks, network_size)
                        
                except Exception as e:
                    logger.warning(f"AI analysis failed: {e}")
                    analysis = self._get_fallback_analysis(goal, tasks, network_size)
            else:
                # Use rule-based fallback analysis
                analysis = self._get_fallback_analysis(goal, tasks, network_size)
            
            # Cache analysis in goal metadata
            if not goal.goal_metadata:
                goal.goal_metadata = {}
            goal.goal_metadata["ai_analysis"] = analysis
            goal.goal_metadata["analysis_timestamp"] = datetime.utcnow().isoformat()
            
            self.db.commit()
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error generating goal AI analysis: {e}")
            return self._get_fallback_analysis(goal, tasks or [], network_size or 0)
    
    def _get_fallback_analysis(self, goal: Goal, tasks: List[Task], network_size: int) -> Dict[str, Any]:
        """Generate rule-based analysis when AI is not available"""
        total_tasks = len(tasks)
        completed_tasks = len([t for t in tasks if t.status == "completed"])
        
        # Simple rule-based difficulty assessment
        difficulty = "easy"
        if total_tasks > 10 or goal.priority >= 4:
            difficulty = "hard"
        elif total_tasks > 5 or goal.priority >= 3:
            difficulty = "medium"
        
        # Simple success probability calculation
        task_completion_rate = completed_tasks / total_tasks if total_tasks > 0 else 0.5
        network_factor = min(network_size / 50, 1.0)  # Normalize to max 1.0
        priority_factor = goal.priority / 5.0
        
        success_probability = (task_completion_rate + network_factor + priority_factor) / 3
        success_probability = max(0.1, min(0.95, success_probability))  # Clamp between 0.1-0.95
        
        # Generate next steps based on goal status
        next_steps = []
        if completed_tasks == 0:
            next_steps.append("Break down goal into specific actionable tasks")
            next_steps.append("Set realistic timeline and milestones")
        elif task_completion_rate < 0.5:
            next_steps.append("Focus on completing pending tasks")
            next_steps.append("Review and prioritize remaining work")
        else:
            next_steps.append("Maintain momentum on current progress")
            next_steps.append("Prepare for goal completion activities")
        
        if network_size < 20:
            next_steps.append("Expand professional network for better opportunities")
        
        next_steps.append("Schedule regular progress reviews")
        
        return {
            "difficulty_assessment": difficulty,
            "success_probability": round(success_probability, 2),
            "required_connections": self._get_required_connections(goal),
            "next_steps": next_steps[:5],  # Limit to 5 steps
            "timeline_assessment": "realistic",
            "key_risks": [
                "Lack of clear milestones",
                "Insufficient network connections",
                "Time management challenges"
            ],
            "recommended_approach": "Focus on systematic task completion and strategic networking"
        }
    
    def _get_required_connections(self, goal: Goal) -> List[str]:
        """Determine required connection types based on goal"""
        connections = []
        
        # Analyze goal title and description for keywords
        goal_text = (goal.title + " " + (goal.description or "")).lower()
        
        if any(word in goal_text for word in ["funding", "investment", "capital", "raise"]):
            connections.extend(["investor", "venture_capitalist", "angel_investor"])
        
        if any(word in goal_text for word in ["job", "career", "position", "role"]):
            connections.extend(["recruiter", "hiring_manager", "industry_professional"])
        
        if any(word in goal_text for word in ["business", "startup", "company"]):
            connections.extend(["mentor", "entrepreneur", "advisor"])
        
        if any(word in goal_text for word in ["partnership", "collaboration", "joint"]):
            connections.extend(["business_partner", "collaborator"])
        
        if any(word in goal_text for word in ["marketing", "sales", "customers"]):
            connections.extend(["marketing_expert", "sales_professional", "potential_customer"])
        
        # Default connections if none detected
        if not connections:
            connections = ["mentor", "industry_expert", "peer"]
        
        return list(set(connections))  # Remove duplicates
    
    def find_goal_referral_paths(self, goal_id: UUID, user_id: UUID) -> List[Dict[str, Any]]:
        """Find referral paths to achieve goal using pathfinding algorithms"""
        goal = self.get_by_id_and_user(goal_id, user_id)
        if not goal:
            return []
        
        try:
            # Import pathfinding service
            from app.services.pathfinding_service import PathfindingService, PathOptimization
            
            pathfinding_service = PathfindingService(self.db)
            
            # Get required connections for this goal
            required_connections = self._get_required_connections(goal)
            
            referral_paths = []
            
            # Find paths to each type of required connection
            for connection_type in required_connections[:3]:  # Limit to top 3
                try:
                    # Find people in network who might have connections to this type
                    target_criteria = {
                        "connection_type": connection_type,
                        "industry_relevance": True
                    }
                    
                    paths = pathfinding_service.find_goal_oriented_paths(
                        user_id=user_id,
                        target_criteria=target_criteria,
                        optimization=PathOptimization.BALANCED,
                        max_paths=3
                    )
                    
                    for path in paths:
                        referral_paths.append({
                            "target_connection_type": connection_type,
                            "path": path["path"],
                            "success_probability": path.get("success_probability", 0.5),
                            "path_length": len(path["path"]),
                            "estimated_timeline": f"{len(path['path']) * 2}-{len(path['path']) * 4} weeks"
                        })
                        
                except Exception as e:
                    logger.warning(f"Failed to find referral path for {connection_type}: {e}")
                    continue
            
            return referral_paths[:5]  # Return top 5 paths
            
        except Exception as e:
            logger.error(f"Error finding goal referral paths: {e}")
            return []
    
    def get_goal_recent_activity(self, goal_id: UUID, user_id: UUID) -> List[Dict[str, Any]]:
        """Get recent activity related to this goal"""
        try:
            # Get recent tasks for this goal
            recent_tasks = (
                self.db.query(Task)
                .filter(
                    and_(
                        Task.goal_id == goal_id,
                        Task.updated_at >= datetime.utcnow() - timedelta(days=30)
                    )
                )
                .order_by(desc(Task.updated_at))
                .limit(10)
                .all()
            )
            
            activity = []
            
            for task in recent_tasks:
                activity.append({
                    "type": "task_update",
                    "description": f"Task '{task.title}' {task.status}",
                    "timestamp": task.updated_at.isoformat() if task.updated_at else None,
                    "related_id": str(task.task_id)
                })
            
            # Get recent interactions with goal-related people
            goal = self.get_by_id_and_user(goal_id, user_id)
            if goal and goal.related_people:
                recent_interactions = (
                    self.db.query(Interaction)
                    .filter(
                        and_(
                            Interaction.user_id == user_id,
                            Interaction.person_id.in_(goal.related_people),
                            Interaction.date >= datetime.utcnow() - timedelta(days=30)
                        )
                    )
                    .order_by(desc(Interaction.date))
                    .limit(5)
                    .all()
                )
                
                for interaction in recent_interactions:
                    activity.append({
                        "type": "interaction",
                        "description": f"{interaction.interaction_type} with related contact",
                        "timestamp": interaction.date.isoformat() if interaction.date else None,
                        "related_id": str(interaction.person_id)
                    })
            
            # Sort by timestamp and return recent activity
            activity.sort(key=lambda x: x["timestamp"] or "", reverse=True)
            return activity[:10]
            
        except Exception as e:
            logger.error(f"Error getting goal recent activity: {e}")
            return []
    
    def get_milestone_progress(self, goal_id: UUID, user_id: UUID) -> Dict[str, Any]:
        """Get milestone progress tracking for goal"""
        goal = self.get_by_id_and_user(goal_id, user_id)
        if not goal:
            return {}
        
        try:
            # Get all tasks for this goal
            tasks = (
                self.db.query(Task)
                .filter(Task.goal_id == goal_id)
                .order_by(Task.created_at)
                .all()
            )
            
            # Calculate milestone progress
            total_tasks = len(tasks)
            completed_tasks = len([t for t in tasks if t.status == "completed"])
            in_progress_tasks = len([t for t in tasks if t.status == "in_progress"])
            pending_tasks = total_tasks - completed_tasks - in_progress_tasks
            
            # Calculate progress percentage
            progress_percentage = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
            
            # Analyze task completion timeline
            completed_task_dates = [
                t.completion_date for t in tasks 
                if t.status == "completed" and t.completion_date
            ]
            
            # Calculate velocity (tasks completed per week)
            if len(completed_task_dates) >= 2:
                date_range = max(completed_task_dates) - min(completed_task_dates)
                weeks = max(1, date_range.days / 7)
                velocity = len(completed_task_dates) / weeks
            else:
                velocity = 0.5  # Default velocity
            
            # Estimate completion date
            remaining_tasks = pending_tasks + in_progress_tasks
            estimated_weeks = remaining_tasks / velocity if velocity > 0 else remaining_tasks * 2
            estimated_completion = datetime.utcnow() + timedelta(weeks=estimated_weeks)
            
            # Success criteria progress
            success_criteria = goal.success_criteria
            criteria_progress = []
            
            for i, criterion in enumerate(success_criteria):
                # Simple heuristic: assume each criterion is tied to tasks
                tasks_per_criterion = total_tasks / len(success_criteria) if success_criteria else 0
                criterion_completed_tasks = min(completed_tasks - (i * tasks_per_criterion), tasks_per_criterion)
                criterion_progress = (criterion_completed_tasks / tasks_per_criterion * 100) if tasks_per_criterion > 0 else 0
                
                criteria_progress.append({
                    "criterion": criterion,
                    "progress_percentage": max(0, min(100, criterion_progress))
                })
            
            return {
                "overall_progress_percentage": round(progress_percentage, 1),
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "in_progress_tasks": in_progress_tasks,
                "pending_tasks": pending_tasks,
                "velocity_tasks_per_week": round(velocity, 2),
                "estimated_completion_date": estimated_completion.isoformat(),
                "success_criteria_progress": criteria_progress,
                "on_track": progress_percentage >= 70 or velocity >= 1.0
            }
            
        except Exception as e:
            logger.error(f"Error calculating milestone progress: {e}")
            return {}
    
    def get_recommended_connections(self, goal_id: UUID, user_id: UUID) -> List[Dict[str, Any]]:
        """Get AI-recommended connections for goal achievement"""
        goal = self.get_by_id_and_user(goal_id, user_id)
        if not goal:
            return []
        
        try:
            # Get user's existing network
            existing_connections = (
                self.db.query(Person)
                .filter(Person.user_id == user_id)
                .all()
            )
            
            # Get required connection types
            required_connections = self._get_required_connections(goal)
            
            recommendations = []
            
            # Analyze existing network for second-degree connections
            for person in existing_connections:
                # Get this person's connections (people they know)
                person_connections = (
                    self.db.query(Knows)
                    .filter(Knows.person_id == person.person_id)
                    .all()
                )
                
                for connection in person_connections:
                    # Get the known person's details
                    known_person = (
                        self.db.query(Person)
                        .filter(Person.person_id == connection.known_person_id)
                        .first()
                    )
                    
                    if known_person:
                        # Score relevance based on goal requirements
                        relevance_score = self._calculate_connection_relevance(
                            known_person, required_connections, goal
                        )
                        
                        if relevance_score > 0.3:  # Only include relevant connections
                            recommendations.append({
                                "person_id": str(known_person.person_id),
                                "name": f"{known_person.first_name} {known_person.last_name}".strip(),
                                "connection_type": self._infer_connection_type(known_person),
                                "relevance_score": round(relevance_score, 2),
                                "referral_path": [
                                    {
                                        "person_id": str(person.person_id),
                                        "name": f"{person.first_name} {person.last_name}".strip(),
                                        "relationship_strength": connection.trust_level or 3
                                    }
                                ],
                                "recommendation_reason": self._get_recommendation_reason(
                                    known_person, required_connections
                                )
                            })
            
            # Sort by relevance score and return top recommendations
            recommendations.sort(key=lambda x: x["relevance_score"], reverse=True)
            return recommendations[:10]
            
        except Exception as e:
            logger.error(f"Error getting recommended connections: {e}")
            return []
    
    def _calculate_connection_relevance(
        self, person: Person, required_connections: List[str], goal: Goal
    ) -> float:
        """Calculate how relevant a person is to the goal"""
        score = 0.0
        
        # Check if person's details match required connection types
        person_details = person.details or {}
        person_text = (
            f"{person.first_name} {person.last_name} "
            f"{person_details.get('title', '')} "
            f"{person_details.get('company', '')} "
            f"{person_details.get('industry', '')}"
        ).lower()
        
        # Score based on matching required connection types
        for conn_type in required_connections:
            conn_keywords = self._get_connection_keywords(conn_type)
            for keyword in conn_keywords:
                if keyword in person_text:
                    score += 0.3
                    break
        
        # Bonus for industry alignment
        goal_text = (goal.title + " " + (goal.description or "")).lower()
        if person_details.get('industry'):
            industry = person_details['industry'].lower()
            if any(word in goal_text for word in industry.split()):
                score += 0.2
        
        return min(1.0, score)
    
    def _get_connection_keywords(self, connection_type: str) -> List[str]:
        """Get keywords associated with connection types"""
        keyword_map = {
            "investor": ["investor", "investment", "venture", "capital", "fund"],
            "mentor": ["mentor", "advisor", "coach", "guide", "senior"],
            "entrepreneur": ["entrepreneur", "founder", "ceo", "startup", "business"],
            "recruiter": ["recruiter", "hr", "talent", "hiring", "recruitment"],
            "marketing_expert": ["marketing", "growth", "digital", "brand", "advertising"]
        }
        
        return keyword_map.get(connection_type, [connection_type.replace("_", " ")])
    
    def _infer_connection_type(self, person: Person) -> str:
        """Infer connection type from person's details"""
        person_details = person.details or {}
        title = person_details.get('title', '').lower()
        company = person_details.get('company', '').lower()
        
        if any(word in title for word in ['ceo', 'founder', 'entrepreneur']):
            return 'entrepreneur'
        elif any(word in title for word in ['investor', 'venture', 'capital']):
            return 'investor'
        elif any(word in title for word in ['recruiter', 'hr', 'talent']):
            return 'recruiter'
        elif any(word in title for word in ['marketing', 'growth', 'digital']):
            return 'marketing_expert'
        elif any(word in title for word in ['senior', 'director', 'vp', 'manager']):
            return 'industry_professional'
        else:
            return 'professional'
    
    def _get_recommendation_reason(self, person: Person, required_connections: List[str]) -> str:
        """Generate reason for recommending this connection"""
        person_details = person.details or {}
        title = person_details.get('title', '')
        company = person_details.get('company', '')
        
        if title and company:
            return f"Has relevant experience as {title} at {company}"
        elif title:
            return f"Professional background as {title}"
        elif company:
            return f"Works at {company}"
        else:
            return "May have relevant industry connections"
    
    def predict_goal_success(self, goal_id: UUID, user_id: UUID) -> Dict[str, Any]:
        """Predict goal success probability using AI and statistical analysis"""
        goal = self.get_by_id_and_user(goal_id, user_id)
        if not goal:
            return {"prediction": "unknown", "confidence": 0.0}
        
        try:
            # Get goal progress data
            milestone_progress = self.get_milestone_progress(goal_id, user_id)
            
            # Get network strength
            network_size = (
                self.db.query(func.count(Person.person_id))
                .filter(Person.user_id == user_id)
                .scalar()
            ) or 0
            
            # Calculate success probability based on multiple factors
            factors = {
                "progress_factor": milestone_progress.get("overall_progress_percentage", 0) / 100,
                "velocity_factor": min(1.0, milestone_progress.get("velocity_tasks_per_week", 0) / 2),
                "network_factor": min(1.0, network_size / 50),
                "priority_factor": goal.priority / 5.0,
                "timeline_factor": self._calculate_timeline_factor(goal)
            }
            
            # Weighted average of factors
            weights = {
                "progress_factor": 0.3,
                "velocity_factor": 0.25,
                "network_factor": 0.2,
                "priority_factor": 0.15,
                "timeline_factor": 0.1
            }
            
            success_probability = sum(
                factors[factor] * weights[factor] for factor in factors
            )
            
            # Clamp between 0.1 and 0.95
            success_probability = max(0.1, min(0.95, success_probability))
            
            # Determine prediction category
            if success_probability >= 0.8:
                prediction = "very_likely"
            elif success_probability >= 0.6:
                prediction = "likely"
            elif success_probability >= 0.4:
                prediction = "possible"
            else:
                prediction = "challenging"
            
            # Calculate confidence based on data quality
            data_quality = (
                (1.0 if milestone_progress.get("total_tasks", 0) > 0 else 0.5) +
                (1.0 if network_size > 10 else 0.7) +
                (1.0 if goal.target_date else 0.8)
            ) / 3
            
            return {
                "prediction": prediction,
                "success_probability": round(success_probability, 2),
                "confidence": round(data_quality, 2),
                "factors": {
                    "progress_score": round(factors["progress_factor"], 2),
                    "velocity_score": round(factors["velocity_factor"], 2),
                    "network_score": round(factors["network_factor"], 2),
                    "priority_score": round(factors["priority_factor"], 2),
                    "timeline_score": round(factors["timeline_factor"], 2)
                },
                "key_success_drivers": self._get_success_drivers(factors),
                "improvement_suggestions": self._get_improvement_suggestions(factors)
            }
            
        except Exception as e:
            logger.error(f"Error predicting goal success: {e}")
            return {
                "prediction": "unknown",
                "success_probability": 0.5,
                "confidence": 0.0,
                "error": str(e)
            }
    
    def _calculate_timeline_factor(self, goal: Goal) -> float:
        """Calculate factor based on timeline reasonableness"""
        if not goal.target_date:
            return 0.5  # Neutral if no target date
        
        time_remaining = goal.target_date - datetime.utcnow()
        days_remaining = time_remaining.days
        
        if days_remaining < 0:
            return 0.2  # Past due
        elif days_remaining < 30:
            return 0.6  # Very tight timeline
        elif days_remaining < 90:
            return 0.8  # Reasonable timeline
        else:
            return 1.0  # Plenty of time
    
    def _get_success_drivers(self, factors: Dict[str, float]) -> List[str]:
        """Identify key success drivers based on factor scores"""
        drivers = []
        
        if factors["progress_factor"] >= 0.7:
            drivers.append("Strong progress momentum")
        if factors["velocity_factor"] >= 0.7:
            drivers.append("High task completion velocity")
        if factors["network_factor"] >= 0.7:
            drivers.append("Strong professional network")
        if factors["priority_factor"] >= 0.8:
            drivers.append("High goal priority")
        
        if not drivers:
            drivers.append("Consistent effort and focus")
        
        return drivers
    
    def _get_improvement_suggestions(self, factors: Dict[str, float]) -> List[str]:
        """Get suggestions for improving success probability"""
        suggestions = []
        
        if factors["progress_factor"] < 0.5:
            suggestions.append("Focus on completing more tasks to build momentum")
        if factors["velocity_factor"] < 0.5:
            suggestions.append("Increase task completion rate through better time management")
        if factors["network_factor"] < 0.5:
            suggestions.append("Expand professional network to access more opportunities")
        if factors["timeline_factor"] < 0.5:
            suggestions.append("Review and adjust timeline expectations")
        
        if len(suggestions) == 0:
            suggestions.append("Continue current approach and maintain consistency")
        
        return suggestions
    
    def generate_goal_activity_feed(self, goal_id: UUID, user_id: UUID, limit: int = 20) -> List[Dict[str, Any]]:
        """Generate comprehensive activity feed for goal"""
        try:
            activities = []
            
            # Get recent activity
            recent_activity = self.get_goal_recent_activity(goal_id, user_id)
            activities.extend(recent_activity)
            
            # Add milestone achievements
            milestone_progress = self.get_milestone_progress(goal_id, user_id)
            if milestone_progress.get("completed_tasks", 0) > 0:
                activities.append({
                    "type": "milestone",
                    "description": f"{milestone_progress['completed_tasks']} of {milestone_progress['total_tasks']} tasks completed",
                    "timestamp": datetime.utcnow().isoformat(),
                    "progress_percentage": milestone_progress["overall_progress_percentage"]
                })
            
            # Add AI insights
            ai_analysis = self.get_goal_ai_analysis(goal_id, user_id)
            if ai_analysis and not ai_analysis.get("error"):
                activities.append({
                    "type": "ai_insight",
                    "description": f"AI suggests: {ai_analysis.get('recommended_approach', 'Focus on systematic progress')}",
                    "timestamp": datetime.utcnow().isoformat(),
                    "insights": ai_analysis.get("next_steps", [])
                })
            
            # Sort by timestamp and limit results
            activities.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            return activities[:limit]
            
        except Exception as e:
            logger.error(f"Error generating goal activity feed: {e}")
            return []
