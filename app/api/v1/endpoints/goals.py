"""
Goal management endpoints
"""

from typing import Any, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api.deps import get_current_user, get_db
from app.models.goal import GoalStatus
from app.models.user import User
from app.schemas.goal import Goal, GoalCreate, GoalUpdate
from app.services.goal_service import GoalService
from uuid import UUID as UUID_TYPE


def validate_uuid(uuid_string: str) -> None:
    """Validate UUID format and raise 400 if invalid"""
    try:
        UUID_TYPE(uuid_string)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid UUID format")

router = APIRouter()


@router.get("/", response_model=List[Goal])
async def get_goals(
    status: Optional[GoalStatus] = Query(None, description="Filter by goal status"),
    skip: int = Query(0, ge=0, description="Number of goals to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of goals to return"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get user goals with optional filtering
    GET /api/v1/goals/
    """
    goal_service = GoalService(db)
    goals = goal_service.get_by_user(
        user_id=current_user.user_id, status=status, skip=skip, limit=limit
    )
    # Convert SQLAlchemy objects to Pydantic schemas
    return [Goal.model_validate(goal) for goal in goals]


@router.post("/", response_model=Goal)
async def create_goal(
    goal_data: GoalCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Create a new goal
    POST /api/v1/goals/
    """
    goal_service = GoalService(db)

    # Create goal using the service's create method
    # Add user_id to the goal data
    goal_dict = goal_data.dict()
    goal_dict["user_id"] = str(current_user.user_id)

    # Create goal directly using the model
    from app.models.goal import Goal
    goal = Goal(**goal_dict)

    db.add(goal)
    db.commit()
    db.refresh(goal)
    return goal


@router.get("/{goal_id}", response_model=Goal)
async def get_goal(
    goal_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get specific goal
    GET /api/v1/goals/{id}
    """
    validate_uuid(goal_id)
    goal_service = GoalService(db)

    goal = goal_service.get_by_id_and_user(UUID(goal_id), current_user.user_id)
    if not goal:
        raise HTTPException(status_code=404, detail="Goal not found")

    # Convert SQLAlchemy object to Pydantic schema
    return Goal.model_validate(goal)


@router.get("/{goal_id}/dashboard", response_model=dict)
async def get_goal_dashboard(
    goal_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get goal dashboard with comprehensive data
    GET /api/v1/goals/{id}/dashboard
    """
    validate_uuid(goal_id)
    goal_service = GoalService(db)

    dashboard_data = goal_service.get_goal_dashboard_data(UUID(goal_id), current_user.user_id)
    if not dashboard_data:
        raise HTTPException(status_code=404, detail="Goal not found")

    return dashboard_data


@router.put("/{goal_id}", response_model=Goal)
async def update_goal(
    goal_id: str,
    goal_update: GoalUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Update goal
    PUT /api/v1/goals/{id}
    """
    validate_uuid(goal_id)
    goal_service = GoalService(db)

    goal = goal_service.get_by_id_and_user(UUID(goal_id), current_user.user_id)
    if not goal:
        raise HTTPException(status_code=404, detail="Goal not found")

    updated_goal = goal_service.update(UUID(goal_id), goal_update.dict(exclude_unset=True))
    # Convert SQLAlchemy object to Pydantic schema
    return Goal.model_validate(updated_goal)


@router.delete("/{goal_id}")
async def delete_goal(
    goal_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Delete goal
    DELETE /api/v1/goals/{id}
    """
    validate_uuid(goal_id)
    goal_service = GoalService(db)

    goal = goal_service.get_by_id_and_user(UUID(goal_id), current_user.user_id)
    if not goal:
        raise HTTPException(status_code=404, detail="Goal not found")

    goal_service.delete(goal)
    return {"message": "Goal deleted successfully"}


@router.post("/{goal_id}/complete", response_model=Goal)
async def complete_goal(
    goal_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Mark goal as completed
    POST /api/v1/goals/{id}/complete
    """
    validate_uuid(goal_id)
    goal_service = GoalService(db)

    goal = goal_service.mark_completed(UUID(goal_id), current_user.user_id)
    if not goal:
        raise HTTPException(status_code=404, detail="Goal not found")

    return goal
