"""
<PERSON><PERSON><PERSON> (AI Assistant) endpoints
LLM-powered intelligent assistant with function calling capabilities
"""

from typing import Any, Optional
import uuid
from datetime import datetime

from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session

from app.api.deps import get_current_user
from app.core.database import get_db
from app.models.user import User
from app.services.intelligent_copilot import IntelligentCopilot
from app.services.ai_engine_service import AIEngineService
from app.core.config import settings

router = APIRouter()


@router.post("/converse", response_model=dict)
async def converse_with_copilot(
    message_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Handle conversational input with AI copilot using LLM and function calling
    POST /api/v1/copilot/converse
    """
    try:
        message = message_data.get("message", "")
        conversation_id = message_data.get("conversation_id")
        context = message_data.get("context", {})

        if not message:
            return {
                "response": "I'm here to help! Please ask me something about your network, goals, or any networking questions you have.",
                "conversation_id": str(uuid.uuid4()),
                "confidence": 1.0,
                "timestamp": datetime.utcnow().isoformat()
            }

        # Check if OpenAI API key is configured
        if not settings.OPENAI_API_KEY:
            return {
                "response": "I apologize, but the AI assistant is not configured. Please contact your administrator.",
                "conversation_id": conversation_id or str(uuid.uuid4()),
                "error": "OpenAI API key not configured",
                "timestamp": datetime.utcnow().isoformat()
            }

        # Initialize intelligent copilot
        copilot = IntelligentCopilot(db, current_user)

        # Process the message with LLM and function calling
        response = await copilot.process_message(
            message=message,
            conversation_id=conversation_id,
            context=context
        )

        return response

    except Exception as e:
        return {
            "response": "I apologize, but I encountered an error processing your request. Please try again.",
            "conversation_id": conversation_id or str(uuid.uuid4()),
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


@router.get("/suggestions", response_model=dict)
async def get_ai_suggestions(
    suggestion_type: Optional[str] = Query(None, alias="type"),
    priority: Optional[str] = Query(None),
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Get AI-generated proactive suggestions using LLM analysis
    GET /api/v1/copilot/suggestions
    """
    try:
        # Check if OpenAI API key is configured
        if not settings.OPENAI_API_KEY:
            # Fallback to basic AI engine suggestions
            ai_engine = AIEngineService(db)
            suggestions = ai_engine.get_active_suggestions(current_user.user_id)

            # Apply filters
            if suggestion_type:
                suggestions = [s for s in suggestions if s.get("type") == suggestion_type]
            if priority:
                suggestions = [s for s in suggestions if s.get("priority") == priority]

            return {
                "suggestions": suggestions[:limit],
                "source": "basic_ai_engine",
                "timestamp": datetime.now().isoformat()
            }

        # Use intelligent copilot for enhanced suggestions
        copilot = IntelligentCopilot(db, current_user)

        # Get proactive suggestions with LLM analysis
        result = await copilot.get_proactive_suggestions(
            suggestion_type=suggestion_type,
            limit=limit
        )

        return result

    except Exception as e:
        # Fallback to basic suggestions on error
        try:
            ai_engine = AIEngineService(db)
            suggestions = ai_engine.get_active_suggestions(current_user.user_id)
            return {
                "suggestions": suggestions[:limit],
                "source": "fallback",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        except:
            return {
                "suggestions": [],
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }


# Additional endpoints for enhanced copilot functionality

@router.post("/analyze", response_model=dict)
async def analyze_message_intent(
    message_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Analyze message intent and entities without executing actions
    POST /api/v1/copilot/analyze
    """
    try:
        message = message_data.get("message", "")

        if not message:
            raise HTTPException(status_code=400, detail="Message is required")

        if not settings.OPENAI_API_KEY:
            return {
                "intent": "unknown",
                "confidence": 0.0,
                "entities": [],
                "error": "AI analysis not available - OpenAI API key not configured"
            }

        # Initialize intelligent copilot
        copilot = IntelligentCopilot(db, current_user)

        # Analyze intent
        analysis = await copilot.analyze_conversation_intent(message)

        return analysis

    except Exception as e:
        return {
            "intent": "unknown",
            "confidence": 0.0,
            "entities": [],
            "error": str(e)
        }


@router.get("/conversation/{conversation_id}", response_model=dict)
async def get_conversation_history(
    conversation_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Get conversation history for a specific conversation
    GET /api/v1/copilot/conversation/{conversation_id}
    """
    try:
        if not settings.OPENAI_API_KEY:
            return {
                "conversation_id": conversation_id,
                "messages": [],
                "error": "Conversation history not available - OpenAI API key not configured"
            }

        # Initialize intelligent copilot
        copilot = IntelligentCopilot(db, current_user)

        # Get conversation history
        messages = copilot.get_conversation_history(conversation_id)

        return {
            "conversation_id": conversation_id,
            "messages": messages,
            "message_count": len(messages)
        }

    except Exception as e:
        return {
            "conversation_id": conversation_id,
            "messages": [],
            "error": str(e)
        }


@router.delete("/conversation/{conversation_id}", response_model=dict)
async def clear_conversation_history(
    conversation_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Clear conversation history for a specific conversation
    DELETE /api/v1/copilot/conversation/{conversation_id}
    """
    try:
        if not settings.OPENAI_API_KEY:
            return {
                "success": False,
                "error": "Conversation management not available - OpenAI API key not configured"
            }

        # Initialize intelligent copilot
        copilot = IntelligentCopilot(db, current_user)

        # Clear conversation
        copilot.clear_conversation(conversation_id)

        return {
            "success": True,
            "conversation_id": conversation_id,
            "message": "Conversation history cleared"
        }

    except Exception as e:
        return {
            "success": False,
            "conversation_id": conversation_id,
            "error": str(e)
        }
