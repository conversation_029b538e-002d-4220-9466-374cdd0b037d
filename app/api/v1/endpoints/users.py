"""
User management endpoints
"""

from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.deps import get_current_user
from app.core.database import get_db
from app.models.user import User
from app.schemas.user import User as UserSchema
from app.schemas.user import UserProfile, UserUpdate
from app.services.user_service import UserService

router = APIRouter()


@router.get("/me", response_model=UserProfile)
def get_current_user_profile(
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Get current user profile
    """
    return UserProfile(
        user_id=current_user.user_id,
        email=current_user.email,
        first_name=current_user.first_name,
        last_name=current_user.last_name,
        full_name=f"{current_user.first_name} {current_user.last_name}",
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        created_at=current_user.created_at,
        last_login=current_user.last_login,
        settings=current_user.settings or {},
    )


@router.put("/me", response_model=UserSchema)
def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Update current user profile
    """
    user_service = UserService(db)

    # Update user
    updated_user = user_service.update(current_user.user_id, user_update)
    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
        )

    return updated_user


@router.put("/me/preferences", response_model=UserSchema)
def update_user_preferences(
    preferences: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Update user relationship decision preferences
    Based on technical documentation: stated_decision_factors
    """
    user_service = UserService(db)

    # Validate preferences structure
    expected_factors = [
        "emotional_weight",
        "value_weight",
        "trust_weight",
        "information_weight",
        "role_weight",
        "coercive_weight",
    ]

    # Ensure all weights are between 0 and 1 and sum to 1
    total_weight = 0
    for factor in expected_factors:
        if factor not in preferences:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Missing preference factor: {factor}",
            )
        weight = preferences[factor]
        if not isinstance(weight, (int, float)) or weight < 0 or weight > 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid weight for {factor}: must be between 0 and 1",
            )
        total_weight += weight

    if abs(total_weight - 1.0) > 0.01:  # Allow small floating point errors
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Preference weights must sum to 1.0",
        )

    # Update user settings with preferences
    current_settings = current_user.settings or {}
    current_settings["stated_decision_factors"] = preferences

    updated_user = user_service.update_settings(
        current_user.user_id, current_settings
    )
    return updated_user


@router.get("/me/calibration-request")
def get_calibration_request(
    current_user: User = Depends(get_current_user), db: Session = Depends(get_db)
) -> Any:
    """
    Check if there are pending preference calibration requests
    Based on technical documentation: preference calibration suggestion
    """
    # TODO: Implement AI logic to detect preference misalignment
    # For now, return a placeholder response
    return {
        "has_pending_calibration": False,
        "calibration_data": None,
        "message": "No pending calibration requests",
    }


@router.post("/me/calibrate")
def process_calibration(
    calibration_response: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Process user response to preference calibration suggestion
    """
    # TODO: Implement calibration processing logic
    return {
        "status": "processed",
        "message": "Calibration response processed successfully",
    }
