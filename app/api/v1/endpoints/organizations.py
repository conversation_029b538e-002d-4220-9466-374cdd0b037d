"""
Organization management endpoints
"""

import uuid
from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.api.deps import get_current_user
from app.core.database import get_db
from app.models.user import User
from app.schemas.organization import (
    OrganizationCreate,
    OrganizationUpdate,
    OrganizationResponse,
    OrganizationSummary,
    OrganizationWithEmployees,
    OrganizationSearch,
    OrganizationStats,
    WorksAtCreate,
    WorksAtUpdate,
    WorksAtResponse
)
from app.services.organization_service import OrganizationService

router = APIRouter()


@router.post("/", response_model=OrganizationResponse, status_code=status.HTTP_201_CREATED)
def create_organization(
    org_data: OrganizationCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> OrganizationResponse:
    """Create a new organization"""
    try:
        service = OrganizationService(db)
        return service.create_organization(org_data, current_user.user_id)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/", response_model=List[OrganizationSummary])
def get_organizations(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    query: str = Query(None, description="Search query"),
    industry: str = Query(None, description="Filter by industry"),
    size: str = Query(None, description="Filter by size"),
    location: str = Query(None, description="Filter by location"),
    has_connections: bool = Query(None, description="Filter by connection status"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> List[OrganizationSummary]:
    """Get list of organizations with optional search and filtering"""
    service = OrganizationService(db)
    search_params = None
    
    if any([query, industry, size, location, has_connections is not None]):
        search_params = OrganizationSearch(
            query=query,
            industry=industry,
            size=size,
            location=location,
            has_connections=has_connections
        )
    
    return service.get_organizations(
        user_id=current_user.user_id,
        skip=skip,
        limit=limit,
        search_params=search_params
    )


@router.get("/{org_id}", response_model=OrganizationResponse)
def get_organization(
    org_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> OrganizationResponse:
    """Get organization by ID"""
    service = OrganizationService(db)
    organization = service.get_organization_by_id(org_id, current_user.user_id)
    
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Organization not found"
        )
    
    return organization


@router.put("/{org_id}", response_model=OrganizationResponse)
def update_organization(
    org_id: uuid.UUID,
    org_data: OrganizationUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> OrganizationResponse:
    """Update organization"""
    try:
        service = OrganizationService(db)
        organization = service.update_organization(org_id, org_data, current_user.user_id)
        
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
        return organization
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{org_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_organization(
    org_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> None:
    """Delete organization"""
    try:
        service = OrganizationService(db)
        success = service.delete_organization(org_id, current_user.user_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/{org_id}/employees", response_model=OrganizationWithEmployees)
def get_organization_with_employees(
    org_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> OrganizationWithEmployees:
    """Get organization with its employees"""
    service = OrganizationService(db)
    organization = service.get_organization_with_employees(org_id, current_user.user_id)
    
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Organization not found"
        )
    
    return organization


@router.get("/stats/summary", response_model=OrganizationStats)
def get_organization_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> OrganizationStats:
    """Get organization statistics for the user"""
    service = OrganizationService(db)
    return service.get_organization_stats(current_user.user_id)


# Work relationship endpoints

@router.post("/work-relationships", response_model=WorksAtResponse, status_code=status.HTTP_201_CREATED)
def create_work_relationship(
    work_data: WorksAtCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> WorksAtResponse:
    """Create a work relationship between person and organization"""
    try:
        service = OrganizationService(db)
        return service.create_work_relationship(work_data, current_user.user_id)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/work-relationships/{person_id}/{org_id}", response_model=WorksAtResponse)
def update_work_relationship(
    person_id: uuid.UUID,
    org_id: uuid.UUID,
    work_data: WorksAtUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> WorksAtResponse:
    """Update a work relationship"""
    try:
        service = OrganizationService(db)
        work_relationship = service.update_work_relationship(
            person_id, org_id, work_data, current_user.user_id
        )
        
        if not work_relationship:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Work relationship not found"
            )
        
        return work_relationship
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/work-relationships/{person_id}/{org_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_work_relationship(
    person_id: uuid.UUID,
    org_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> None:
    """Delete a work relationship"""
    try:
        service = OrganizationService(db)
        success = service.delete_work_relationship(person_id, org_id, current_user.user_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Work relationship not found"
            )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/persons/{person_id}/work-history", response_model=List[WorksAtResponse])
def get_person_work_history(
    person_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> List[WorksAtResponse]:
    """Get work history for a specific person"""
    service = OrganizationService(db)
    return service.get_person_work_history(person_id, current_user.user_id)
