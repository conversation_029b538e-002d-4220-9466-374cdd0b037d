"""
Graph visualization and network analysis endpoints
"""

import uuid
from typing import Any, List

from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app.api.deps import get_current_user
from app.core.database import get_db
from app.models.user import User
from app.services.graph_service import GraphService

router = APIRouter()


@router.get("/full", response_model=dict)
async def get_full_graph(
    layout: str = Query("force_directed", description="Layout algorithm: force_directed, circular, hierarchical, radial"),
    current_user: User = Depends(get_current_user), 
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Get complete network graph data for visualization
    GET /api/v1/graph/full?layout=force_directed
    """
    # Convert AsyncSession to Session for graph service
    sync_db = db.sync_session if hasattr(db, 'sync_session') else db
    graph_service = GraphService(sync_db)
    
    try:
        graph_data = await graph_service.generate_full_network_graph(
            current_user.user_id, layout_type=layout
        )
        return graph_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating graph: {str(e)}")


@router.get("/filtered", response_model=dict)
async def get_filtered_graph(
    tags: str = Query(None, description="Comma-separated list of tags to filter by"),
    archetype: str = Query(None, description="Filter by relationship archetype"),
    organization: str = Query(None, description="Filter by organization"),
    min_score: int = Query(None, ge=0, le=100, description="Minimum relationship score"),
    max_nodes: int = Query(None, ge=1, le=1000, description="Maximum number of nodes"),
    layout: str = Query("force_directed", description="Layout algorithm"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get filtered network graph data
    GET /api/v1/graph/filtered?tags=tech,startup&archetype=colleague&max_nodes=50
    """
    sync_db = db.sync_session if hasattr(db, 'sync_session') else db
    graph_service = GraphService(sync_db)
    
    try:
        graph_data = await graph_service.generate_filtered_network_graph(
            user_id=current_user.user_id,
            tags=tags.split(",") if tags else None,
            archetype=archetype,
            organization=organization,
            min_relationship_score=min_score,
            max_nodes=max_nodes,
            layout_type=layout
        )
        return graph_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating filtered graph: {str(e)}")


@router.get("/node/{person_id}", response_model=dict)
async def get_node_details(
    person_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Get detailed information about a specific node
    GET /api/v1/graph/node/{person_id}
    """
    sync_db = db.sync_session if hasattr(db, 'sync_session') else db
    graph_service = GraphService(sync_db)
    
    try:
        person_uuid = uuid.UUID(person_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid person ID format")
    
    try:
        node_details = await graph_service.get_node_details(current_user.user_id, person_uuid)
        return node_details
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting node details: {str(e)}")


@router.get("/clusters", response_model=dict)
async def get_graph_clusters(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Get graph clusters/communities analysis
    GET /api/v1/graph/clusters
    """
    sync_db = db.sync_session if hasattr(db, 'sync_session') else db
    graph_service = GraphService(sync_db)
    
    try:
        clusters = await graph_service.get_graph_clusters(current_user.user_id)
        return clusters
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error analyzing clusters: {str(e)}")


@router.get("/stats", response_model=dict)
async def get_graph_statistics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Get network graph statistics and analytics
    GET /api/v1/graph/stats
    """
    sync_db = db.sync_session if hasattr(db, 'sync_session') else db
    graph_service = GraphService(sync_db)
    
    try:
        # Generate full graph to get complete statistics
        graph_data = await graph_service.generate_full_network_graph(current_user.user_id)
        
        # Extract statistics
        metadata = graph_data.get("metadata", {})
        nodes = graph_data.get("nodes", [])
        edges = graph_data.get("edges", [])
        
        # Calculate archetype distribution
        archetype_counts = {}
        relationship_scores = []
        
        for node in nodes:
            if node.get("type") == "person":
                archetype = node.get("data", {}).get("archetype", "unknown")
                archetype_counts[archetype] = archetype_counts.get(archetype, 0) + 1
                
                score = node.get("data", {}).get("relationship_score", 0)
                if score > 0:
                    relationship_scores.append(score)
        
        # Calculate statistics
        avg_relationship_score = sum(relationship_scores) / len(relationship_scores) if relationship_scores else 0
        strong_relationships = len([s for s in relationship_scores if s >= 70])
        weak_relationships = len([s for s in relationship_scores if s < 50])
        
        return {
            "network_overview": {
                "total_connections": metadata.get("total_nodes", 0) - 1,  # Exclude user node
                "total_relationships": metadata.get("total_edges", 0),
                "network_density": metadata.get("density", 0),
                "clustering_coefficient": metadata.get("clustering", 0),
                "average_path_length": metadata.get("average_path_length", 0)
            },
            "relationship_strength": {
                "average_score": round(avg_relationship_score, 2),
                "strong_relationships": strong_relationships,
                "weak_relationships": weak_relationships,
                "distribution": {
                    "strong (70-100)": strong_relationships,
                    "medium (50-69)": len([s for s in relationship_scores if 50 <= s < 70]),
                    "weak (0-49)": weak_relationships
                }
            },
            "archetype_distribution": archetype_counts,
            "generated_at": metadata.get("generated_at")
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating statistics: {str(e)}")
