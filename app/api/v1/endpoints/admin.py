"""
Admin endpoints for testing and maintenance
"""

from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.deps import get_current_user
from app.core.database import get_db
from app.models.user import User
from app.models.person import Person
from app.models.goal import Goal
from app.models.task import Task
from app.models.organization import Organization
from app.models.interaction import Interaction
from app.models.note import Note
from app.models.tag import Tag
from app.models.relationship import Knows

router = APIRouter()


@router.post("/reset-test-data", response_model=dict)
async def reset_test_data(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Reset all test data for the current user
    WARNING: This will delete ALL user data!
    """
    try:
        user_id = current_user.user_id
        
        # Delete all user-related data in correct order (respecting foreign keys)
        # 1. Delete relationships first
        db.query(Knows).filter(Knows.user_id == user_id).delete()
        
        # 2. Delete interactions and notes
        db.query(Interaction).filter(Interaction.user_id == user_id).delete()
        db.query(Note).filter(Note.user_id == user_id).delete()
        
        # 3. Delete tasks (they reference goals)
        db.query(Task).filter(Task.user_id == user_id).delete()
        
        # 4. Delete goals
        db.query(Goal).filter(Goal.user_id == user_id).delete()
        
        # 5. Delete persons
        db.query(Person).filter(Person.user_id == user_id).delete()
        
        # 6. Delete organizations
        db.query(Organization).filter(Organization.user_id == user_id).delete()
        
        # 7. Delete tags
        db.query(Tag).filter(Tag.user_id == user_id).delete()
        
        # Commit all deletions
        db.commit()
        
        return {
            "message": "Test data reset successfully",
            "user_id": str(user_id),
            "status": "success"
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reset test data: {str(e)}"
        )


@router.delete("/delete-user", response_model=dict)
async def delete_user_completely(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Completely delete the current user and all their data
    WARNING: This action is irreversible!
    """
    try:
        user_id = current_user.user_id
        
        # Delete all user-related data first (same as reset)
        db.query(Knows).filter(Knows.user_id == user_id).delete()
        db.query(Interaction).filter(Interaction.user_id == user_id).delete()
        db.query(Note).filter(Note.user_id == user_id).delete()
        db.query(Task).filter(Task.user_id == user_id).delete()
        db.query(Goal).filter(Goal.user_id == user_id).delete()
        db.query(Person).filter(Person.user_id == user_id).delete()
        db.query(Organization).filter(Organization.user_id == user_id).delete()
        db.query(Tag).filter(Tag.user_id == user_id).delete()
        
        # Finally delete the user
        db.query(User).filter(User.user_id == user_id).delete()
        
        # Commit all deletions
        db.commit()
        
        return {
            "message": "User deleted completely",
            "user_id": str(user_id),
            "status": "success"
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete user: {str(e)}"
        )


@router.get("/test-data-status", response_model=dict)
async def get_test_data_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get status of test data for the current user
    """
    try:
        user_id = current_user.user_id
        
        # Count all user data
        counts = {
            "persons": db.query(Person).filter(Person.user_id == user_id).count(),
            "goals": db.query(Goal).filter(Goal.user_id == user_id).count(),
            "tasks": db.query(Task).filter(Task.user_id == user_id).count(),
            "organizations": db.query(Organization).filter(Organization.user_id == user_id).count(),
            "interactions": db.query(Interaction).filter(Interaction.user_id == user_id).count(),
            "notes": db.query(Note).filter(Note.user_id == user_id).count(),
            "relationships": db.query(Knows).filter(Knows.user_id == user_id).count(),
            "tags": db.query(Tag).filter(Tag.user_id == user_id).count(),
        }
        
        total_records = sum(counts.values())
        
        return {
            "user_id": str(user_id),
            "email": current_user.email,
            "total_records": total_records,
            "data_counts": counts,
            "status": "success"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get test data status: {str(e)}"
        )
