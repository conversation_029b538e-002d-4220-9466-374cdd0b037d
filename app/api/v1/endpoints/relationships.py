"""
Relationship management endpoints with archetype system and advanced features
"""

import uuid
from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.api.deps import get_current_user
from app.core.database import get_db
from app.models.user import User
from app.services.relationship_service import RelationshipService, RelationshipArchetype


router = APIRouter()


# Pydantic models for request/response
class RelationshipCreateRequest(BaseModel):
    """Request model for creating relationships"""
    to_person_id: str = Field(..., description="UUID of the target person")
    archetype: str = Field(..., description="Relationship archetype")
    relationship_foundation: Dict[str, Any] = Field(default_factory=dict, description="How the relationship was formed")
    custom_dimensions: Dict[str, int] = Field(default=None, description="Custom dimension scores (0-100)")
    initial_interaction: Dict[str, Any] = Field(default=None, description="Details of initial interaction")


class RelationshipUpdateRequest(BaseModel):
    """Request model for updating relationships"""
    archetype: str = Field(default=None, description="New relationship archetype")
    relationship_foundation: Dict[str, Any] = Field(default=None, description="Foundation updates")
    dimensions: Dict[str, int] = Field(default=None, description="Dimension updates (0-100)")


class DimensionUpdateRequest(BaseModel):
    """Request model for updating relationship dimensions"""
    dimensions: Dict[str, int] = Field(..., description="Dimension scores to update (0-100)")
    interaction_context: str = Field(default=None, description="Context for the dimension update")


@router.post("/{from_person_id}/relationships", response_model=dict)
async def create_relationship(
    from_person_id: str,
    relationship_data: RelationshipCreateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Create a new relationship with archetype template application
    POST /api/v1/relationships/{from_person_id}/relationships
    """
    try:
        from_person_uuid = uuid.UUID(from_person_id)
        to_person_uuid = uuid.UUID(relationship_data.to_person_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid UUID format")
    
    relationship_service = RelationshipService(db)
    
    # Validate relationship data
    validation_errors = relationship_service.validate_relationship_data({
        "archetype": relationship_data.archetype,
        "dimensions": relationship_data.custom_dimensions or {},
        "relationship_foundation": relationship_data.relationship_foundation
    })
    
    if validation_errors:
        raise HTTPException(status_code=400, detail=f"Validation errors: {'; '.join(validation_errors)}")
    
    try:
        relationship = relationship_service.create_relationship(
            from_person_id=from_person_uuid,
            to_person_id=to_person_uuid,
            user_id=current_user.user_id,
            archetype=relationship_data.archetype,
            relationship_foundation=relationship_data.relationship_foundation,
            custom_dimensions=relationship_data.custom_dimensions,
            initial_interaction=relationship_data.initial_interaction
        )
        
        return {
            "relationship_id": f"{from_person_id}-{relationship_data.to_person_id}",
            "from_person_id": str(relationship.from_person_id),
            "to_person_id": str(relationship.to_person_id),
            "archetype": relationship.archetype,
            "overall_score": relationship.overall_score,
            "dimensions": relationship.dimensions,
            "foundation": relationship.relationship_foundation,
            "created_at": relationship.created_at.isoformat() + "Z",
            "message": "Relationship created successfully with archetype template applied"
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating relationship: {str(e)}")


@router.put("/{from_person_id}/relationships/{to_person_id}", response_model=dict)
async def update_relationship(
    from_person_id: str,
    to_person_id: str,
    update_data: RelationshipUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Update an existing relationship
    PUT /api/v1/relationships/{from_person_id}/relationships/{to_person_id}
    """
    try:
        from_person_uuid = uuid.UUID(from_person_id)
        to_person_uuid = uuid.UUID(to_person_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid UUID format")
    
    relationship_service = RelationshipService(db)
    
    # Build updates dictionary
    updates = {}
    if update_data.archetype is not None:
        updates["archetype"] = update_data.archetype
    if update_data.relationship_foundation is not None:
        updates["relationship_foundation"] = update_data.relationship_foundation
    if update_data.dimensions is not None:
        updates["dimensions"] = update_data.dimensions
    
    if not updates:
        raise HTTPException(status_code=400, detail="No updates provided")
    
    try:
        relationship = relationship_service.update_relationship(
            from_person_id=from_person_uuid,
            to_person_id=to_person_uuid,
            user_id=current_user.user_id,
            updates=updates
        )
        
        if not relationship:
            raise HTTPException(status_code=404, detail="Relationship not found")
        
        return {
            "relationship_id": f"{from_person_id}-{to_person_id}",
            "from_person_id": str(relationship.from_person_id),
            "to_person_id": str(relationship.to_person_id),
            "archetype": relationship.archetype,
            "overall_score": relationship.overall_score,
            "dimensions": relationship.dimensions,
            "foundation": relationship.relationship_foundation,
            "updated_at": relationship.updated_at.isoformat() + "Z" if relationship.updated_at else None,
            "message": "Relationship updated successfully"
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating relationship: {str(e)}")


@router.delete("/{from_person_id}/relationships/{to_person_id}", status_code=204)
async def delete_relationship(
    from_person_id: str,
    to_person_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> None:
    """
    Delete a relationship
    DELETE /api/v1/relationships/{from_person_id}/relationships/{to_person_id}
    """
    try:
        from_person_uuid = uuid.UUID(from_person_id)
        to_person_uuid = uuid.UUID(to_person_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid UUID format")
    
    relationship_service = RelationshipService(db)
    
    try:
        deleted = relationship_service.delete_relationship(
            from_person_id=from_person_uuid,
            to_person_id=to_person_uuid,
            user_id=current_user.user_id
        )
        
        if not deleted:
            raise HTTPException(status_code=404, detail="Relationship not found")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting relationship: {str(e)}")


@router.get("/{from_person_id}/relationships/{to_person_id}", response_model=dict)
async def get_relationship_details(
    from_person_id: str,
    to_person_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Get detailed relationship information including archetype insights
    GET /api/v1/relationships/{from_person_id}/relationships/{to_person_id}
    """
    try:
        from_person_uuid = uuid.UUID(from_person_id)
        to_person_uuid = uuid.UUID(to_person_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid UUID format")
    
    relationship_service = RelationshipService(db)
    
    try:
        details = relationship_service.get_relationship_details(
            from_person_id=from_person_uuid,
            to_person_id=to_person_uuid,
            user_id=current_user.user_id
        )
        
        if not details:
            raise HTTPException(status_code=404, detail="Relationship not found")
        
        return details
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting relationship details: {str(e)}")


@router.post("/{from_person_id}/relationships/{to_person_id}/dimensions", response_model=dict)
async def update_relationship_dimensions(
    from_person_id: str,
    to_person_id: str,
    dimension_data: DimensionUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Update relationship dimensions based on interaction or assessment
    POST /api/v1/relationships/{from_person_id}/relationships/{to_person_id}/dimensions
    """
    try:
        from_person_uuid = uuid.UUID(from_person_id)
        to_person_uuid = uuid.UUID(to_person_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid UUID format")
    
    relationship_service = RelationshipService(db)
    
    # Add interaction context to updates
    updates = {"dimensions": dimension_data.dimensions}
    
    try:
        relationship = relationship_service.update_relationship(
            from_person_id=from_person_uuid,
            to_person_id=to_person_uuid,
            user_id=current_user.user_id,
            updates=updates
        )
        
        if not relationship:
            raise HTTPException(status_code=404, detail="Relationship not found")
        
        return {
            "relationship_id": f"{from_person_id}-{to_person_id}",
            "updated_dimensions": dimension_data.dimensions,
            "new_overall_score": relationship.overall_score,
            "all_dimensions": relationship.dimensions,
            "interaction_context": dimension_data.interaction_context,
            "updated_at": relationship.updated_at.isoformat() + "Z" if relationship.updated_at else None,
            "message": "Relationship dimensions updated successfully"
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating dimensions: {str(e)}")


@router.get("/archetypes", response_model=dict)
async def get_archetype_templates(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Get all available relationship archetype templates
    GET /api/v1/relationships/archetypes
    """
    relationship_service = RelationshipService(db)
    templates = relationship_service.get_archetype_templates()
    
    return {
        "archetypes": templates,
        "available_types": list(templates.keys()),
        "total_count": len(templates)
    }


@router.get("/archetypes/{archetype}", response_model=dict)
async def get_archetype_template(
    archetype: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Get specific archetype template details
    GET /api/v1/relationships/archetypes/{archetype}
    """
    relationship_service = RelationshipService(db)
    template = relationship_service.get_archetype_template(archetype)
    
    if not template:
        raise HTTPException(status_code=404, detail=f"Archetype template '{archetype}' not found")
    
    return {
        "archetype": archetype,
        "template": template
    }


@router.get("/{person_id}/recommendations", response_model=List[dict])
async def get_relationship_recommendations(
    person_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Get archetype recommendations for establishing a relationship with a person
    GET /api/v1/relationships/{person_id}/recommendations
    """
    try:
        person_uuid = uuid.UUID(person_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid UUID format")
    
    relationship_service = RelationshipService(db)
    
    try:
        recommendations = relationship_service.get_relationship_recommendations(
            user_id=current_user.user_id,
            person_id=person_uuid
        )
        
        return recommendations
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting recommendations: {str(e)}")


@router.post("/validate", response_model=dict)
async def validate_relationship_data(
    relationship_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Validate relationship data before creation/update
    POST /api/v1/relationships/validate
    """
    relationship_service = RelationshipService(db)
    errors = relationship_service.validate_relationship_data(relationship_data)
    
    return {
        "valid": len(errors) == 0,
        "errors": errors,
        "data": relationship_data
    }


@router.get("/stats", response_model=dict)
async def get_relationship_statistics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Get relationship statistics and archetype distribution
    GET /api/v1/relationships/stats
    """
    from sqlalchemy import func
    from app.models.relationship import Knows
    
    try:
        # Get archetype distribution
        archetype_stats = db.query(
            Knows.archetype,
            func.count(Knows.from_person_id).label('count'),
            func.avg(
                func.cast(func.jsonb_extract_path_text(Knows.relationship_depth, 'overall_score'), 
                         func.INTEGER)
            ).label('avg_score')
        ).filter(
            Knows.user_id == current_user.user_id
        ).group_by(Knows.archetype).all()
        
        # Total relationships
        total_relationships = db.query(func.count(Knows.from_person_id)).filter(
            Knows.user_id == current_user.user_id
        ).scalar()
        
        # Strong relationships (score > 70)
        strong_relationships = db.query(func.count(Knows.from_person_id)).filter(
            Knows.user_id == current_user.user_id,
            func.cast(func.jsonb_extract_path_text(Knows.relationship_depth, 'overall_score'), 
                     func.INTEGER) > 70
        ).scalar()
        
        archetype_distribution = {}
        archetype_scores = {}
        
        for stat in archetype_stats:
            archetype_distribution[stat.archetype or "unknown"] = stat.count
            archetype_scores[stat.archetype or "unknown"] = round(float(stat.avg_score or 0), 1)
        
        return {
            "total_relationships": total_relationships,
            "strong_relationships": strong_relationships,
            "weak_relationships": total_relationships - strong_relationships,
            "archetype_distribution": archetype_distribution,
            "average_scores_by_archetype": archetype_scores,
            "relationship_health": {
                "strong_ratio": round(strong_relationships / max(total_relationships, 1), 2),
                "most_common_archetype": max(archetype_distribution.items(), key=lambda x: x[1])[0] if archetype_distribution else "none",
                "highest_scoring_archetype": max(archetype_scores.items(), key=lambda x: x[1])[0] if archetype_scores else "none"
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting statistics: {str(e)}")