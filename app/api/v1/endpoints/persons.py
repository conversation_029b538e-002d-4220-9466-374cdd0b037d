"""
Person management endpoints
Based on technical documentation requirements traceability matrix
"""

from typing import Any, List, Dict, Optional
import uuid
import csv
import io
from datetime import datetime

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    Query,
    status,
    BackgroundTasks,
    UploadFile,
    File
)
from fastapi.responses import JSONResponse, StreamingResponse
from sqlalchemy.orm import Session

from app.api.deps import get_current_user
from app.core.database import get_db
from app.models.user import User
from app.schemas.person import PersonCreate, PersonInDB, PersonListResponse, PersonSummary, PersonUpdate
from app.services.person_service import PersonService

router = APIRouter()


def validate_uuid(uuid_string: str) -> None:
    """Validate UUID format and raise 400 if invalid"""
    try:
        uuid.UUID(uuid_string)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid UUID format")

@router.post("/", response_model=PersonInDB)
async def create_person(
    person_data: PersonCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> PersonInDB:
    """
    Create a new person in the user's network
    POST /api/v1/persons
    """
    # 1. Validate person data (handled by FastAPI + Pydantic)
    person_dict = person_data.dict()

    # 2a. Check for duplicate relationships in user's network via service
    person_service = PersonService(db)
    duplicate = person_service.check_duplicate_relationship(
        current_user.user_id,
        person_data.first_name,
        person_data.last_name
    )

    if duplicate:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Relationship with {person_data.first_name} {person_data.last_name} already exists"
        )

    # 2b. Perform actual creation after validation
    new_person = person_service.create_person(
        current_user.user_id,
        person_data.dict()
    )

    # 3. Background analytics processing
    # TODO: Implement background task
    # background_tasks.add_task(process_network_graph_cache, db, new_person.person_id)

    # 4. Return created person with proper response model
    return PersonInDB(
        person_id=new_person.person_id,
        user_id=str(new_person.user_id),
        first_name=new_person.first_name,
        last_name=new_person.last_name,
        contact_info=new_person.contact_info,
        social_profiles=new_person.social_profiles,
        professional_info=new_person.professional_info,
        personal_details=new_person.personal_details,
        stated_decision_factors=new_person.stated_decision_factors,
        learned_decision_factors=new_person.learned_decision_factors,
        is_user=new_person.is_user,
        created_at=new_person.created_at,
        updated_at=new_person.updated_at
    )


@router.get("/tags", response_model=List[str])
async def get_all_tags(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> List[str]:
    """
    Get all unique tags used by the user
    GET /api/v1/persons/tags
    """
    person_service = PersonService(db)
    tags = person_service.get_all_tags(current_user.user_id)
    return sorted(tags)


@router.get("/export", response_class=StreamingResponse)
async def export_persons_to_csv(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> StreamingResponse:
    """
    Export persons to CSV file
    GET /api/v1/persons/export
    """
    person_service = PersonService(db)
    persons = person_service.get_all_by_user(current_user.user_id, skip=0, limit=10000)

    # Create CSV content
    output = io.StringIO()
    fieldnames = [
        'first_name', 'last_name', 'email', 'phone',
        'company', 'title', 'tags', 'created_at'
    ]
    writer = csv.DictWriter(output, fieldnames=fieldnames)
    writer.writeheader()

    for person in persons:
        # Extract data from person object
        contact_info = person.contact_info or {}
        professional_info = person.professional_info or {}
        personal_details = person.personal_details or {}

        tags = personal_details.get("tags", [])
        tags_str = ", ".join(tags) if isinstance(tags, list) else ""

        writer.writerow({
            'first_name': person.first_name,
            'last_name': person.last_name,
            'email': contact_info.get('email', ''),
            'phone': contact_info.get('phone', ''),
            'company': professional_info.get('company', ''),
            'title': professional_info.get('title', ''),
            'tags': tags_str,
            'created_at': person.created_at.isoformat() if person.created_at else ''
        })

    # Create response
    output.seek(0)
    response = StreamingResponse(
        io.BytesIO(output.getvalue().encode('utf-8')),
        media_type='text/csv',
        headers={"Content-Disposition": "attachment; filename=persons_export.csv"}
    )

    return response


@router.post("/import", response_model=dict)
async def import_persons_from_csv(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Import persons from CSV file
    POST /api/v1/persons/import
    """
    if not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="Only CSV files are supported")

    person_service = PersonService(db)

    try:
        # Read CSV content
        content = await file.read()
        csv_content = content.decode('utf-8')
        csv_reader = csv.DictReader(io.StringIO(csv_content))

        imported_count = 0
        errors = []

        for row_num, row in enumerate(csv_reader, start=2):  # Start from 2 (header is row 1)
            try:
                # Map CSV columns to person data
                person_data = {
                    "first_name": row.get("first_name", "").strip(),
                    "last_name": row.get("last_name", "").strip(),
                    "contact_info": {},
                    "professional_info": {},
                    "personal_details": {}
                }

                # Add contact info if available
                if row.get("email"):
                    person_data["contact_info"]["email"] = row["email"].strip()
                if row.get("phone"):
                    person_data["contact_info"]["phone"] = row["phone"].strip()

                # Add professional info if available
                if row.get("company"):
                    person_data["professional_info"]["company"] = row["company"].strip()
                if row.get("title"):
                    person_data["professional_info"]["title"] = row["title"].strip()

                # Add tags if available
                if row.get("tags"):
                    tags = [tag.strip() for tag in row["tags"].split(",") if tag.strip()]
                    person_data["personal_details"]["tags"] = tags

                # Validate required fields
                if not person_data["first_name"] or not person_data["last_name"]:
                    errors.append(f"Row {row_num}: Missing required fields (first_name, last_name)")
                    continue

                # Check for duplicates
                duplicate = person_service.check_duplicate_relationship(
                    current_user.user_id,
                    person_data["first_name"],
                    person_data["last_name"]
                )

                if duplicate:
                    errors.append(f"Row {row_num}: Person {person_data['first_name']} {person_data['last_name']} already exists")
                    continue

                # Create person
                person_service.create_person(current_user.user_id, person_data)
                imported_count += 1

            except Exception as e:
                errors.append(f"Row {row_num}: {str(e)}")

        return {
            "imported_count": imported_count,
            "total_rows": row_num - 1 if 'row_num' in locals() else 0,
            "errors": errors,
            "message": f"Successfully imported {imported_count} persons"
        }

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing CSV file: {str(e)}")


@router.get("/", response_model=PersonListResponse)
async def get_persons(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = Query(None, description="Search by name"),
    company: Optional[str] = Query(None, description="Filter by company"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> PersonListResponse:
    """
    Get list of persons in user's network with optional search and filtering
    GET /api/v1/persons
    """
    person_service = PersonService(db)

    # Apply search and filtering
    if search or company:
        persons = person_service.search_persons(
            user_id=current_user.user_id,
            search=search,
            company=company,
            skip=skip,
            limit=limit
        )
        total_count = person_service.count_search_results(
            user_id=current_user.user_id,
            search=search,
            company=company
        )
    else:
        persons = person_service.get_all_by_user(current_user.user_id, skip, limit)
        total_count = person_service.count_by_user(current_user.user_id)

    # Convert to PersonSummary objects
    items = []
    for person in persons:
        person_summary = PersonSummary(
            person_id=person.person_id,
            first_name=person.first_name,
            last_name=person.last_name,
            full_name=f"{person.first_name} {person.last_name}".strip(),
            profile_picture=person.profile_picture,
            is_user=person.is_user
        )
        items.append(person_summary)

    has_more = (skip + limit) < total_count

    return PersonListResponse(
        items=items,
        total=total_count,
        skip=skip,
        limit=limit,
        has_more=has_more
    )


@router.get("/{person_id}", response_model=PersonInDB)
async def get_person(
    person_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> PersonInDB:
    """
    Get a specific person by ID
    GET /api/v1/persons/{id}
    """
    validate_uuid(person_id)

    person_service = PersonService(db)
    person = person_service.get(person_id)
    if not person or person.user_id != current_user.user_id:
        raise HTTPException(status_code=404, detail="Person not found")

    return PersonInDB(
        person_id=person.person_id,
        user_id=str(person.user_id),
        first_name=person.first_name,
        last_name=person.last_name,
        contact_info=person.contact_info,
        social_profiles=person.social_profiles,
        professional_info=person.professional_info,
        personal_details=person.personal_details,
        stated_decision_factors=person.stated_decision_factors,
        learned_decision_factors=person.learned_decision_factors,
        is_user=person.is_user,
        created_at=person.created_at,
        updated_at=person.updated_at
    )


@router.put("/{person_id}", response_model=PersonInDB)
async def update_person(
    person_id: str,
    person_data: PersonUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> PersonInDB:
    """
    Update a person's information
    PUT /api/v1/persons/{id}
    """
    person_service = PersonService(db)
    person = person_service.get(person_id)
    if not person or person.user_id != current_user.user_id:
        raise HTTPException(status_code=404, detail="Person not found")

    # Update person with new data
    updated_person = person_service.update(person_id, person_data.dict(exclude_unset=True))

    return PersonInDB(
        person_id=updated_person.person_id,
        user_id=str(updated_person.user_id),
        first_name=updated_person.first_name,
        last_name=updated_person.last_name,
        contact_info=updated_person.contact_info,
        social_profiles=updated_person.social_profiles,
        professional_info=updated_person.professional_info,
        personal_details=updated_person.personal_details,
        stated_decision_factors=updated_person.stated_decision_factors,
        learned_decision_factors=updated_person.learned_decision_factors,
        is_user=updated_person.is_user,
        created_at=updated_person.created_at,
        updated_at=updated_person.updated_at
    )


@router.delete("/{person_id}", status_code=204)
async def delete_person(
    person_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> None:
    """
    Delete a person from the network
    DELETE /api/v1/persons/{id}
    """
    person_service = PersonService(db)
    person = person_service.get(person_id)
    if not person or person.user_id != current_user.user_id:
        raise HTTPException(status_code=404, detail="Person not found")

    person_service.delete(person_id)
    return None


@router.post("/{person_id}/apply-archetype", response_model=dict)
async def apply_archetype_to_relationship(
    person_id: str,
    archetype_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Apply relationship archetype to a person
    POST /api/v1/persons/{id}/apply-archetype
    """
    validate_uuid(person_id)

    # Validate that the person exists and belongs to the user
    person_service = PersonService(db)
    person = person_service.get(person_id)
    if not person or person.user_id != current_user.user_id:
        raise HTTPException(status_code=404, detail="Person not found")

    # For now, return a mock relationship response with the expected fields
    # TODO: Implement actual relationship creation logic
    relationship_id = str(uuid.uuid4())

    return {
        "relationship_id": relationship_id,
        "from_person_id": person_id,
        "target_person_id": archetype_data.get("target_person_id"),
        "archetype": archetype_data.get("archetype"),
        "archetype_details": archetype_data.get("archetype_details", {}),
        "created_at": "2025-07-04T02:00:00Z",
        "message": "Relationship archetype applied successfully"
    }


@router.get("/{person_id}/relationship-prism/{target_person_id}", response_model=dict)
async def get_relationship_prism_analysis(
    person_id: str,
    target_person_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get multi-dimensional relationship analysis between two persons
    GET /api/v1/persons/{id}/relationship-prism/{target_id}
    """
    validate_uuid(person_id)
    validate_uuid(target_person_id)

    # Validate that both persons exist and belong to the user
    person_service = PersonService(db)
    person1 = person_service.get(person_id)
    person2 = person_service.get(target_person_id)

    if not person1 or person1.user_id != current_user.user_id:
        raise HTTPException(status_code=404, detail="Person not found")
    if not person2 or person2.user_id != current_user.user_id:
        raise HTTPException(status_code=404, detail="Target person not found")

    # Generate relationship analysis (mock implementation)
    analysis = {
        "relationship_strength": 0.75,
        "dimensions": [
            {
                "name": "professional",
                "score": 0.8,
                "description": "Strong professional connection"
            },
            {
                "name": "personal",
                "score": 0.6,
                "description": "Moderate personal relationship"
            },
            {
                "name": "communication",
                "score": 0.7,
                "description": "Regular communication pattern"
            },
            {
                "name": "mutual_benefit",
                "score": 0.8,
                "description": "High potential for mutual benefit"
            }
        ],
        "insights": [
            "Strong professional alignment",
            "Regular communication pattern",
            "Potential for collaboration"
        ],
        "recommendations": [
            "Schedule regular check-ins",
            "Explore collaboration opportunities",
            "Share relevant industry insights"
        ],
        "last_updated": datetime.utcnow().isoformat() + "Z"
    }

    return analysis


@router.post("/{person_id}/tags", response_model=dict)
async def add_tags_to_person(
    person_id: str,
    tags: List[str],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Add tags to a person
    POST /api/v1/persons/{id}/tags
    """
    validate_uuid(person_id)

    person_service = PersonService(db)
    person = person_service.get(person_id)
    if not person or person.user_id != current_user.user_id:
        raise HTTPException(status_code=404, detail="Person not found")

    # Add tags to person's metadata
    current_tags = person.personal_details.get("tags", []) if person.personal_details else []
    new_tags = list(set(current_tags + tags))  # Remove duplicates

    # Update person with new tags
    update_data = {"personal_details": {**person.personal_details, "tags": new_tags}}
    updated_person = person_service.update(person_id, update_data)

    return {
        "person_id": person_id,
        "tags": new_tags,
        "message": f"Added {len(tags)} tags to person"
    }


@router.post("/{person_id}/remove-tags", response_model=dict)
async def remove_tags_from_person(
    person_id: str,
    tags: List[str],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Remove tags from a person
    POST /api/v1/persons/{id}/remove-tags
    """
    validate_uuid(person_id)

    person_service = PersonService(db)
    person = person_service.get(person_id)
    if not person or person.user_id != current_user.user_id:
        raise HTTPException(status_code=404, detail="Person not found")

    # Remove tags from person's metadata
    current_tags = person.personal_details.get("tags", []) if person.personal_details else []
    remaining_tags = [tag for tag in current_tags if tag not in tags]

    # Update person with remaining tags
    update_data = {"personal_details": {**person.personal_details, "tags": remaining_tags}}
    person_service.update(person_id, update_data)

    return {
        "person_id": person_id,
        "tags": remaining_tags,
        "message": f"Removed {len(tags)} tags from person"
    }


@router.get("/tags", response_model=List[str])
async def get_all_tags(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> List[str]:
    """
    Get all unique tags used by the user
    GET /api/v1/persons/tags
    """
    person_service = PersonService(db)
    tags = person_service.get_all_tags(current_user.user_id)
    return sorted(tags)


@router.post("/{person_id}/relationships", response_model=dict)
async def create_relationship(
    person_id: str,
    relationship_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Create a relationship between two persons
    POST /api/v1/persons/{id}/relationships
    """
    validate_uuid(person_id)

    target_person_id = relationship_data.get("target_person_id")
    if not target_person_id:
        raise HTTPException(status_code=400, detail="target_person_id is required")

    validate_uuid(target_person_id)

    person_service = PersonService(db)

    # Validate both persons exist and belong to user
    person1 = person_service.get(person_id)
    person2 = person_service.get(target_person_id)

    if not person1 or person1.user_id != current_user.user_id:
        raise HTTPException(status_code=404, detail="Person not found")
    if not person2 or person2.user_id != current_user.user_id:
        raise HTTPException(status_code=404, detail="Target person not found")

    # Create relationship using service
    relationship = person_service.create_relationship(
        from_person_id=person_id,
        to_person_id=target_person_id,
        user_id=current_user.user_id,
        archetype=relationship_data.get("archetype", "colleague"),
        relationship_foundation=relationship_data.get("relationship_foundation", {}),
        relationship_depth=relationship_data.get("relationship_depth", {})
    )

    return {
        "relationship_id": f"{person_id}-{target_person_id}",
        "from_person_id": person_id,
        "to_person_id": target_person_id,
        "archetype": relationship.archetype or "colleague",
        "created_at": datetime.now().isoformat() + "Z",
        "message": "Relationship created successfully"
    }


@router.get("/{person_id}/relationships", response_model=List[dict])
async def get_person_relationships(
    person_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> List[dict]:
    """
    Get all relationships for a person
    GET /api/v1/persons/{id}/relationships
    """
    validate_uuid(person_id)

    person_service = PersonService(db)
    person = person_service.get(person_id)
    if not person or person.user_id != current_user.user_id:
        raise HTTPException(status_code=404, detail="Person not found")

    relationships = person_service.get_person_relationships(person_id, current_user.user_id)

    return relationships






