# Network Graph Visualization API

Complete API documentation for network graph visualization endpoints.

## Overview

The Graph API provides real-time network visualization data generation with advanced filtering, layout algorithms, and WebSocket support for live updates.

## REST Endpoints

### 1. Get Full Network Graph

**GET** `/api/v1/graph/full`

Generate complete network graph for user with all relationships.

**Query Parameters:**
- `layout` (optional): Layout algorithm - `force_directed`, `circular`, `hierarchical`, `radial`
  - Default: `force_directed`

**Response:**
```json
{
  "nodes": [
    {
      "id": "uuid",
      "label": "Person Name",
      "type": "person|user",
      "size": 15,
      "color": "#2196F3",
      "x": 100.5,
      "y": 200.3,
      "data": {
        "name": "Full Name",
        "archetype": "colleague",
        "relationship_score": 85,
        "company": "Company Name",
        "title": "Job Title",
        "tags": ["tag1", "tag2"],
        "is_user": false
      }
    }
  ],
  "edges": [
    {
      "from": "user_uuid",
      "to": "person_uuid",
      "strength": 0.85,
      "width": 4.25,
      "color": "#2196F3aa",
      "type": "colleague",
      "data": {
        "archetype": "colleague",
        "overall_score": 85,
        "dimensions": {...}
      }
    }
  ],
  "layout": {
    "type": "force_directed",
    "positions": {
      "node_id": {"x": 100.5, "y": 200.3}
    }
  },
  "metadata": {
    "total_nodes": 50,
    "total_edges": 75,
    "density": 0.3,
    "clustering_coefficient": 0.65,
    "average_path_length": 2.4,
    "generated_at": "2025-07-08T12:00:00Z"
  }
}
```

### 2. Get Filtered Network Graph

**GET** `/api/v1/graph/filtered`

Generate filtered network graph based on criteria.

**Query Parameters:**
- `tags` (optional): Comma-separated tags to filter by
- `archetype` (optional): Filter by relationship archetype
- `organization` (optional): Filter by organization/company
- `min_score` (optional): Minimum relationship score (0-100)
- `max_nodes` (optional): Maximum number of nodes (1-1000)
- `layout` (optional): Layout algorithm

**Example:**
```
GET /api/v1/graph/filtered?tags=tech,startup&archetype=colleague&max_nodes=50&layout=radial
```

**Response:** Same structure as full graph

### 3. Get Node Details

**GET** `/api/v1/graph/node/{person_id}`

Get detailed information about a specific node.

**Response:**
```json
{
  "person": {
    "id": "uuid",
    "name": "Full Name",
    "professional_info": {...},
    "personal_details": {...},
    "contact_info": {...}
  },
  "relationship": {
    "archetype": "colleague",
    "overall_score": 85,
    "dimensions": {...},
    "foundation": {...}
  },
  "mutual_connections": [
    {
      "id": "uuid",
      "name": "Connection Name",
      "company": "Company"
    }
  ]
}
```

### 4. Get Graph Clusters

**GET** `/api/v1/graph/clusters`

Detect and return graph clusters/communities.

**Response:**
```json
{
  "clusters": [
    {
      "cluster_id": 0,
      "size": 8,
      "nodes": [
        {
          "id": "uuid",
          "label": "Person Name",
          "type": "person"
        }
      ]
    }
  ],
  "metadata": {
    "total_clusters": 3,
    "algorithm": "greedy_modularity",
    "generated_at": "2025-07-08T12:00:00Z"
  }
}
```

### 5. Get Graph Statistics

**GET** `/api/v1/graph/stats`

Get comprehensive network statistics and analytics.

**Response:**
```json
{
  "network_overview": {
    "total_connections": 45,
    "total_relationships": 45,
    "network_density": 0.3,
    "clustering_coefficient": 0.65,
    "average_path_length": 2.4
  },
  "relationship_strength": {
    "average_score": 72.5,
    "strong_relationships": 15,
    "weak_relationships": 8,
    "distribution": {
      "strong (70-100)": 15,
      "medium (50-69)": 22,
      "weak (0-49)": 8
    }
  },
  "archetype_distribution": {
    "colleague": 20,
    "friend": 12,
    "mentor": 5,
    "client": 8
  },
  "generated_at": "2025-07-08T12:00:00Z"
}
```

## WebSocket Endpoint

### Real-time Graph Updates

**WebSocket** `/api/v1/graph/ws/{user_id}`

Real-time bidirectional communication for graph updates.

**Connection:**
```javascript
const ws = new WebSocket('ws://localhost:8000/api/v1/graph/ws/user_uuid');
```

**Client Messages:**

1. **Request Graph Update**
```json
{
  "type": "request_graph_update",
  "layout": "force_directed",
  "filters": {
    "tags": ["tech"],
    "archetype": "colleague",
    "max_nodes": 50
  },
  "request_id": "unique_id"
}
```

2. **Request Node Details**
```json
{
  "type": "request_node_details",
  "person_id": "uuid",
  "request_id": "unique_id"
}
```

3. **Request Clusters**
```json
{
  "type": "request_clusters",
  "request_id": "unique_id"
}
```

4. **Ping (Keep-alive)**
```json
{
  "type": "ping",
  "timestamp": "2025-07-08T12:00:00Z"
}
```

**Server Messages:**

1. **Connection Established**
```json
{
  "type": "connection_established",
  "user_id": "uuid",
  "timestamp": "unique_id"
}
```

2. **Graph Data Response**
```json
{
  "type": "graph_data",
  "data": {...}, // Full graph data structure
  "request_id": "unique_id"
}
```

3. **Graph Update Notification**
```json
{
  "type": "graph_update",
  "data": {
    "change_type": "person_added|relationship_updated|person_deleted",
    "change_data": {...},
    "requires_refresh": true
  },
  "timestamp": "unique_id"
}
```

4. **Error Response**
```json
{
  "type": "error",
  "message": "Error description",
  "request_id": "unique_id"
}
```

## Layout Algorithms

### 1. Force-Directed (`force_directed`)
- **Use case:** General purpose, organic-looking layouts
- **Characteristics:** Nodes repel each other, edges act as springs
- **Best for:** Medium-sized networks (10-500 nodes)

### 2. Circular (`circular`)
- **Use case:** Equal importance visualization
- **Characteristics:** Nodes arranged in a circle
- **Best for:** Small networks, equal relationship importance

### 3. Hierarchical (`hierarchical`)
- **Use case:** Organizational structures, clear hierarchies
- **Characteristics:** Tree-like structure with levels
- **Best for:** Networks with clear hierarchy patterns

### 4. Radial (`radial`)
- **Use case:** User-centric visualization
- **Characteristics:** User at center, connections radiating outward
- **Best for:** Personal network visualization

## Color Coding

### Relationship Archetypes
- **Colleague:** `#2196F3` (Blue)
- **Friend:** `#FF9800` (Orange)
- **Mentor:** `#9C27B0` (Purple)
- **Client:** `#4CAF50` (Green)
- **Family:** `#F44336` (Red)
- **Acquaintance:** `#607D8B` (Blue Grey)
- **Partner:** `#E91E63` (Pink)
- **Unknown:** `#9E9E9E` (Grey)

### Node Sizes
- **User Node:** 25px (fixed)
- **Person Nodes:** 15px + (relationship_score/100 * 10px)
- **Range:** 15-25px based on relationship strength

### Edge Properties
- **Width:** 1-5px based on relationship strength
- **Opacity:** 30%-100% based on relationship strength
- **Color:** Matches archetype with alpha channel

## Performance Considerations

### Large Networks (500+ nodes)
- Use `max_nodes` parameter to limit results
- Implement pagination for very large networks
- Consider using simplified layouts for better performance

### Real-time Updates
- WebSocket connections automatically clean up on disconnect
- Broadcast notifications only to active connections
- Implement client-side caching for better responsiveness

### Filtering Best Practices
- Combine multiple filters for targeted results
- Use `min_score` to focus on strong relationships
- Apply `organization` filters for professional network views

## Error Handling

### Common HTTP Errors
- **400 Bad Request:** Invalid UUID format, invalid parameters
- **401 Unauthorized:** Missing or invalid authentication
- **404 Not Found:** Person/relationship not found
- **500 Internal Server Error:** Database or processing errors

### WebSocket Errors
- Connection drops are handled gracefully
- Invalid message formats return error responses
- Malformed UUIDs return specific error messages

## Integration Examples

### Frontend Integration
```javascript
// Fetch full graph
const response = await fetch('/api/v1/graph/full?layout=force_directed');
const graphData = await response.json();

// WebSocket for real-time updates
const ws = new WebSocket(`ws://localhost:8000/api/v1/graph/ws/${userId}`);
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  if (message.type === 'graph_update') {
    // Refresh graph visualization
    refreshGraph();
  }
};
```

### Filtering Examples
```javascript
// Professional network view
const professionalGraph = await fetch(
  '/api/v1/graph/filtered?archetype=colleague&organization=TechCorp&min_score=50'
);

// Close friends network
const friendsGraph = await fetch(
  '/api/v1/graph/filtered?archetype=friend&tags=personal&max_nodes=20'
);
```