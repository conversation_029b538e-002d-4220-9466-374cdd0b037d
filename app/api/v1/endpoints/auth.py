"""
Authentication endpoints
"""

from typing import Any
from datetime import datetime, timed<PERSON><PERSON>
import uuid

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from supabase import Client

from app.api.deps import get_current_user, oauth2_scheme
from app.core.database import get_db, get_supabase
from app.core.security import get_password_hash, verify_password
from app.core.config import settings
from app.models.user import User
from app.schemas.auth import Token, UserCreate, UserLogin
from app.schemas.user import User as UserSchema
from app.services.user_service import UserService

router = APIRouter()


@router.post("/register-local", response_model=UserSchema, summary="Local User Registration",
             description="Register a new user directly in the local database. For testing purposes only.")
def register_local(
    user_data: UserCreate,
    db: Session = Depends(get_db),
) -> Any:
    """
    Handles user registration for local testing environments, bypassing Supabase.
    This endpoint creates a user with a hashed password and stores it in the local database.
    """
    user_service = UserService(db)

    # Prevent duplicate user registration by checking if the email already exists.
    if user_service.get_by_email(user_data.email):
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="A user with this email address has already been registered.",
        )

    # Create a new user record with a securely hashed password.
    # A unique user ID is generated to serve as the primary key.
    new_user_data = {
        "user_id": str(uuid.uuid4()),
        "email": user_data.email,
        "first_name": user_data.first_name,
        "last_name": user_data.last_name,
        "hashed_password": get_password_hash(user_data.password),
        "is_active": True,
        "is_verified": True,  # Users are automatically verified in the local test environment.
    }

    # Create the user in the database and return the user's data.
    user = user_service.create(new_user_data)
    return user


@router.post("/login-local", response_model=Token, summary="Local User Login",
             description="Authenticate a user against the local database. For testing purposes only.")
def login_local(
    user_login: UserLogin,
    db: Session = Depends(get_db),
) -> Any:
    """
    Handles user authentication for local testing by validating credentials against the local database.
    It returns a JWT token if the credentials are correct.
    """
    from jose import jwt

    user_service = UserService(db)
    user = user_service.get_by_email(user_login.email)

    # Verify that the user exists and the provided password is correct.
    if not user or not verify_password(user_login.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="The email or password you entered is incorrect.",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Ensure the user's account is active and not disabled.
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="This user account has been deactivated.",
        )

    # Generate a JWT token for the authenticated user.
    # The token includes the user's ID and an expiration time.
    expiration_delta = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    now = datetime.utcnow()
    expires_at = now + expiration_delta

    token_data = {
        "sub": str(user.user_id),
        "exp": expires_at,
        "iat": now,
        "iss": "nexus-local-auth",
        "aud": "nexus-app",
        "jti": str(uuid.uuid4()),  # Add unique JWT ID to ensure uniqueness
    }

    access_token = jwt.encode(token_data, settings.SECRET_KEY, algorithm=settings.ALGORITHM)

    # Return the token and its metadata.
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": int(expiration_delta.total_seconds()),
        "refresh_token": f"local-refresh-token-{user.user_id}",
    }


@router.post("/refresh", response_model=Token, summary="Refresh Access Token",
             description="Refresh an access token using a refresh token. For testing purposes only.")
def refresh_token_local(
    refresh_data: dict,
    db: Session = Depends(get_db),
) -> Any:
    """
    Refreshes an access token using a provided refresh token for local testing.
    This endpoint simulates token refresh without involving Supabase.
    """
    from jose import jwt

    refresh_token = refresh_data.get("refresh_token")
    if not refresh_token or not refresh_token.startswith("local-refresh-token-"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="The provided refresh token is invalid or has expired.",
        )

    try:
        # Extract the user ID from the refresh token.
        user_id = refresh_token.replace("local-refresh-token-", "")
        user_service = UserService(db)
        user = user_service.get(user_id)

        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="The user associated with this token could not be found or is inactive.",
            )

        # Generate a new JWT access token.
        expiration_delta = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        now = datetime.utcnow()
        expires_at = now + expiration_delta

        token_data = {
            "sub": str(user.user_id),
            "exp": expires_at,
            "iat": now,
            "iss": "nexus-local-auth",
            "aud": "nexus-app",
            "jti": str(uuid.uuid4()),  # Add unique JWT ID to ensure uniqueness
        }

        new_access_token = jwt.encode(token_data, settings.SECRET_KEY, algorithm=settings.ALGORITHM)

        # Return the new token. The refresh token remains the same.
        return {
            "access_token": new_access_token,
            "token_type": "bearer",
            "expires_in": int(expiration_delta.total_seconds()),
            "refresh_token": refresh_token,
        }
    except Exception as e:
        # Catch any other exceptions and return an unauthorized error.
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"An unexpected error occurred during token refresh: {e}",
        )


@router.post("/logout-local", summary="Local User Logout",
             description="Logs out a user by blacklisting their token. For testing purposes only.")
def logout_local(
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Handles user logout for local testing by adding the user's current token to a blacklist.
    This prevents the token from being used for subsequent authenticated requests.
    """
    # To prevent circular imports, the blacklist is imported here.
    from app.api.deps import _blacklisted_tokens
    
    # Add the token to the blacklist to invalidate it.
    _blacklisted_tokens.add(token)
    
    # Return a confirmation message.
    return {"message": "You have been successfully logged out."}


@router.post("/register", response_model=UserSchema)
def register(
    user_data: UserCreate,
    db: Session = Depends(get_db),
    supabase: Client = Depends(get_supabase),
) -> Any:
    """
    Register a new user with Supabase
    """
    # This is the production registration endpoint.
    # It creates a user in Supabase and then mirrors it to the local database.
    
    try:
        # Attempt to sign up the user in Supabase
        res = supabase.auth.sign_up(
            {
                "email": user_data.email,
                "password": user_data.password,
                "options": {
                    "data": {
                        "first_name": user_data.first_name,
                        "last_name": user_data.last_name,
                    }
                },
            }
        )
    except Exception as e:
        # If Supabase returns an error, it's often a cryptic `APIError`.
        # We'll return a more user-friendly message.
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to register user with authentication service: {e}",
        )

    if not res.user:
        # This case handles scenarios where Supabase doesn't return a user object,
        # which can happen if the user already exists or there's a configuration issue.
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Could not register user. The email might already be in use.",
        )

    # Once the user is created in Supabase, create a corresponding record
    # in the local application database.
    user_service = UserService(db)
    
    # Prepare user data for local database insertion.
    # The user ID from Supabase is used as the primary key to link the two records.
    new_user_data = {
        "user_id": res.user.id,  # Use the ID from Supabase
        "email": user_data.email,
        "first_name": user_data.first_name,
        "last_name": user_data.last_name,
        "is_active": True,
        "is_verified": False,  # Email verification is handled by Supabase
    }
    
    # Create the user in the local database.
    user = user_service.create(new_user_data)

    return user


@router.post("/login", response_model=Token)
def login(
    user_login: UserLogin,
    db: Session = Depends(get_db),
    supabase: Client = Depends(get_supabase),
) -> Any:
    """
    Login and get access token from Supabase.
    This endpoint handles user authentication against the Supabase service.
    """
    try:
        # Authenticate the user with their email and password using Supabase.
        res = supabase.auth.sign_in_with_password(
            {"email": user_login.email, "password": user_login.password}
        )
    except Exception as e:
        # Catch exceptions from Supabase and return a standard unauthorized error.
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication failed: {e}",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not res.session or not res.user:
        # If Supabase doesn't return a session or user, the credentials are invalid.
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # After successful login, update the user's `last_login` timestamp
    # and other metadata in the local database to keep it synced with Supabase.
    user_service = UserService(db)
    user = user_service.get(res.user.id)
    
    if user:
        # Prepare the data to be updated in the local database.
        update_data = {
            "last_login": res.user.last_sign_in_at,
            "first_name": res.user.user_metadata.get("first_name", user.first_name),
            "last_name": res.user.user_metadata.get("last_name", user.last_name),
        }
        # Perform the update.
        user_service.update(user.user_id, update_data)

    # Return the authentication tokens provided by Supabase.
    return {
        "access_token": res.session.access_token,
        "token_type": "bearer",
        "expires_in": res.session.expires_in,
        "refresh_token": res.session.refresh_token,
    }


@router.post("/logout")
def logout(
    supabase: Client = Depends(get_supabase),
    current_user: UserSchema = Depends(get_current_user),
) -> Any:
    """
    Logout user from Supabase.
    This endpoint invalidates the user's current session.
    """
    try:
        # The `sign_out` method in Supabase invalidates the user's access token.
        supabase.auth.sign_out()
    except Exception as e:
        # If there's an issue with the sign-out process, return an error.
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Logout failed: {e}",
        )
    
    # Confirm successful logout.
    return {"message": "Successfully logged out"}
