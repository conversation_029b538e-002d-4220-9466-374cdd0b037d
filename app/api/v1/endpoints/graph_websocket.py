"""
WebSocket endpoints for real-time graph updates
"""

import json
import uuid
from typing import Dict, Set

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.graph_service import GraphService
from app.models.user import User


router = APIRouter()


class GraphConnectionManager:
    """
    Manages WebSocket connections for real-time graph updates
    """
    
    def __init__(self):
        # Store active connections by user_id
        self.active_connections: Dict[str, Set[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str):
        """Accept WebSocket connection and add to user's connections"""
        await websocket.accept()
        
        if user_id not in self.active_connections:
            self.active_connections[user_id] = set()
        
        self.active_connections[user_id].add(websocket)
        
        # Send initial connection confirmation
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "user_id": user_id,
            "timestamp": str(uuid.uuid4())
        }))
    
    async def disconnect(self, websocket: WebSocket, user_id: str):
        """Remove WebSocket connection"""
        if user_id in self.active_connections:
            self.active_connections[user_id].discard(websocket)
            
            # Clean up empty user entries
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
    
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """Send message to specific WebSocket"""
        try:
            await websocket.send_text(json.dumps(message))
        except:
            # Connection likely closed
            pass
    
    async def broadcast_to_user(self, message: dict, user_id: str):
        """Send message to all connections for a specific user"""
        if user_id in self.active_connections:
            disconnected = set()
            
            for websocket in self.active_connections[user_id]:
                try:
                    await websocket.send_text(json.dumps(message))
                except:
                    # Mark for removal
                    disconnected.add(websocket)
            
            # Clean up disconnected connections
            for websocket in disconnected:
                self.active_connections[user_id].discard(websocket)
    
    async def broadcast_graph_update(self, update_data: dict, user_id: str):
        """Broadcast graph update to user's connections"""
        message = {
            "type": "graph_update",
            "data": update_data,
            "timestamp": str(uuid.uuid4())
        }
        await self.broadcast_to_user(message, user_id)


# Global connection manager instance
graph_manager = GraphConnectionManager()


@router.websocket("/ws/{user_id}")
async def websocket_graph_updates(
    websocket: WebSocket,
    user_id: str,
    db: Session = Depends(get_db)
):
    """
    WebSocket endpoint for real-time graph updates
    ws://localhost:8000/api/v1/graph/ws/{user_id}
    """
    await graph_manager.connect(websocket, user_id)
    graph_service = GraphService(db)
    
    try:
        while True:
            # Wait for messages from client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            message_type = message.get("type")
            
            if message_type == "request_graph_update":
                # Client requesting fresh graph data
                try:
                    user_uuid = uuid.UUID(user_id)
                    layout_type = message.get("layout", "force_directed")
                    filters = message.get("filters", {})
                    
                    if filters:
                        # Generate filtered graph
                        graph_data = await graph_service.generate_filtered_network_graph(
                            user_id=user_uuid,
                            tags=filters.get("tags"),
                            archetype=filters.get("archetype"),
                            organization=filters.get("organization"),
                            min_relationship_score=filters.get("min_score"),
                            max_nodes=filters.get("max_nodes"),
                            layout_type=layout_type
                        )
                    else:
                        # Generate full graph
                        graph_data = await graph_service.generate_full_network_graph(
                            user_uuid, layout_type=layout_type
                        )
                    
                    await graph_manager.send_personal_message({
                        "type": "graph_data",
                        "data": graph_data,
                        "request_id": message.get("request_id")
                    }, websocket)
                    
                except Exception as e:
                    await graph_manager.send_personal_message({
                        "type": "error",
                        "message": f"Error generating graph: {str(e)}",
                        "request_id": message.get("request_id")
                    }, websocket)
            
            elif message_type == "request_node_details":
                # Client requesting detailed node information
                try:
                    user_uuid = uuid.UUID(user_id)
                    person_uuid = uuid.UUID(message.get("person_id"))
                    
                    node_details = await graph_service.get_node_details(user_uuid, person_uuid)
                    
                    await graph_manager.send_personal_message({
                        "type": "node_details",
                        "data": node_details,
                        "request_id": message.get("request_id")
                    }, websocket)
                    
                except Exception as e:
                    await graph_manager.send_personal_message({
                        "type": "error",
                        "message": f"Error getting node details: {str(e)}",
                        "request_id": message.get("request_id")
                    }, websocket)
            
            elif message_type == "request_clusters":
                # Client requesting cluster analysis
                try:
                    user_uuid = uuid.UUID(user_id)
                    clusters = await graph_service.get_graph_clusters(user_uuid)
                    
                    await graph_manager.send_personal_message({
                        "type": "clusters",
                        "data": clusters,
                        "request_id": message.get("request_id")
                    }, websocket)
                    
                except Exception as e:
                    await graph_manager.send_personal_message({
                        "type": "error",
                        "message": f"Error analyzing clusters: {str(e)}",
                        "request_id": message.get("request_id")
                    }, websocket)
            
            elif message_type == "ping":
                # Keep-alive ping
                await graph_manager.send_personal_message({
                    "type": "pong",
                    "timestamp": message.get("timestamp")
                }, websocket)
            
            else:
                # Unknown message type
                await graph_manager.send_personal_message({
                    "type": "error",
                    "message": f"Unknown message type: {message_type}"
                }, websocket)
                
    except WebSocketDisconnect:
        await graph_manager.disconnect(websocket, user_id)
    except Exception as e:
        print(f"WebSocket error: {e}")
        await graph_manager.disconnect(websocket, user_id)


async def notify_graph_change(user_id: str, change_type: str, change_data: dict):
    """
    Utility function to notify connected clients of graph changes
    Call this when relationships or persons are added/updated/deleted
    """
    update_message = {
        "change_type": change_type,  # "person_added", "relationship_updated", etc.
        "change_data": change_data,
        "requires_refresh": True
    }
    
    await graph_manager.broadcast_graph_update(update_message, user_id)


# Export the manager for use in other modules
__all__ = ["router", "notify_graph_change", "graph_manager"]