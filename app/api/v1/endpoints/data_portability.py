"""
Data import/export endpoints for data portability
"""

import os
import uuid
from typing import Any, List

from fastapi import APIRouter, BackgroundTasks, Depends, File, HTTPException, UploadFile, status
from sqlalchemy.orm import Session

from app.api.deps import get_current_user
from app.core.database import get_db
from app.models.user import User
from app.schemas.data_portability import (
    ExportRequest,
    ImportRequest,
    ExportJobResponse,
    ImportJobResponse,
    JobSummary,
    DataPreview,
    ExportFormat,
    ImportFormat
)
from app.services.data_export_service import DataExportService
from app.services.data_import_service import DataImportService

router = APIRouter()


@router.post("/export", response_model=ExportJobResponse, status_code=status.HTTP_201_CREATED)
def trigger_export(
    export_request: ExportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> ExportJobResponse:
    """
    Trigger data export job
    POST /api/v1/data/export
    """
    try:
        service = DataExportService(db)
        
        # Create export job
        job = service.create_export_job(export_request, current_user.user_id)
        
        # Schedule background processing
        background_tasks.add_task(service.process_export_job, job.job_id)
        
        return ExportJobResponse(
            job_id=job.job_id,
            status=job.status,
            created_at=job.created_at,
            updated_at=job.updated_at,
            user_id=job.user_id,
            format=ExportFormat(job.format),
            include_privacy_data=job.config.get("include_privacy_data", False),
            entities=job.config.get("entities", []),
            download_url=None,
            file_size=None,
            expires_at=None,
            progress=0
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create export job: {str(e)}"
        )


@router.get("/export/{job_id}", response_model=ExportJobResponse)
def get_export_status(
    job_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> ExportJobResponse:
    """
    Get export job status and download link
    GET /api/v1/data/export/{job_id}
    """
    service = DataExportService(db)
    job = service.get_export_job(job_id, current_user.user_id)
    
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Export job not found"
        )
    
    return ExportJobResponse(
        job_id=job.job_id,
        status=job.status,
        created_at=job.created_at,
        updated_at=job.updated_at,
        user_id=job.user_id,
        format=ExportFormat(job.format),
        include_privacy_data=job.config.get("include_privacy_data", False),
        entities=job.config.get("entities", []),
        download_url=job.get_download_url(),
        file_size=job.file_size,
        expires_at=job.expires_at,
        progress=job.progress
    )


@router.post("/import", response_model=ImportJobResponse, status_code=status.HTTP_201_CREATED)
def import_data(
    file: UploadFile = File(...),
    import_request: ImportRequest = Depends(),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> ImportJobResponse:
    """
    Import data from uploaded file
    POST /api/v1/data/import
    """
    # Validate file type
    content_type_map = {
        "text/csv": ImportFormat.CSV,
        "application/json": ImportFormat.JSON,
        "text/vcard": ImportFormat.VCARD,
        "text/x-vcard": ImportFormat.VCARD
    }
    
    if file.content_type not in content_type_map:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported file type: {file.content_type}"
        )
    
    try:
        # Read file content
        file_content = file.file.read().decode('utf-8')
        file_size = len(file_content.encode('utf-8'))
        
        # Validate file size (max 10MB)
        if file_size > 10 * 1024 * 1024:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="File size exceeds 10MB limit"
            )
        
        service = DataImportService(db)
        
        # Set format from file content type if not provided
        if not hasattr(import_request, 'format') or not import_request.format:
            import_request.format = content_type_map[file.content_type]
        
        # Create import job
        job = service.create_import_job(
            import_request,
            file.filename or "upload",
            file_size,
            current_user.user_id
        )
        
        # Schedule background processing
        background_tasks.add_task(service.process_import_job, job.job_id, file_content)
        
        return ImportJobResponse(
            job_id=job.job_id,
            status=job.status,
            created_at=job.created_at,
            updated_at=job.updated_at,
            user_id=job.user_id,
            format=ImportFormat(job.format),
            filename=job.filename,
            file_size=job.file_size,
            duplicate_handling=job.config.get("duplicate_handling", "skip"),
            validation_strict=job.config.get("validation_strict", True),
            records_total=None,
            records_processed=None,
            records_imported=None,
            records_failed=None,
            records_skipped=None,
            errors=[],
            warnings=[]
        )
        
    except UnicodeDecodeError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File encoding not supported. Please use UTF-8 encoding."
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to process import file: {str(e)}"
        )


@router.get("/import/{job_id}", response_model=ImportJobResponse)
def get_import_status(
    job_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> ImportJobResponse:
    """
    Get import job status
    GET /api/v1/data/import/{job_id}
    """
    service = DataImportService(db)
    job = service.get_import_job(job_id, current_user.user_id)
    
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Import job not found"
        )
    
    return ImportJobResponse(
        job_id=job.job_id,
        status=job.status,
        created_at=job.created_at,
        updated_at=job.updated_at,
        user_id=job.user_id,
        format=ImportFormat(job.format),
        filename=job.filename,
        file_size=job.file_size,
        duplicate_handling=job.config.get("duplicate_handling", "skip"),
        validation_strict=job.config.get("validation_strict", True),
        records_total=job.records_total,
        records_processed=job.records_processed,
        records_imported=job.records_imported,
        records_failed=job.records_failed,
        records_skipped=job.records_skipped,
        errors=job.errors or [],
        warnings=job.warnings or []
    )


@router.post("/preview", response_model=DataPreview)
def preview_import_data(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> DataPreview:
    """
    Preview import data and get suggested mappings
    POST /api/v1/data/preview
    """
    # Validate file type
    content_type_map = {
        "text/csv": ImportFormat.CSV,
        "application/json": ImportFormat.JSON,
        "text/vcard": ImportFormat.VCARD,
        "text/x-vcard": ImportFormat.VCARD
    }
    
    if file.content_type not in content_type_map:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported file type: {file.content_type}"
        )
    
    try:
        # Read file content (limit to first 1MB for preview)
        file_content = file.file.read(1024 * 1024).decode('utf-8')
        
        service = DataImportService(db)
        format = content_type_map[file.content_type]
        
        return service.preview_import_data(file_content, format)
        
    except UnicodeDecodeError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File encoding not supported. Please use UTF-8 encoding."
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to preview file: {str(e)}"
        )


@router.get("/jobs", response_model=List[JobSummary])
def get_data_jobs(
    job_type: str = None,  # "export" or "import"
    limit: int = 50,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> List[JobSummary]:
    """
    Get user's data export/import jobs
    GET /api/v1/data/jobs
    """
    from app.models.data_job import DataJob
    from sqlalchemy import and_, desc
    
    query = (
        db.query(DataJob)
        .filter(DataJob.user_id == current_user.user_id)
    )
    
    if job_type:
        if job_type not in ["export", "import"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="job_type must be 'export' or 'import'"
            )
        query = query.filter(DataJob.job_type == job_type)
    
    jobs = (
        query
        .order_by(desc(DataJob.created_at))
        .limit(limit)
        .all()
    )
    
    return [
        JobSummary(
            job_id=job.job_id,
            job_type=job.job_type,
            status=job.status,
            created_at=job.created_at,
            progress=job.progress
        )
        for job in jobs
    ]


@router.get("/download/{job_id}")
def download_export_file(
    job_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Download export file
    GET /api/v1/data/download/{job_id}
    """
    from fastapi.responses import FileResponse
    
    service = DataExportService(db)
    file_path = service.get_file_path(job_id, current_user.user_id)
    
    if not file_path or not os.path.exists(file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Export file not found or expired"
        )
    
    # Get job for filename
    job = service.get_export_job(job_id, current_user.user_id)
    filename = f"nexus_export_{job.format}.{job.format}" if job else f"export_{job_id}"
    
    return FileResponse(
        path=file_path,
        filename=filename,
        media_type="application/octet-stream"
    )
