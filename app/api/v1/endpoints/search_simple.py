"""
Simple search endpoints for compatibility with SQLite
"""

from typing import Any, List

from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, text, select, case, and_, or_, Integer, desc
from datetime import datetime, timedelta
from typing import Optional

from app.api.deps import get_current_user
from app.core.database import get_db
from app.models.user import User
from app.models.person import Person
from app.models.relationship import Knows
from app.models.interaction import Interaction

router = APIRouter()


@router.get("/persons", response_model=dict)
def search_persons(
    q: str = Query(..., description="Search query"),
    limit: int = Query(10, ge=1, le=100, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Number of results to skip (for pagination)"),
    similarity_threshold: float = Query(0.3, ge=0.0, le=1.0, description="Similarity threshold"),
    # Filtering parameters
    company: str = Query(None, description="Filter by company/organization"),
    tags: str = Query(None, description="Filter by tags (comma-separated)"),
    relationship_type: str = Query(None, description="Filter by relationship archetype"),
    interaction_since: str = Query(None, description="Filter by interactions since date (YYYY-MM-DD)"),
    # Advanced options
    include_metadata: bool = Query(True, description="Include search metadata and facets"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Simple search for persons using LIKE queries for compatibility.
    GET /api/v1/search/persons?q=...&limit=10&offset=0&company=...&tags=...
    """
    # Build simple search query using LIKE
    query_lower = q.lower()
    
    # Parse date filter if provided
    interaction_since_date = None
    if interaction_since:
        try:
            interaction_since_date = datetime.strptime(interaction_since, '%Y-%m-%d')
        except ValueError:
            # Invalid date format, ignore filter
            pass
    
    # Parse tags filter if provided
    tag_filters = []
    if tags:
        tag_filters = [tag.strip().lower() for tag in tags.split(',') if tag.strip()]
    
    # Build simple query with basic scoring
    base_query = select(
        Person.person_id,
        Person.first_name,
        Person.last_name,
        Person.professional_info,
        Person.personal_details,
        # Simple scoring based on exact and partial matches
        case(
            (func.lower(Person.first_name) == query_lower, 1.0),
            (func.lower(Person.first_name).like(f'%{query_lower}%'), 0.7),
            else_=0.0
        ).label('first_name_similarity'),
        case(
            (func.lower(Person.last_name) == query_lower, 1.0),
            (func.lower(Person.last_name).like(f'%{query_lower}%'), 0.7),
            else_=0.0
        ).label('last_name_similarity'),
        case(
            (func.lower(Person.first_name + ' ' + Person.last_name) == query_lower, 1.0),
            (func.lower(Person.first_name + ' ' + Person.last_name).like(f'%{query_lower}%'), 0.8),
            else_=0.0
        ).label('full_name_similarity'),
        # Simplified word similarity (same as regular similarity for now)
        case(
            (func.lower(Person.first_name).like(f'%{query_lower}%'), 0.6),
            else_=0.0
        ).label('first_name_word_similarity'),
        case(
            (func.lower(Person.last_name).like(f'%{query_lower}%'), 0.6),
            else_=0.0
        ).label('last_name_word_similarity'),
        case(
            (func.lower(Person.first_name + ' ' + Person.last_name).like(f'%{query_lower}%'), 0.7),
            else_=0.0
        ).label('full_name_word_similarity'),
    )
    
    # Base filters
    filters = [
        (Person.user_id == current_user.user_id),
        (Person.is_user == False),  # Exclude user's own person record
    ]
    
    # Simple search filters using LIKE
    search_filters = [
        func.lower(Person.first_name).like(f'%{query_lower}%'),
        func.lower(Person.last_name).like(f'%{query_lower}%'),
        func.lower(Person.first_name + ' ' + Person.last_name).like(f'%{query_lower}%'),
    ]
    
    # Add company search if available
    if query_lower:
        search_filters.append(
            func.lower(func.coalesce(func.json_extract(Person.professional_info, '$.company'), '')).like(f'%{query_lower}%')
        )
    
    filters.append(or_(*search_filters))
    
    # Apply additional filters
    if company:
        filters.append(
            func.lower(func.coalesce(func.json_extract(Person.professional_info, '$.company'), '')).like(f'%{company.lower()}%')
        )
    
    if tag_filters:
        # Filter by tags in personal_details using JSON contains operator
        for tag in tag_filters:
            # Check if the tags array contains the specified tag (case-insensitive)
            filters.append(
                func.json_extract(Person.personal_details, '$.tags').like(f'%"{tag}"%')
            )
    
    if relationship_type:
        # Join with Knows table to filter by relationship archetype
        filters.append(
            select(Knows.archetype)
            .where(and_(
                Knows.to_person_id == Person.person_id,
                Knows.user_id == current_user.user_id,
                func.lower(Knows.archetype).like(f'%{relationship_type.lower()}%')
            ))
            .exists()
        )
    
    if interaction_since_date:
        # Filter by recent interactions
        filters.append(
            select(Interaction.interaction_id)
            .where(and_(
                Interaction.person_id == Person.person_id,
                Interaction.user_id == current_user.user_id,
                Interaction.interaction_date >= interaction_since_date
            ))
            .exists()
        )
    
    # Create the complete search query
    search_query = base_query.where(and_(*filters)).order_by(
        Person.first_name,
        Person.last_name
    ).offset(offset).limit(limit)
    
    # Execute the main search query
    result = db.execute(search_query)
    rows = result.fetchall()
    
    # Get total count for pagination metadata (without offset/limit)
    count_query = select(func.count(Person.person_id)).where(and_(*filters))
    total_count = db.execute(count_query).scalar()
    
    # Format results
    search_results = []
    for row in rows:
        # Extract company and title from professional_info JSON
        professional_info = row.professional_info or {}
        company = professional_info.get('company', '')
        title = professional_info.get('title', '')
        
        # Extract tags from personal_details
        personal_details = row.personal_details or {}
        person_tags = personal_details.get('tags', [])
        
        # Determine which fields matched based on similarity scores
        match_fields = []
        if row.first_name_similarity >= similarity_threshold or row.first_name_word_similarity >= similarity_threshold:
            match_fields.append('first_name')
        if row.last_name_similarity >= similarity_threshold or row.last_name_word_similarity >= similarity_threshold:
            match_fields.append('last_name')
        if row.full_name_similarity >= similarity_threshold or row.full_name_word_similarity >= similarity_threshold:
            match_fields.append('full_name')
        if company and query_lower in company.lower():
            match_fields.append('company')
        
        # Calculate simple relevance score
        relevance_score = max(
            row.first_name_similarity or 0,
            row.last_name_similarity or 0,
            row.full_name_similarity or 0
        )
        
        search_results.append({
            "person_id": str(row.person_id),
            "first_name": row.first_name,
            "last_name": row.last_name,
            "full_name": f"{row.first_name} {row.last_name}",
            "company": company,
            "title": title,
            "tags": person_tags,
            "match_score": float(relevance_score),
            "relevance_score": float(relevance_score),
            "match_fields": match_fields,
        })
    
    # Build response
    response = {
        "results": search_results,
        "pagination": {
            "total": total_count,
            "limit": limit,
            "offset": offset,
            "has_more": (offset + limit) < total_count
        }
    }
    
    if include_metadata:
        response["metadata"] = {
            "query": q,
            "similarity_threshold": similarity_threshold,
            "filters_applied": {
                "company": company,
                "tags": tags,
                "relationship_type": relationship_type,
                "interaction_since": interaction_since
            },
            "search_optimization": {
                "indexes_available": False,  # Simple LIKE search
                "fuzzy_search_enabled": False,
                "similarity_threshold_recommended": 0.3
            }
        }
    
    return response
