# API Endpoints Directory

Individual FastAPI router modules for each resource.

## Endpoints

- `auth.py` - Authentication (register, login, refresh tokens)
- `users.py` - User profile management
- `persons.py` - Contact management
- `organizations.py` - Organization management
- `goals.py` - Goal tracking and management
- `tasks.py` - Task management
- `copilot.py` - AI assistant chat interface
- `ai_engine.py` - AI analysis and suggestions
- `graph.py` - Network visualization endpoints
- `data_portability.py` - Import/export functionality
- `integrations.py` - External service connections
- `search.py` - Search functionality across entities

## Pattern

Each file follows the FastAPI router pattern with:
- Router definition with prefix and tags
- CRUD operations for the resource
- Proper HTTP status codes and responses
- Authentication dependencies where needed