"""
Task management endpoints
"""

from typing import Any, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api.deps import get_current_user, get_db
from app.models.user import User
from app.schemas.task import Task, TaskCreate, TaskUpdate
from app.services.task_service import TaskService
from uuid import UUID as UUID_TYPE


def validate_uuid(uuid_string: str) -> None:
    """Validate UUID format and raise 400 if invalid"""
    try:
        UUID_TYPE(uuid_string)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid UUID format")

router = APIRouter()


@router.get("/", response_model=List[Task])
async def get_tasks(
    completed: Optional[bool] = Query(None, description="Filter by completion status"),
    goal_id: Optional[str] = Query(None, description="Filter by goal ID"),
    skip: int = Query(0, ge=0, description="Number of tasks to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of tasks to return"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get user tasks with optional filtering
    GET /api/v1/tasks/
    """
    task_service = TaskService(db)

    goal_uuid = None
    if goal_id:
        validate_uuid(goal_id)
        goal_uuid = UUID(goal_id)

    tasks = task_service.get_by_user(
        user_id=current_user.user_id,
        completed=completed,
        goal_id=goal_uuid,
        skip=skip,
        limit=limit
    )
    # Convert SQLAlchemy objects to Pydantic schemas
    return [Task.model_validate(task) for task in tasks]


@router.post("/", response_model=Task)
async def create_task(
    task_data: TaskCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Create a new task
    POST /api/v1/tasks/
    """
    task_service = TaskService(db)

    # Create task using the service's create method
    # Add user_id to the task data
    task_dict = task_data.dict()
    task_dict["user_id"] = str(current_user.user_id)

    # Create task directly using the model
    from app.models.task import Task
    task = Task(**task_dict)

    db.add(task)
    db.commit()
    db.refresh(task)
    return task


@router.get("/{task_id}", response_model=Task)
async def get_task(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get specific task
    GET /api/v1/tasks/{id}
    """
    validate_uuid(task_id)
    task_service = TaskService(db)

    task = task_service.get_by_id_and_user(UUID(task_id), current_user.user_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # Convert SQLAlchemy object to Pydantic schema
    return Task.model_validate(task)


@router.put("/{task_id}", response_model=Task)
async def update_task(
    task_id: str,
    task_update: TaskUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Update task
    PUT /api/v1/tasks/{id}
    """
    validate_uuid(task_id)
    task_service = TaskService(db)

    task = task_service.get_by_id_and_user(UUID(task_id), current_user.user_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    updated_task = task_service.update(UUID(task_id), task_update.dict(exclude_unset=True))
    # Convert SQLAlchemy object to Pydantic schema
    return Task.model_validate(updated_task)


@router.delete("/{task_id}")
async def delete_task(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Delete task
    DELETE /api/v1/tasks/{id}
    """
    validate_uuid(task_id)
    task_service = TaskService(db)

    task = task_service.get_by_id_and_user(UUID(task_id), current_user.user_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    task_service.delete(task)
    return {"message": "Task deleted successfully"}


@router.post("/{task_id}/complete", response_model=Task)
async def complete_task(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Mark task as completed
    POST /api/v1/tasks/{id}/complete
    """
    validate_uuid(task_id)
    task_service = TaskService(db)

    task = task_service.mark_completed(UUID(task_id), current_user.user_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    return task
