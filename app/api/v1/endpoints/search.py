"""
Search endpoints for fuzzy search functionality
"""

from typing import Any, List

from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, text, select, case, and_, or_, Integer, desc
from datetime import datetime, timedelta
from typing import Optional

from app.api.deps import get_current_user
from app.core.database import get_db
from app.models.user import User
from app.models.person import Person
from app.models.relationship import Knows
from app.models.interaction import Interaction
# Removed complex database compatibility layer for now

router = APIRouter()


@router.get("/persons", response_model=dict)
def search_persons(
    q: str = Query(..., description="Search query"),
    limit: int = Query(10, ge=1, le=100, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Number of results to skip (for pagination)"),
    similarity_threshold: float = Query(0.3, ge=0.0, le=1.0, description="Similarity threshold"),
    # Filtering parameters
    company: str = Query(None, description="Filter by company/organization"),
    tags: str = Query(None, description="Filter by tags (comma-separated)"),
    relationship_type: str = Query(None, description="Filter by relationship archetype"),
    interaction_since: str = Query(None, description="Filter by interactions since date (YYYY-MM-DD)"),
    # Advanced options
    include_metadata: bool = Query(True, description="Include search metadata and facets"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Fuzzy search for persons using PostgreSQL pg_trgm extension with advanced ranking,
    pagination, and filtering capabilities.
    GET /api/v1/search/persons?q=...&limit=10&offset=0&company=...&tags=...
    """
    # Build fuzzy search query using pg_trgm similarity functions
    query_lower = q.lower()
    
    # Parse date filter if provided
    interaction_since_date = None
    if interaction_since:
        try:
            interaction_since_date = datetime.strptime(interaction_since, '%Y-%m-%d')
        except ValueError:
            # Invalid date format, ignore filter
            pass
    
    # Parse tags filter if provided
    tag_filters = []
    if tags:
        tag_filters = [tag.strip().lower() for tag in tags.split(',') if tag.strip()]
    
    # Build compatible search query
    search_components = build_compatible_search_query(query_lower, Person, similarity_threshold)

    # Build the base query with all similarity calculations
    base_query = select(
        Person.person_id,
        Person.first_name,
        Person.last_name,
        Person.professional_info,
        Person.personal_details,
        # Calculate similarity scores for different fields
        search_components['similarity_scores']['first_name_similarity'].label('first_name_similarity'),
        search_components['similarity_scores']['last_name_similarity'].label('last_name_similarity'),
        search_components['similarity_scores']['full_name_similarity'].label('full_name_similarity'),
        # Calculate word similarity for partial matching
        search_components['similarity_scores']['first_name_word_similarity'].label('first_name_word_similarity'),
        search_components['similarity_scores']['last_name_word_similarity'].label('last_name_word_similarity'),
        search_components['similarity_scores']['full_name_word_similarity'].label('full_name_word_similarity'),
        # Calculate base similarity score
        search_components['base_similarity'].label('base_similarity'),
        # Organization/company similarity boost
        case(
            (func.lower(func.coalesce(get_db_specific_json_extract_func(Person.professional_info, 'company'), '')).like(f'%{query_lower}%'), 0.2),
            else_=0.0
        ).label('company_boost'),
        # Relationship strength boost - get relationship score if exists
        func.coalesce(
            select(func.cast(get_db_specific_json_extract_func(Knows.relationship_depth, 'overall_score'), Integer) / 100.0 * 0.3)
            .where(and_(Knows.to_person_id == Person.person_id, Knows.user_id == current_user.user_id))
            .scalar_subquery(),
            0.0
        ).label('relationship_boost'),
        # Calculate final relevance score with all boost factors
        (search_components['base_similarity'] +
        case(
            (func.lower(func.coalesce(get_db_specific_json_extract_func(Person.professional_info, 'company'), '')).like(f'%{query_lower}%'), 0.2),
            else_=0.0
        ) +
        func.coalesce(
            select(func.cast(get_db_specific_json_extract_func(Knows.relationship_depth, 'overall_score'), Integer) / 100.0 * 0.3)
            .where(and_(Knows.to_person_id == Person.person_id, Knows.user_id == current_user.user_id))
            .scalar_subquery(),
            0.0
        )).label('relevance_score')
    )
    
    # Apply base filters
    filters = [
        (Person.user_id == current_user.user_id),
        (Person.is_user == False),  # Exclude user's own person record
    ]
    
    # Use the compatible search filters
    filters.append(or_(*search_components['filters']))
    
    # Apply additional filters
    if company:
        filters.append(
            func.lower(func.coalesce(get_db_specific_json_extract_func(Person.professional_info, 'company'), '')).like(f'%{company.lower()}%')
        )

    if tag_filters:
        # Filter by tags in personal_details using JSON contains operator
        for tag in tag_filters:
            # Check if the tags array contains the specified tag (case-insensitive)
            filters.append(
                get_db_specific_json_extract_func(Person.personal_details, 'tags').like(f'%"{tag}"%')
            )
    
    if relationship_type:
        # Join with Knows table to filter by relationship archetype
        filters.append(
            select(Knows.archetype)
            .where(and_(
                Knows.to_person_id == Person.person_id,
                Knows.user_id == current_user.user_id,
                func.lower(Knows.archetype).like(f'%{relationship_type.lower()}%')
            ))
            .exists()
        )
    
    if interaction_since_date:
        # Filter by recent interactions (this would need proper interaction-person mapping)
        # For now, we'll use relationship creation/update date as a proxy
        filters.append(
            select(Knows.updated_at)
            .where(and_(
                Knows.to_person_id == Person.person_id,
                Knows.user_id == current_user.user_id,
                Knows.updated_at >= interaction_since_date
            ))
            .exists()
        )
    
    # Create the complete search query
    # Apply all filters and create the search query
    search_query = base_query.where(and_(*filters)).order_by(
        text('relevance_score DESC'),
        Person.first_name,
        Person.last_name
    ).offset(offset).limit(limit)
    
    # Execute the main search query
    result = db.execute(search_query)
    rows = result.fetchall()
    
    # Get total count for pagination metadata (without offset/limit)
    count_query = select(func.count(Person.person_id)).where(and_(*filters))
    total_count = db.execute(count_query).scalar()
    
    # Format results
    search_results = []
    for row in rows:
        # Extract company and title from professional_info JSON
        professional_info = row.professional_info or {}
        company = professional_info.get('company', '')
        title = professional_info.get('title', '')
        
        # Extract tags from personal_details
        personal_details = row.personal_details or {}
        person_tags = personal_details.get('tags', [])
        
        # Determine which fields matched based on similarity scores
        match_fields = []
        if row.first_name_similarity >= similarity_threshold or row.first_name_word_similarity >= similarity_threshold:
            match_fields.append('first_name')
        if row.last_name_similarity >= similarity_threshold or row.last_name_word_similarity >= similarity_threshold:
            match_fields.append('last_name')
        if row.full_name_similarity >= similarity_threshold or row.full_name_word_similarity >= similarity_threshold:
            match_fields.append('full_name')
        if row.company_boost > 0:
            match_fields.append('company')
        
        # Calculate boost details for transparency
        boost_details = {
            "base_similarity": float(row.base_similarity),
            "company_boost": float(row.company_boost),
            "relationship_boost": float(row.relationship_boost)
        }
        
        search_results.append({
            "person_id": str(row.person_id),
            "first_name": row.first_name,
            "last_name": row.last_name,
            "full_name": f"{row.first_name} {row.last_name}",
            "company": company,
            "title": title,
            "tags": person_tags,
            "match_score": float(row.base_similarity),  # Base similarity for compatibility
            "relevance_score": float(row.relevance_score),  # Enhanced relevance score
            "match_fields": match_fields,
            "boost_details": boost_details,  # Detailed boost breakdown
        })
    
    # Build response with pagination metadata
    response = {
        "results": search_results,
        "pagination": {
            "total": total_count,
            "limit": limit,
            "offset": offset,
            "has_next": offset + limit < total_count,
            "has_previous": offset > 0,
            "total_pages": (total_count + limit - 1) // limit,
            "current_page": (offset // limit) + 1
        }
    }
    
    # Add search metadata and facets if requested
    if include_metadata:
        # Get available filter facets
        facets = {}
        
        # Get available companies
        company_query = select(
            func.jsonb_extract_path_text(Person.professional_info, 'company').label('company'),
            func.count().label('count')
        ).where(
            and_(
                Person.user_id == current_user.user_id,
                Person.is_user == False,
                func.jsonb_extract_path_text(Person.professional_info, 'company').isnot(None),
                func.jsonb_extract_path_text(Person.professional_info, 'company') != ''
            )
        ).group_by(func.jsonb_extract_path_text(Person.professional_info, 'company')).order_by(desc('count'))
        
        company_results = db.execute(company_query).fetchall()
        facets['companies'] = [
            {"value": row.company, "count": row.count}
            for row in company_results[:10]  # Limit to top 10
            if row.company
        ]
        
        # Get available relationship types
        relationship_query = select(
            Knows.archetype,
            func.count().label('count')
        ).where(
            and_(
                Knows.user_id == current_user.user_id,
                Knows.archetype.isnot(None),
                Knows.archetype != ''
            )
        ).group_by(Knows.archetype).order_by(desc('count'))
        
        rel_results = db.execute(relationship_query).fetchall()
        facets['relationship_types'] = [
            {"value": row.archetype, "count": row.count}
            for row in rel_results[:10]  # Limit to top 10
            if row.archetype
        ]
        
        response["metadata"] = {
            "query": q,
            "similarity_threshold": similarity_threshold,
            "filters_applied": {
                "company": company,
                "tags": tags,
                "relationship_type": relationship_type,
                "interaction_since": interaction_since
            },
            "facets": facets
        }
    
    return response


@router.get("/organizations", response_model=List[dict])
def search_organizations(
    q: str = Query(..., description="Search query"),
    limit: int = Query(10, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Search for organizations
    """
    # TODO: Implement organization search
    return [
        {
            "org_id": "org_1",
            "name": "Tech Corporation",
            "industry": "Technology",
            "match_score": 0.9,
            "employee_count": 5,
        }
    ]


@router.get("/global", response_model=dict)
def global_search(
    q: str = Query(..., description="Search query"),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Global search across all entities
    """
    # TODO: Implement global search
    return {
        "query": q,
        "results": {
            "persons": [],
            "organizations": [],
            "goals": [],
            "tasks": [],
            "notes": [],
        },
        "total_results": 0,
    }


@router.get("/suggestions", response_model=List[str])
def search_suggestions(
    q: str = Query(..., min_length=1, max_length=100, description="Search query prefix"),
    limit: int = Query(5, ge=1, le=20, description="Maximum number of suggestions"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get search suggestions/autocomplete for person names and companies
    GET /api/v1/search/suggestions?q=...
    """
    query_lower = q.lower()
    suggestions = set()
    
    # Get name suggestions from persons
    name_query = select(
        Person.first_name,
        Person.last_name,
        func.concat(Person.first_name, ' ', Person.last_name).label('full_name')
    ).where(
        and_(
            Person.user_id == current_user.user_id,
            Person.is_user == False,
            or_(
                func.lower(Person.first_name).like(f'{query_lower}%'),
                func.lower(Person.last_name).like(f'{query_lower}%'),
                func.lower(func.concat(Person.first_name, ' ', Person.last_name)).like(f'%{query_lower}%')
            )
        )
    ).limit(limit * 2)  # Get more results to filter and sort
    
    name_results = db.execute(name_query).fetchall()
    
    # Add name suggestions
    for row in name_results:
        first_name = row.first_name or ""
        last_name = row.last_name or ""
        full_name = f"{first_name} {last_name}".strip()
        
        # Add individual names if they match
        if first_name.lower().startswith(query_lower):
            suggestions.add(first_name)
        if last_name.lower().startswith(query_lower):
            suggestions.add(last_name)
        if query_lower in full_name.lower():
            suggestions.add(full_name)
    
    # Get company suggestions
    company_query = select(
        func.distinct(func.jsonb_extract_path_text(Person.professional_info, 'company')).label('company')
    ).where(
        and_(
            Person.user_id == current_user.user_id,
            Person.is_user == False,
            func.jsonb_extract_path_text(Person.professional_info, 'company').isnot(None),
            func.lower(func.jsonb_extract_path_text(Person.professional_info, 'company')).like(f'{query_lower}%')
        )
    ).limit(limit)
    
    company_results = db.execute(company_query).fetchall()
    
    # Add company suggestions
    for row in company_results:
        company = row.company
        if company and company.strip():
            suggestions.add(company)
    
    # Sort suggestions by relevance (starts with query first, then contains)
    suggestions_list = list(suggestions)
    
    # Sort: exact match first, then starts with, then contains
    def suggestion_sort_key(suggestion: str) -> tuple:
        lower_suggestion = suggestion.lower()
        if lower_suggestion == query_lower:
            return (0, len(suggestion))  # Exact match, shorter first
        elif lower_suggestion.startswith(query_lower):
            return (1, len(suggestion))  # Starts with, shorter first
        else:
            return (2, len(suggestion))  # Contains, shorter first
    
    suggestions_list.sort(key=suggestion_sort_key)
    
    return suggestions_list[:limit]


@router.get("/analytics", response_model=dict)
def search_analytics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get search analytics and insights
    GET /api/v1/search/analytics
    """
    # Basic search analytics
    analytics = {
        "total_persons": db.query(func.count(Person.person_id)).filter(
            Person.user_id == current_user.user_id,
            Person.is_user == False
        ).scalar(),
        "total_relationships": db.query(func.count(Knows.from_person_id)).filter(
            Knows.user_id == current_user.user_id
        ).scalar(),
        "companies": {},
        "search_optimization": {
            "indexes_available": True,  # pg_trgm indexes
            "fuzzy_search_enabled": True,
            "similarity_threshold_recommended": 0.3
        }
    }
    
    # Company distribution
    company_stats = db.execute(
        select(
            func.jsonb_extract_path_text(Person.professional_info, 'company').label('company'),
            func.count().label('count')
        ).where(
            and_(
                Person.user_id == current_user.user_id,
                Person.is_user == False,
                func.jsonb_extract_path_text(Person.professional_info, 'company').isnot(None),
                func.jsonb_extract_path_text(Person.professional_info, 'company') != ''
            )
        ).group_by(
            func.jsonb_extract_path_text(Person.professional_info, 'company')
        ).order_by(desc('count'))
    ).fetchall()
    
    analytics["companies"] = {
        row.company: row.count for row in company_stats if row.company
    }
    
    # Search performance insights
    total_searchable = analytics["total_persons"]
    if total_searchable > 0:
        if total_searchable < 100:
            performance_tier = "optimal"
        elif total_searchable < 1000:
            performance_tier = "good"
        elif total_searchable < 10000:
            performance_tier = "moderate"
        else:
            performance_tier = "large"
        
        analytics["search_optimization"]["performance_tier"] = performance_tier
        analytics["search_optimization"]["estimated_search_time"] = {
            "optimal": "< 0.1s",
            "good": "< 0.5s", 
            "moderate": "< 1.0s",
            "large": "< 2.0s"
        }.get(performance_tier, "unknown")
    
    return analytics
