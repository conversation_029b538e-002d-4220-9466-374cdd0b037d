"""
Main API router for v1 endpoints
Based on the requirements traceability matrix from technical documentation
"""

from fastapi import APIRouter

from app.api.v1.endpoints import (admin, ai_engine, auth, copilot, data_portability,
                                  goals, graph, integrations, organizations,
                                  persons, relationships, search_simple, tasks, users)
from app.api.v1.endpoints import graph_websocket

api_router = APIRouter()

# Authentication endpoints
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])

# User management endpoints
api_router.include_router(users.router, prefix="/users", tags=["users"])

# Core entity endpoints
api_router.include_router(persons.router, prefix="/persons", tags=["persons"])
api_router.include_router(relationships.router, prefix="/relationships", tags=["relationships"])
api_router.include_router(
    organizations.router, prefix="/organizations", tags=["organizations"]
)

# Goal and task management endpoints
api_router.include_router(goals.router, prefix="/goals", tags=["goals"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["tasks"])

# AI and intelligent features
api_router.include_router(copilot.router, prefix="/copilot", tags=["copilot"])
api_router.include_router(ai_engine.router, prefix="/ai", tags=["ai-engine"])

# Graph and network analysis
api_router.include_router(graph.router, prefix="/graph", tags=["graph"])
api_router.include_router(graph_websocket.router, prefix="/graph", tags=["graph-websocket"])

# Data management
api_router.include_router(
    data_portability.router, prefix="/data", tags=["data-portability"]
)

# External integrations
api_router.include_router(
    integrations.router, prefix="/integrations", tags=["integrations"]
)

# Search functionality
api_router.include_router(search_simple.router, prefix="/search", tags=["search"])

# Admin endpoints (for testing and maintenance)
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
