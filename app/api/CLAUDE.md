# API Directory - FastAPI REST API Layer

This directory contains the comprehensive REST API implementation for the Nexus relationship management platform.

## API Architecture

### RESTful Design
The API follows RESTful principles with:
- **Resource-Based URLs**: Clear endpoint hierarchy
- **HTTP Method Semantics**: Proper use of GET, POST, PUT, DELETE
- **Status Codes**: Appropriate HTTP status code usage
- **Content Negotiation**: JSON request/response handling
- **Error Handling**: Standardized error responses

### Security Implementation
- **JWT Authentication**: Secure token-based authentication
- **User Data Isolation**: Complete separation between user accounts
- **Input Validation**: Comprehensive Pydantic schema validation
- **Stateless Design**: Multi-instance deployment ready with no session affinity
- **Rate Limiting**: API abuse prevention (ready for implementation)
- **CORS Configuration**: Secure cross-origin request handling

## Core Infrastructure

### `deps.py` - Dependency Injection System
**Centralized dependency management for authentication and database access**

**Key Dependencies**:
```python
def get_current_user(token: str = Depends(oauth2_scheme)) -> User:
    # JWT token validation and user retrieval
    # Automatic token expiration handling
    # Secure user authentication

def get_db() -> Generator[Session, None, None]:
    # Database session management
    # Automatic connection cleanup
    # Transaction handling
```

**Features**:
- **Authentication**: JWT token validation and user retrieval
- **Database Sessions**: SQLAlchemy session lifecycle management
- **Error Handling**: Automatic HTTP exception raising
- **Security**: Token validation with expiration checking

### `v1/api.py` - Main API Router Configuration
**Central API router aggregating all endpoint modules**

**Router Integration**:
```python
api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(persons.router, prefix="/persons", tags=["contacts"])
api_router.include_router(organizations.router, prefix="/organizations", tags=["organizations"])
# ... additional router inclusions
```

## API Endpoints

### `v1/endpoints/` - Individual Endpoint Modules

#### `auth.py` - Authentication and User Management
**Secure user authentication with JWT token management**

**Endpoints**:
- `POST /auth/register` - User registration with validation
- `POST /auth/login` - User login with JWT token generation
- `POST /auth/refresh` - Token refresh for extended sessions
- `GET /auth/me` - Current user profile information

**Security Features**:
- Password complexity validation
- Secure password hashing with bcrypt
- JWT token generation and validation
- Rate limiting protection (configurable)

#### `persons.py` - Contact Management
**Comprehensive contact management with relationship integration**

**Core Endpoints**:
- `POST /persons/` - Create new contact
- `GET /persons/` - List contacts with filtering and pagination
- `GET /persons/{person_id}` - Get contact details
- `PUT /persons/{person_id}` - Update contact information
- `DELETE /persons/{person_id}` - Delete contact

**Advanced Features**:
- Contact search with fuzzy matching
- Relationship tracking and management
- Work history integration
- Tag-based organization
- Bulk operations support

#### `organizations.py` - Organization Management
**Complete organizational CRUD with work relationship tracking**

**Organization Endpoints**:
- `POST /organizations/` - Create organization
- `GET /organizations/` - List organizations with search/filtering
- `GET /organizations/{org_id}` - Get organization details
- `PUT /organizations/{org_id}` - Update organization
- `DELETE /organizations/{org_id}` - Delete organization with cascade
- `GET /organizations/{org_id}/employees` - Get organization with employees
- `GET /organizations/stats/summary` - Organization statistics

**Work Relationship Endpoints**:
- `POST /organizations/work-relationships` - Create work relationship
- `PUT /organizations/work-relationships/{person_id}/{org_id}` - Update work relationship
- `DELETE /organizations/work-relationships/{person_id}/{org_id}` - Delete work relationship
- `GET /organizations/persons/{person_id}/work-history` - Person's work history

#### `relationships.py` - Six-Dimensional Relationship Management
**Sophisticated relationship management with archetype system**

**Relationship Endpoints**:
- `POST /relationships/` - Create new relationship
- `GET /relationships/` - List relationships with filtering
- `GET /relationships/{person_id}/{known_person_id}` - Get specific relationship
- `PUT /relationships/{person_id}/{known_person_id}` - Update relationship
- `DELETE /relationships/{person_id}/{known_person_id}` - Delete relationship

**Advanced Features**:
- `POST /relationships/bulk` - Bulk relationship operations
- `GET /relationships/archetypes` - Get available archetypes
- `POST /relationships/apply-archetype` - Apply archetype template
- `GET /relationships/stats` - Relationship statistics
- `POST /relationships/dimensions/update` - Update specific dimensions
- `GET /relationships/history/{person_id}/{known_person_id}` - Relationship history
- `POST /relationships/validate` - Validate relationship data

#### `goals.py` - AI-Enhanced Goal Management
**Goal tracking with AI-powered achievement strategies**

**Goal Endpoints**:
- `POST /goals/` - Create new goal
- `GET /goals/` - List goals with filtering
- `GET /goals/{goal_id}` - Get goal details
- `PUT /goals/{goal_id}` - Update goal
- `DELETE /goals/{goal_id}` - Delete goal

**AI-Powered Features**:
- `GET /goals/{goal_id}/dashboard` - Comprehensive goal dashboard
- `GET /goals/{goal_id}/ai-analysis` - AI-powered achievement strategy
- `GET /goals/{goal_id}/recommendations` - Recommended connections
- `GET /goals/{goal_id}/success-prediction` - Success probability analysis
- `POST /goals/{goal_id}/person/{person_id}` - Associate person with goal

#### `ai_engine.py` - AI-Powered Networking Intelligence
**Core AI functionality for relationship analysis and insights**

**AI Endpoints**:
- `GET /ai/suggestions` - Get AI-powered networking suggestions
- `POST /ai/analyze-relationship` - Analyze specific relationship
- `GET /ai/network-insights` - Comprehensive network analysis
- `POST /ai/find-referral-path` - Find referral paths for introductions
- `GET /ai/goal-person-matching/{goal_id}` - Match persons to goals

**AI Features**:
- Proactive networking suggestions
- Relationship strength analysis
- Network expansion recommendations
- Goal-oriented connection matching

#### `graph.py` - Network Visualization
**Real-time network graph data generation**

**Graph Endpoints**:
- `GET /graph/data` - Get network graph data
- `GET /graph/filtered` - Get filtered graph data
- `GET /graph/person/{person_id}` - Get person-centered graph
- `GET /graph/organization/{org_id}` - Get organization-centered graph

**Visualization Features**:
- Multiple layout algorithms
- Community detection
- Centrality analysis
- Real-time filtering

#### `search.py` - Advanced Search Engine
**High-performance fuzzy search with PostgreSQL pg_trgm**

**Search Endpoints**:
- `GET /search/persons` - Search contacts with fuzzy matching
- `GET /search/organizations` - Search organizations
- `GET /search/global` - Global search across all entities
- `GET /search/suggestions` - Search suggestions and autocomplete

**Search Features**:
- Sub-50ms query performance
- Relevance scoring and ranking
- Multi-field search capabilities
- Advanced filtering options

#### `data_portability.py` - Data Export/Import System
**Multi-format data portability with background processing**

**Export Endpoints**:
- `POST /data/export` - Trigger export job
- `GET /data/export/{job_id}` - Get export status and download link
- `GET /data/download/{job_id}` - Download export file

**Import Endpoints**:
- `POST /data/import` - Import data from file
- `GET /data/import/{job_id}` - Get import status and results
- `POST /data/preview` - Preview import data with suggested mappings

**Job Management**:
- `GET /data/jobs` - List export/import jobs

**Format Support**:
- JSON: Complete data export with metadata
- CSV: Tabular export with custom field mapping
- vCard: Contact format for interoperability

#### `copilot.py` - Stateless AI Chat Interface
**AI-powered networking guidance with multi-instance conversation persistence**

**Chat Endpoints**:
- `POST /copilot/converse` - Interactive AI chat with database-backed conversation history
- `POST /copilot/analyze` - Analyze message intent and entities without executing actions
- `GET /copilot/suggestions` - Get enhanced AI suggestions with LLM recommendations
- `GET /copilot/conversation/{conversation_id}` - Retrieve conversation history (stateless)
- `DELETE /copilot/conversation/{conversation_id}` - Clear conversation history

**Stateless Features**:
- **Database-Backed Conversations**: All conversation history stored in database
- **Multi-Instance Ready**: No session affinity required for load balancing
- **User Isolation**: Complete conversation data separation between users
- **Tool Call Persistence**: Complex AI function calls stored and retrievable

#### `integrations.py` - External Service Integrations
**External service connections (Google Calendar/Contacts) - Task 10**

**Integration Endpoints** (To be implemented):
- `POST /integrations/google/calendar/sync` - Sync Google Calendar
- `POST /integrations/google/contacts/sync` - Sync Google Contacts
- `GET /integrations/status` - Get integration status

## API Response Patterns

### Success Responses
```json
{
  "success": true,
  "data": {...},
  "message": "Operation completed successfully"
}
```

### Error Responses
```json
{
  "detail": "Validation error description",
  "error_type": "validation_error",
  "field_errors": {...}
}
```

### Pagination Responses
```json
{
  "items": [...],
  "total": 150,
  "page": 1,
  "per_page": 20,
  "pages": 8,
  "has_next": true,
  "has_prev": false
}
```

## Performance Characteristics

### Response Times
- **Simple CRUD Operations**: < 50ms average
- **Search Operations**: < 100ms with fuzzy matching
- **AI Analysis**: 2-5 seconds with caching
- **Graph Generation**: < 200ms for 1000+ nodes
- **Background Jobs**: Async processing with progress tracking

### Scalability Features
- **Stateless Architecture**: Multi-instance deployment ready
- **Connection Pooling**: Efficient database connection management
- **Query Optimization**: Indexed queries with minimal N+1 problems
- **Response Caching**: Intelligent caching for AI analysis
- **Async Processing**: Background jobs for long-running operations
- **Load Balancing**: No session affinity required

## Security Implementation

### Authentication Flow
1. User registration with password validation
2. Login with JWT token generation
3. Token validation on protected endpoints
4. Automatic token refresh for extended sessions

### Data Protection
- **User Isolation**: Complete data separation between users
- **Input Validation**: Comprehensive Pydantic schema validation
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Proper output encoding
- **Rate Limiting**: API abuse prevention (configurable)

### Error Handling
- **Secure Error Messages**: No sensitive information leakage
- **Standardized Responses**: Consistent error format
- **Logging**: Comprehensive error logging for debugging
- **Graceful Degradation**: Fallback mechanisms for service failures

For complete schema definitions and business logic, refer to the `../schemas/` and `../services/` directories.