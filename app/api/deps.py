"""
API dependencies
"""

from typing import Generator, Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON>2P<PERSON>wordBearer
from sqlalchemy.orm import Session
from supabase import Client

from app.core.config import settings
from app.core.database import get_db, get_supabase
from app.core.security import create_credentials_exception
from app.models.user import User
from app.services.user_service import UserService

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")


# Simple token blacklist for testing (moved here to avoid circular import)
_blacklisted_tokens = set()

def get_current_user_local(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db),
) -> User:
    """
    Get current authenticated user from local database (for testing)
    """
    # Check if token is blacklisted
    if token in _blacklisted_tokens:
        raise create_credentials_exception("Token has been invalidated")

    # For testing, extract user_id from test token
    if token.startswith("test-token-"):
        try:
            # Extract user_id from token format: test-token-{uuid}-xxx
            # Remove the prefix and suffix to get the UUID
            token_without_prefix = token[11:]  # Remove "test-token-"
            # Find the last occurrence of "-xxx" pattern
            last_dash_index = token_without_prefix.rfind("-")
            if last_dash_index > 0:
                user_id = token_without_prefix[:last_dash_index]
                user_service = UserService(db)
                user = user_service.get(user_id)
                if user and user.is_active:
                    return user
        except Exception:
            pass

    # Handle JWT tokens
    try:
        from jose import jwt
        from app.core.config import settings

        # Decode JWT token
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
            audience="nexus-app",
            issuer="nexus-local-auth"
        )
        user_id = payload.get("sub")
        if user_id is None:
            raise create_credentials_exception("Could not validate credentials")

        user_service = UserService(db)
        user = user_service.get(user_id)
        if user is None or not user.is_active:
            raise create_credentials_exception("Could not validate credentials")

        return user
    except jwt.JWTError:
        pass

    raise create_credentials_exception("Could not validate credentials")


def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db),
    supabase: Client = Depends(get_supabase),
) -> User:
    """
    Get current authenticated user from Supabase or local JWT
    """
    # For testing with SQLite, use local authentication
    if settings.DB_SOURCE == "sqlite":
        return get_current_user_local(token, db)

    # Try local JWT first (for testing endpoints like register-local/login-local)
    try:
        from jose import jwt

        # Check if this is a local JWT token
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
            audience="nexus-app",
            issuer="nexus-local-auth"
        )
        user_id = payload.get("sub")
        if user_id:
            user_service = UserService(db)
            user = user_service.get(user_id)
            if user and user.is_active:
                return user
    except jwt.JWTError:
        pass  # Not a local JWT, try Supabase

    # Try Supabase authentication
    try:
        user_response = supabase.auth.get_user(token)
        supabase_user = user_response.user
    except Exception:
        raise create_credentials_exception("Could not validate credentials")

    if not supabase_user:
        raise create_credentials_exception("User not found")

    user_service = UserService(db)
    user = user_service.get_by_email(supabase_user.email)

    if not user:
        # If user doesn't exist in our DB, create them
        new_user_data = {
            "id": supabase_user.id,
            "email": supabase_user.email,
            "is_active": True,
            "is_verified": True,  # Or based on Supabase user properties
        }
        user = user_service.create(new_user_data, from_supabase=True)

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user"
        )

    return user


def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Get current active user
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user"
        )
    return current_user


def get_current_verified_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Get current verified user
    """
    if not current_user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="User not verified"
        )
    return current_user
