"""
Tests for AI Engine Service
Comprehensive tests for AI-powered relationship analysis
"""

import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from app.services.ai_engine_service import AIEngineService
from app.models.user import User
from app.models.person import Person
from app.models.relationship import Knows
from app.models.interaction import Interaction
from app.tests.base import BaseIntegrationTestCase


class TestAIEngineService:
    """Test AI Engine Service functionality"""

    def setup_method(self):
        self.test_user_id = uuid.uuid4()
        self.test_person_id = uuid.uuid4()

    @pytest.mark.asyncio
    async def test_get_active_suggestions_basic(self, db):
        """Test getting active suggestions"""
        ai_service = AIEngineService(db)
        
        # Create test user
        self.test_user = User(
            user_id=uuid.uuid4(),
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_active=True
        )
        db.add(self.test_user)

        # Create test persons
        self.test_person1 = Person(
            person_id=uuid.uuid4(),
            user_id=self.test_user.user_id,
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            professional_info={
                "title": "Software Engineer",
                "company": "TechCorp",
                "industry": "Technology"
            },
            is_user=False
        )

        self.test_person2 = Person(
            person_id=uuid.uuid4(),
            user_id=self.test_user.user_id,
            first_name="Jane",
            last_name="Smith",
            professional_info={
                "title": "Product Manager",
                "company": "StartupCo",
                "industry": "Technology"
            },
            is_user=False
        )

        db.add_all([self.test_person1, self.test_person2])
        
        # Create test relationship
        self.test_relationship = Knows(
            from_person_id=self.test_user.user_id,
            to_person_id=self.test_person1.person_id,
            user_id=self.test_user.user_id,
            archetype="colleague",
            relationship_depth={
                "overall_score": 75,
                "dimensions": {
                    "emotional_intimacy": 60,
                    "professional_collaboration": 85,
                    "trust_level": 70,
                    "communication_frequency": 80,
                    "shared_experience_value": 65,
                    "reciprocity_balance": 75
                },
                "history": [
                    {
                        "timestamp": "2025-06-01T10:00:00Z",
                        "event": "Created",
                        "score_change": "+0"
                    }
                ]
            }
        )
        db.add(self.test_relationship)
        db.commit()

        # Test basic suggestion generation
        suggestions = await ai_service.get_active_suggestions(self.test_user.user_id)
        
        assert isinstance(suggestions, list)
        # Should have at least some suggestions
        assert len(suggestions) > 0
        
        # Check suggestion structure
        for suggestion in suggestions:
            assert "id" in suggestion
            assert "type" in suggestion
            assert "title" in suggestion
            assert "priority_score" in suggestion

    @pytest.mark.asyncio
    async def test_find_dormant_relationships(self, db):
        """Test dormant relationship detection"""
        ai_service = AIEngineService(db)

        # Create test user
        test_user = User(
            user_id=uuid.uuid4(),
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_active=True
        )
        db.add(test_user)

        # Create test person
        test_person2 = Person(
            person_id=uuid.uuid4(),
            user_id=test_user.user_id,
            first_name="Jane",
            last_name="Smith",
            is_user=False
        )
        db.add(test_person2)

        # Create a high-scoring relationship without recent interactions
        dormant_rel = Knows(
            from_person_id=test_user.user_id,
            to_person_id=test_person2.person_id,
            user_id=test_user.user_id,
            archetype="friend",
            relationship_depth={
                "overall_score": 85,  # High score
                "dimensions": {
                    "emotional_intimacy": 80,
                    "professional_collaboration": 70,
                    "trust_level": 90,
                    "communication_frequency": 60,
                    "shared_experience_value": 85,
                    "reciprocity_balance": 80
                }
            }
        )
        db.add(dormant_rel)
        db.commit()

        relationships = [dormant_rel]
        dormant = await ai_service._find_dormant_relationships(
            test_user.user_id, relationships
        )

        # Should find the dormant relationship (no recent interactions)
        assert len(dormant) >= 1
        assert dormant_rel in dormant

    @pytest.mark.asyncio
    async def test_find_relationship_imbalances(self):
        """Test relationship imbalance detection"""
        # Create relationship with high variance in dimensions
        imbalanced_rel = Knows(
            from_person_id=self.test_user.user_id,
            to_person_id=self.test_person2.person_id,
            user_id=self.test_user.user_id,
            archetype="mentor",
            relationship_depth={
                "overall_score": 65,
                "dimensions": {
                    "emotional_intimacy": 30,  # Very low
                    "professional_collaboration": 95,  # Very high
                    "trust_level": 40,  # Low
                    "communication_frequency": 90,  # Very high
                    "shared_experience_value": 35,  # Low
                    "reciprocity_balance": 25  # Very low
                }
            }
        )
        self.db_session.add(imbalanced_rel)
        self.db_session.commit()
        
        relationships = [imbalanced_rel]
        imbalanced = await self.ai_service._find_relationship_imbalances(
            self.test_user.user_id, relationships
        )
        
        # Should detect the imbalanced relationship
        assert len(imbalanced) >= 1
        assert imbalanced_rel in imbalanced

    @pytest.mark.asyncio
    async def test_get_relationship_prism(self):
        """Test relationship prism analysis"""
        analysis = await self.ai_service.get_relationship_prism(
            self.test_user.user_id,
            self.test_user.user_id,
            self.test_person1.person_id
        )
        
        assert analysis["relationship_exists"] is True
        assert "overall_score" in analysis
        assert "dimensions" in analysis
        assert "radar_chart_data" in analysis
        assert "asymmetry_analysis" in analysis
        assert "personalized_insights" in analysis
        assert "recommendations" in analysis
        assert "enhanced_metrics" in analysis
        assert "trend_analysis" in analysis
        
        # Check radar chart data structure
        radar_data = analysis["radar_chart_data"]
        assert len(radar_data) == 6  # Six dimensions
        for dimension_data in radar_data:
            assert "dimension" in dimension_data
            assert "score" in dimension_data
            assert "weight" in dimension_data
            assert "weighted_score" in dimension_data

    @pytest.mark.asyncio
    async def test_calculate_relationship_score_new_relationship(self):
        """Test relationship scoring for new relationship"""
        new_person = Person(
            person_id=uuid.uuid4(),
            user_id=self.test_user.user_id,
            first_name="Alice",
            last_name="Johnson",
            professional_info={
                "title": "Designer",
                "company": "CreativeAgency"
            },
            is_user=False
        )
        self.db_session.add(new_person)
        self.db_session.commit()
        
        interaction_data = {
            "interaction_type": "meeting",
            "sentiment": "positive",
            "context": "Initial business meeting"
        }
        
        result = await self.ai_service.calculate_relationship_score(
            self.test_user.user_id,
            new_person.person_id,
            interaction_data
        )
        
        assert "error" not in result
        assert "overall_score" in result
        assert "dimensions" in result
        assert "previous_dimensions" in result
        assert "score_change" in result
        assert "user_preferences" in result
        
        # Should have default dimensions since it's a new relationship
        dimensions = result["dimensions"]
        assert len(dimensions) == 6
        assert all(0 <= score <= 100 for score in dimensions.values())

    @pytest.mark.asyncio
    async def test_update_dimensions_from_interaction(self):
        """Test dimension updates based on interaction"""
        current_dimensions = {
            "emotional_intimacy": 50,
            "professional_collaboration": 60,
            "trust_level": 55,
            "communication_frequency": 40,
            "shared_experience_value": 45,
            "reciprocity_balance": 50
        }
        
        interaction_data = {
            "interaction_type": "collaboration",
            "sentiment": "positive",
            "context": "Successful project completion"
        }
        
        updated = await self.ai_service._update_dimensions_from_interaction(
            current_dimensions, interaction_data, self.test_person1
        )
        
        # Professional collaboration should increase for collaboration interaction
        assert updated["professional_collaboration"] > current_dimensions["professional_collaboration"]
        # Communication frequency should increase slightly
        assert updated["communication_frequency"] > current_dimensions["communication_frequency"]
        # All scores should remain in valid range
        assert all(0 <= score <= 100 for score in updated.values())

    @pytest.mark.asyncio
    async def test_get_user_decision_factors(self):
        """Test user preference learning"""
        preferences = await self.ai_service._get_user_decision_factors(self.test_user.user_id)
        
        assert isinstance(preferences, dict)
        expected_weights = [
            "emotional_weight", "value_weight", "trust_weight",
            "information_weight", "role_weight", "coercive_weight"
        ]
        
        for weight in expected_weights:
            assert weight in preferences
            assert 0 <= preferences[weight] <= 1
        
        # Weights should approximately sum to 1
        total_weight = sum(preferences.values())
        assert 0.9 <= total_weight <= 1.1

    @pytest.mark.asyncio
    async def test_calculate_weighted_overall_score(self):
        """Test weighted overall score calculation"""
        dimensions = {
            "emotional_intimacy": 70,
            "professional_collaboration": 80,
            "trust_level": 75,
            "communication_frequency": 60,
            "shared_experience_value": 65,
            "reciprocity_balance": 70
        }
        
        user_preferences = {
            "emotional_weight": 0.3,  # High weight on emotional
            "value_weight": 0.25,
            "trust_weight": 0.2,
            "information_weight": 0.1,
            "role_weight": 0.1,
            "coercive_weight": 0.05
        }
        
        score = await self.ai_service._calculate_weighted_overall_score(
            dimensions, user_preferences
        )
        
        assert 0 <= score <= 100
        assert isinstance(score, int)
        
        # Since emotional_intimacy has high weight and good score,
        # overall score should be reasonable
        assert score >= 60

    @pytest.mark.asyncio
    async def test_analyze_relationship_asymmetry_advanced(self):
        """Test advanced asymmetry analysis"""
        # Test with balanced relationship
        balanced_rel = self.test_relationship
        analysis = await self.ai_service._analyze_relationship_asymmetry_advanced(balanced_rel)
        
        assert "is_asymmetric" in analysis
        assert "asymmetry_score" in analysis
        assert "dominant_dimensions" in analysis
        assert "weak_dimensions" in analysis
        assert "analysis" in analysis
        
        # Create highly asymmetric relationship for testing
        asymmetric_rel = Knows(
            from_person_id=self.test_user.user_id,
            to_person_id=self.test_person2.person_id,
            user_id=self.test_user.user_id,
            archetype="client",
            relationship_depth={
                "overall_score": 60,
                "dimensions": {
                    "emotional_intimacy": 10,  # Very low
                    "professional_collaboration": 90,  # Very high
                    "trust_level": 20,  # Very low
                    "communication_frequency": 85,  # Very high
                    "shared_experience_value": 15,  # Very low
                    "reciprocity_balance": 95  # Very high
                }
            }
        )
        
        asymmetric_analysis = await self.ai_service._analyze_relationship_asymmetry_advanced(asymmetric_rel)
        
        # Should detect asymmetry
        assert asymmetric_analysis["is_asymmetric"] is True
        assert asymmetric_analysis["asymmetry_score"] > 0.3
        assert len(asymmetric_analysis["dominant_dimensions"]) > 0
        assert len(asymmetric_analysis["weak_dimensions"]) > 0

    @pytest.mark.asyncio
    async def test_analyze_relationship_trends(self):
        """Test relationship trend analysis"""
        # Create relationship with history
        trend_rel = Knows(
            from_person_id=self.test_user.user_id,
            to_person_id=self.test_person2.person_id,
            user_id=self.test_user.user_id,
            archetype="colleague",
            relationship_depth={
                "overall_score": 80,
                "dimensions": {},
                "history": [
                    {"timestamp": "2025-05-01T10:00:00Z", "event": "Created", "score_change": "+0"},
                    {"timestamp": "2025-05-15T10:00:00Z", "event": "Good meeting", "score_change": "+5"},
                    {"timestamp": "2025-06-01T10:00:00Z", "event": "Collaboration", "score_change": "+3"},
                    {"timestamp": "2025-06-15T10:00:00Z", "event": "Project success", "score_change": "+7"}
                ]
            }
        )
        
        trend_analysis = self.ai_service._analyze_relationship_trends(trend_rel)
        
        assert "trend" in trend_analysis
        assert "trend_strength" in trend_analysis
        assert "recent_changes" in trend_analysis
        assert "prediction" in trend_analysis
        
        # Should detect improving trend
        assert trend_analysis["trend"] == "improving"
        assert trend_analysis["trend_strength"] > 0

    @pytest.mark.asyncio
    async def test_relationship_prism_nonexistent(self):
        """Test relationship prism for non-existent relationship"""
        non_existent_person_id = uuid.uuid4()
        
        analysis = await self.ai_service.get_relationship_prism(
            self.test_user.user_id,
            self.test_user.user_id,
            non_existent_person_id
        )
        
        assert analysis["relationship_exists"] is False
        assert "error" in analysis

    @pytest.mark.asyncio 
    async def test_calculate_enhanced_relationship_metrics(self):
        """Test enhanced relationship metrics calculation"""
        user_preferences = await self.ai_service._get_user_decision_factors(self.test_user.user_id)
        
        metrics = await self.ai_service._calculate_enhanced_relationship_metrics(
            self.test_relationship, user_preferences
        )
        
        assert "personalized_score" in metrics
        assert "compatibility_index" in metrics  
        assert "growth_potential" in metrics
        assert "maintenance_effort" in metrics
        assert "strategic_value" in metrics
        
        # All metrics should be reasonable values
        assert 0 <= metrics["personalized_score"] <= 100
        assert 0 <= metrics["compatibility_index"] <= 1
        assert 0 <= metrics["growth_potential"] <= 100
        assert 0 <= metrics["maintenance_effort"] <= 1
        assert 0 <= metrics["strategic_value"] <= 1

    @patch('app.services.ai_engine_service.OpenAIClient')
    @pytest.mark.asyncio
    async def test_ai_integration_fallback(self, mock_openai):
        """Test that service works without OpenAI integration"""
        # Mock OpenAI to return None (simulate no API key)
        mock_openai.return_value = None
        
        # Create new service instance without OpenAI
        service_no_ai = AIEngineService(self.db_session)
        service_no_ai.openai_client = None
        
        # Should still work without AI features
        suggestions = await service_no_ai.get_active_suggestions(self.test_user.user_id)
        assert isinstance(suggestions, list)
        
        analysis = await service_no_ai.get_relationship_prism(
            self.test_user.user_id,
            self.test_user.user_id, 
            self.test_person1.person_id
        )
        assert analysis["relationship_exists"] is True