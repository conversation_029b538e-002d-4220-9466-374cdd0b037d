"""
Comprehensive tests for referral pathfinding with graph algorithms
"""

import uuid
import pytest
from datetime import datetime
from typing import Dict, List, Any

from app.models.person import Person
from app.models.relationship import Knows
from app.models.user import User
from app.services.pathfinding_service import (
    PathfindingService, 
    PathfindingAlgorithm, 
    PathOptimization,
    PathNode,
    PathEdge,
    ReferralPath
)
from app.services.ai_engine_service import AIEngineService


class TestPathfindingService:
    """Test the PathfindingService functionality"""
    
    def setup_method(self):
        """Setup test data for each test"""
        self.test_user_id = uuid.uuid4()
        self.test_persons = [uuid.uuid4() for _ in range(6)]  # Create 6 test persons
    
    def create_test_network(self, db) -> None:
        """Create a test network with known structure"""
        # Create user person (node 0)
        user_person = Person(
            person_id=self.test_persons[0],
            user_id=self.test_user_id,
            first_name="Test",
            last_name="User",
            is_user=True
        )
        
        # Create other persons (nodes 1-5)
        persons = [user_person]
        for i in range(1, 6):
            person = Person(
                person_id=self.test_persons[i],
                user_id=self.test_user_id,
                first_name=f"Person",
                last_name=f"{i}",
                professional_info={
                    "company": f"Company{i}",
                    "title": f"Title{i}"
                },
                is_user=False
            )
            persons.append(person)
        
        db.add_all(persons)
        
        # Create relationships to form a network
        # User -> Person1 (strong colleague)
        rel1 = Knows(
            from_person_id=self.test_persons[0],
            to_person_id=self.test_persons[1],
            user_id=self.test_user_id,
            archetype="colleague",
            relationship_depth={
                "overall_score": 85,
                "dimensions": {
                    "emotional_intimacy": 40,
                    "professional_collaboration": 90,
                    "trust_level": 80,
                    "communication_frequency": 85,
                    "shared_experience_value": 75,
                    "reciprocity_balance": 80
                }
            }
        )
        
        # Person1 -> Person2 (friend)
        rel2 = Knows(
            from_person_id=self.test_persons[1],
            to_person_id=self.test_persons[2],
            user_id=self.test_user_id,
            archetype="friend",
            relationship_depth={
                "overall_score": 90,
                "dimensions": {
                    "emotional_intimacy": 85,
                    "professional_collaboration": 40,
                    "trust_level": 95,
                    "communication_frequency": 80,
                    "shared_experience_value": 90,
                    "reciprocity_balance": 85
                }
            }
        )
        
        # Person2 -> Person3 (mentor)
        rel3 = Knows(
            from_person_id=self.test_persons[2],
            to_person_id=self.test_persons[3],
            user_id=self.test_user_id,
            archetype="mentor",
            relationship_depth={
                "overall_score": 75,
                "dimensions": {
                    "emotional_intimacy": 60,
                    "professional_collaboration": 80,
                    "trust_level": 90,
                    "communication_frequency": 50,
                    "shared_experience_value": 70,
                    "reciprocity_balance": 40
                }
            }
        )
        
        # User -> Person4 (direct weak connection)
        rel4 = Knows(
            from_person_id=self.test_persons[0],
            to_person_id=self.test_persons[4],
            user_id=self.test_user_id,
            archetype="acquaintance",
            relationship_depth={
                "overall_score": 30,
                "dimensions": {
                    "emotional_intimacy": 20,
                    "professional_collaboration": 30,
                    "trust_level": 40,
                    "communication_frequency": 20,
                    "shared_experience_value": 25,
                    "reciprocity_balance": 50
                }
            }
        )
        
        # Person4 -> Person3 (alternative path)
        rel5 = Knows(
            from_person_id=self.test_persons[4],
            to_person_id=self.test_persons[3],
            user_id=self.test_user_id,
            archetype="colleague",
            relationship_depth={
                "overall_score": 70,
                "dimensions": {
                    "emotional_intimacy": 30,
                    "professional_collaboration": 80,
                    "trust_level": 60,
                    "communication_frequency": 70,
                    "shared_experience_value": 50,
                    "reciprocity_balance": 65
                }
            }
        )
        
        db.add_all([rel1, rel2, rel3, rel4, rel5])
        db.commit()
    
    def test_pathfinding_service_initialization(self, db):
        """Test PathfindingService initializes correctly"""
        service = PathfindingService(db)
        
        assert service.db == db
        assert service.graph_service is not None
        assert len(service.archetype_weights) > 0
        assert len(service.success_factors) > 0
        assert service._path_cache == {}
    
    def test_dijkstra_pathfinding(self, db):
        """Test Dijkstra algorithm pathfinding"""
        self.create_test_network(db)
        service = PathfindingService(db)
        
        # Find path from user (0) to Person3 (3)
        paths = service.find_referral_paths(
            user_id=self.test_user_id,
            target_person_id=self.test_persons[3],
            algorithm=PathfindingAlgorithm.DIJKSTRA,
            optimization=PathOptimization.SHORTEST,
            max_paths=2
        )

        # Debug information
        print(f"User ID: {self.test_user_id}")
        print(f"Target Person ID: {self.test_persons[3]}")
        print(f"Found {len(paths)} paths")
        if len(paths) == 0:
            # Check if persons exist in database
            user_person = db.query(Person).filter(Person.person_id == self.test_persons[0]).first()
            target_person = db.query(Person).filter(Person.person_id == self.test_persons[3]).first()
            print(f"User person exists: {user_person is not None}")
            print(f"Target person exists: {target_person is not None}")

            # Check relationships
            relationships = db.query(Knows).filter(Knows.user_id == self.test_user_id).all()
            print(f"Total relationships: {len(relationships)}")
            for rel in relationships:
                print(f"  {rel.from_person_id} -> {rel.to_person_id}")

        assert len(paths) > 0
        path = paths[0]
        
        assert isinstance(path, ReferralPath)
        assert path.algorithm == "dijkstra"
        assert path.path_length >= 2  # Should be at least 2 hops
        assert len(path.nodes) == path.path_length + 1  # nodes = hops + 1
        assert len(path.edges) == path.path_length  # edges = hops
        assert path.success_probability > 0
        assert path.confidence_score > 0
    
    def test_path_optimization_strategies(self, db):
        """Test different optimization strategies"""
        self.create_test_network(db)
        service = PathfindingService(db)
        
        target_id = self.test_persons[3]
        
        # Test shortest path optimization
        shortest_paths = service.find_referral_paths(
            user_id=self.test_user_id,
            target_person_id=target_id,
            optimization=PathOptimization.SHORTEST
        )
        
        # Test strongest path optimization
        strongest_paths = service.find_referral_paths(
            user_id=self.test_user_id,
            target_person_id=target_id,
            optimization=PathOptimization.STRONGEST
        )
        
        # Test success probability optimization
        success_paths = service.find_referral_paths(
            user_id=self.test_user_id,
            target_person_id=target_id,
            optimization=PathOptimization.SUCCESS_PROBABILITY
        )
        
        assert len(shortest_paths) > 0
        assert len(strongest_paths) > 0
        assert len(success_paths) > 0
        
        # Paths should be sorted by their respective optimization criteria
        if len(shortest_paths) > 1:
            assert shortest_paths[0].path_length <= shortest_paths[1].path_length
        
        if len(strongest_paths) > 1:
            assert strongest_paths[0].confidence_score >= strongest_paths[1].confidence_score
    
    def test_goal_oriented_pathfinding(self, db):
        """Test goal-oriented pathfinding with criteria"""
        self.create_test_network(db)
        service = PathfindingService(db)
        
        # Search for people at "Company2"
        goal_criteria = {
            "description": "Find contacts at Company2",
            "company": "Company2"
        }
        
        paths = service.find_goal_oriented_paths(
            user_id=self.test_user_id,
            goal_description=goal_criteria["description"],
            target_criteria=goal_criteria,
            max_paths=3
        )
        
        # Should find paths (Person2 works at Company2)
        assert len(paths) >= 0  # May be 0 if no matching criteria
        
        for path in paths:
            assert isinstance(path, ReferralPath)
            assert path.confidence_score > 0
    
    def test_path_caching(self, db):
        """Test path caching functionality"""
        self.create_test_network(db)
        service = PathfindingService(db)
        
        target_id = self.test_persons[3]
        
        # First call - should compute and cache
        paths1 = service.find_referral_paths(
            user_id=self.test_user_id,
            target_person_id=target_id
        )
        
        # Check cache is populated
        assert len(service._path_cache) > 0
        
        # Second call - should use cache
        paths2 = service.find_referral_paths(
            user_id=self.test_user_id,
            target_person_id=target_id
        )
        
        # Results should be identical
        assert len(paths1) == len(paths2)
        if paths1:
            assert paths1[0].path_id == paths2[0].path_id
        
        # Clear cache
        service.clear_cache()
        assert len(service._path_cache) == 0
    
    def test_network_statistics(self, db):
        """Test network statistics generation"""
        self.create_test_network(db)
        service = PathfindingService(db)
        
        stats = service.get_path_statistics(self.test_user_id)
        
        assert "total_nodes" in stats
        assert "total_edges" in stats
        assert "network_density" in stats
        assert "average_clustering" in stats
        assert "connected_components" in stats
        assert "cache_size" in stats
        
        assert stats["total_nodes"] > 0
        assert stats["total_edges"] > 0
        assert 0 <= stats["network_density"] <= 1
        assert stats["connected_components"] >= 1
    
    def test_edge_weight_calculation(self, db):
        """Test edge weight calculation logic"""
        self.create_test_network(db)
        service = PathfindingService(db)
        
        # Get a relationship for testing
        relationship = db.query(Knows).filter(
            Knows.user_id == self.test_user_id
        ).first()
        
        weight = service._calculate_edge_weight(relationship)
        
        assert isinstance(weight, float)
        assert weight > 0  # Weights should be positive
        
        # Stronger relationships should have lower weights (better paths)
        strong_rel = Knows(
            from_person_id=self.test_persons[0],
            to_person_id=self.test_persons[1],
            user_id=self.test_user_id,
            archetype="mentor",
            relationship_depth={"overall_score": 95}
        )
        
        weak_rel = Knows(
            from_person_id=self.test_persons[0],
            to_person_id=self.test_persons[1],
            user_id=self.test_user_id,
            archetype="acquaintance",
            relationship_depth={"overall_score": 30}
        )
        
        strong_weight = service._calculate_edge_weight(strong_rel)
        weak_weight = service._calculate_edge_weight(weak_rel)
        
        assert strong_weight < weak_weight  # Stronger = lower weight
    
    def test_success_probability_calculation(self, db):
        """Test success probability calculation"""
        self.create_test_network(db)
        service = PathfindingService(db)
        
        persons = db.query(Person).filter(Person.user_id == self.test_user_id).all()
        person_lookup = {p.person_id: p for p in persons}
        
        relationship = db.query(Knows).filter(
            Knows.user_id == self.test_user_id
        ).first()
        
        success_prob = service._calculate_success_probability(relationship, person_lookup)
        
        assert isinstance(success_prob, float)
        assert 0 <= success_prob <= 1
    
    def test_pathfinding_with_no_path(self, db):
        """Test pathfinding when no path exists"""
        # Create isolated nodes
        user_person = Person(
            person_id=self.test_persons[0],
            user_id=self.test_user_id,
            first_name="Test",
            last_name="User",
            is_user=True
        )
        
        isolated_person = Person(
            person_id=self.test_persons[1],
            user_id=self.test_user_id,
            first_name="Isolated",
            last_name="Person",
            is_user=False
        )
        
        db.add_all([user_person, isolated_person])
        db.commit()
        
        service = PathfindingService(db)
        
        # Try to find path to isolated person
        paths = service.find_referral_paths(
            user_id=self.test_user_id,
            target_person_id=self.test_persons[1]
        )
        
        assert len(paths) == 0  # No path should exist


class TestAIEnginePathfinding:
    """Test AI Engine integration with pathfinding"""
    
    def setup_method(self):
        """Setup test data"""
        self.test_user_id = uuid.uuid4()
        self.test_person_id = uuid.uuid4()
    
    def test_find_referral_path_integration(self, db):
        """Test AI Engine referral path finding"""
        # Create minimal test data
        user_person = Person(
            person_id=self.test_user_id,
            user_id=self.test_user_id,
            first_name="Test",
            last_name="User",
            is_user=True
        )
        
        target_person = Person(
            person_id=self.test_person_id,
            user_id=self.test_user_id,
            first_name="Target",
            last_name="Person",
            is_user=False
        )
        
        db.add_all([user_person, target_person])
        db.commit()
        
        ai_engine = AIEngineService(db)
        
        # Test pathfinding call
        result = ai_engine.find_referral_path(
            user_id=self.test_user_id,
            target_person_id=self.test_person_id
        )
        
        # Should return valid response structure even if no paths found
        assert "paths" in result
        assert "alternative_strategies" in result
        assert "search_parameters" in result
        assert isinstance(result["paths"], list)
        assert isinstance(result["alternative_strategies"], list)
    
    def test_goal_oriented_pathfinding_integration(self, db):
        """Test goal-oriented pathfinding through AI Engine"""
        # Create minimal test data
        user_person = Person(
            person_id=self.test_user_id,
            user_id=self.test_user_id,
            first_name="Test",
            last_name="User",
            is_user=True
        )
        
        db.add(user_person)
        db.commit()
        
        ai_engine = AIEngineService(db)
        
        # Test goal-oriented search
        target_criteria = {
            "description": "Find startup CTOs",
            "title_keywords": ["CTO", "Chief Technology Officer"],
            "company": "TechCorp"
        }
        
        result = ai_engine.find_referral_path(
            user_id=self.test_user_id,
            target_criteria=target_criteria
        )
        
        assert "paths" in result
        assert "target" in result
        assert result["target"] == target_criteria["description"]


if __name__ == "__main__":
    pytest.main([__file__])