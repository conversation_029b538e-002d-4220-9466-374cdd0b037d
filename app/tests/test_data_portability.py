"""
Data portability tests
Tests for data import/export functionality and async task processing
"""

import csv
import io
import json
from unittest.mock import Mock, patch

import pytest
from fastapi.testclient import TestClient

from app.tests.base import BaseAPITestCase, BaseIntegrationTestCase
from app.tests.test_config import TEST_FILES
from app.tests.utils import TestDataFactory


class TestDataExport(BaseAPITestCase):
    """Test data export functionality"""

    def setup_method(self):
        super().setup_method()
        self.export_endpoint = "/api/v1/data/export"
        self.export_status_endpoint = "/api/v1/data/export/status"

    def test_export_data_json_format(self, client: TestClient):
        """Test exporting data in JSON format."""
        self.authenticate_user(client)

        # Create some test data first
        network = self.create_test_network(client, num_persons=3, num_organizations=2)

        export_request = {
            "format": "json",
            "include": ["persons", "organizations", "goals", "tasks"],
            "options": {"include_relationships": True, "include_metadata": True},
        }

        response = client.post(
            self.export_endpoint, json=export_request, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Data export endpoint not fully implemented yet")

        data = self.assert_successful_response(response, 202)  # Async operation

        # Should return task ID for async processing
        assert "task_id" in data or "export_id" in data
        task_id = data.get("task_id") or data.get("export_id")

        # Check export status
        status_response = client.get(
            f"{self.export_status_endpoint}/{task_id}", headers=self.auth_headers
        )

        if status_response.status_code == 404:
            pytest.skip("Export status endpoint not implemented yet")

        status_data = self.assert_successful_response(status_response)

        # Should contain status information
        assert "status" in status_data
        assert status_data["status"] in ["pending", "processing", "completed", "failed"]

    def test_export_data_csv_format(self, client: TestClient):
        """Test exporting data in CSV format."""
        self.authenticate_user(client)

        # Create test data
        network = self.create_test_network(client, num_persons=5)

        export_request = {
            "format": "csv",
            "include": ["persons"],
            "options": {"flatten_nested": True},
        }

        response = client.post(
            self.export_endpoint, json=export_request, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Data export endpoint not fully implemented yet")

        data = self.assert_successful_response(response, 202)

        # Should handle CSV export request
        assert "task_id" in data or "export_id" in data

    def test_export_data_selective_entities(self, client: TestClient):
        """Test exporting only selected entity types."""
        self.authenticate_user(client)

        # Create test data
        network = self.create_test_network(client, num_persons=2, num_organizations=1)

        # Export only persons
        export_request = {
            "format": "json",
            "include": ["persons"],
            "filters": {"created_after": "2024-01-01", "include_archived": False},
        }

        response = client.post(
            self.export_endpoint, json=export_request, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Data export endpoint not fully implemented yet")

        data = self.assert_successful_response(response, 202)

        # Should process selective export
        assert "task_id" in data or "export_id" in data

    def test_export_data_with_relationships(self, client: TestClient):
        """Test exporting data including relationships."""
        self.authenticate_user(client)

        # Create test network with relationships
        network = self.create_test_network(client, num_persons=3)

        export_request = {
            "format": "json",
            "include": ["persons", "relationships"],
            "options": {
                "include_relationship_depth": True,
                "include_interaction_history": True,
            },
        }

        response = client.post(
            self.export_endpoint, json=export_request, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Data export endpoint not fully implemented yet")

        data = self.assert_successful_response(response, 202)

        # Should handle relationship export
        assert "task_id" in data or "export_id" in data

    def test_export_data_large_dataset(self, client: TestClient):
        """Test exporting large dataset."""
        self.authenticate_user(client)

        # Create larger test network
        network = self.create_test_network(client, num_persons=20, num_organizations=5)

        export_request = {
            "format": "json",
            "include": ["persons", "organizations", "relationships"],
            "options": {"compress": True, "chunk_size": 100},
        }

        response = client.post(
            self.export_endpoint, json=export_request, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Data export endpoint not fully implemented yet")

        data = self.assert_successful_response(response, 202)

        # Should handle large dataset export
        assert "task_id" in data or "export_id" in data

        # Should provide estimated completion time for large exports
        if "estimated_completion" in data:
            assert isinstance(data["estimated_completion"], str)

    def test_get_export_download_link(self, client: TestClient):
        """Test getting download link for completed export."""
        self.authenticate_user(client)

        # Mock completed export
        with patch("app.services.export_service.get_export_status") as mock_status:
            mock_status.return_value = {
                "status": "completed",
                "download_url": "/api/v1/data/export/download/test-export-id",
                "file_size": 1024,
                "expires_at": "2024-07-01T00:00:00Z",
            }

            response = client.get(
                f"{self.export_status_endpoint}/test-export-id",
                headers=self.auth_headers,
            )

            if response.status_code == 404:
                pytest.skip("Export status endpoint not implemented yet")

            data = self.assert_successful_response(response)

            # Should provide download information
            assert data["status"] == "completed"
            if "download_url" in data:
                assert data["download_url"].startswith("/api/v1/data/export/download/")

    def test_export_data_validation_errors(self, client: TestClient):
        """Test export with invalid parameters."""
        self.authenticate_user(client)

        # Invalid format
        invalid_request = {"format": "invalid_format", "include": ["persons"]}

        response = client.post(
            self.export_endpoint, json=invalid_request, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Data export endpoint not fully implemented yet")

        self.assert_validation_error_response(response)

        # Empty include list
        invalid_request = {"format": "json", "include": []}

        response = client.post(
            self.export_endpoint, json=invalid_request, headers=self.auth_headers
        )
        self.assert_validation_error_response(response)

    def test_export_unauthorized_access(self, client: TestClient):
        """Test export without authentication."""
        export_request = {"format": "json", "include": ["persons"]}

        response = client.post(self.export_endpoint, json=export_request)
        self.assert_unauthorized_response(response)


class TestDataImport(BaseAPITestCase):
    """Test data import functionality"""

    def setup_method(self):
        super().setup_method()
        self.import_endpoint = "/api/v1/data/import"
        self.import_status_endpoint = "/api/v1/data/import/status"

    def create_test_csv_content(self):
        """Create test CSV content for import."""
        csv_content = io.StringIO()
        writer = csv.writer(csv_content)

        # Write headers
        writer.writerow(
            ["first_name", "last_name", "email", "phone", "company", "title"]
        )

        # Write test data
        test_rows = [
            [
                "John",
                "Doe",
                "<EMAIL>",
                "************",
                "TechCorp",
                "Engineer",
            ],
            [
                "Jane",
                "Smith",
                "<EMAIL>",
                "************",
                "DesignCo",
                "Designer",
            ],
            ["Bob", "Johnson", "<EMAIL>", "************", "StartupIO", "PM"],
        ]

        for row in test_rows:
            writer.writerow(row)

        return csv_content.getvalue()

    def create_test_json_content(self):
        """Create test JSON content for import."""
        test_data = {
            "persons": [
                {
                    "first_name": "Alice",
                    "last_name": "Wilson",
                    "contact_info": {
                        "email": "<EMAIL>",
                        "phone": "************",
                    },
                    "professional_info": {
                        "title": "Data Scientist",
                        "company": "AI Corp",
                    },
                },
                {
                    "first_name": "Charlie",
                    "last_name": "Brown",
                    "contact_info": {"email": "<EMAIL>"},
                    "professional_info": {
                        "title": "Product Manager",
                        "company": "ProductCo",
                    },
                },
            ],
            "organizations": [
                {
                    "name": "AI Corp",
                    "details": {"industry": "Technology", "size": "100-500"},
                }
            ],
        }

        return json.dumps(test_data)

    def test_import_csv_data(self, client: TestClient):
        """Test importing data from CSV file."""
        self.authenticate_user(client)

        csv_content = self.create_test_csv_content()

        # Simulate file upload
        files = {"file": ("contacts.csv", csv_content, "text/csv")}

        data = {
            "format": "csv",
            "entity_type": "persons",
            "options": json.dumps(
                {
                    "mapping": {
                        "first_name": "first_name",
                        "last_name": "last_name",
                        "email": "contact_info.email",
                        "phone": "contact_info.phone",
                        "company": "professional_info.company",
                        "title": "professional_info.title",
                    },
                    "skip_duplicates": True,
                    "validate_emails": True,
                }
            ),
        }

        response = client.post(
            self.import_endpoint, files=files, data=data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Data import endpoint not fully implemented yet")

        response_data = self.assert_successful_response(
            response, 202
        )  # Async operation

        # Should return task ID for async processing
        assert "task_id" in response_data or "import_id" in response_data
        task_id = response_data.get("task_id") or response_data.get("import_id")

        # Check import status
        status_response = client.get(
            f"{self.import_status_endpoint}/{task_id}", headers=self.auth_headers
        )

        if status_response.status_code == 404:
            pytest.skip("Import status endpoint not implemented yet")

        status_data = self.assert_successful_response(status_response)

        # Should contain status and progress information
        assert "status" in status_data
        assert status_data["status"] in ["pending", "processing", "completed", "failed"]

    def test_import_json_data(self, client: TestClient):
        """Test importing data from JSON file."""
        self.authenticate_user(client)

        json_content = self.create_test_json_content()

        files = {"file": ("data.json", json_content, "application/json")}

        data = {
            "format": "json",
            "options": json.dumps(
                {
                    "merge_strategy": "update_existing",
                    "create_missing_organizations": True,
                }
            ),
        }

        response = client.post(
            self.import_endpoint, files=files, data=data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Data import endpoint not fully implemented yet")

        response_data = self.assert_successful_response(response, 202)

        # Should handle JSON import
        assert "task_id" in response_data or "import_id" in response_data

    def test_import_data_with_validation_errors(self, client: TestClient):
        """Test importing data with validation errors."""
        self.authenticate_user(client)

        # Create CSV with invalid data
        invalid_csv = "first_name,last_name,email\nJohn,Doe,invalid-email\nJane,,<EMAIL>\n"

        files = {"file": ("invalid.csv", invalid_csv, "text/csv")}

        data = {
            "format": "csv",
            "entity_type": "persons",
            "options": json.dumps({"strict_validation": True, "stop_on_error": False}),
        }

        response = client.post(
            self.import_endpoint, files=files, data=data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Data import endpoint not fully implemented yet")

        response_data = self.assert_successful_response(response, 202)

        # Should process even with validation errors
        task_id = response_data.get("task_id") or response_data.get("import_id")

        # Check status - should report validation errors
        status_response = client.get(
            f"{self.import_status_endpoint}/{task_id}", headers=self.auth_headers
        )

        if status_response.status_code == 200:
            status_data = self.assert_successful_response(status_response)

            # Should report validation issues
            if "validation_errors" in status_data or "errors" in status_data:
                errors = status_data.get("validation_errors") or status_data.get(
                    "errors"
                )
                assert isinstance(errors, list)

    def test_import_data_duplicate_handling(self, client: TestClient):
        """Test handling of duplicate data during import."""
        self.authenticate_user(client)

        # First, create a person manually
        person_data = self.factory.create_person_data(
            first_name="John", last_name="Doe", email="<EMAIL>"
        )

        create_response = client.post(
            "/api/v1/persons", json=person_data, headers=self.auth_headers
        )
        if create_response.status_code == 501:
            pytest.skip(
                "Person creation required for duplicate test but not implemented"
            )

        # Now import CSV with the same person
        csv_content = "first_name,last_name,email\nJohn,Doe,<EMAIL>\n"

        files = {"file": ("duplicates.csv", csv_content, "text/csv")}

        data = {
            "format": "csv",
            "entity_type": "persons",
            "options": json.dumps(
                {
                    "duplicate_strategy": "skip",  # or "update" or "create_new"
                    "match_fields": ["email"],
                }
            ),
        }

        response = client.post(
            self.import_endpoint, files=files, data=data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Data import endpoint not fully implemented yet")

        response_data = self.assert_successful_response(response, 202)

        # Should handle duplicates according to strategy
        assert "task_id" in response_data or "import_id" in response_data

    def test_import_large_file(self, client: TestClient):
        """Test importing large data file."""
        self.authenticate_user(client)

        # Create large CSV content
        large_csv = io.StringIO()
        writer = csv.writer(large_csv)
        writer.writerow(["first_name", "last_name", "email"])

        # Generate many rows
        for i in range(1000):
            writer.writerow([f"Person{i}", "Test", f"person{i}@example.com"])

        files = {"file": ("large_import.csv", large_csv.getvalue(), "text/csv")}

        data = {
            "format": "csv",
            "entity_type": "persons",
            "options": json.dumps({"batch_size": 100, "progress_updates": True}),
        }

        response = client.post(
            self.import_endpoint, files=files, data=data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Data import endpoint not fully implemented yet")

        response_data = self.assert_successful_response(response, 202)

        # Should handle large file import
        assert "task_id" in response_data or "import_id" in response_data

        # Should provide progress information
        if "estimated_total_records" in response_data:
            assert response_data["estimated_total_records"] >= 1000

    def test_import_unsupported_format(self, client: TestClient):
        """Test importing unsupported file format."""
        self.authenticate_user(client)

        files = {"file": ("data.xml", "<data></data>", "application/xml")}

        data = {"format": "xml", "entity_type": "persons"}

        response = client.post(
            self.import_endpoint, files=files, data=data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Data import endpoint not fully implemented yet")

        # Should reject unsupported format
        assert response.status_code in [400, 422]

    def test_import_unauthorized_access(self, client: TestClient):
        """Test import without authentication."""
        files = {"file": ("test.csv", "data", "text/csv")}

        response = client.post(self.import_endpoint, files=files)
        self.assert_unauthorized_response(response)
