"""
Graph visualization tests
Tests for network graph generation and visualization data
"""

import pytest
from fastapi.testclient import TestClient

from app.tests.base import BaseAPITestCase, BaseIntegrationTestCase
from app.tests.utils import TestDataFactory


class TestGraphVisualization(BaseAPITestCase):
    """Test graph visualization functionality"""

    def setup_method(self):
        super().setup_method()
        self.graph_endpoint = "/api/v1/graph/full"
        self.persons_endpoint = "/api/v1/persons"
        self.organizations_endpoint = "/api/v1/organizations"

    def test_get_empty_graph(self, client: TestClient):
        """Test getting graph data when user has no network."""
        self.authenticate_user(client)

        response = client.get(self.graph_endpoint, headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Graph endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should return valid graph structure even when empty
        assert "nodes" in data
        assert "edges" in data or "links" in data
        assert isinstance(data["nodes"], list)

        # Should at least contain the user node
        if data["nodes"]:
            user_node = next(
                (node for node in data["nodes"] if node.get("type") == "user"), None
            )
            assert user_node is not None, "Graph should contain user node"

    def test_get_graph_with_persons(self, client: TestClient):
        """Test getting graph data with persons in network."""
        self.authenticate_user(client)

        # Create test network
        network = self.create_test_network(client, num_persons=3, num_organizations=1)

        if not network["persons"]:
            pytest.skip("Test network creation required but failed")

        response = client.get(self.graph_endpoint, headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Graph endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should contain nodes for user and created persons
        assert len(data["nodes"]) >= len(network["persons"]) + 1  # +1 for user

        # Verify node structure
        if data["nodes"]:
            node = data["nodes"][0]
            required_fields = ["id", "label", "type"]
            for field in required_fields:
                assert field in node, f"Node should have {field} field"

            # Should have different node types
            node_types = {node.get("type") for node in data["nodes"]}
            assert "user" in node_types or "person" in node_types

    def test_get_graph_with_relationships(self, client: TestClient):
        """Test getting graph data with relationships."""
        self.authenticate_user(client)

        # Create test network with relationships
        network = self.create_test_network(client, num_persons=3)

        if not network["persons"] or len(network["persons"]) < 2:
            pytest.skip("Test network with relationships required but failed")

        # Create relationships (if endpoint exists)
        person1_id = network["persons"][0]["person_id"]
        person2_id = network["persons"][1]["person_id"]

        relationship_data = {"target_person_id": person2_id, "archetype": "colleague"}

        rel_response = client.post(
            f"{self.persons_endpoint}/{person1_id}/apply-archetype",
            json=relationship_data,
            headers=self.auth_headers,
        )

        # Get graph data
        response = client.get(self.graph_endpoint, headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Graph endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should contain edges/links if relationships exist
        edges_key = "edges" if "edges" in data else "links"
        if edges_key in data and rel_response.status_code == 201:
            edges = data[edges_key]
            assert isinstance(edges, list)

            if edges:
                edge = edges[0]
                edge_fields = ["source", "target"]
                for field in edge_fields:
                    assert field in edge, f"Edge should have {field} field"

    def test_get_graph_with_organizations(self, client: TestClient):
        """Test getting graph data including organizations."""
        self.authenticate_user(client)

        # Create test network with organizations
        network = self.create_test_network(client, num_persons=2, num_organizations=2)

        if not network["organizations"]:
            pytest.skip("Test network with organizations required but failed")

        response = client.get(self.graph_endpoint, headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Graph endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should include organization nodes
        org_nodes = [
            node for node in data["nodes"] if node.get("type") == "organization"
        ]

        # If organizations are included in graph, verify structure
        if org_nodes:
            org_node = org_nodes[0]
            assert "id" in org_node
            assert "label" in org_node
            assert org_node["type"] == "organization"

    def test_get_filtered_graph(self, client: TestClient):
        """Test getting filtered graph data."""
        self.authenticate_user(client)

        # Create test network
        network = self.create_test_network(client, num_persons=5)

        if not network["persons"]:
            pytest.skip("Test network creation required but failed")

        # Test filtering by node type
        response = client.get(
            f"{self.graph_endpoint}?node_types=person", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Graph endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # If filtering is implemented, should only contain person nodes
        if "node_types" in response.url:
            person_nodes = [
                node for node in data["nodes"] if node.get("type") == "person"
            ]
            non_person_nodes = [
                node for node in data["nodes"] if node.get("type") != "person"
            ]

            # Should have more person nodes than non-person nodes when filtering
            assert len(person_nodes) >= len(non_person_nodes)

    def test_get_graph_with_layout_options(self, client: TestClient):
        """Test getting graph data with layout preferences."""
        self.authenticate_user(client)

        # Create test network
        network = self.create_test_network(client, num_persons=3)

        if not network["persons"]:
            pytest.skip("Test network creation required but failed")

        # Test different layout options
        layout_options = ["force", "circular", "hierarchical"]

        for layout in layout_options:
            response = client.get(
                f"{self.graph_endpoint}?layout={layout}", headers=self.auth_headers
            )

            if response.status_code == 501:
                pytest.skip("Graph endpoint not fully implemented yet")

            data = self.assert_successful_response(response)

            # Should return valid graph data regardless of layout
            assert "nodes" in data

            # If layout is implemented, might include position data
            if data["nodes"] and "layout" in response.url:
                node = data["nodes"][0]
                # Position data might be included
                position_fields = ["x", "y", "position"]
                has_position = any(field in node for field in position_fields)
                # This is optional - not all implementations include positions

    def test_get_graph_performance_large_network(self, client: TestClient):
        """Test graph generation performance with larger network."""
        self.authenticate_user(client)

        # Create larger test network
        network = self.create_test_network(client, num_persons=10, num_organizations=3)

        if len(network["persons"]) < 5:
            pytest.skip("Large test network creation required but failed")

        response = client.get(self.graph_endpoint, headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Graph endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should handle larger networks efficiently
        assert len(data["nodes"]) >= 10

        # Response should be reasonably fast (this is implicit in the test passing)
        # In a real scenario, you might want to measure response time

    def test_graph_data_structure_validation(self, client: TestClient):
        """Test that graph data follows expected structure."""
        self.authenticate_user(client)

        # Create minimal test network
        network = self.create_test_network(client, num_persons=2)

        response = client.get(self.graph_endpoint, headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Graph endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Validate overall structure
        assert isinstance(data, dict)
        assert "nodes" in data

        # Validate nodes structure
        nodes = data["nodes"]
        assert isinstance(nodes, list)

        for node in nodes:
            assert isinstance(node, dict)
            assert "id" in node
            assert "label" in node or "name" in node

            # Type should be specified
            if "type" in node:
                assert node["type"] in ["user", "person", "organization"]

            # Visual properties might be included
            visual_props = ["size", "color", "shape"]
            # These are optional but common in graph visualizations

        # Validate edges/links structure if present
        edges_key = "edges" if "edges" in data else "links"
        if edges_key in data:
            edges = data[edges_key]
            assert isinstance(edges, list)

            for edge in edges:
                assert isinstance(edge, dict)
                assert "source" in edge
                assert "target" in edge

                # Weight or strength might be included
                if "weight" in edge:
                    assert isinstance(edge["weight"], (int, float))

    def test_graph_unauthorized_access(self, client: TestClient):
        """Test graph endpoint without authentication."""
        response = client.get(self.graph_endpoint)
        self.assert_unauthorized_response(response)

    def test_graph_cross_user_isolation(self, client: TestClient, test_user_data):
        """Test that users only see their own network graph."""
        # Create first user and network
        auth1 = self.authenticate_user(client, test_user_data)
        network1 = self.create_test_network(client, num_persons=2)

        response1 = client.get(self.graph_endpoint, headers=self.auth_headers)

        if response1.status_code == 501:
            pytest.skip("Graph endpoint not fully implemented yet")

        data1 = self.assert_successful_response(response1)
        user1_node_count = len(data1["nodes"])

        # Create second user
        user2_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "first_name": "User2",
            "last_name": "Test",
        }
        auth2 = self.authenticate_user(client, user2_data)

        # User2 should see empty/minimal graph
        response2 = client.get(self.graph_endpoint, headers=auth2["headers"])
        data2 = self.assert_successful_response(response2)

        # User2 should have fewer nodes (only themselves, no shared network)
        assert len(data2["nodes"]) < user1_node_count


class TestGraphAnalytics(BaseAPITestCase):
    """Test graph analytics and metrics"""

    def test_get_graph_metrics(self, client: TestClient):
        """Test getting graph analytics metrics."""
        self.authenticate_user(client)

        # Create test network
        network = self.create_test_network(client, num_persons=5)

        response = client.get("/api/v1/graph/metrics", headers=self.auth_headers)

        if response.status_code == 404:
            pytest.skip("Graph metrics endpoint not implemented yet")

        data = self.assert_successful_response(response)

        # Should contain network metrics
        expected_metrics = [
            "node_count",
            "edge_count",
            "density",
            "clustering_coefficient",
        ]

        for metric in expected_metrics:
            if metric in data:
                assert isinstance(data[metric], (int, float))

    def test_get_centrality_analysis(self, client: TestClient):
        """Test getting centrality analysis of network."""
        self.authenticate_user(client)

        # Create test network
        network = self.create_test_network(client, num_persons=5)

        response = client.get("/api/v1/graph/centrality", headers=self.auth_headers)

        if response.status_code == 404:
            pytest.skip("Graph centrality endpoint not implemented yet")

        data = self.assert_successful_response(response)

        # Should contain centrality measures
        centrality_types = ["betweenness", "closeness", "degree", "eigenvector"]

        for centrality_type in centrality_types:
            if centrality_type in data:
                assert isinstance(data[centrality_type], dict)
                # Should map person IDs to centrality scores

    def test_get_community_detection(self, client: TestClient):
        """Test community detection in network."""
        self.authenticate_user(client)

        # Create test network
        network = self.create_test_network(client, num_persons=8)

        response = client.get("/api/v1/graph/communities", headers=self.auth_headers)

        if response.status_code == 404:
            pytest.skip("Graph community detection endpoint not implemented yet")

        data = self.assert_successful_response(response)

        # Should contain community information
        if "communities" in data:
            communities = data["communities"]
            assert isinstance(communities, list)

            for community in communities:
                assert "id" in community
                assert "members" in community
                assert isinstance(community["members"], list)
