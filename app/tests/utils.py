"""
Test utilities and helper functions
"""

import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.goal import Goal
from app.models.organization import Organization
from app.models.person import Person
from app.models.task import Task
from app.models.user import User


class TestDataFactory:
    """Factory class for creating test data"""

    @staticmethod
    def create_user_data(
        email: str = "<EMAIL>",
        password: str = "testpassword123",
        first_name: str = "Test",
        last_name: str = "User",
    ) -> Dict[str, Any]:
        """Create user test data"""
        return {
            "email": email,
            "password": password,
            "first_name": first_name,
            "last_name": last_name,
        }

    @staticmethod
    def create_person_data(
        first_name: str = "<PERSON>",
        last_name: str = "<PERSON><PERSON>",
        email: Optional[str] = None,
        phone: Optional[str] = None,
        **kwargs,
    ) -> Dict[str, Any]:
        """Create person test data"""
        contact_info = {}
        if email:
            contact_info["email"] = email
        if phone:
            contact_info["phone"] = phone

        return {
            "first_name": first_name,
            "last_name": last_name,
            "contact_info": contact_info,
            "social_profiles": kwargs.get("social_profiles", {}),
            "professional_info": kwargs.get("professional_info", {}),
            "personal_details": kwargs.get("personal_details", {}),
            **{
                k: v
                for k, v in kwargs.items()
                if k not in ["social_profiles", "professional_info", "personal_details"]
            },
        }

    @staticmethod
    def create_organization_data(
        name: str = "Test Organization", industry: str = "Technology", **kwargs
    ) -> Dict[str, Any]:
        """Create organization test data"""
        return {
            "name": name,
            "description": kwargs.get("description", f"A {industry.lower()} company"),
            "details": {
                "industry": industry,
                "size": kwargs.get("size", "100-500"),
                "website": kwargs.get(
                    "website", f"https://{name.lower().replace(' ', '')}.com"
                ),
                **kwargs.get("details", {}),
            },
        }

    @staticmethod
    def create_goal_data(
        title: str = "Test Goal",
        description: str = "A test goal for development",
        status: str = "active",
        days_from_now: int = 30,
        **kwargs,
    ) -> Dict[str, Any]:
        """Create goal test data"""
        return {
            "title": title,
            "description": description,
            "status": status,
            "target_date": (datetime.now() + timedelta(days=days_from_now)).isoformat(),
            "priority": kwargs.get("priority", 1),
            **{k: v for k, v in kwargs.items() if k not in ["priority"]},
        }

    @staticmethod
    def create_task_data(
        title: str = "Test Task",
        description: str = "A test task for development",
        priority: int = 1,
        days_from_now: int = 7,
        **kwargs,
    ) -> Dict[str, Any]:
        """Create task test data"""
        return {
            "title": title,
            "description": description,
            "priority": priority,
            "is_completed": kwargs.get("is_completed", False),
            "due_date": (datetime.now() + timedelta(days=days_from_now)).isoformat(),
            **{k: v for k, v in kwargs.items() if k not in ["is_completed"]},
        }


class TestAuthHelper:
    """Helper class for authentication in tests"""

    @staticmethod
    def register_and_login_user(
        client: TestClient, user_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Register a user and return login token"""
        if user_data is None:
            user_data = TestDataFactory.create_user_data()

        # Register user
        register_response = client.post("/api/v1/auth/register", json=user_data)
        assert register_response.status_code == 200

        # Login user
        login_data = {"email": user_data["email"], "password": user_data["password"]}
        login_response = client.post("/api/v1/auth/login", json=login_data)
        assert login_response.status_code == 200

        token_data = login_response.json()
        return {
            "user": register_response.json(),
            "token": token_data["access_token"],
            "headers": {"Authorization": f"Bearer {token_data['access_token']}"},
        }

    @staticmethod
    def get_auth_headers(token: str) -> Dict[str, str]:
        """Get authorization headers for API requests"""
        return {"Authorization": f"Bearer {token}"}


class TestAssertions:
    """Custom assertion helpers for tests"""

    @staticmethod
    def assert_valid_uuid(value: str, field_name: str = "id"):
        """Assert that a value is a valid UUID"""
        try:
            uuid.UUID(value)
        except (ValueError, TypeError):
            raise AssertionError(f"{field_name} '{value}' is not a valid UUID")

    @staticmethod
    def assert_valid_datetime(value: str, field_name: str = "datetime"):
        """Assert that a value is a valid ISO datetime string"""
        try:
            datetime.fromisoformat(value.replace("Z", "+00:00"))
        except (ValueError, TypeError):
            raise AssertionError(f"{field_name} '{value}' is not a valid ISO datetime")

    @staticmethod
    def assert_response_structure(
        response_data: Dict[str, Any],
        required_fields: List[str],
        optional_fields: Optional[List[str]] = None,
    ):
        """Assert that response has required structure"""
        optional_fields = optional_fields or []

        # Check required fields
        for field in required_fields:
            assert (
                field in response_data
            ), f"Required field '{field}' missing from response"

        # Check no unexpected fields (only if we have a complete field list)
        if optional_fields:
            all_expected_fields = set(required_fields + optional_fields)
            actual_fields = set(response_data.keys())
            unexpected_fields = actual_fields - all_expected_fields
            assert (
                not unexpected_fields
            ), f"Unexpected fields in response: {unexpected_fields}"

    @staticmethod
    def assert_error_response(
        response_data: Dict[str, Any],
        expected_code: Optional[str] = None,
        expected_message_contains: Optional[str] = None,
    ):
        """Assert that response is a valid error response"""
        assert (
            "code" in response_data or "detail" in response_data
        ), "Error response missing error information"

        if expected_code:
            assert (
                response_data.get("code") == expected_code
            ), f"Expected error code '{expected_code}'"

        if expected_message_contains:
            message = response_data.get("message") or response_data.get("detail", "")
            assert (
                expected_message_contains.lower() in message.lower()
            ), f"Expected message to contain '{expected_message_contains}'"


class MockAIService:
    """Mock AI service for testing AI-related functionality"""

    @staticmethod
    def mock_copilot_response(user_input: str) -> Dict[str, Any]:
        """Generate mock copilot response"""
        return {
            "response": f"I understand you want to: {user_input}",
            "intent": "general_query",
            "entities": [],
            "suggested_actions": [
                {
                    "type": "create_task",
                    "title": "Follow up on conversation",
                    "description": f"Based on: {user_input}",
                }
            ],
            "confidence": 0.85,
        }

    @staticmethod
    def mock_ai_suggestions() -> List[Dict[str, Any]]:
        """Generate mock AI suggestions"""
        return [
            {
                "id": str(uuid.uuid4()),
                "type": "reconnect",
                "title": "Reconnect with John Doe",
                "description": "You haven't spoken with John in 3 months",
                "priority": "medium",
                "suggested_action": "Send a message to catch up",
            },
            {
                "id": str(uuid.uuid4()),
                "type": "introduction",
                "title": "Introduce Alice to Bob",
                "description": "Alice and Bob both work in AI and could benefit from knowing each other",
                "priority": "low",
                "suggested_action": "Send introduction email",
            },
        ]

    @staticmethod
    def mock_network_diagnosis() -> Dict[str, Any]:
        """Generate mock network diagnosis"""
        return {
            "overall_health": 0.75,
            "network_size": 150,
            "active_connections": 45,
            "dormant_connections": 105,
            "insights": [
                {
                    "type": "strength",
                    "message": "Strong connections in technology sector",
                },
                {
                    "type": "weakness",
                    "message": "Limited connections in marketing domain",
                },
                {
                    "type": "opportunity",
                    "message": "Several mutual connections could facilitate introductions",
                },
            ],
            "recommendations": [
                "Reconnect with 5 dormant connections this month",
                "Attend marketing industry events to expand network",
            ],
        }
