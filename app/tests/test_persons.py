"""
Person management CRUD tests
"""

import pytest
from fastapi.testclient import Test<PERSON>lient

from app.tests.base import BaseCRUDTestCase
from app.tests.test_config import RESPONSE_FIELDS, TEST_PERSON_TEMPLATES
from app.tests.utils import TestDataFactory


class TestPersonCRUD(BaseCRUDTestCase):
    """Test Person CRUD operations"""

    endpoint_base = "/api/v1/persons"
    create_data_factory = TestDataFactory.create_person_data
    required_response_fields = RESPONSE_FIELDS["person"]["required"]
    optional_response_fields = RESPONSE_FIELDS["person"]["optional"]

    def test_create_person_with_minimal_data(self, client: TestClient):
        """Test creating person with minimal required data."""
        self.authenticate_user(client)

        minimal_data = {"first_name": "<PERSON>", "last_name": "Doe"}

        response = client.post(
            self.endpoint_base, json=minimal_data, headers=self.auth_headers
        )

        if response.status_code == 501:  # Not implemented
            pytest.skip("Person creation endpoint not fully implemented yet")

        data = self.assert_successful_response(response, 200)

        assert data["first_name"] == "John"
        assert data["last_name"] == "Doe"
        assert data["user_id"] == self.authenticated_user["user_id"]
        self.assertions.assert_valid_uuid(data["person_id"])

    def test_create_person_with_full_data(self, client: TestClient):
        """Test creating person with complete data."""
        self.authenticate_user(client)

        full_data = self.factory.create_person_data(
            first_name="Jane",
            last_name="Smith",
            email="<EMAIL>",
            phone="+1234567890",
            social_profiles={
                "linkedin": "https://linkedin.com/in/janesmith",
                "twitter": "https://twitter.com/janesmith",
            },
            professional_info={
                "title": "Senior Developer",
                "company": "Tech Corp",
                "department": "Engineering",
                "years_experience": 5,
            },
            personal_details={
                "birthday": "1990-01-15",
                "interests": ["coding", "photography"],
                "location": "San Francisco, CA",
            },
        )

        response = client.post(
            self.endpoint_base, json=full_data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Person creation endpoint not fully implemented yet")

        data = self.assert_successful_response(response, 201)

        # Verify all data was saved correctly
        assert data["first_name"] == "Jane"
        assert data["last_name"] == "Smith"
        assert data["contact_info"]["email"] == "<EMAIL>"
        assert data["professional_info"]["title"] == "Senior Developer"
        assert "linkedin" in data["social_profiles"]

    def test_create_person_with_duplicate_email(self, client: TestClient):
        """Test creating person with duplicate email in same user's network."""
        self.authenticate_user(client)

        person_data = self.factory.create_person_data(
            first_name="John", last_name="Doe", email="<EMAIL>"
        )

        # Create first person
        response1 = client.post(
            self.endpoint_base, json=person_data, headers=self.auth_headers
        )

        if response1.status_code == 501:
            pytest.skip("Person creation endpoint not fully implemented yet")

        self.assert_successful_response(response1, 201)

        # Try to create second person with same email
        person_data["first_name"] = "Jane"  # Different name, same email
        response2 = client.post(
            self.endpoint_base, json=person_data, headers=self.auth_headers
        )

        # Should either allow (different persons can have same email) or reject
        assert response2.status_code in [201, 400, 409]

    def test_get_persons_list_empty(self, client: TestClient):
        """Test getting empty persons list."""
        self.authenticate_user(client)

        response = client.get(self.endpoint_base, headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Person list endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert isinstance(data, list)
        assert len(data) == 0

    def test_get_persons_list_with_data(self, client: TestClient):
        """Test getting persons list with data."""
        self.authenticate_user(client)

        # Create a few persons first
        for i in range(3):
            person_data = self.factory.create_person_data(
                first_name=f"Person{i+1}",
                last_name="Test",
                email=f"person{i+1}@test.com",
            )
            response = client.post(
                self.endpoint_base, json=person_data, headers=self.auth_headers
            )
            if response.status_code == 501:
                pytest.skip("Person creation endpoint not fully implemented yet")

        # Get the list
        response = client.get(self.endpoint_base, headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Person list endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert isinstance(data, list)
        assert len(data) == 3

        # Verify structure of first item
        if data:
            self.assertions.assert_response_structure(
                data[0], self.required_response_fields, self.optional_response_fields
            )

    def test_get_persons_list_pagination(self, client: TestClient):
        """Test persons list pagination."""
        self.authenticate_user(client)

        # Create multiple persons
        for i in range(15):
            person_data = self.factory.create_person_data(
                first_name=f"Person{i+1:02d}", last_name="Test"
            )
            response = client.post(
                self.endpoint_base, json=person_data, headers=self.auth_headers
            )
            if response.status_code == 501:
                pytest.skip("Person creation endpoint not fully implemented yet")

        # Test pagination
        response = client.get(
            f"{self.endpoint_base}?skip=0&limit=10", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Person list endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert len(data) <= 10

        # Test second page
        response = client.get(
            f"{self.endpoint_base}?skip=10&limit=10", headers=self.auth_headers
        )
        data = self.assert_successful_response(response)
        assert len(data) <= 10

    def test_update_person_partial(self, client: TestClient):
        """Test partial update of person."""
        self.authenticate_user(client)

        # Create person first
        person_data = self.factory.create_person_data()
        create_response = client.post(
            self.endpoint_base, json=person_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Person creation endpoint not fully implemented yet")

        created_person = self.assert_successful_response(create_response, 201)
        person_id = created_person["person_id"]

        # Update only some fields
        update_data = {
            "professional_info": {"title": "Updated Title", "company": "New Company"}
        }

        response = client.put(
            f"{self.endpoint_base}/{person_id}",
            json=update_data,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Person update endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Verify update
        assert data["professional_info"]["title"] == "Updated Title"
        assert data["professional_info"]["company"] == "New Company"
        # Original data should remain
        assert data["first_name"] == person_data["first_name"]

    def test_update_person_contact_info(self, client: TestClient):
        """Test updating person's contact information."""
        self.authenticate_user(client)

        # Create person
        person_data = self.factory.create_person_data(
            email="<EMAIL>", phone="+1111111111"
        )
        create_response = client.post(
            self.endpoint_base, json=person_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Person creation endpoint not fully implemented yet")

        created_person = self.assert_successful_response(create_response, 201)
        person_id = created_person["person_id"]

        # Update contact info
        update_data = {
            "contact_info": {
                "email": "<EMAIL>",
                "phone": "+2222222222",
                "address": "123 New Street, City, State",
            }
        }

        response = client.put(
            f"{self.endpoint_base}/{person_id}",
            json=update_data,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Person update endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert data["contact_info"]["email"] == "<EMAIL>"
        assert data["contact_info"]["phone"] == "+2222222222"

    def test_delete_person_success(self, client: TestClient):
        """Test successful person deletion."""
        self.authenticate_user(client)

        # Create person
        person_data = self.factory.create_person_data()
        create_response = client.post(
            self.endpoint_base, json=person_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Person creation endpoint not fully implemented yet")

        created_person = self.assert_successful_response(create_response, 201)
        person_id = created_person["person_id"]

        # Delete person
        response = client.delete(
            f"{self.endpoint_base}/{person_id}", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Person deletion endpoint not fully implemented yet")

        self.assert_successful_response(response, 204)

        # Verify person is deleted
        get_response = client.get(
            f"{self.endpoint_base}/{person_id}", headers=self.auth_headers
        )
        self.assert_not_found_response(get_response)

    def test_person_validation_errors(self, client: TestClient):
        """Test person creation validation errors."""
        self.authenticate_user(client)

        # Test missing required fields
        invalid_data = {
            "last_name": "Doe"
            # Missing first_name
        }

        response = client.post(
            self.endpoint_base, json=invalid_data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Person creation endpoint not fully implemented yet")

        self.assert_validation_error_response(response)

        # Test empty names
        invalid_data = {"first_name": "", "last_name": "Doe"}

        response = client.post(
            self.endpoint_base, json=invalid_data, headers=self.auth_headers
        )
        self.assert_validation_error_response(response)

    def test_person_cross_user_isolation(self, client: TestClient, test_user_data):
        """Test that users can only access their own persons."""
        # Create first user and person
        auth1 = self.authenticate_user(client, test_user_data)

        person_data = self.factory.create_person_data(
            first_name="User1", last_name="Person"
        )
        create_response = client.post(
            self.endpoint_base, json=person_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Person creation endpoint not fully implemented yet")

        created_person = self.assert_successful_response(create_response, 201)
        person_id = created_person["person_id"]

        # Create second user
        user2_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "first_name": "User2",
            "last_name": "Test",
        }
        auth2 = self.authenticate_user(client, user2_data)

        # User2 should not be able to access User1's person
        response = client.get(
            f"{self.endpoint_base}/{person_id}", headers=auth2["headers"]
        )
        self.assert_not_found_response(response)

        # User2 should not see User1's persons in list
        response = client.get(self.endpoint_base, headers=auth2["headers"])
        if response.status_code != 501:
            data = self.assert_successful_response(response)
            assert len(data) == 0  # Should be empty for user2
