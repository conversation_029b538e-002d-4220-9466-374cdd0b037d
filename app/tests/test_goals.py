"""
Goal management CRUD tests
"""

from datetime import datetime, timedelta

import pytest
from fastapi.testclient import TestClient

from app.tests.base import BaseCRUDTestCase
from app.tests.test_config import RESPONSE_FIELDS, TEST_GOAL_TEMPLATES
from app.tests.utils import TestDataFactory


class TestGoalCRUD(BaseCRUDTestCase):
    """Test Goal CRUD operations"""

    endpoint_base = "/api/v1/goals"
    create_data_factory = TestDataFactory.create_goal_data
    required_response_fields = RESPONSE_FIELDS["goal"]["required"]
    optional_response_fields = RESPONSE_FIELDS["goal"]["optional"]

    def test_create_goal_minimal(self, client: TestClient):
        """Test creating goal with minimal data."""
        self.authenticate_user(client)

        minimal_data = {"title": "Test Goal", "status": "active"}

        response = client.post(
            self.endpoint_base, json=minimal_data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Goal creation endpoint not fully implemented yet")

        data = self.assert_successful_response(response, 201)

        assert data["title"] == "Test Goal"
        assert data["status"] == "active"
        assert data["user_id"] == self.authenticated_user["user_id"]
        self.assertions.assert_valid_uuid(data["goal_id"])

    def test_create_goal_full_data(self, client: TestClient):
        """Test creating goal with complete data."""
        self.authenticate_user(client)

        full_data = self.factory.create_goal_data(
            title="Advance to Senior Engineer",
            description="Get promoted to senior engineer role within the next year by improving technical skills and taking on leadership responsibilities",
            status="active",
            days_from_now=365,
            priority=1,
            category="career",
            tags=["career", "promotion", "engineering"],
            success_criteria=[
                "Complete advanced technical training",
                "Lead at least 2 major projects",
                "Mentor 2 junior developers",
                "Receive positive performance review",
            ],
        )

        response = client.post(
            self.endpoint_base, json=full_data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Goal creation endpoint not fully implemented yet")

        data = self.assert_successful_response(response, 201)

        # Verify all data was saved correctly
        assert data["title"] == "Advance to Senior Engineer"
        assert data["description"] == full_data["description"]
        assert data["status"] == "active"
        assert data["priority"] == 1
        if "category" in data:
            assert data["category"] == "career"
        if "target_date" in data:
            self.assertions.assert_valid_datetime(data["target_date"])

    def test_create_goal_with_target_date(self, client: TestClient):
        """Test creating goal with specific target date."""
        self.authenticate_user(client)

        target_date = (datetime.now() + timedelta(days=90)).isoformat()
        goal_data = {
            "title": "Learn Machine Learning",
            "description": "Complete ML course and build 3 projects",
            "status": "active",
            "target_date": target_date,
            "priority": 1,
        }

        response = client.post(
            self.endpoint_base, json=goal_data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Goal creation endpoint not fully implemented yet")

        data = self.assert_successful_response(response, 201)

        assert data["title"] == "Learn Machine Learning"
        if "target_date" in data:
            # Should preserve the target date
            assert data["target_date"] is not None

    def test_get_goals_list_empty(self, client: TestClient):
        """Test getting empty goals list."""
        self.authenticate_user(client)

        response = client.get(self.endpoint_base, headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Goal list endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert isinstance(data, list)
        assert len(data) == 0

    def test_get_goals_list_with_data(self, client: TestClient):
        """Test getting goals list with data."""
        self.authenticate_user(client)

        # Create goals using templates
        for template_name, template_data in TEST_GOAL_TEMPLATES.items():
            goal_data = self.factory.create_goal_data(**template_data)
            response = client.post(
                self.endpoint_base, json=goal_data, headers=self.auth_headers
            )
            if response.status_code == 501:
                pytest.skip("Goal creation endpoint not fully implemented yet")

        # Get the list
        response = client.get(self.endpoint_base, headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Goal list endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert isinstance(data, list)
        assert len(data) == len(TEST_GOAL_TEMPLATES)

        # Verify structure of first item
        if data:
            self.assertions.assert_response_structure(
                data[0], self.required_response_fields, self.optional_response_fields
            )

    def test_get_goals_by_status(self, client: TestClient):
        """Test filtering goals by status."""
        self.authenticate_user(client)

        # Create goals with different statuses
        statuses = ["active", "completed", "paused"]
        for status in statuses:
            goal_data = self.factory.create_goal_data(
                title=f"{status.title()} Goal", status=status
            )
            response = client.post(
                self.endpoint_base, json=goal_data, headers=self.auth_headers
            )
            if response.status_code == 501:
                pytest.skip("Goal creation endpoint not fully implemented yet")

        # Test filtering by status
        response = client.get(
            f"{self.endpoint_base}?status=active", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Goal filtering not implemented yet")
        elif response.status_code == 200:
            data = self.assert_successful_response(response)
            # If filtering is implemented, should only return active goals
            for goal in data:
                assert goal["status"] == "active"

    def test_update_goal_status(self, client: TestClient):
        """Test updating goal status."""
        self.authenticate_user(client)

        # Create goal
        goal_data = self.factory.create_goal_data(
            title="Status Update Goal", status="active"
        )
        create_response = client.post(
            self.endpoint_base, json=goal_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Goal creation endpoint not fully implemented yet")

        created_goal = self.assert_successful_response(create_response, 201)
        goal_id = created_goal["goal_id"]

        # Update status
        update_data = {"status": "completed"}

        response = client.put(
            f"{self.endpoint_base}/{goal_id}",
            json=update_data,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Goal update endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert data["status"] == "completed"
        # Original data should remain
        assert data["title"] == "Status Update Goal"

    def test_update_goal_priority(self, client: TestClient):
        """Test updating goal priority."""
        self.authenticate_user(client)

        # Create goal
        goal_data = self.factory.create_goal_data(title="Priority Goal", priority=3)
        create_response = client.post(
            self.endpoint_base, json=goal_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Goal creation endpoint not fully implemented yet")

        created_goal = self.assert_successful_response(create_response, 201)
        goal_id = created_goal["goal_id"]

        # Update priority
        update_data = {"priority": 1}

        response = client.put(
            f"{self.endpoint_base}/{goal_id}",
            json=update_data,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Goal update endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert data["priority"] == 1

    def test_get_goal_dashboard(self, client: TestClient):
        """Test getting goal dashboard/intelligence."""
        self.authenticate_user(client)

        # Create goal
        goal_data = self.factory.create_goal_data(
            title="Dashboard Goal",
            description="A goal for testing dashboard functionality",
        )
        create_response = client.post(
            self.endpoint_base, json=goal_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Goal creation endpoint not fully implemented yet")

        created_goal = self.assert_successful_response(create_response, 201)
        goal_id = created_goal["goal_id"]

        # Get goal dashboard
        response = client.get(
            f"{self.endpoint_base}/{goal_id}/dashboard", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Goal dashboard endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Dashboard should contain goal information and analytics
        assert isinstance(data, dict)
        # Expected dashboard fields (implementation dependent)
        expected_fields = ["goal", "progress", "tasks", "related_persons", "insights"]
        # At least some of these should be present
        assert any(field in data for field in expected_fields)

    def test_delete_goal_success(self, client: TestClient):
        """Test successful goal deletion."""
        self.authenticate_user(client)

        # Create goal
        goal_data = self.factory.create_goal_data(title="To Delete Goal")
        create_response = client.post(
            self.endpoint_base, json=goal_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Goal creation endpoint not fully implemented yet")

        created_goal = self.assert_successful_response(create_response, 201)
        goal_id = created_goal["goal_id"]

        # Delete goal
        response = client.delete(
            f"{self.endpoint_base}/{goal_id}", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Goal deletion endpoint not fully implemented yet")

        self.assert_successful_response(response, 204)

        # Verify goal is deleted
        get_response = client.get(
            f"{self.endpoint_base}/{goal_id}", headers=self.auth_headers
        )
        self.assert_not_found_response(get_response)

    def test_goal_validation_errors(self, client: TestClient):
        """Test goal creation validation errors."""
        self.authenticate_user(client)

        # Test missing required fields
        invalid_data = {"description": "Missing title"}

        response = client.post(
            self.endpoint_base, json=invalid_data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Goal creation endpoint not fully implemented yet")

        self.assert_validation_error_response(response)

        # Test invalid status
        invalid_data = {"title": "Test Goal", "status": "invalid_status"}

        response = client.post(
            self.endpoint_base, json=invalid_data, headers=self.auth_headers
        )
        # Should either validate status or accept any string
        assert response.status_code in [201, 422]

    def test_goal_cross_user_isolation(self, client: TestClient, test_user_data):
        """Test that users can only access their own goals."""
        # Create first user and goal
        auth1 = self.authenticate_user(client, test_user_data)

        goal_data = self.factory.create_goal_data(title="User1 Goal")
        create_response = client.post(
            self.endpoint_base, json=goal_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Goal creation endpoint not fully implemented yet")

        created_goal = self.assert_successful_response(create_response, 201)
        goal_id = created_goal["goal_id"]

        # Create second user
        user2_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "first_name": "User2",
            "last_name": "Test",
        }
        auth2 = self.authenticate_user(client, user2_data)

        # User2 should not be able to access User1's goal
        response = client.get(
            f"{self.endpoint_base}/{goal_id}", headers=auth2["headers"]
        )
        self.assert_not_found_response(response)

        # User2 should not see User1's goals in list
        response = client.get(self.endpoint_base, headers=auth2["headers"])
        if response.status_code != 501:
            data = self.assert_successful_response(response)
            assert len(data) == 0  # Should be empty for user2
