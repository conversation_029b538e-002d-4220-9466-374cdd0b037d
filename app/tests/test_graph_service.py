"""
Tests for Graph Service
Comprehensive tests for network graph visualization data generation
"""

import pytest
import uuid
from datetime import datetime

from app.services.graph_service import GraphService
from app.models.user import User
from app.models.person import Person
from app.models.relationship import Knows
from app.tests.base import BaseIntegrationTestCase


class TestGraphService(BaseIntegrationTestCase):
    """Test Graph Service functionality"""

    def setup_method(self):
        super().setup_method()
        self.graph_service = GraphService(self.db_session)
        
        # Create test user
        self.test_user = User(
            user_id=uuid.uuid4(),
            email="<EMAIL>",
            hashed_password="hashed_password",
            is_active=True
        )
        self.db_session.add(self.test_user)
        
        # Create user person record
        self.user_person = Person(
            person_id=self.test_user.user_id,
            user_id=self.test_user.user_id,
            first_name="Test",
            last_name="User",
            is_user=True
        )
        self.db_session.add(self.user_person)
        
        # Create test persons
        self.test_persons = []
        for i in range(5):
            person = Person(
                person_id=uuid.uuid4(),
                user_id=self.test_user.user_id,
                first_name=f"Person{i}",
                last_name=f"LastName{i}",
                professional_info={
                    "title": f"Engineer {i}",
                    "company": f"Company{i % 3}",  # 3 different companies
                    "industry": "Technology"
                },
                personal_details={
                    "tags": [f"tag{i}", "common_tag"] if i % 2 == 0 else [f"tag{i}"]
                },
                is_user=False
            )
            self.test_persons.append(person)
            self.db_session.add(person)
        
        # Create test relationships
        self.test_relationships = []
        archetypes = ["colleague", "friend", "mentor", "client", "acquaintance"]
        
        for i, person in enumerate(self.test_persons):
            relationship = Knows(
                from_person_id=self.test_user.user_id,
                to_person_id=person.person_id,
                user_id=self.test_user.user_id,
                archetype=archetypes[i % len(archetypes)],
                relationship_depth={
                    "overall_score": 50 + (i * 10),  # Varying scores
                    "dimensions": {
                        "emotional_intimacy": 40 + (i * 5),
                        "professional_collaboration": 60 + (i * 3),
                        "trust_level": 50 + (i * 4),
                        "communication_frequency": 30 + (i * 8),
                        "shared_experience_value": 45 + (i * 6),
                        "reciprocity_balance": 55 + (i * 2)
                    }
                }
            )
            self.test_relationships.append(relationship)
            self.db_session.add(relationship)
        
        self.db_session.commit()

    @pytest.mark.asyncio
    async def test_generate_full_network_graph(self):
        """Test full network graph generation"""
        graph_data = await self.graph_service.generate_full_network_graph(
            self.test_user.user_id, layout_type="force_directed"
        )
        
        # Check structure
        assert "nodes" in graph_data
        assert "edges" in graph_data
        assert "layout" in graph_data
        assert "metadata" in graph_data
        
        # Check nodes
        nodes = graph_data["nodes"]
        assert len(nodes) == 6  # 1 user + 5 persons
        
        # Check user node
        user_nodes = [n for n in nodes if n["type"] == "user"]
        assert len(user_nodes) == 1
        assert user_nodes[0]["label"] == "You"
        assert user_nodes[0]["size"] == 25
        
        # Check person nodes
        person_nodes = [n for n in nodes if n["type"] == "person"]
        assert len(person_nodes) == 5
        
        # Check edges
        edges = graph_data["edges"]
        assert len(edges) == 5  # One edge per relationship
        
        # Check layout
        layout = graph_data["layout"]
        assert layout["type"] == "force_directed"
        assert "positions" in layout
        
        # Check metadata
        metadata = graph_data["metadata"]
        assert metadata["total_nodes"] == 6
        assert metadata["total_edges"] == 5
        assert "density" in metadata
        assert "clustering_coefficient" in metadata

    @pytest.mark.asyncio
    async def test_generate_filtered_network_graph(self):
        """Test filtered network graph generation"""
        # Test archetype filter
        graph_data = await self.graph_service.generate_filtered_network_graph(
            user_id=self.test_user.user_id,
            archetype="colleague"
        )
        
        nodes = graph_data["nodes"]
        person_nodes = [n for n in nodes if n["type"] == "person"]
        
        # Should only include colleagues
        for node in person_nodes:
            assert node["data"]["archetype"] == "colleague"
        
        # Test organization filter
        graph_data = await self.graph_service.generate_filtered_network_graph(
            user_id=self.test_user.user_id,
            organization="Company0"
        )
        
        person_nodes = [n for n in graph_data["nodes"] if n["type"] == "person"]
        for node in person_nodes:
            assert "Company0" in node["data"]["company"]
        
        # Test max_nodes limit
        graph_data = await self.graph_service.generate_filtered_network_graph(
            user_id=self.test_user.user_id,
            max_nodes=2
        )
        
        person_nodes = [n for n in graph_data["nodes"] if n["type"] == "person"]
        assert len(person_nodes) <= 2

    @pytest.mark.asyncio
    async def test_different_layout_algorithms(self):
        """Test different layout algorithms"""
        layouts = ["force_directed", "circular", "radial"]
        
        for layout_type in layouts:
            graph_data = await self.graph_service.generate_full_network_graph(
                self.test_user.user_id, layout_type=layout_type
            )
            
            assert graph_data["layout"]["type"] == layout_type
            positions = graph_data["layout"]["positions"]
            
            # Should have positions for all nodes
            assert len(positions) == len(graph_data["nodes"])
            
            # Positions should have x and y coordinates
            for node_id, pos in positions.items():
                assert "x" in pos
                assert "y" in pos
                assert isinstance(pos["x"], (int, float))
                assert isinstance(pos["y"], (int, float))

    @pytest.mark.asyncio
    async def test_get_node_details(self):
        """Test node details retrieval"""
        person = self.test_persons[0]
        
        node_details = await self.graph_service.get_node_details(
            self.test_user.user_id, person.person_id
        )
        
        assert "person" in node_details
        assert "relationship" in node_details
        assert "mutual_connections" in node_details
        
        # Check person data
        person_data = node_details["person"]
        assert person_data["id"] == str(person.person_id)
        assert person_data["name"] == f"{person.first_name} {person.last_name}"
        assert "professional_info" in person_data
        assert "personal_details" in person_data
        
        # Check relationship data
        relationship_data = node_details["relationship"]
        assert "archetype" in relationship_data
        assert "overall_score" in relationship_data
        assert "dimensions" in relationship_data

    @pytest.mark.asyncio
    async def test_get_node_details_nonexistent(self):
        """Test node details for non-existent person"""
        fake_id = uuid.uuid4()
        
        result = await self.graph_service.get_node_details(
            self.test_user.user_id, fake_id
        )
        
        assert "error" in result
        assert result["error"] == "Person not found"

    @pytest.mark.asyncio
    async def test_get_graph_clusters(self):
        """Test graph clustering analysis"""
        clusters = await self.graph_service.get_graph_clusters(self.test_user.user_id)
        
        assert "clusters" in clusters
        assert "metadata" in clusters
        
        # Check metadata
        metadata = clusters["metadata"]
        assert "total_clusters" in metadata
        assert "algorithm" in metadata
        assert metadata["algorithm"] == "greedy_modularity"
        
        # Check cluster structure
        for cluster in clusters["clusters"]:
            assert "cluster_id" in cluster
            assert "size" in cluster
            assert "nodes" in cluster
            
            # Check node structure in clusters
            for node in cluster["nodes"]:
                assert "id" in node
                assert "label" in node
                assert "type" in node

    def test_archetype_color_mapping(self):
        """Test archetype color mapping"""
        test_cases = [
            ("colleague", "#2196F3"),
            ("friend", "#FF9800"),
            ("mentor", "#9C27B0"),
            ("client", "#4CAF50"),
            ("unknown", "#9E9E9E"),
            (None, "#9E9E9E")
        ]
        
        for archetype, expected_color in test_cases:
            color = self.graph_service._get_archetype_color(archetype)
            assert color == expected_color

    def test_graph_metrics_calculation(self):
        """Test graph metrics calculation"""
        # Create a simple NetworkX graph for testing
        import networkx as nx
        
        G = nx.Graph()
        G.add_edges_from([("A", "B"), ("B", "C"), ("C", "A")])  # Triangle
        
        metrics = self.graph_service._calculate_graph_metrics(G, self.test_relationships)
        
        assert "density" in metrics
        assert "clustering" in metrics
        assert "avg_path_length" in metrics
        
        # Triangle should have high clustering
        assert metrics["clustering"] == 1.0
        assert metrics["density"] == 1.0  # Complete triangle

    def test_node_size_calculation(self):
        """Test node size calculation based on relationship strength"""
        # Test with different relationship scores
        positions = {}
        
        # Create test data with varying scores
        test_relationships = []
        for i, person in enumerate(self.test_persons[:3]):
            rel = Knows(
                from_person_id=self.test_user.user_id,
                to_person_id=person.person_id,
                user_id=self.test_user.user_id,
                relationship_depth={"overall_score": [30, 70, 90][i]}
            )
            test_relationships.append(rel)
        
        nodes = self.graph_service._generate_nodes_data(
            self.user_person, self.test_persons[:3], test_relationships, positions
        )
        
        person_nodes = [n for n in nodes if n["type"] == "person"]
        
        # Higher scores should result in larger nodes
        sizes = [node["size"] for node in person_nodes]
        assert sizes[2] > sizes[1] > sizes[0]  # 90 > 70 > 30

    @pytest.mark.asyncio
    async def test_empty_network(self):
        """Test graph generation with no relationships"""
        # Create user with no relationships
        empty_user = User(
            user_id=uuid.uuid4(),
            email="<EMAIL>", 
            hashed_password="hashed",
            is_active=True
        )
        self.db_session.add(empty_user)
        
        empty_user_person = Person(
            person_id=empty_user.user_id,
            user_id=empty_user.user_id,
            first_name="Empty",
            last_name="User",
            is_user=True
        )
        self.db_session.add(empty_user_person)
        self.db_session.commit()
        
        graph_data = await self.graph_service.generate_full_network_graph(empty_user.user_id)
        
        # Should have only user node
        assert len(graph_data["nodes"]) == 1
        assert len(graph_data["edges"]) == 0
        assert graph_data["nodes"][0]["type"] == "user"

    @pytest.mark.asyncio
    async def test_performance_with_large_network(self):
        """Test performance with larger network (simplified test)"""
        import time
        
        start_time = time.time()
        graph_data = await self.graph_service.generate_full_network_graph(self.test_user.user_id)
        end_time = time.time()
        
        # Should complete within reasonable time (even with small network)
        assert end_time - start_time < 5.0  # 5 seconds max
        
        # Should generate valid data
        assert len(graph_data["nodes"]) > 0
        assert "metadata" in graph_data