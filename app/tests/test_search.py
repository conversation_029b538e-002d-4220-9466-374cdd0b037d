"""
Search functionality tests
Tests for fuzzy search, filtering, and search algorithms
"""

import pytest
import time
import statistics
from fastapi.testclient import TestClient
from typing import List, Dict, Any

from app.tests.base import BaseAPITestCase, BaseIntegrationTestCase
from app.tests.utils import TestDataFactory


class TestPersonSearch(BaseAPITestCase):
    """Test person search functionality"""

    def setup_method(self):
        super().setup_method()
        self.search_endpoint = "/api/v1/search/persons"
        self.persons_endpoint = "/api/v1/persons"

    def create_test_persons_for_search(self, client: TestClient):
        """Create diverse test persons for search testing"""
        test_persons = [
            {
                "first_name": "<PERSON>",
                "last_name": "<PERSON><PERSON>",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "title": "Software Engineer",
                    "company": "TechCorp",
                },
            },
            {
                "first_name": "<PERSON>",
                "last_name": "<PERSON>",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {"title": "UX Designer", "company": "DesignCo"},
            },
            {
                "first_name": "<PERSON>",
                "last_name": "<PERSON>",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "title": "Product Manager",
                    "company": "StartupIO",
                },
            },
            {
                "first_name": "Sarah",
                "last_name": "Williams",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "title": "Professor",
                    "company": "Tech University",
                },
            },
            {
                "first_name": "David",
                "last_name": "Brown",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "title": "Senior Consultant",
                    "company": "ConsultingFirm",
                },
            },
        ]

        created_persons = []
        for person_data in test_persons:
            response = client.post(
                self.persons_endpoint, json=person_data, headers=self.auth_headers
            )
            if response.status_code == 501:
                pytest.skip(
                    "Person creation required for search tests but not implemented"
                )
            created_persons.append(self.assert_successful_response(response, 200))

        return created_persons

    def test_search_persons_by_name_exact(self, client: TestClient):
        """Test exact name search."""
        self.authenticate_user(client)
        self.create_test_persons_for_search(client)

        response = client.get(
            f"{self.search_endpoint}?q=John Doe", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Person search endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert isinstance(data, dict)
        assert "results" in data
        assert "pagination" in data
        assert "metadata" in data

        results = data["results"]
        assert isinstance(results, list)

        # Should find John Doe
        if results:
            found_john = any(
                person.get("first_name") == "John" and person.get("last_name") == "Doe"
                for person in results
            )
            assert found_john, "Should find John Doe in search results"

    def test_search_persons_by_name_partial(self, client: TestClient):
        """Test partial name search."""
        self.authenticate_user(client)
        self.create_test_persons_for_search(client)

        response = client.get(
            f"{self.search_endpoint}?q=John", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Person search endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should find persons with "John" in their name
        if data:
            john_results = [
                person
                for person in data
                if "john" in person.get("first_name", "").lower()
                or "john" in person.get("last_name", "").lower()
            ]
            assert len(john_results) > 0, "Should find persons with 'John' in name"

    def test_search_persons_fuzzy_matching(self, client: TestClient):
        """Test fuzzy matching for typos."""
        self.authenticate_user(client)
        self.create_test_persons_for_search(client)

        # Search with typo
        response = client.get(
            f"{self.search_endpoint}?q=Jon Doe", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Person search endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should still find John Doe despite typo (if fuzzy search is implemented)
        if data:
            # Check if fuzzy matching is working
            fuzzy_results = [
                person
                for person in data
                if person.get("first_name") == "John"
                and person.get("last_name") == "Doe"
            ]
            # This is aspirational - fuzzy search might not be implemented yet
            # For now, we just verify the endpoint works
            assert isinstance(data, list)

    def test_search_persons_by_company(self, client: TestClient):
        """Test search by company name."""
        self.authenticate_user(client)
        self.create_test_persons_for_search(client)

        response = client.get(
            f"{self.search_endpoint}?q=TechCorp", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Person search endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should find persons working at TechCorp
        if data:
            techcorp_results = [
                person
                for person in data
                if "techcorp" in str(person.get("professional_info", {})).lower()
            ]
            # If company search is implemented, should find results
            assert isinstance(data, list)

    def test_search_persons_by_title(self, client: TestClient):
        """Test search by job title."""
        self.authenticate_user(client)
        self.create_test_persons_for_search(client)

        response = client.get(
            f"{self.search_endpoint}?q=Engineer", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Person search endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should find persons with "Engineer" in their title
        if data:
            engineer_results = [
                person
                for person in data
                if "engineer" in str(person.get("professional_info", {})).lower()
            ]
            assert isinstance(data, list)

    def test_search_persons_with_filters(self, client: TestClient):
        """Test search with additional filters."""
        self.authenticate_user(client)
        self.create_test_persons_for_search(client)

        # Search with company filter
        response = client.get(
            f"{self.search_endpoint}?q=John&company=TechCorp", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Person search endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should filter results by company
        if data and "company" in client.get(f"{self.search_endpoint}?q=test").url:
            # If filtering is implemented, verify results
            for person in data:
                if (
                    "professional_info" in person
                    and "company" in person["professional_info"]
                ):
                    assert "techcorp" in person["professional_info"]["company"].lower()

    def test_search_persons_pagination(self, client: TestClient):
        """Test search result pagination."""
        self.authenticate_user(client)
        self.create_test_persons_for_search(client)

        # Search with pagination
        response = client.get(
            f"{self.search_endpoint}?q=&limit=3&skip=0", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Person search endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should respect pagination limits
        assert len(data) <= 3

    def test_search_persons_empty_query(self, client: TestClient):
        """Test search with empty query."""
        self.authenticate_user(client)
        self.create_test_persons_for_search(client)

        response = client.get(f"{self.search_endpoint}?q=", headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Person search endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should return all persons or handle empty query gracefully
        assert isinstance(data, list)

    def test_search_persons_no_results(self, client: TestClient):
        """Test search with no matching results."""
        self.authenticate_user(client)
        self.create_test_persons_for_search(client)

        response = client.get(
            f"{self.search_endpoint}?q=NonexistentPerson", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Person search endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should return empty list
        assert isinstance(data, list)
        assert len(data) == 0

    def test_search_persons_case_insensitive(self, client: TestClient):
        """Test case-insensitive search."""
        self.authenticate_user(client)
        self.create_test_persons_for_search(client)

        # Search with different cases
        responses = [
            client.get(f"{self.search_endpoint}?q=john", headers=self.auth_headers),
            client.get(f"{self.search_endpoint}?q=JOHN", headers=self.auth_headers),
            client.get(f"{self.search_endpoint}?q=John", headers=self.auth_headers),
        ]

        if responses[0].status_code == 501:
            pytest.skip("Person search endpoint not fully implemented yet")

        results = [self.assert_successful_response(response) for response in responses]

        # All should return similar results (case insensitive)
        for result in results:
            assert isinstance(result, list)

    def test_search_persons_special_characters(self, client: TestClient):
        """Test search with special characters."""
        self.authenticate_user(client)

        # Create person with special characters
        special_person = {
            "first_name": "José",
            "last_name": "García-López",
            "contact_info": {"email": "<EMAIL>"},
        }

        create_response = client.post(
            self.persons_endpoint, json=special_person, headers=self.auth_headers
        )
        if create_response.status_code == 501:
            pytest.skip("Person creation required for this test but not implemented")

        # Search for person with special characters
        response = client.get(
            f"{self.search_endpoint}?q=José", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Person search endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should handle unicode characters properly
        assert isinstance(data, list)


class TestOrganizationSearch(BaseAPITestCase):
    """Test organization search functionality"""

    def setup_method(self):
        super().setup_method()
        self.search_endpoint = "/api/v1/search/organizations"
        self.organizations_endpoint = "/api/v1/organizations"

    def create_test_organizations_for_search(self, client: TestClient):
        """Create test organizations for search testing"""
        test_orgs = [
            {
                "name": "TechCorp Industries",
                "details": {"industry": "Technology", "size": "1000-5000"},
            },
            {
                "name": "DesignCo Studio",
                "details": {"industry": "Design", "size": "50-100"},
            },
            {
                "name": "StartupIO",
                "details": {"industry": "Technology", "size": "10-50"},
            },
            {
                "name": "ConsultingFirm LLC",
                "details": {"industry": "Consulting", "size": "500-1000"},
            },
        ]

        created_orgs = []
        for org_data in test_orgs:
            response = client.post(
                self.organizations_endpoint, json=org_data, headers=self.auth_headers
            )
            if response.status_code == 501:
                pytest.skip(
                    "Organization creation required for search tests but not implemented"
                )
            created_orgs.append(self.assert_successful_response(response, 201))

        return created_orgs

    def test_search_organizations_by_name(self, client: TestClient):
        """Test organization search by name."""
        self.authenticate_user(client)
        self.create_test_organizations_for_search(client)

        response = client.get(
            f"{self.search_endpoint}?q=TechCorp", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Organization search endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert isinstance(data, list)

        # Should find TechCorp
        if data:
            techcorp_found = any(
                "techcorp" in org.get("name", "").lower() for org in data
            )
            assert techcorp_found or len(data) == 0  # Either found or no results

    def test_search_organizations_by_industry(self, client: TestClient):
        """Test organization search by industry."""
        self.authenticate_user(client)
        self.create_test_organizations_for_search(client)

        response = client.get(
            f"{self.search_endpoint}?q=Technology", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Organization search endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should find technology companies
        if data:
            tech_orgs = [
                org
                for org in data
                if "technology" in str(org.get("details", {})).lower()
            ]
            assert isinstance(data, list)

    def test_search_organizations_unauthorized(self, client: TestClient):
        """Test organization search without authentication."""
        response = client.get(f"{self.search_endpoint}?q=test")
        self.assert_unauthorized_response(response)


class TestAdvancedPersonSearch(BaseAPITestCase):
    """Test advanced person search functionality with fuzzy search, ranking, and filtering"""

    def setup_method(self):
        super().setup_method()
        self.search_endpoint = "/api/v1/search/persons"
        self.persons_endpoint = "/api/v1/persons"

    def create_comprehensive_test_data(self, client: TestClient) -> List[Dict[str, Any]]:
        """Create comprehensive test data for advanced search testing"""
        test_persons = [
            {
                "first_name": "John",
                "last_name": "Smith",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "title": "Senior Software Engineer",
                    "company": "Tech Corp",
                    "industry": "Technology"
                },
                "personal_details": {
                    "tags": ["developer", "tech", "startup"],
                    "interests": ["coding", "ai"]
                },
            },
            {
                "first_name": "Jane",
                "last_name": "Johnson",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "title": "Lead UX Designer", 
                    "company": "Design Studio",
                    "industry": "Design"
                },
                "personal_details": {
                    "tags": ["designer", "creative", "startup"],
                    "interests": ["design", "ux"]
                },
            },
            {
                "first_name": "Jonathan",
                "last_name": "Davis",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "title": "Management Consultant",
                    "company": "Consulting Inc",
                    "industry": "Consulting"
                },
                "personal_details": {
                    "tags": ["consultant", "business"],
                    "interests": ["strategy", "finance"]
                },
            },
            {
                "first_name": "Michael",
                "last_name": "Brown",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "title": "Product Manager",
                    "company": "Startup IO",
                    "industry": "Technology"
                },
                "personal_details": {
                    "tags": ["product", "startup", "tech"],
                    "interests": ["innovation", "product"]
                },
            },
            {
                "first_name": "Sarah",
                "last_name": "Wilson",
                "contact_info": {"email": "<EMAIL>"},
                "professional_info": {
                    "title": "Computer Science Professor",
                    "company": "Tech University",
                    "industry": "Education"
                },
                "personal_details": {
                    "tags": ["academic", "research", "tech"],
                    "interests": ["research", "ai", "teaching"]
                },
            }
        ]

        created_persons = []
        for person_data in test_persons:
            response = client.post(
                self.persons_endpoint, json=person_data, headers=self.auth_headers
            )
            if response.status_code == 501:
                pytest.skip("Person creation required for advanced search tests")
            created_persons.append(self.assert_successful_response(response, 201))

        return created_persons

    def test_fuzzy_search_similarity(self, client: TestClient):
        """Test fuzzy search with similarity scoring"""
        self.authenticate_user(client)
        self.create_comprehensive_test_data(client)

        # Test fuzzy matching with typos
        test_cases = [
            ("Jon", "John"),  # Missing letter
            ("Johnn", "John"),  # Extra letter  
            ("Jhon", "John"),  # Transposed letters
            ("Jane", "Jane"),  # Exact match
            ("Jame", "Jane"),  # Close match
        ]

        for query, expected_name in test_cases:
            response = client.get(
                f"{self.search_endpoint}?q={query}&similarity_threshold=0.2",
                headers=self.auth_headers
            )
            
            if response.status_code == 501:
                pytest.skip("Advanced search not implemented yet")

            data = self.assert_successful_response(response)
            
            # Check if response has new structure with results array
            if isinstance(data, dict) and "results" in data:
                results = data["results"]
                pagination = data.get("pagination", {})
                
                # Verify pagination metadata
                assert "total" in pagination
                assert "limit" in pagination
                assert "offset" in pagination
                
                # Look for fuzzy matches
                found_match = any(
                    expected_name.lower() in person.get("first_name", "").lower()
                    for person in results
                )
                
                if found_match:
                    # Verify relevance scoring
                    for person in results:
                        if "relevance_score" in person:
                            assert isinstance(person["relevance_score"], (int, float))
                            assert person["relevance_score"] >= 0
                        
                        if "match_fields" in person:
                            assert isinstance(person["match_fields"], list)
                            
                        if "boost_details" in person:
                            assert isinstance(person["boost_details"], dict)

    def test_pagination_functionality(self, client: TestClient):
        """Test search pagination with offset and limit"""
        self.authenticate_user(client)
        self.create_comprehensive_test_data(client)

        # Test pagination parameters
        test_cases = [
            {"limit": 2, "offset": 0},
            {"limit": 2, "offset": 2}, 
            {"limit": 3, "offset": 1},
            {"limit": 10, "offset": 0},
        ]

        for params in test_cases:
            response = client.get(
                f"{self.search_endpoint}?q=a&limit={params['limit']}&offset={params['offset']}&similarity_threshold=0.1",
                headers=self.auth_headers
            )
            
            if response.status_code == 501:
                pytest.skip("Pagination not implemented yet")

            data = self.assert_successful_response(response)
            
            if isinstance(data, dict) and "results" in data:
                results = data["results"]
                pagination = data["pagination"]
                
                # Verify pagination constraints
                assert len(results) <= params["limit"]
                assert pagination["limit"] == params["limit"]
                assert pagination["offset"] == params["offset"]
                assert pagination["current_page"] == (params["offset"] // params["limit"]) + 1
                
                # Verify pagination logic
                if pagination["total"] > params["offset"]:
                    expected_results = min(params["limit"], pagination["total"] - params["offset"])
                    assert len(results) <= expected_results

    def test_company_filtering(self, client: TestClient):
        """Test filtering by company name"""
        self.authenticate_user(client)
        self.create_comprehensive_test_data(client)

        # Test company filters
        company_filters = ["Tech", "Design", "Consulting"]
        
        for company_filter in company_filters:
            response = client.get(
                f"{self.search_endpoint}?q=a&company={company_filter}&similarity_threshold=0.1",
                headers=self.auth_headers
            )
            
            if response.status_code == 501:
                pytest.skip("Company filtering not implemented yet")

            data = self.assert_successful_response(response)
            
            if isinstance(data, dict) and "results" in data:
                results = data["results"]
                metadata = data.get("metadata", {})
                
                # Verify filtering metadata
                filters_applied = metadata.get("filters_applied", {})
                assert filters_applied.get("company") == company_filter
                
                # Verify filtered results
                for person in results:
                    company = person.get("company", "")
                    if company:  # Only check if company is present
                        assert company_filter.lower() in company.lower()

    def test_relationship_type_filtering(self, client: TestClient):
        """Test filtering by relationship archetype"""
        self.authenticate_user(client)
        self.create_comprehensive_test_data(client)

        # Test relationship type filtering
        response = client.get(
            f"{self.search_endpoint}?q=John&relationship_type=friend&similarity_threshold=0.2",
            headers=self.auth_headers
        )
        
        if response.status_code == 501:
            pytest.skip("Relationship filtering not implemented yet")

        data = self.assert_successful_response(response)
        
        if isinstance(data, dict) and "results" in data:
            metadata = data.get("metadata", {})
            filters_applied = metadata.get("filters_applied", {})
            assert filters_applied.get("relationship_type") == "friend"

    def test_tag_filtering(self, client: TestClient):
        """Test filtering by personal tags"""
        self.authenticate_user(client)
        self.create_comprehensive_test_data(client)

        # Test tag filtering
        tag_filters = ["tech", "startup", "designer"]
        
        for tag_filter in tag_filters:
            response = client.get(
                f"{self.search_endpoint}?q=a&tags={tag_filter}&similarity_threshold=0.1",
                headers=self.auth_headers
            )
            
            if response.status_code == 501:
                pytest.skip("Tag filtering not implemented yet")

            data = self.assert_successful_response(response)
            
            if isinstance(data, dict) and "results" in data:
                results = data["results"]
                metadata = data.get("metadata", {})
                
                # Verify filtering metadata
                filters_applied = metadata.get("filters_applied", {})
                assert filters_applied.get("tags") == tag_filter
                
                # Verify tagged results (if any found)
                for person in results:
                    tags = person.get("tags", [])
                    if tags:  # Only check if tags are present
                        tag_match = any(tag_filter.lower() in tag.lower() for tag in tags)
                        # Note: This might not always be true due to search query matching

    def test_search_facets_metadata(self, client: TestClient):
        """Test search facets and metadata generation"""
        self.authenticate_user(client)
        self.create_comprehensive_test_data(client)

        response = client.get(
            f"{self.search_endpoint}?q=a&include_metadata=true&similarity_threshold=0.1",
            headers=self.auth_headers
        )
        
        if response.status_code == 501:
            pytest.skip("Search metadata not implemented yet")

        data = self.assert_successful_response(response)
        
        if isinstance(data, dict) and "metadata" in data:
            metadata = data["metadata"]
            
            # Verify metadata structure
            assert "query" in metadata
            assert "similarity_threshold" in metadata
            assert "filters_applied" in metadata
            assert "facets" in metadata
            
            facets = metadata["facets"]
            
            # Verify facets structure
            if "companies" in facets:
                for company_facet in facets["companies"]:
                    assert "value" in company_facet
                    assert "count" in company_facet
                    assert isinstance(company_facet["count"], int)
            
            if "relationship_types" in facets:
                for rel_facet in facets["relationship_types"]:
                    assert "value" in rel_facet
                    assert "count" in rel_facet
                    assert isinstance(rel_facet["count"], int)

    def test_relevance_scoring_and_ranking(self, client: TestClient):
        """Test relevance scoring and result ranking"""
        self.authenticate_user(client)
        self.create_comprehensive_test_data(client)

        response = client.get(
            f"{self.search_endpoint}?q=John&similarity_threshold=0.2",
            headers=self.auth_headers
        )
        
        if response.status_code == 501:
            pytest.skip("Relevance scoring not implemented yet")

        data = self.assert_successful_response(response)
        
        if isinstance(data, dict) and "results" in data:
            results = data["results"]
            
            if len(results) > 1:
                # Verify results are ordered by relevance score (descending)
                relevance_scores = []
                for person in results:
                    if "relevance_score" in person:
                        relevance_scores.append(person["relevance_score"])
                        
                        # Verify boost details structure
                        if "boost_details" in person:
                            boost_details = person["boost_details"]
                            assert "base_similarity" in boost_details
                            assert "company_boost" in boost_details
                            assert "relationship_boost" in boost_details
                
                # Check if scores are in descending order
                if len(relevance_scores) > 1:
                    for i in range(len(relevance_scores) - 1):
                        assert relevance_scores[i] >= relevance_scores[i + 1], \
                            "Results should be ordered by relevance score (descending)"

    def test_combined_filters(self, client: TestClient):
        """Test multiple filters working together"""
        self.authenticate_user(client)
        self.create_comprehensive_test_data(client)

        # Test multiple filters combined
        response = client.get(
            f"{self.search_endpoint}?q=John&company=Tech&tags=tech&similarity_threshold=0.2",
            headers=self.auth_headers
        )
        
        if response.status_code == 501:
            pytest.skip("Combined filtering not implemented yet")

        data = self.assert_successful_response(response)
        
        if isinstance(data, dict) and "metadata" in data:
            filters_applied = data["metadata"]["filters_applied"]
            assert filters_applied["company"] == "Tech"
            assert filters_applied["tags"] == "tech"


class TestSearchPerformance(BaseAPITestCase):
    """Test search performance and benchmarking"""

    def setup_method(self):
        super().setup_method()
        self.search_endpoint = "/api/v1/search/persons"
        self.persons_endpoint = "/api/v1/persons"

    def create_large_dataset(self, client: TestClient, count: int = 50) -> List[Dict[str, Any]]:
        """Create a larger dataset for performance testing"""
        import random
        import string
        
        first_names = ["John", "Jane", "Michael", "Sarah", "David", "Lisa", "Chris", "Emma", "Alex", "Maria"]
        last_names = ["Smith", "Johnson", "Brown", "Davis", "Wilson", "Miller", "Moore", "Taylor", "Anderson", "Thomas"]
        companies = ["Tech Corp", "Design Studio", "Consulting Inc", "Startup IO", "Innovation Labs"]
        titles = ["Engineer", "Designer", "Manager", "Consultant", "Analyst", "Director", "Specialist"]
        
        created_persons = []
        for i in range(count):
            person_data = {
                "first_name": random.choice(first_names),
                "last_name": random.choice(last_names),
                "contact_info": {
                    "email": f"user{i}@{random.choice(['tech', 'design', 'startup'])}.com"
                },
                "professional_info": {
                    "title": f"{random.choice(['Senior', 'Lead', 'Principal', ''])} {random.choice(titles)}".strip(),
                    "company": random.choice(companies),
                    "industry": random.choice(["Technology", "Design", "Consulting", "Education"])
                },
                "personal_details": {
                    "tags": random.sample(["tech", "startup", "design", "business", "innovation"], 
                                        random.randint(1, 3)),
                    "interests": random.sample(["coding", "design", "management", "research"], 
                                             random.randint(1, 2))
                }
            }
            
            response = client.post(
                self.persons_endpoint, json=person_data, headers=self.auth_headers
            )
            if response.status_code == 501:
                pytest.skip("Person creation required for performance tests")
            created_persons.append(self.assert_successful_response(response, 201))
            
        return created_persons

    def measure_search_performance(self, client: TestClient, query: str, iterations: int = 5) -> Dict[str, float]:
        """Measure search performance metrics"""
        response_times = []
        
        for _ in range(iterations):
            start_time = time.time()
            response = client.get(
                f"{self.search_endpoint}?q={query}&similarity_threshold=0.2",
                headers=self.auth_headers
            )
            end_time = time.time()
            
            if response.status_code == 501:
                pytest.skip("Search performance testing requires implemented search")
            
            self.assert_successful_response(response)
            response_times.append(end_time - start_time)
        
        return {
            "avg_response_time": statistics.mean(response_times),
            "min_response_time": min(response_times),
            "max_response_time": max(response_times),
            "median_response_time": statistics.median(response_times),
            "std_deviation": statistics.stdev(response_times) if len(response_times) > 1 else 0
        }

    def test_search_performance_baseline(self, client: TestClient):
        """Test baseline search performance with moderate dataset"""
        self.authenticate_user(client)
        self.create_large_dataset(client, count=20)

        # Measure performance for different search types
        search_queries = [
            "John",      # Name search
            "Tech",      # Company search  
            "Engineer",  # Title search
            "Jo",        # Partial match
        ]
        
        performance_results = {}
        for query in search_queries:
            try:
                perf_metrics = self.measure_search_performance(client, query)
                performance_results[query] = perf_metrics
                
                # Assert reasonable performance (adjust thresholds as needed)
                assert perf_metrics["avg_response_time"] < 2.0, \
                    f"Search for '{query}' took too long: {perf_metrics['avg_response_time']:.3f}s"
                assert perf_metrics["max_response_time"] < 5.0, \
                    f"Slowest search for '{query}' took too long: {perf_metrics['max_response_time']:.3f}s"
                    
            except Exception as e:
                pytest.skip(f"Performance testing failed for query '{query}': {e}")
        
        # Log performance results for monitoring
        print(f"\nSearch Performance Results:")
        for query, metrics in performance_results.items():
            print(f"  '{query}': {metrics['avg_response_time']:.3f}s avg, {metrics['max_response_time']:.3f}s max")

    def test_pagination_performance(self, client: TestClient):
        """Test pagination performance with different page sizes"""
        self.authenticate_user(client)
        self.create_large_dataset(client, count=30)

        # Test different pagination scenarios
        pagination_tests = [
            {"limit": 5, "offset": 0},
            {"limit": 10, "offset": 10},
            {"limit": 20, "offset": 0},
            {"limit": 5, "offset": 25},
        ]
        
        for params in pagination_tests:
            try:
                perf_metrics = self.measure_search_performance(
                    client, 
                    f"a&limit={params['limit']}&offset={params['offset']}"
                )
                
                # Pagination should not significantly impact performance
                assert perf_metrics["avg_response_time"] < 3.0, \
                    f"Paginated search took too long: {perf_metrics['avg_response_time']:.3f}s"
                    
            except Exception as e:
                pytest.skip(f"Pagination performance testing failed: {e}")

    def test_filter_performance(self, client: TestClient):
        """Test performance impact of various filters"""
        self.authenticate_user(client)
        self.create_large_dataset(client, count=25)

        # Test filter combinations
        filter_tests = [
            "a&company=Tech",
            "John&tags=tech",
            "a&company=Design&tags=startup",
        ]
        
        for filter_query in filter_tests:
            try:
                perf_metrics = self.measure_search_performance(client, filter_query)
                
                # Filtered searches should still be reasonably fast
                assert perf_metrics["avg_response_time"] < 3.0, \
                    f"Filtered search took too long: {perf_metrics['avg_response_time']:.3f}s"
                    
            except Exception as e:
                pytest.skip(f"Filter performance testing failed: {e}")

    def test_concurrent_search_performance(self, client: TestClient):
        """Test search performance under concurrent load"""
        import threading
        import queue
        
        self.authenticate_user(client)
        self.create_large_dataset(client, count=15)

        # Simulate concurrent searches
        def concurrent_search(results_queue: queue.Queue, search_id: int):
            try:
                start_time = time.time()
                response = client.get(
                    f"{self.search_endpoint}?q=John&similarity_threshold=0.2",
                    headers=self.auth_headers
                )
                end_time = time.time()
                
                if response.status_code != 501:
                    self.assert_successful_response(response)
                    results_queue.put({
                        "search_id": search_id,
                        "response_time": end_time - start_time,
                        "success": True
                    })
                else:
                    results_queue.put({
                        "search_id": search_id,
                        "success": False,
                        "reason": "Not implemented"
                    })
            except Exception as e:
                results_queue.put({
                    "search_id": search_id,
                    "success": False,
                    "error": str(e)
                })

        # Run concurrent searches
        num_concurrent = 5
        results_queue = queue.Queue()
        threads = []
        
        for i in range(num_concurrent):
            thread = threading.Thread(target=concurrent_search, args=(results_queue, i))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=10)  # 10 second timeout
        
        # Collect results
        concurrent_results = []
        while not results_queue.empty():
            result = results_queue.get()
            concurrent_results.append(result)
        
        # Verify concurrent performance
        successful_searches = [r for r in concurrent_results if r.get("success", False)]
        
        if successful_searches:
            response_times = [r["response_time"] for r in successful_searches]
            avg_concurrent_time = statistics.mean(response_times)
            
            # Concurrent searches should not degrade too much
            assert avg_concurrent_time < 5.0, \
                f"Concurrent searches too slow: {avg_concurrent_time:.3f}s average"
            
            print(f"\nConcurrent Search Results:")
            print(f"  Successful: {len(successful_searches)}/{num_concurrent}")
            print(f"  Average time: {avg_concurrent_time:.3f}s")
        else:
            # Check if all failed due to not being implemented
            not_implemented = all(r.get("reason") == "Not implemented" for r in concurrent_results)
            if not_implemented:
                pytest.skip("Concurrent search testing requires implemented search")
            else:
                pytest.fail("All concurrent searches failed unexpectedly")
