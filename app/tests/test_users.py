"""
User management tests
"""

import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>

from app.tests.base import BaseAPITestCase
from app.tests.test_config import RESPONSE_FIELDS, TEST_USERS


class TestUserProfile(BaseAPITestCase):
    """Test user profile management"""

    def test_get_user_profile_success(self, client: TestClient, test_user_data):
        """Test getting user profile."""
        self.authenticate_user(client, test_user_data)

        response = client.get("/api/v1/users/me", headers=self.auth_headers)
        data = self.assert_successful_response(response)

        # Verify response structure
        self.assertions.assert_response_structure(
            data,
            RESPONSE_FIELDS["user"]["required"],
            RESPONSE_FIELDS["user"]["optional"],
        )

        # Verify user data
        assert data["email"] == test_user_data["email"]
        assert data["first_name"] == test_user_data["first_name"]
        assert data["last_name"] == test_user_data["last_name"]
        assert data["is_active"] is True
        self.assertions.assert_valid_uuid(data["user_id"])

    def test_get_user_profile_unauthorized(self, client: TestClient):
        """Test getting user profile without authentication."""
        response = client.get("/api/v1/users/me")
        self.assert_unauthorized_response(response)

    def test_update_user_profile_success(self, client: TestClient, test_user_data):
        """Test updating user profile."""
        self.authenticate_user(client, test_user_data)

        update_data = {"first_name": "Updated", "last_name": "Name"}

        response = client.put(
            "/api/v1/users/me", json=update_data, headers=self.auth_headers
        )

        # This endpoint might not be implemented yet
        if response.status_code == 404:
            pytest.skip("User profile update endpoint not implemented yet")
        else:
            data = self.assert_successful_response(response)
            assert data["first_name"] == "Updated"
            assert data["last_name"] == "Name"

    def test_update_user_email_success(self, client: TestClient, test_user_data):
        """Test updating user email."""
        self.authenticate_user(client, test_user_data)

        update_data = {"email": "<EMAIL>"}

        response = client.put(
            "/api/v1/users/me", json=update_data, headers=self.auth_headers
        )

        if response.status_code == 404:
            pytest.skip("User profile update endpoint not implemented yet")
        else:
            # Email update might require verification
            assert response.status_code in [200, 202]  # 202 for pending verification


class TestUserPreferences(BaseAPITestCase):
    """Test user preferences management"""

    def test_get_user_preferences_success(self, client: TestClient, test_user_data):
        """Test getting user preferences."""
        self.authenticate_user(client, test_user_data)

        response = client.get("/api/v1/users/me/preferences", headers=self.auth_headers)

        if response.status_code == 404:
            pytest.skip("User preferences endpoint not implemented yet")
        else:
            data = self.assert_successful_response(response)
            assert isinstance(data, dict)
            # Should contain preference categories
            expected_categories = ["stated_decision_factors", "notification_settings"]
            for category in expected_categories:
                if category in data:
                    assert isinstance(data[category], dict)

    def test_update_user_preferences_success(self, client: TestClient, test_user_data):
        """Test updating user preferences."""
        self.authenticate_user(client, test_user_data)

        preferences_data = {
            "stated_decision_factors": {
                "relationship_importance": 0.8,
                "professional_focus": 0.9,
                "personal_connection": 0.7,
                "frequency_preference": 0.6,
            },
            "notification_settings": {
                "email_notifications": True,
                "push_notifications": False,
                "weekly_digest": True,
                "ai_suggestions": True,
            },
            "privacy_settings": {
                "profile_visibility": "connections_only",
                "contact_info_sharing": "limited",
            },
        }

        response = client.put(
            "/api/v1/users/me/preferences",
            json=preferences_data,
            headers=self.auth_headers,
        )

        if response.status_code == 404:
            pytest.skip("User preferences endpoint not implemented yet")
        else:
            data = self.assert_successful_response(response)

            # Verify preferences were updated
            if "stated_decision_factors" in data:
                assert data["stated_decision_factors"]["relationship_importance"] == 0.8
            if "notification_settings" in data:
                assert data["notification_settings"]["email_notifications"] is True

    def test_update_partial_preferences(self, client: TestClient, test_user_data):
        """Test updating only some preferences."""
        self.authenticate_user(client, test_user_data)

        partial_preferences = {"notification_settings": {"email_notifications": False}}

        response = client.put(
            "/api/v1/users/me/preferences",
            json=partial_preferences,
            headers=self.auth_headers,
        )

        if response.status_code == 404:
            pytest.skip("User preferences endpoint not implemented yet")
        else:
            data = self.assert_successful_response(response)
            # Should update only the specified preferences
            if "notification_settings" in data:
                assert data["notification_settings"]["email_notifications"] is False

    def test_invalid_preference_values(self, client: TestClient, test_user_data):
        """Test updating preferences with invalid values."""
        self.authenticate_user(client, test_user_data)

        invalid_preferences = {
            "stated_decision_factors": {
                "relationship_importance": 1.5,  # Should be between 0 and 1
                "invalid_factor": "invalid_value",
            }
        }

        response = client.put(
            "/api/v1/users/me/preferences",
            json=invalid_preferences,
            headers=self.auth_headers,
        )

        if response.status_code == 404:
            pytest.skip("User preferences endpoint not implemented yet")
        else:
            # Should either validate and reject, or sanitize the values
            assert response.status_code in [200, 422]


class TestUserCalibration(BaseAPITestCase):
    """Test user preference calibration system"""

    def test_get_calibration_request_success(self, client: TestClient, test_user_data):
        """Test getting calibration request."""
        self.authenticate_user(client, test_user_data)

        response = client.get(
            "/api/v1/users/me/calibration-request", headers=self.auth_headers
        )

        if response.status_code == 404:
            pytest.skip("Calibration request endpoint not implemented yet")
        else:
            data = self.assert_successful_response(response)

            # Should contain calibration questions or indicate no calibration needed
            if "questions" in data:
                assert isinstance(data["questions"], list)
                if data["questions"]:
                    question = data["questions"][0]
                    assert "question_id" in question
                    assert "question_text" in question
                    assert "options" in question or "question_type" in question
            elif "status" in data:
                assert data["status"] in [
                    "no_calibration_needed",
                    "calibration_available",
                ]

    def test_submit_calibration_response_success(
        self, client: TestClient, test_user_data
    ):
        """Test submitting calibration response."""
        self.authenticate_user(client, test_user_data)

        calibration_data = {
            "responses": [
                {
                    "question_id": "relationship_priority",
                    "answer": "professional_growth",
                    "confidence": 0.8,
                },
                {
                    "question_id": "networking_style",
                    "answer": "quality_over_quantity",
                    "confidence": 0.9,
                },
                {
                    "question_id": "communication_frequency",
                    "answer": "weekly",
                    "confidence": 0.7,
                },
            ]
        }

        response = client.post(
            "/api/v1/users/me/calibrate",
            json=calibration_data,
            headers=self.auth_headers,
        )

        if response.status_code == 404:
            pytest.skip("Calibration submission endpoint not implemented yet")
        else:
            data = self.assert_successful_response(response)

            # Should return updated learned factors or confirmation
            assert "status" in data or "learned_decision_factors" in data
            if "learned_decision_factors" in data:
                assert isinstance(data["learned_decision_factors"], dict)

    def test_submit_invalid_calibration_response(
        self, client: TestClient, test_user_data
    ):
        """Test submitting invalid calibration response."""
        self.authenticate_user(client, test_user_data)

        invalid_calibration = {
            "responses": [
                {"question_id": "nonexistent_question", "answer": "invalid_answer"}
            ]
        }

        response = client.post(
            "/api/v1/users/me/calibrate",
            json=invalid_calibration,
            headers=self.auth_headers,
        )

        if response.status_code == 404:
            pytest.skip("Calibration submission endpoint not implemented yet")
        else:
            # Should validate and reject invalid responses
            assert response.status_code in [400, 422]

    def test_calibration_unauthorized(self, client: TestClient):
        """Test calibration endpoints without authentication."""
        # Test calibration request
        response = client.get("/api/v1/users/me/calibration-request")
        self.assert_unauthorized_response(response)

        # Test calibration submission
        response = client.post("/api/v1/users/me/calibrate", json={})
        self.assert_unauthorized_response(response)


class TestUserSettings(BaseAPITestCase):
    """Test user settings and configuration"""

    def test_get_user_settings_success(self, client: TestClient, test_user_data):
        """Test getting user settings."""
        self.authenticate_user(client, test_user_data)

        response = client.get("/api/v1/users/me/settings", headers=self.auth_headers)

        if response.status_code == 404:
            pytest.skip("User settings endpoint not implemented yet")
        else:
            data = self.assert_successful_response(response)
            assert isinstance(data, dict)

    def test_update_privacy_settings(self, client: TestClient, test_user_data):
        """Test updating privacy settings."""
        self.authenticate_user(client, test_user_data)

        privacy_settings = {
            "profile_visibility": "public",
            "contact_info_sharing": "connections_only",
            "ai_analysis_consent": True,
            "data_export_format": "json",
        }

        response = client.put(
            "/api/v1/users/me/settings/privacy",
            json=privacy_settings,
            headers=self.auth_headers,
        )

        if response.status_code == 404:
            pytest.skip("Privacy settings endpoint not implemented yet")
        else:
            data = self.assert_successful_response(response)
            assert data["profile_visibility"] == "public"

    def test_deactivate_user_account(self, client: TestClient, test_user_data):
        """Test deactivating user account."""
        self.authenticate_user(client, test_user_data)

        deactivation_data = {
            "reason": "temporary_break",
            "feedback": "Taking a break from networking",
        }

        response = client.post(
            "/api/v1/users/me/deactivate",
            json=deactivation_data,
            headers=self.auth_headers,
        )

        if response.status_code == 404:
            pytest.skip("Account deactivation endpoint not implemented yet")
        else:
            data = self.assert_successful_response(response)
            assert data["status"] == "deactivated"

    def test_delete_user_account(self, client: TestClient, test_user_data):
        """Test deleting user account."""
        self.authenticate_user(client, test_user_data)

        deletion_data = {"confirmation": "DELETE", "reason": "no_longer_needed"}

        response = client.delete(
            "/api/v1/users/me", json=deletion_data, headers=self.auth_headers
        )

        if response.status_code == 404:
            pytest.skip("Account deletion endpoint not implemented yet")
        else:
            # Should either delete immediately or schedule for deletion
            assert response.status_code in [200, 202, 204]
