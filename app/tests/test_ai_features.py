"""
AI Features tests
Tests for Copilot, AI suggestions, network diagnosis, and referral path finding
"""

from unittest.mock import Mock, patch

import pytest
from fastapi.testclient import TestClient

from app.tests.base import BaseAITestCase, BaseIntegrationTestCase
from app.tests.test_config import MOCK_AI_RESPONSES
from app.tests.utils import MockAIService


class TestCopilotFeatures(BaseAITestCase):
    """Test Copilot conversational interface"""

    def test_copilot_converse_basic(self, client: TestClient):
        """Test basic copilot conversation."""
        self.authenticate_user(client)

        conversation_data = {
            "message": "Help me find a mentor in machine learning",
            "context": {
                "current_goals": ["learn_ml"],
                "user_background": "software_engineer",
            },
        }

        response = client.post(
            "/api/v1/copilot/converse",
            json=conversation_data,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Copilot converse endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        self.assert_ai_response_structure(data, "copilot")

        # Should provide a meaningful response
        assert len(data["response"]) > 0
        assert data["intent"] in MOCK_AI_RESPONSES["copilot_intents"]

    def test_copilot_converse_with_entities(self, client: TestClient):
        """Test copilot conversation with entity extraction."""
        self.authenticate_user(client)

        conversation_data = {
            "message": "Remind me to call John Doe tomorrow about the AI project",
            "context": {},
        }

        response = client.post(
            "/api/v1/copilot/converse",
            json=conversation_data,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Copilot converse endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        self.assert_ai_response_structure(data, "copilot")

        # Should extract entities like person name, time, and topic
        if "entities" in data:
            entities = data["entities"]
            entity_types = [entity.get("type") for entity in entities]
            expected_types = ["person", "time", "topic"]
            assert any(etype in entity_types for etype in expected_types)

    def test_copilot_converse_with_suggested_actions(self, client: TestClient):
        """Test copilot conversation with action suggestions."""
        self.authenticate_user(client)

        conversation_data = {
            "message": "I want to expand my network in the fintech industry",
            "context": {"current_network_size": 50, "industries": ["technology"]},
        }

        response = client.post(
            "/api/v1/copilot/converse",
            json=conversation_data,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Copilot converse endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        self.assert_ai_response_structure(data, "copilot")

        # Should suggest actionable steps
        if "suggested_actions" in data:
            actions = data["suggested_actions"]
            assert isinstance(actions, list)
            if actions:
                action = actions[0]
                assert "type" in action
                assert "title" in action or "description" in action

    @patch("app.services.ai_service.OpenAI")
    def test_copilot_with_mock_ai_service(self, mock_openai, client: TestClient):
        """Test copilot with mocked AI service."""
        self.authenticate_user(client)

        # Mock AI response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = (
            '{"intent": "create_task", "response": "I can help you create a task", "entities": []}'
        )
        mock_openai.return_value.chat.completions.create.return_value = mock_response

        conversation_data = {"message": "Create a task to research AI companies"}

        response = client.post(
            "/api/v1/copilot/converse",
            json=conversation_data,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Copilot converse endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert data["intent"] == "create_task"

    def test_copilot_conversation_history(self, client: TestClient):
        """Test maintaining conversation history."""
        self.authenticate_user(client)

        # First message
        conversation_data1 = {
            "message": "I'm looking for a job in AI",
            "conversation_id": "test_conversation_1",
        }

        response1 = client.post(
            "/api/v1/copilot/converse",
            json=conversation_data1,
            headers=self.auth_headers,
        )

        if response1.status_code == 501:
            pytest.skip("Copilot converse endpoint not fully implemented yet")

        data1 = self.assert_successful_response(response1)
        conversation_id = data1.get("conversation_id", "test_conversation_1")

        # Follow-up message
        conversation_data2 = {
            "message": "What companies should I target?",
            "conversation_id": conversation_id,
        }

        response2 = client.post(
            "/api/v1/copilot/converse",
            json=conversation_data2,
            headers=self.auth_headers,
        )

        data2 = self.assert_successful_response(response2)

        # Should maintain context from previous message
        assert data2["conversation_id"] == conversation_id

    def test_copilot_invalid_input(self, client: TestClient):
        """Test copilot with invalid input."""
        self.authenticate_user(client)

        # Empty message
        invalid_data = {"message": ""}

        response = client.post(
            "/api/v1/copilot/converse", json=invalid_data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Copilot converse endpoint not fully implemented yet")

        self.assert_validation_error_response(response)


class TestAISuggestions(BaseAITestCase):
    """Test AI suggestions functionality"""

    def test_get_ai_suggestions_basic(self, client: TestClient):
        """Test getting basic AI suggestions."""
        self.authenticate_user(client)

        response = client.get("/api/v1/copilot/suggestions", headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("AI suggestions endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        self.assert_ai_response_structure(data, "suggestions")

        # Should return list of suggestions
        if data:  # If not empty
            suggestion = data[0]
            assert suggestion["type"] in MOCK_AI_RESPONSES["suggestion_types"]

    def test_get_ai_suggestions_with_filters(self, client: TestClient):
        """Test getting AI suggestions with filters."""
        self.authenticate_user(client)

        # Filter by suggestion type
        response = client.get(
            "/api/v1/copilot/suggestions?type=reconnect", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("AI suggestions endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # If filtering is implemented, all suggestions should be of requested type
        if data and "type" in data[0]:
            for suggestion in data:
                assert suggestion["type"] == "reconnect"

    def test_get_ai_suggestions_with_priority(self, client: TestClient):
        """Test getting AI suggestions ordered by priority."""
        self.authenticate_user(client)

        response = client.get(
            "/api/v1/copilot/suggestions?priority=high", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("AI suggestions endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should return high priority suggestions
        if data and "priority" in data[0]:
            for suggestion in data:
                assert suggestion["priority"] in ["high", "medium", "low"]

    def test_dismiss_ai_suggestion(self, client: TestClient):
        """Test dismissing an AI suggestion."""
        self.authenticate_user(client)

        # Get suggestions first
        suggestions_response = client.get(
            "/api/v1/copilot/suggestions", headers=self.auth_headers
        )

        if suggestions_response.status_code == 501:
            pytest.skip("AI suggestions endpoint not fully implemented yet")

        suggestions = self.assert_successful_response(suggestions_response)

        if not suggestions:
            pytest.skip("No suggestions available to dismiss")

        suggestion_id = suggestions[0]["id"]

        # Dismiss suggestion
        dismiss_data = {
            "reason": "not_relevant",
            "feedback": "This suggestion doesn't apply to my current situation",
        }

        response = client.post(
            f"/api/v1/copilot/suggestions/{suggestion_id}/dismiss",
            json=dismiss_data,
            headers=self.auth_headers,
        )

        if response.status_code == 404:
            pytest.skip("Suggestion dismissal endpoint not implemented yet")

        self.assert_successful_response(response, 200)

    def test_accept_ai_suggestion(self, client: TestClient):
        """Test accepting and acting on an AI suggestion."""
        self.authenticate_user(client)

        # Get suggestions first
        suggestions_response = client.get(
            "/api/v1/copilot/suggestions", headers=self.auth_headers
        )

        if suggestions_response.status_code == 501:
            pytest.skip("AI suggestions endpoint not fully implemented yet")

        suggestions = self.assert_successful_response(suggestions_response)

        if not suggestions:
            pytest.skip("No suggestions available to accept")

        suggestion_id = suggestions[0]["id"]

        # Accept suggestion
        accept_data = {
            "action": "create_task",
            "parameters": {
                "title": "Follow up on suggestion",
                "due_date": "2024-07-01",
            },
        }

        response = client.post(
            f"/api/v1/copilot/suggestions/{suggestion_id}/accept",
            json=accept_data,
            headers=self.auth_headers,
        )

        if response.status_code == 404:
            pytest.skip("Suggestion acceptance endpoint not implemented yet")

        data = self.assert_successful_response(response, 201)

        # Should create the requested action (e.g., task)
        assert "created" in data or "task_id" in data or "action_result" in data


class TestNetworkDiagnosis(BaseAITestCase):
    """Test network health diagnosis functionality"""

    def test_get_network_diagnosis_basic(self, client: TestClient):
        """Test basic network diagnosis."""
        self.authenticate_user(client)

        response = client.get("/api/v1/ai/network/diagnosis", headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Network diagnosis endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        self.assert_ai_response_structure(data, "diagnosis")

        # Should provide network health metrics
        assert isinstance(data["overall_health"], (int, float))
        assert 0 <= data["overall_health"] <= 1
        assert isinstance(data["network_size"], int)
        assert data["network_size"] >= 0

    def test_network_diagnosis_with_insights(self, client: TestClient):
        """Test network diagnosis with detailed insights."""
        self.authenticate_user(client)

        response = client.get(
            "/api/v1/ai/network/diagnosis?include_insights=true",
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Network diagnosis endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should include insights and recommendations
        if "insights" in data:
            insights = data["insights"]
            assert isinstance(insights, list)
            if insights:
                insight = insights[0]
                assert "type" in insight
                assert insight["type"] in [
                    "strength",
                    "weakness",
                    "opportunity",
                    "threat",
                ]
                assert "message" in insight

        if "recommendations" in data:
            recommendations = data["recommendations"]
            assert isinstance(recommendations, list)

    def test_network_diagnosis_by_category(self, client: TestClient):
        """Test network diagnosis filtered by category."""
        self.authenticate_user(client)

        response = client.get(
            "/api/v1/ai/network/diagnosis?category=professional",
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Network diagnosis endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should focus on professional network aspects
        if "category_focus" in data:
            assert data["category_focus"] == "professional"

    @patch("app.services.ai_service.analyze_network")
    def test_network_diagnosis_with_mock_analysis(
        self, mock_analyze, client: TestClient
    ):
        """Test network diagnosis with mocked analysis."""
        self.authenticate_user(client)

        # Mock network analysis
        mock_analyze.return_value = MockAIService.mock_network_diagnosis()

        response = client.get("/api/v1/ai/network/diagnosis", headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Network diagnosis endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert data["overall_health"] == 0.75
        assert data["network_size"] == 150


class TestReferralPathFinding(BaseAITestCase):
    """Test referral path finding functionality"""

    def test_find_referral_path_basic(self, client: TestClient):
        """Test basic referral path finding."""
        self.authenticate_user(client)

        path_request = {
            "target_person_name": "Elon Musk",
            "target_criteria": {
                "industry": "technology",
                "role": "CEO",
                "company": "Tesla",
            },
            "max_degrees": 3,
        }

        response = client.post(
            "/api/v1/ai/network/referral-path",
            json=path_request,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Referral path endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should return possible paths
        assert "paths" in data
        assert isinstance(data["paths"], list)

        if data["paths"]:
            path = data["paths"][0]
            assert "steps" in path
            assert "strength" in path or "estimated_success_rate" in path

            # Each step should have person information
            if path["steps"]:
                step = path["steps"][0]
                assert "person_id" in step or "person_name" in step

    def test_find_referral_path_with_specific_target(self, client: TestClient):
        """Test finding referral path to specific person in network."""
        self.authenticate_user(client)

        # First create a test network
        network = self.create_test_network(client, num_persons=3)

        if not network["persons"]:
            pytest.skip("Test network creation required but failed")

        target_person_id = network["persons"][-1]["person_id"]

        path_request = {
            "target_person_id": target_person_id,
            "purpose": "job_referral",
            "max_degrees": 2,
        }

        response = client.post(
            "/api/v1/ai/network/referral-path",
            json=path_request,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Referral path endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should find path to the target person
        if data["paths"]:
            path = data["paths"][0]
            final_step = path["steps"][-1]
            assert final_step["person_id"] == target_person_id

    def test_find_referral_path_no_path_found(self, client: TestClient):
        """Test referral path finding when no path exists."""
        self.authenticate_user(client)

        path_request = {
            "target_person_name": "Completely Unknown Person",
            "target_criteria": {"industry": "nonexistent_industry"},
        }

        response = client.post(
            "/api/v1/ai/network/referral-path",
            json=path_request,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Referral path endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        # Should indicate no paths found
        assert data["paths"] == [] or "no_path_found" in data

        # Should provide alternative strategies
        if "alternative_strategies" in data:
            assert isinstance(data["alternative_strategies"], list)

    def test_referral_path_validation_errors(self, client: TestClient):
        """Test referral path finding with invalid input."""
        self.authenticate_user(client)

        # Missing required fields
        invalid_request = {"max_degrees": 5}  # Missing target information

        response = client.post(
            "/api/v1/ai/network/referral-path",
            json=invalid_request,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Referral path endpoint not fully implemented yet")

        self.assert_validation_error_response(response)


class TestAIErrorHandling(BaseAITestCase):
    """Test AI feature error handling and edge cases"""

    def test_ai_service_unavailable(self, client: TestClient):
        """Test behavior when AI service is unavailable."""
        self.authenticate_user(client)

        with patch("app.services.ai_service.OpenAI") as mock_openai:
            # Simulate AI service failure
            mock_openai.side_effect = Exception("AI service unavailable")

            response = client.post(
                "/api/v1/copilot/converse",
                json={"message": "Hello"},
                headers=self.auth_headers,
            )

            if response.status_code == 501:
                pytest.skip("Copilot endpoint not implemented yet")

            # Should handle gracefully
            assert response.status_code in [200, 503]

            if response.status_code == 200:
                data = response.json()
                # Should provide fallback response
                assert "response" in data
                assert "error" in data or "fallback" in data

    def test_ai_unauthorized_access(self, client: TestClient):
        """Test AI endpoints without authentication."""
        # Test copilot without auth
        response = client.post("/api/v1/copilot/converse", json={"message": "Hello"})
        self.assert_unauthorized_response(response)

        # Test suggestions without auth
        response = client.get("/api/v1/copilot/suggestions")
        self.assert_unauthorized_response(response)

        # Test network diagnosis without auth
        response = client.get("/api/v1/ai/network/diagnosis")
        self.assert_unauthorized_response(response)

        # Test referral path without auth
        response = client.post(
            "/api/v1/ai/network/referral-path", json={"target_person_name": "Test"}
        )
        self.assert_unauthorized_response(response)
