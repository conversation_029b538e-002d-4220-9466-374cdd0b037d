"""
Async task processing tests
Tests for background task processing, job queues, and task monitoring
"""

import time
from unittest.mock import Mock, patch

import pytest
from fastapi.testclient import TestClient

from app.tests.base import BaseAPITestCase
from app.tests.utils import TestDataFactory


class TestAsyncTaskProcessing(BaseAPITestCase):
    """Test async task processing functionality"""

    def setup_method(self):
        super().setup_method()
        self.tasks_endpoint = "/api/v1/tasks/async"
        self.task_status_endpoint = "/api/v1/tasks/async/status"

    def test_create_async_task(self, client: TestClient):
        """Test creating an async task."""
        self.authenticate_user(client)

        task_request = {
            "task_type": "data_export",
            "parameters": {"format": "json", "include": ["persons", "organizations"]},
            "priority": "normal",
        }

        response = client.post(
            self.tasks_endpoint, json=task_request, headers=self.auth_headers
        )

        if response.status_code == 404:
            pytest.skip("Async task endpoint not implemented yet")

        data = self.assert_successful_response(response, 201)

        # Should return task information
        assert "task_id" in data
        assert "status" in data
        assert data["status"] in ["pending", "queued", "processing"]

        # Should include task metadata
        if "created_at" in data:
            self.assertions.assert_valid_datetime(data["created_at"])

        if "estimated_duration" in data:
            assert isinstance(data["estimated_duration"], (int, float))

    def test_get_task_status(self, client: TestClient):
        """Test getting task status."""
        self.authenticate_user(client)

        # Create a task first
        task_request = {
            "task_type": "network_analysis",
            "parameters": {"analysis_type": "centrality", "include_metrics": True},
        }

        create_response = client.post(
            self.tasks_endpoint, json=task_request, headers=self.auth_headers
        )

        if create_response.status_code == 404:
            pytest.skip("Async task endpoint not implemented yet")

        task_data = self.assert_successful_response(create_response, 201)
        task_id = task_data["task_id"]

        # Get task status
        status_response = client.get(
            f"{self.task_status_endpoint}/{task_id}", headers=self.auth_headers
        )

        status_data = self.assert_successful_response(status_response)

        # Should contain comprehensive status information
        assert "task_id" in status_data
        assert "status" in status_data
        assert status_data["status"] in [
            "pending",
            "queued",
            "processing",
            "completed",
            "failed",
            "cancelled",
        ]

        # Should include progress information
        if "progress" in status_data:
            progress = status_data["progress"]
            assert isinstance(progress, dict)
            if "percentage" in progress:
                assert 0 <= progress["percentage"] <= 100

        # Should include timing information
        timing_fields = ["created_at", "started_at", "updated_at"]
        for field in timing_fields:
            if field in status_data:
                self.assertions.assert_valid_datetime(status_data[field])

    def test_list_user_tasks(self, client: TestClient):
        """Test listing user's async tasks."""
        self.authenticate_user(client)

        # Create multiple tasks
        task_types = ["data_export", "network_analysis", "ai_suggestions_refresh"]
        created_tasks = []

        for task_type in task_types:
            task_request = {"task_type": task_type, "parameters": {"test": True}}

            response = client.post(
                self.tasks_endpoint, json=task_request, headers=self.auth_headers
            )

            if response.status_code == 404:
                pytest.skip("Async task endpoint not implemented yet")

            task_data = self.assert_successful_response(response, 201)
            created_tasks.append(task_data)

        # List all tasks
        list_response = client.get(self.tasks_endpoint, headers=self.auth_headers)

        if list_response.status_code == 404:
            pytest.skip("Task listing endpoint not implemented yet")

        tasks_list = self.assert_successful_response(list_response)

        # Should return list of tasks
        assert isinstance(tasks_list, list)
        assert len(tasks_list) >= len(created_tasks)

        # Verify task structure
        if tasks_list:
            task = tasks_list[0]
            required_fields = ["task_id", "task_type", "status"]
            for field in required_fields:
                assert field in task

    def test_filter_tasks_by_status(self, client: TestClient):
        """Test filtering tasks by status."""
        self.authenticate_user(client)

        # Create a task
        task_request = {"task_type": "data_import", "parameters": {"file_type": "csv"}}

        create_response = client.post(
            self.tasks_endpoint, json=task_request, headers=self.auth_headers
        )

        if create_response.status_code == 404:
            pytest.skip("Async task endpoint not implemented yet")

        # Filter by status
        filter_response = client.get(
            f"{self.tasks_endpoint}?status=pending", headers=self.auth_headers
        )

        if filter_response.status_code == 404:
            pytest.skip("Task filtering not implemented yet")

        filtered_tasks = self.assert_successful_response(filter_response)

        # Should only return tasks with specified status
        for task in filtered_tasks:
            if "status" in task:
                assert task["status"] == "pending"

    def test_filter_tasks_by_type(self, client: TestClient):
        """Test filtering tasks by type."""
        self.authenticate_user(client)

        # Create tasks of different types
        task_types = ["data_export", "network_analysis"]

        for task_type in task_types:
            task_request = {"task_type": task_type, "parameters": {}}

            response = client.post(
                self.tasks_endpoint, json=task_request, headers=self.auth_headers
            )

            if response.status_code == 404:
                pytest.skip("Async task endpoint not implemented yet")

        # Filter by type
        filter_response = client.get(
            f"{self.tasks_endpoint}?task_type=data_export", headers=self.auth_headers
        )

        if filter_response.status_code == 404:
            pytest.skip("Task filtering not implemented yet")

        filtered_tasks = self.assert_successful_response(filter_response)

        # Should only return tasks of specified type
        for task in filtered_tasks:
            if "task_type" in task:
                assert task["task_type"] == "data_export"

    def test_cancel_task(self, client: TestClient):
        """Test cancelling a running task."""
        self.authenticate_user(client)

        # Create a long-running task
        task_request = {
            "task_type": "large_data_export",
            "parameters": {"format": "json", "include_all": True},
        }

        create_response = client.post(
            self.tasks_endpoint, json=task_request, headers=self.auth_headers
        )

        if create_response.status_code == 404:
            pytest.skip("Async task endpoint not implemented yet")

        task_data = self.assert_successful_response(create_response, 201)
        task_id = task_data["task_id"]

        # Cancel the task
        cancel_response = client.post(
            f"{self.tasks_endpoint}/{task_id}/cancel", headers=self.auth_headers
        )

        if cancel_response.status_code == 404:
            pytest.skip("Task cancellation endpoint not implemented yet")

        cancel_data = self.assert_successful_response(cancel_response)

        # Should confirm cancellation
        assert "status" in cancel_data
        assert cancel_data["status"] in ["cancelled", "cancelling"]

        # Verify task status is updated
        status_response = client.get(
            f"{self.task_status_endpoint}/{task_id}", headers=self.auth_headers
        )

        if status_response.status_code == 200:
            status_data = self.assert_successful_response(status_response)
            assert status_data["status"] in ["cancelled", "cancelling"]

    def test_retry_failed_task(self, client: TestClient):
        """Test retrying a failed task."""
        self.authenticate_user(client)

        # Mock a failed task
        with patch("app.services.task_service.get_task_status") as mock_status:
            mock_status.return_value = {
                "task_id": "failed-task-id",
                "status": "failed",
                "error": "Network timeout",
                "retry_count": 1,
                "max_retries": 3,
            }

            # Retry the task
            retry_response = client.post(
                f"{self.tasks_endpoint}/failed-task-id/retry", headers=self.auth_headers
            )

            if retry_response.status_code == 404:
                pytest.skip("Task retry endpoint not implemented yet")

            retry_data = self.assert_successful_response(retry_response, 201)

            # Should create new task or reset existing task
            assert "task_id" in retry_data
            assert "status" in retry_data
            assert retry_data["status"] in ["pending", "queued"]

    def test_task_progress_updates(self, client: TestClient):
        """Test task progress updates."""
        self.authenticate_user(client)

        # Create a task that reports progress
        task_request = {
            "task_type": "batch_processing",
            "parameters": {"total_items": 100, "batch_size": 10},
        }

        create_response = client.post(
            self.tasks_endpoint, json=task_request, headers=self.auth_headers
        )

        if create_response.status_code == 404:
            pytest.skip("Async task endpoint not implemented yet")

        task_data = self.assert_successful_response(create_response, 201)
        task_id = task_data["task_id"]

        # Check progress multiple times (simulating real-time updates)
        for _ in range(3):
            time.sleep(0.1)  # Small delay

            status_response = client.get(
                f"{self.task_status_endpoint}/{task_id}", headers=self.auth_headers
            )

            if status_response.status_code == 200:
                status_data = self.assert_successful_response(status_response)

                # Should show progress information
                if "progress" in status_data:
                    progress = status_data["progress"]

                    if "current_item" in progress and "total_items" in progress:
                        assert progress["current_item"] <= progress["total_items"]

                    if "percentage" in progress:
                        assert 0 <= progress["percentage"] <= 100

    def test_task_result_retrieval(self, client: TestClient):
        """Test retrieving task results."""
        self.authenticate_user(client)

        # Mock a completed task with results
        with patch("app.services.task_service.get_task_status") as mock_status:
            mock_status.return_value = {
                "task_id": "completed-task-id",
                "status": "completed",
                "result": {
                    "processed_items": 50,
                    "success_count": 48,
                    "error_count": 2,
                    "output_file": "/downloads/result.json",
                },
                "completed_at": "2024-06-22T10:00:00Z",
            }

            # Get task results
            result_response = client.get(
                f"{self.task_status_endpoint}/completed-task-id",
                headers=self.auth_headers,
            )

            if result_response.status_code == 404:
                pytest.skip("Task status endpoint not implemented yet")

            result_data = self.assert_successful_response(result_response)

            # Should contain result information
            assert result_data["status"] == "completed"
            if "result" in result_data:
                result = result_data["result"]
                assert isinstance(result, dict)

                # Should contain processing statistics
                stats_fields = ["processed_items", "success_count", "error_count"]
                for field in stats_fields:
                    if field in result:
                        assert isinstance(result[field], int)

    def test_task_error_handling(self, client: TestClient):
        """Test task error handling and reporting."""
        self.authenticate_user(client)

        # Create a task that will fail
        task_request = {
            "task_type": "invalid_operation",
            "parameters": {"invalid_param": "value"},
        }

        response = client.post(
            self.tasks_endpoint, json=task_request, headers=self.auth_headers
        )

        if response.status_code == 404:
            pytest.skip("Async task endpoint not implemented yet")

        # Should either reject invalid task type or create task that will fail
        if response.status_code == 201:
            task_data = self.assert_successful_response(response, 201)
            task_id = task_data["task_id"]

            # Check if task fails gracefully
            status_response = client.get(
                f"{self.task_status_endpoint}/{task_id}", headers=self.auth_headers
            )

            if status_response.status_code == 200:
                status_data = self.assert_successful_response(status_response)

                # If task failed, should contain error information
                if status_data["status"] == "failed":
                    assert "error" in status_data or "error_message" in status_data
        else:
            # Should reject invalid task type
            self.assert_validation_error_response(response)

    def test_task_queue_priority(self, client: TestClient):
        """Test task queue priority handling."""
        self.authenticate_user(client)

        # Create tasks with different priorities
        priorities = ["low", "normal", "high", "urgent"]

        for priority in priorities:
            task_request = {
                "task_type": "test_task",
                "parameters": {},
                "priority": priority,
            }

            response = client.post(
                self.tasks_endpoint, json=task_request, headers=self.auth_headers
            )

            if response.status_code == 404:
                pytest.skip("Async task endpoint not implemented yet")

            if response.status_code == 201:
                task_data = self.assert_successful_response(response, 201)

                # Should accept priority setting
                if "priority" in task_data:
                    assert task_data["priority"] == priority

    def test_task_unauthorized_access(self, client: TestClient):
        """Test task endpoints without authentication."""
        # Test creating task without auth
        task_request = {"task_type": "test_task", "parameters": {}}

        response = client.post(self.tasks_endpoint, json=task_request)
        self.assert_unauthorized_response(response)

        # Test getting task status without auth
        response = client.get(f"{self.task_status_endpoint}/test-task-id")
        self.assert_unauthorized_response(response)

        # Test listing tasks without auth
        response = client.get(self.tasks_endpoint)
        self.assert_unauthorized_response(response)

    def test_task_cross_user_isolation(self, client: TestClient, test_user_data):
        """Test that users can only access their own tasks."""
        # Create first user and task
        auth1 = self.authenticate_user(client, test_user_data)

        task_request = {"task_type": "user1_task", "parameters": {}}

        create_response = client.post(
            self.tasks_endpoint, json=task_request, headers=self.auth_headers
        )

        if create_response.status_code == 404:
            pytest.skip("Async task endpoint not implemented yet")

        task_data = self.assert_successful_response(create_response, 201)
        task_id = task_data["task_id"]

        # Create second user
        user2_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "first_name": "User2",
            "last_name": "Test",
        }
        auth2 = self.authenticate_user(client, user2_data)

        # User2 should not be able to access User1's task
        response = client.get(
            f"{self.task_status_endpoint}/{task_id}", headers=auth2["headers"]
        )
        self.assert_not_found_response(response)

        # User2 should not see User1's tasks in list
        list_response = client.get(self.tasks_endpoint, headers=auth2["headers"])
        if list_response.status_code == 200:
            tasks_list = self.assert_successful_response(list_response)

            # Should not contain User1's task
            user1_tasks = [
                task for task in tasks_list if task.get("task_id") == task_id
            ]
            assert len(user1_tasks) == 0
