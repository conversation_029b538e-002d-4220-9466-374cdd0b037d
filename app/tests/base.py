"""
Base test classes for different types of tests
"""

from typing import Any, Dict, Optional

import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.ext.asyncio import AsyncSession

from app.tests.utils import TestAssertions, TestAuthHelper, TestDataFactory
from app.api.deps import get_db


class BaseTestCase:
    """Base test case with common utilities"""

    def setup_method(self):
        """Setup method called before each test"""
        self.factory = TestDataFactory()
        self.auth_helper = TestAuthHelper()
        self.assertions = TestAssertions()


class BaseAPITestCase(BaseTestCase):
    """Base test case for API endpoint testing"""

    def setup_method(self):
        super().setup_method()
        self.authenticated_user = None
        self.auth_headers = None

    def authenticate_user(
        self, client: TestClient, user_data: Optional[Dict[str, Any]] = None
    ):
        """Authenticate a user and store auth info"""
        if user_data is None:
            user_data = self.factory.create_user_data()

        # Create user directly in database for testing
        from app.models.user import User
        from app.core.security import get_password_hash

        # Get the database session from the client's dependency override
        db_gen = client.app.dependency_overrides[get_db]()
        db = next(db_gen)

        # Create user in database
        hashed_password = get_password_hash(user_data["password"])
        user = User(
            email=user_data["email"],
            hashed_password=hashed_password,
            first_name=user_data["first_name"],
            last_name=user_data["last_name"],
            is_active=True,
            is_verified=True
        )
        db.add(user)
        db.commit()
        db.refresh(user)

        # Create mock token for authentication
        token = "test_access_token"
        self.authenticated_user = {
            "user_id": str(user.user_id),
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name
        }
        self.auth_headers = {"Authorization": f"Bearer {token}"}

        return {
            "user": self.authenticated_user,
            "token": token,
            "headers": self.auth_headers
        }

    def assert_successful_response(self, response, expected_status: int = 200):
        """Assert that response is successful"""
        assert (
            response.status_code == expected_status
        ), f"Expected status {expected_status}, got {response.status_code}: {response.text}"
        return response.json()

    def assert_error_response(
        self, response, expected_status: int, expected_message: Optional[str] = None
    ):
        """Assert that response is an error"""
        assert (
            response.status_code == expected_status
        ), f"Expected status {expected_status}, got {response.status_code}"

        data = response.json()
        if expected_message:
            self.assertions.assert_error_response(
                data, expected_message_contains=expected_message
            )
        return data

    def assert_unauthorized_response(self, response):
        """Assert that response is unauthorized"""
        return self.assert_error_response(response, 401, "unauthorized")

    def assert_forbidden_response(self, response):
        """Assert that response is forbidden"""
        return self.assert_error_response(response, 403, "forbidden")

    def assert_not_found_response(self, response):
        """Assert that response is not found"""
        return self.assert_error_response(response, 404, "not found")

    def assert_validation_error_response(self, response):
        """Assert that response is a validation error"""
        return self.assert_error_response(response, 422, "validation")


class BaseCRUDTestCase(BaseAPITestCase):
    """Base test case for CRUD operations"""

    # Override these in subclasses
    endpoint_base = ""  # e.g., "/api/v1/persons"
    create_data_factory = None  # e.g., TestDataFactory.create_person_data
    required_response_fields = []  # e.g., ["person_id", "first_name", "last_name"]
    optional_response_fields = []  # e.g., ["created_at", "updated_at"]

    def _create_test_data(self):
        """Helper method to create test data using the factory"""
        # Call the static method directly to avoid passing self as first argument
        return self.__class__.create_data_factory()

    def test_create_success(self, client: TestClient):
        """Test successful creation"""
        if not self.create_data_factory:
            pytest.skip("create_data_factory not defined")

        self.authenticate_user(client)
        test_data = self._create_test_data()

        response = client.post(
            self.endpoint_base, json=test_data, headers=self.auth_headers
        )
        data = self.assert_successful_response(response, 201)

        self.assertions.assert_response_structure(
            data, self.required_response_fields, self.optional_response_fields
        )
        return data

    def test_create_unauthorized(self, client: TestClient):
        """Test creation without authentication"""
        if not self.create_data_factory:
            pytest.skip("create_data_factory not defined")

        test_data = self._create_test_data()
        response = client.post(self.endpoint_base, json=test_data)
        self.assert_unauthorized_response(response)

    def test_get_list_success(self, client: TestClient):
        """Test successful list retrieval"""
        self.authenticate_user(client)

        response = client.get(self.endpoint_base, headers=self.auth_headers)
        data = self.assert_successful_response(response)

        assert isinstance(data, list), "Response should be a list"
        return data

    def test_get_list_unauthorized(self, client: TestClient):
        """Test list retrieval without authentication"""
        response = client.get(self.endpoint_base)
        self.assert_unauthorized_response(response)

    def test_get_by_id_success(self, client: TestClient):
        """Test successful retrieval by ID"""
        if not self.create_data_factory:
            pytest.skip("create_data_factory not defined")

        self.authenticate_user(client)

        # Create an item first
        test_data = self.create_data_factory()
        create_response = client.post(
            self.endpoint_base, json=test_data, headers=self.auth_headers
        )
        created_item = self.assert_successful_response(create_response, 201)

        # Get the item by ID
        item_id = created_item[
            self.required_response_fields[0]
        ]  # Assume first field is ID
        response = client.get(
            f"{self.endpoint_base}/{item_id}", headers=self.auth_headers
        )
        data = self.assert_successful_response(response)

        self.assertions.assert_response_structure(
            data, self.required_response_fields, self.optional_response_fields
        )
        return data

    def test_get_by_id_not_found(self, client: TestClient):
        """Test retrieval of non-existent item"""
        self.authenticate_user(client)

        fake_id = "00000000-0000-0000-0000-000000000000"
        response = client.get(
            f"{self.endpoint_base}/{fake_id}", headers=self.auth_headers
        )
        self.assert_not_found_response(response)

    def test_update_success(self, client: TestClient):
        """Test successful update"""
        if not self.create_data_factory:
            pytest.skip("create_data_factory not defined")

        self.authenticate_user(client)

        # Create an item first
        test_data = self.create_data_factory()
        create_response = client.post(
            self.endpoint_base, json=test_data, headers=self.auth_headers
        )
        created_item = self.assert_successful_response(create_response, 201)

        # Update the item
        item_id = created_item[self.required_response_fields[0]]
        update_data = {"description": "Updated description"}
        response = client.put(
            f"{self.endpoint_base}/{item_id}",
            json=update_data,
            headers=self.auth_headers,
        )
        data = self.assert_successful_response(response)

        return data

    def test_delete_success(self, client: TestClient):
        """Test successful deletion"""
        if not self.create_data_factory:
            pytest.skip("create_data_factory not defined")

        self.authenticate_user(client)

        # Create an item first
        test_data = self.create_data_factory()
        create_response = client.post(
            self.endpoint_base, json=test_data, headers=self.auth_headers
        )
        created_item = self.assert_successful_response(create_response, 201)

        # Delete the item
        item_id = created_item[self.required_response_fields[0]]
        response = client.delete(
            f"{self.endpoint_base}/{item_id}", headers=self.auth_headers
        )
        self.assert_successful_response(response, 204)

        # Verify item is deleted
        get_response = client.get(
            f"{self.endpoint_base}/{item_id}", headers=self.auth_headers
        )
        self.assert_not_found_response(get_response)


class BaseAITestCase(BaseAPITestCase):
    """Base test case for AI-related functionality"""

    def setup_method(self):
        super().setup_method()
        # Mock AI services will be set up in individual test methods
        pass

    def assert_ai_response_structure(self, data: Dict[str, Any], response_type: str):
        """Assert AI response has expected structure"""
        if response_type == "copilot":
            required_fields = ["response", "intent"]
            optional_fields = ["entities", "suggested_actions", "confidence"]
        elif response_type == "suggestions":
            assert isinstance(data, list), "AI suggestions should be a list"
            if data:  # If not empty
                required_fields = ["id", "type", "title", "description"]
                optional_fields = ["priority", "suggested_action"]
                self.assertions.assert_response_structure(
                    data[0], required_fields, optional_fields
                )
            return
        elif response_type == "diagnosis":
            required_fields = ["overall_health", "network_size", "insights"]
            optional_fields = [
                "recommendations",
                "active_connections",
                "dormant_connections",
            ]
        else:
            raise ValueError(f"Unknown AI response type: {response_type}")

        self.assertions.assert_response_structure(
            data, required_fields, optional_fields
        )


class BaseIntegrationTestCase(BaseAPITestCase):
    """Base test case for integration tests"""

    def setup_method(self):
        super().setup_method()
        # Integration-specific setup
        pass

    def create_test_network(
        self, client: TestClient, num_persons: int = 5, num_organizations: int = 2
    ):
        """Create a test network of persons and organizations"""
        self.authenticate_user(client)

        # Create organizations
        organizations = []
        for i in range(num_organizations):
            org_data = self.factory.create_organization_data(
                name=f"Test Org {i+1}",
                industry="Technology" if i % 2 == 0 else "Finance",
            )
            response = client.post(
                "/api/v1/organizations", json=org_data, headers=self.auth_headers
            )
            if response.status_code == 201:
                organizations.append(response.json())

        # Create persons
        persons = []
        for i in range(num_persons):
            person_data = self.factory.create_person_data(
                first_name=f"Person{i+1}",
                last_name="Test",
                email=f"person{i+1}@test.com",
            )
            response = client.post(
                "/api/v1/persons", json=person_data, headers=self.auth_headers
            )
            if response.status_code == 201:
                persons.append(response.json())

        return {
            "persons": persons,
            "organizations": organizations,
            "user": self.authenticated_user,
        }
