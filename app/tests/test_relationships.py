"""
Comprehensive tests for relationship management system with archetype templates
"""

import uuid
import pytest
from datetime import datetime
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.models.person import Person
from app.models.relationship import Knows
from app.models.user import User
from app.services.relationship_service import RelationshipService, RelationshipArchetype


class TestRelationshipService:
    """Test the RelationshipService functionality"""
    
    def setup_method(self):
        """Setup test data for each test"""
        self.test_user_id = uuid.uuid4()
        self.test_person1_id = uuid.uuid4()
        self.test_person2_id = uuid.uuid4()
    
    def test_create_relationship_with_archetype(self, db: Session):
        """Test creating relationship with archetype template"""
        # Create test persons
        person1 = Person(
            person_id=self.test_person1_id,
            user_id=self.test_user_id,
            first_name="<PERSON>",
            last_name="Doe",
            is_user=False
        )
        person2 = Person(
            person_id=self.test_person2_id,
            user_id=self.test_user_id,
            first_name="<PERSON>",
            last_name="<PERSON>",
            is_user=False
        )
        db.add_all([person1, person2])
        db.commit()
        
        # Create relationship service
        service = RelationshipService(db)
        
        # Create colleague relationship
        relationship = service.create_relationship(
            from_person_id=self.test_person1_id,
            to_person_id=self.test_person2_id,
            user_id=self.test_user_id,
            archetype=RelationshipArchetype.COLLEAGUE.value,
            relationship_foundation={
                "how_met": "At company meeting",
                "foundation_type": "work"
            }
        )
        
        assert relationship is not None
        assert relationship.archetype == RelationshipArchetype.COLLEAGUE.value
        assert relationship.relationship_foundation["how_met"] == "At company meeting"
        
        # Check default dimensions were applied
        dimensions = relationship.dimensions
        assert dimensions["professional_collaboration"] == 80  # Colleague default
        assert dimensions["emotional_intimacy"] == 30  # Colleague default
        
        # Check overall score was calculated
        assert relationship.overall_score > 0
        
        # Check history was created
        history = relationship.relationship_depth.get("history", [])
        assert len(history) >= 1
        assert "Relationship created" in history[0]["event"]
    
    def test_update_relationship_dimensions(self, db: Session):
        """Test updating relationship dimensions"""
        # Setup similar to above test...
        person1 = Person(
            person_id=self.test_person1_id,
            user_id=self.test_user_id,
            first_name="John",
            last_name="Doe",
            is_user=False
        )
        person2 = Person(
            person_id=self.test_person2_id,
            user_id=self.test_user_id,
            first_name="Jane",
            last_name="Smith",
            is_user=False
        )
        db.add_all([person1, person2])
        db.commit()
        
        service = RelationshipService(db)
        
        # Create initial relationship
        relationship = service.create_relationship(
            from_person_id=self.test_person1_id,
            to_person_id=self.test_person2_id,
            user_id=self.test_user_id,
            archetype=RelationshipArchetype.COLLEAGUE.value
        )
        
        initial_score = relationship.overall_score
        
        # Update dimensions
        updated_relationship = service.update_relationship(
            from_person_id=self.test_person1_id,
            to_person_id=self.test_person2_id,
            user_id=self.test_user_id,
            updates={
                "dimensions": {
                    "trust_level": 90,
                    "communication_frequency": 85
                }
            }
        )
        
        assert updated_relationship is not None
        assert updated_relationship.dimensions["trust_level"] == 90
        assert updated_relationship.dimensions["communication_frequency"] == 85
        
        # Check overall score was recalculated
        assert updated_relationship.overall_score != initial_score
        
        # Check history was updated
        history = updated_relationship.relationship_depth.get("history", [])
        assert any("Dimensions updated" in entry["event"] for entry in history)
    
    def test_relationship_validation(self, db: Session):
        """Test relationship data validation"""
        service = RelationshipService(db)
        
        # Test invalid archetype
        errors = service.validate_relationship_data({
            "archetype": "invalid_archetype",
            "dimensions": {"trust_level": 50}
        })
        assert len(errors) > 0
        assert "Invalid archetype" in errors[0]
        
        # Test invalid dimension values
        errors = service.validate_relationship_data({
            "archetype": RelationshipArchetype.COLLEAGUE.value,
            "dimensions": {"trust_level": 150}  # Over 100
        })
        assert len(errors) > 0
        assert "must be between 0 and 100" in errors[0]
        
        # Test valid data
        errors = service.validate_relationship_data({
            "archetype": RelationshipArchetype.COLLEAGUE.value,
            "dimensions": {"trust_level": 85}
        })
        assert len(errors) == 0
    
    def test_archetype_templates(self, db: Session):
        """Test archetype template functionality"""
        service = RelationshipService(db)
        
        # Get all templates
        templates = service.get_archetype_templates()
        assert len(templates) > 0
        assert RelationshipArchetype.COLLEAGUE.value in templates
        assert RelationshipArchetype.FRIEND.value in templates
        
        # Get specific template
        colleague_template = service.get_archetype_template(RelationshipArchetype.COLLEAGUE.value)
        assert colleague_template is not None
        assert "default_dimensions" in colleague_template
        assert "typical_interactions" in colleague_template
        
        # Check colleague template has expected defaults
        defaults = colleague_template["default_dimensions"]
        assert defaults["professional_collaboration"] == 80
        assert defaults["emotional_intimacy"] == 30
    
    def test_relationship_recommendations(self, db: Session):
        """Test relationship archetype recommendations"""
        # Create person with professional info
        person = Person(
            person_id=self.test_person1_id,
            user_id=self.test_user_id,
            first_name="John",
            last_name="Doe",
            professional_info={
                "company": "TechCorp",
                "title": "Senior Engineer"
            },
            is_user=False
        )
        db.add(person)
        db.commit()
        
        service = RelationshipService(db)
        
        # Get recommendations
        recommendations = service.get_relationship_recommendations(
            user_id=self.test_user_id,
            person_id=self.test_person1_id
        )
        
        assert len(recommendations) > 0
        
        # Should recommend colleague due to company info
        colleague_rec = next(
            (r for r in recommendations if r["archetype"] == RelationshipArchetype.COLLEAGUE.value),
            None
        )
        assert colleague_rec is not None
        assert colleague_rec["confidence"] > 0.5
        
        # Should recommend mentor due to "Senior" title
        mentor_rec = next(
            (r for r in recommendations if r["archetype"] == RelationshipArchetype.MENTOR.value),
            None
        )
        assert mentor_rec is not None
        assert "Senior role" in mentor_rec["reason"]


class TestRelationshipEndpoints:
    """Test the relationship API endpoints"""
    
    def setup_method(self):
        """Setup test client"""
        self.client = TestClient(app)
        self.test_user_id = str(uuid.uuid4())
        self.test_person1_id = str(uuid.uuid4())
        self.test_person2_id = str(uuid.uuid4())
    
    def test_create_relationship_endpoint(self):
        """Test POST /relationships/{from_person_id}/relationships"""
        # This would need proper authentication setup
        # Placeholder test structure:
        
        relationship_data = {
            "to_person_id": self.test_person2_id,
            "archetype": RelationshipArchetype.COLLEAGUE.value,
            "relationship_foundation": {
                "how_met": "Work conference",
                "foundation_type": "professional_event"
            },
            "custom_dimensions": {
                "trust_level": 75,
                "communication_frequency": 60
            }
        }
        
        # Would call:
        # response = self.client.post(
        #     f"/api/v1/relationships/{self.test_person1_id}/relationships",
        #     json=relationship_data,
        #     headers={"Authorization": f"Bearer {token}"}
        # )
        # assert response.status_code == 200
        # assert response.json()["archetype"] == RelationshipArchetype.COLLEAGUE.value
        
        # For now, just test data structure
        assert relationship_data["archetype"] == RelationshipArchetype.COLLEAGUE.value
        assert "to_person_id" in relationship_data
    
    def test_get_archetype_templates_endpoint(self):
        """Test GET /relationships/archetypes"""
        # Would test:
        # response = self.client.get("/api/v1/relationships/archetypes")
        # assert response.status_code == 200
        # data = response.json()
        # assert "archetypes" in data
        # assert RelationshipArchetype.COLLEAGUE.value in data["available_types"]
        
        # For now, verify archetype enum
        assert hasattr(RelationshipArchetype, "COLLEAGUE")
        assert hasattr(RelationshipArchetype, "FRIEND")
        assert hasattr(RelationshipArchetype, "MENTOR")
    
    def test_relationship_validation_endpoint(self):
        """Test POST /relationships/validate"""
        test_data = {
            "archetype": RelationshipArchetype.COLLEAGUE.value,
            "dimensions": {
                "trust_level": 85,
                "communication_frequency": 70
            }
        }
        
        # Would test:
        # response = self.client.post("/api/v1/relationships/validate", json=test_data)
        # assert response.status_code == 200
        # assert response.json()["valid"] is True
        
        # For now, verify data structure
        assert "archetype" in test_data
        assert "dimensions" in test_data
    
    def test_relationship_statistics_endpoint(self):
        """Test GET /relationships/stats"""
        # Would test endpoint that returns:
        # {
        #   "total_relationships": 25,
        #   "strong_relationships": 10,
        #   "weak_relationships": 5,
        #   "archetype_distribution": {...},
        #   "average_scores_by_archetype": {...}
        # }
        
        # For now, verify expected structure exists
        expected_fields = [
            "total_relationships",
            "strong_relationships", 
            "archetype_distribution",
            "relationship_health"
        ]
        
        for field in expected_fields:
            # Would be checked in actual response
            assert isinstance(field, str)


if __name__ == "__main__":
    pytest.main([__file__])