"""
Organization management CRUD tests
"""

import pytest
from fastapi.testclient import Test<PERSON>lient

from app.tests.base import BaseCRUDTestCase
from app.tests.test_config import RESPONSE_FIELDS, TEST_ORGANIZATION_TEMPLATES
from app.tests.utils import TestDataFactory


class TestOrganizationCRUD(BaseCRUDTestCase):
    """Test Organization CRUD operations"""

    endpoint_base = "/api/v1/organizations"
    create_data_factory = TestDataFactory.create_organization_data
    required_response_fields = RESPONSE_FIELDS["organization"]["required"]
    optional_response_fields = RESPONSE_FIELDS["organization"]["optional"]

    def test_create_organization_minimal(self, client: TestClient):
        """Test creating organization with minimal data."""
        self.authenticate_user(client)

        minimal_data = {"name": "Test Organization"}

        response = client.post(
            self.endpoint_base, json=minimal_data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Organization creation endpoint not fully implemented yet")

        data = self.assert_successful_response(response, 201)

        assert data["name"] == "Test Organization"
        assert data["user_id"] == self.authenticated_user["user_id"]
        self.assertions.assert_valid_uuid(data["org_id"])

    def test_create_organization_full_data(self, client: TestClient):
        """Test creating organization with complete data."""
        self.authenticate_user(client)

        full_data = self.factory.create_organization_data(
            name="TechCorp Industries",
            industry="Technology",
            description="A leading technology company specializing in AI solutions",
            size="1000-5000",
            website="https://techcorp.com",
            details={
                "founded": "2010",
                "headquarters": "San Francisco, CA",
                "revenue": "$100M-500M",
                "stock_symbol": "TECH",
                "specialties": ["AI", "Machine Learning", "Cloud Computing"],
            },
        )

        response = client.post(
            self.endpoint_base, json=full_data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Organization creation endpoint not fully implemented yet")

        data = self.assert_successful_response(response, 201)

        # Verify all data was saved correctly
        assert data["name"] == "TechCorp Industries"
        assert data["description"] == full_data["description"]
        assert data["details"]["industry"] == "Technology"
        assert data["details"]["founded"] == "2010"
        assert "AI" in data["details"]["specialties"]

    def test_create_organization_duplicate_name(self, client: TestClient):
        """Test creating organization with duplicate name."""
        self.authenticate_user(client)

        org_data = self.factory.create_organization_data(name="Duplicate Corp")

        # Create first organization
        response1 = client.post(
            self.endpoint_base, json=org_data, headers=self.auth_headers
        )

        if response1.status_code == 501:
            pytest.skip("Organization creation endpoint not fully implemented yet")

        self.assert_successful_response(response1, 201)

        # Try to create second organization with same name
        response2 = client.post(
            self.endpoint_base, json=org_data, headers=self.auth_headers
        )

        # Should either allow (same user can have multiple orgs with same name) or reject
        assert response2.status_code in [201, 400, 409]

    def test_get_organizations_list_empty(self, client: TestClient):
        """Test getting empty organizations list."""
        self.authenticate_user(client)

        response = client.get(self.endpoint_base, headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Organization list endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert isinstance(data, list)
        assert len(data) == 0

    def test_get_organizations_list_with_data(self, client: TestClient):
        """Test getting organizations list with data."""
        self.authenticate_user(client)

        # Create organizations using templates
        for template_name, template_data in TEST_ORGANIZATION_TEMPLATES.items():
            org_data = self.factory.create_organization_data(**template_data)
            response = client.post(
                self.endpoint_base, json=org_data, headers=self.auth_headers
            )
            if response.status_code == 501:
                pytest.skip("Organization creation endpoint not fully implemented yet")

        # Get the list
        response = client.get(self.endpoint_base, headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Organization list endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert isinstance(data, list)
        assert len(data) == len(TEST_ORGANIZATION_TEMPLATES)

        # Verify structure of first item
        if data:
            self.assertions.assert_response_structure(
                data[0], self.required_response_fields, self.optional_response_fields
            )

    def test_get_organization_by_id_success(self, client: TestClient):
        """Test getting organization by ID."""
        self.authenticate_user(client)

        # Create organization
        org_data = self.factory.create_organization_data(
            name="Specific Corp", industry="Finance"
        )
        create_response = client.post(
            self.endpoint_base, json=org_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Organization creation endpoint not fully implemented yet")

        created_org = self.assert_successful_response(create_response, 201)
        org_id = created_org["org_id"]

        # Get organization by ID
        response = client.get(
            f"{self.endpoint_base}/{org_id}", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Organization get by ID endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        assert data["org_id"] == org_id
        assert data["name"] == "Specific Corp"
        assert data["details"]["industry"] == "Finance"

    def test_update_organization_basic_info(self, client: TestClient):
        """Test updating organization basic information."""
        self.authenticate_user(client)

        # Create organization
        org_data = self.factory.create_organization_data(
            name="Original Name", industry="Technology"
        )
        create_response = client.post(
            self.endpoint_base, json=org_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Organization creation endpoint not fully implemented yet")

        created_org = self.assert_successful_response(create_response, 201)
        org_id = created_org["org_id"]

        # Update organization
        update_data = {
            "name": "Updated Name",
            "description": "Updated description of the organization",
        }

        response = client.put(
            f"{self.endpoint_base}/{org_id}",
            json=update_data,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Organization update endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        assert data["name"] == "Updated Name"
        assert data["description"] == "Updated description of the organization"
        # Original details should remain
        assert data["details"]["industry"] == "Technology"

    def test_update_organization_details(self, client: TestClient):
        """Test updating organization details."""
        self.authenticate_user(client)

        # Create organization
        org_data = self.factory.create_organization_data(
            name="Detail Corp",
            details={"industry": "Technology", "size": "100-500", "founded": "2020"},
        )
        create_response = client.post(
            self.endpoint_base, json=org_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Organization creation endpoint not fully implemented yet")

        created_org = self.assert_successful_response(create_response, 201)
        org_id = created_org["org_id"]

        # Update details
        update_data = {
            "details": {
                "industry": "FinTech",  # Changed
                "size": "500-1000",  # Changed
                "founded": "2020",  # Same
                "website": "https://detailcorp.com",  # New
                "headquarters": "New York, NY",  # New
            }
        }

        response = client.put(
            f"{self.endpoint_base}/{org_id}",
            json=update_data,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Organization update endpoint not fully implemented yet")

        data = self.assert_successful_response(response)

        assert data["details"]["industry"] == "FinTech"
        assert data["details"]["size"] == "500-1000"
        assert data["details"]["website"] == "https://detailcorp.com"

    def test_delete_organization_success(self, client: TestClient):
        """Test successful organization deletion."""
        self.authenticate_user(client)

        # Create organization
        org_data = self.factory.create_organization_data(name="To Delete Corp")
        create_response = client.post(
            self.endpoint_base, json=org_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Organization creation endpoint not fully implemented yet")

        created_org = self.assert_successful_response(create_response, 201)
        org_id = created_org["org_id"]

        # Delete organization
        response = client.delete(
            f"{self.endpoint_base}/{org_id}", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Organization deletion endpoint not fully implemented yet")

        self.assert_successful_response(response, 204)

        # Verify organization is deleted
        get_response = client.get(
            f"{self.endpoint_base}/{org_id}", headers=self.auth_headers
        )
        self.assert_not_found_response(get_response)

    def test_organization_validation_errors(self, client: TestClient):
        """Test organization creation validation errors."""
        self.authenticate_user(client)

        # Test missing required fields
        invalid_data = {"description": "Missing name"}

        response = client.post(
            self.endpoint_base, json=invalid_data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Organization creation endpoint not fully implemented yet")

        self.assert_validation_error_response(response)

        # Test empty name
        invalid_data = {"name": ""}

        response = client.post(
            self.endpoint_base, json=invalid_data, headers=self.auth_headers
        )
        self.assert_validation_error_response(response)

    def test_organization_search_by_industry(self, client: TestClient):
        """Test filtering organizations by industry."""
        self.authenticate_user(client)

        # Create organizations in different industries
        industries = ["Technology", "Finance", "Healthcare"]
        for industry in industries:
            org_data = self.factory.create_organization_data(
                name=f"{industry} Corp", industry=industry
            )
            response = client.post(
                self.endpoint_base, json=org_data, headers=self.auth_headers
            )
            if response.status_code == 501:
                pytest.skip("Organization creation endpoint not fully implemented yet")

        # Test filtering by industry (if supported)
        response = client.get(
            f"{self.endpoint_base}?industry=Technology", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Organization filtering not implemented yet")
        elif response.status_code == 200:
            data = self.assert_successful_response(response)
            # If filtering is implemented, should only return Technology orgs
            for org in data:
                if "details" in org and "industry" in org["details"]:
                    assert org["details"]["industry"] == "Technology"

    def test_organization_cross_user_isolation(
        self, client: TestClient, test_user_data
    ):
        """Test that users can only access their own organizations."""
        # Create first user and organization
        auth1 = self.authenticate_user(client, test_user_data)

        org_data = self.factory.create_organization_data(name="User1 Corp")
        create_response = client.post(
            self.endpoint_base, json=org_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Organization creation endpoint not fully implemented yet")

        created_org = self.assert_successful_response(create_response, 201)
        org_id = created_org["org_id"]

        # Create second user
        user2_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "first_name": "User2",
            "last_name": "Test",
        }
        auth2 = self.authenticate_user(client, user2_data)

        # User2 should not be able to access User1's organization
        response = client.get(
            f"{self.endpoint_base}/{org_id}", headers=auth2["headers"]
        )
        self.assert_not_found_response(response)

        # User2 should not see User1's organizations in list
        response = client.get(self.endpoint_base, headers=auth2["headers"])
        if response.status_code != 501:
            data = self.assert_successful_response(response)
            assert len(data) == 0  # Should be empty for user2
