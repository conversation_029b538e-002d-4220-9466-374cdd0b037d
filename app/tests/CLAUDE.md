# Tests Directory

Comprehensive test suite following test pyramid approach.

## Structure

- `base.py` - Base test classes and common utilities
- `conftest.py` - Pytest configuration and fixtures
- `utils.py` - Test data factories and helper functions
- `test_*.py` - Individual test modules for each component

## Test Strategy

- **70% Unit Tests** - Fast, isolated component tests
- **20% Integration Tests** - Database and service integration
- **10% E2E Tests** - Full API endpoint testing

## Test Markers

- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests
- `@pytest.mark.auth` - Authentication related tests
- `@pytest.mark.ai` - AI functionality tests
- `@pytest.mark.crud` - CRUD operation tests

## Running Tests

```bash
# All tests
python scripts/run_tests.py

# By type
python scripts/run_tests.py --type unit
python scripts/run_tests.py --marker auth
```

## Coverage Target

Minimum 90% test coverage required.