"""
Task management CRUD tests
"""

from datetime import datetime, timed<PERSON><PERSON>

import pytest
from fastapi.testclient import TestClient

from app.tests.base import BaseCRUDTestCase
from app.tests.test_config import RESPONSE_FIELDS, TEST_TASK_TEMPLATES
from app.tests.utils import TestDataFactory


class TestTaskCRUD(BaseCRUDTestCase):
    """Test Task CRUD operations"""

    endpoint_base = "/api/v1/tasks"
    create_data_factory = TestDataFactory.create_task_data
    required_response_fields = RESPONSE_FIELDS["task"]["required"]
    optional_response_fields = RESPONSE_FIELDS["task"]["optional"]

    def test_create_task_minimal(self, client: TestClient):
        """Test creating task with minimal data."""
        self.authenticate_user(client)

        minimal_data = {"title": "Test Task", "priority": 1, "is_completed": False}

        response = client.post(
            self.endpoint_base, json=minimal_data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Task creation endpoint not fully implemented yet")

        data = self.assert_successful_response(response, 201)

        assert data["title"] == "Test Task"
        assert data["priority"] == 1
        assert data["is_completed"] is False
        assert data["user_id"] == self.authenticated_user["user_id"]
        self.assertions.assert_valid_uuid(data["task_id"])

    def test_create_task_full_data(self, client: TestClient):
        """Test creating task with complete data."""
        self.authenticate_user(client)

        full_data = self.factory.create_task_data(
            title="Research AI/ML mentors",
            description="Create a comprehensive list of potential AI/ML mentors to reach out to, including their backgrounds and contact information",
            priority=1,
            days_from_now=7,
            is_completed=False,
            estimated_hours=4,
            tags=["research", "networking", "mentorship"],
            category="networking",
        )

        response = client.post(
            self.endpoint_base, json=full_data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Task creation endpoint not fully implemented yet")

        data = self.assert_successful_response(response, 201)

        # Verify all data was saved correctly
        assert data["title"] == "Research AI/ML mentors"
        assert data["description"] == full_data["description"]
        assert data["priority"] == 1
        assert data["is_completed"] is False
        if "due_date" in data:
            self.assertions.assert_valid_datetime(data["due_date"])
        if "estimated_hours" in data:
            assert data["estimated_hours"] == 4

    def test_create_task_with_goal_association(self, client: TestClient):
        """Test creating task associated with a goal."""
        self.authenticate_user(client)

        # First create a goal
        goal_data = TestDataFactory.create_goal_data(
            title="Find a Mentor",
            description="Connect with an experienced professional",
        )
        goal_response = client.post(
            "/api/v1/goals", json=goal_data, headers=self.auth_headers
        )

        if goal_response.status_code == 501:
            pytest.skip("Goal creation required for this test but not implemented")

        created_goal = self.assert_successful_response(goal_response, 201)
        goal_id = created_goal["goal_id"]

        # Create task associated with the goal
        task_data = self.factory.create_task_data(
            title="Research potential mentors", goal_id=goal_id
        )

        response = client.post(
            self.endpoint_base, json=task_data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Task creation endpoint not fully implemented yet")

        data = self.assert_successful_response(response, 201)

        assert data["title"] == "Research potential mentors"
        if "goal_id" in data:
            assert data["goal_id"] == goal_id

    def test_get_tasks_list_empty(self, client: TestClient):
        """Test getting empty tasks list."""
        self.authenticate_user(client)

        response = client.get(self.endpoint_base, headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Task list endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert isinstance(data, list)
        assert len(data) == 0

    def test_get_tasks_list_with_data(self, client: TestClient):
        """Test getting tasks list with data."""
        self.authenticate_user(client)

        # Create tasks using templates
        for template_name, template_data in TEST_TASK_TEMPLATES.items():
            task_data = self.factory.create_task_data(**template_data)
            response = client.post(
                self.endpoint_base, json=task_data, headers=self.auth_headers
            )
            if response.status_code == 501:
                pytest.skip("Task creation endpoint not fully implemented yet")

        # Get the list
        response = client.get(self.endpoint_base, headers=self.auth_headers)

        if response.status_code == 501:
            pytest.skip("Task list endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert isinstance(data, list)
        assert len(data) == len(TEST_TASK_TEMPLATES)

        # Verify structure of first item
        if data:
            self.assertions.assert_response_structure(
                data[0], self.required_response_fields, self.optional_response_fields
            )

    def test_get_tasks_by_completion_status(self, client: TestClient):
        """Test filtering tasks by completion status."""
        self.authenticate_user(client)

        # Create completed and incomplete tasks
        task_statuses = [True, False, False]  # 1 completed, 2 incomplete
        for i, is_completed in enumerate(task_statuses):
            task_data = self.factory.create_task_data(
                title=f"Task {i+1}", is_completed=is_completed
            )
            response = client.post(
                self.endpoint_base, json=task_data, headers=self.auth_headers
            )
            if response.status_code == 501:
                pytest.skip("Task creation endpoint not fully implemented yet")

        # Test filtering by completion status
        response = client.get(
            f"{self.endpoint_base}?completed=false", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Task filtering not implemented yet")
        elif response.status_code == 200:
            data = self.assert_successful_response(response)
            # If filtering is implemented, should only return incomplete tasks
            for task in data:
                assert task["is_completed"] is False

    def test_get_tasks_by_priority(self, client: TestClient):
        """Test filtering tasks by priority."""
        self.authenticate_user(client)

        # Create tasks with different priorities
        priorities = [1, 2, 3, 1, 2]
        for i, priority in enumerate(priorities):
            task_data = self.factory.create_task_data(
                title=f"Priority {priority} Task {i+1}", priority=priority
            )
            response = client.post(
                self.endpoint_base, json=task_data, headers=self.auth_headers
            )
            if response.status_code == 501:
                pytest.skip("Task creation endpoint not fully implemented yet")

        # Test filtering by priority
        response = client.get(
            f"{self.endpoint_base}?priority=1", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Task filtering not implemented yet")
        elif response.status_code == 200:
            data = self.assert_successful_response(response)
            # If filtering is implemented, should only return priority 1 tasks
            for task in data:
                assert task["priority"] == 1

    def test_update_task_completion_status(self, client: TestClient):
        """Test updating task completion status."""
        self.authenticate_user(client)

        # Create task
        task_data = self.factory.create_task_data(
            title="Completion Test Task", is_completed=False
        )
        create_response = client.post(
            self.endpoint_base, json=task_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Task creation endpoint not fully implemented yet")

        created_task = self.assert_successful_response(create_response, 201)
        task_id = created_task["task_id"]

        # Mark task as completed
        update_data = {"is_completed": True}

        response = client.put(
            f"{self.endpoint_base}/{task_id}",
            json=update_data,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Task update endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert data["is_completed"] is True
        # Original data should remain
        assert data["title"] == "Completion Test Task"

    def test_update_task_priority(self, client: TestClient):
        """Test updating task priority."""
        self.authenticate_user(client)

        # Create task
        task_data = self.factory.create_task_data(
            title="Priority Test Task", priority=3
        )
        create_response = client.post(
            self.endpoint_base, json=task_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Task creation endpoint not fully implemented yet")

        created_task = self.assert_successful_response(create_response, 201)
        task_id = created_task["task_id"]

        # Update priority
        update_data = {"priority": 1}

        response = client.put(
            f"{self.endpoint_base}/{task_id}",
            json=update_data,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Task update endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        assert data["priority"] == 1

    def test_update_task_due_date(self, client: TestClient):
        """Test updating task due date."""
        self.authenticate_user(client)

        # Create task
        task_data = self.factory.create_task_data(
            title="Due Date Test Task", days_from_now=7
        )
        create_response = client.post(
            self.endpoint_base, json=task_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Task creation endpoint not fully implemented yet")

        created_task = self.assert_successful_response(create_response, 201)
        task_id = created_task["task_id"]

        # Update due date
        new_due_date = (datetime.now() + timedelta(days=14)).isoformat()
        update_data = {"due_date": new_due_date}

        response = client.put(
            f"{self.endpoint_base}/{task_id}",
            json=update_data,
            headers=self.auth_headers,
        )

        if response.status_code == 501:
            pytest.skip("Task update endpoint not fully implemented yet")

        data = self.assert_successful_response(response)
        if "due_date" in data:
            assert data["due_date"] is not None

    def test_delete_task_success(self, client: TestClient):
        """Test successful task deletion."""
        self.authenticate_user(client)

        # Create task
        task_data = self.factory.create_task_data(title="To Delete Task")
        create_response = client.post(
            self.endpoint_base, json=task_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Task creation endpoint not fully implemented yet")

        created_task = self.assert_successful_response(create_response, 201)
        task_id = created_task["task_id"]

        # Delete task
        response = client.delete(
            f"{self.endpoint_base}/{task_id}", headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Task deletion endpoint not fully implemented yet")

        self.assert_successful_response(response, 204)

        # Verify task is deleted
        get_response = client.get(
            f"{self.endpoint_base}/{task_id}", headers=self.auth_headers
        )
        self.assert_not_found_response(get_response)

    def test_task_validation_errors(self, client: TestClient):
        """Test task creation validation errors."""
        self.authenticate_user(client)

        # Test missing required fields
        invalid_data = {"description": "Missing title and priority"}

        response = client.post(
            self.endpoint_base, json=invalid_data, headers=self.auth_headers
        )

        if response.status_code == 501:
            pytest.skip("Task creation endpoint not fully implemented yet")

        self.assert_validation_error_response(response)

        # Test invalid priority
        invalid_data = {
            "title": "Test Task",
            "priority": -1,  # Invalid priority
            "is_completed": False,
        }

        response = client.post(
            self.endpoint_base, json=invalid_data, headers=self.auth_headers
        )
        # Should either validate priority or accept any integer
        assert response.status_code in [201, 422]

    def test_task_cross_user_isolation(self, client: TestClient, test_user_data):
        """Test that users can only access their own tasks."""
        # Create first user and task
        auth1 = self.authenticate_user(client, test_user_data)

        task_data = self.factory.create_task_data(title="User1 Task")
        create_response = client.post(
            self.endpoint_base, json=task_data, headers=self.auth_headers
        )

        if create_response.status_code == 501:
            pytest.skip("Task creation endpoint not fully implemented yet")

        created_task = self.assert_successful_response(create_response, 201)
        task_id = created_task["task_id"]

        # Create second user
        user2_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "first_name": "User2",
            "last_name": "Test",
        }
        auth2 = self.authenticate_user(client, user2_data)

        # User2 should not be able to access User1's task
        response = client.get(
            f"{self.endpoint_base}/{task_id}", headers=auth2["headers"]
        )
        self.assert_not_found_response(response)

        # User2 should not see User1's tasks in list
        response = client.get(self.endpoint_base, headers=auth2["headers"])
        if response.status_code != 501:
            data = self.assert_successful_response(response)
            assert len(data) == 0  # Should be empty for user2
