"""
Test configuration and constants
"""

from typing import Any, Dict, List

# Test database configuration
TEST_DATABASE_CONFIG = {
    "url": "sqlite+aiosqlite:///./test.db",
    "echo": False,
    "pool_pre_ping": True,
}

# Test user configurations
TEST_USERS = {
    "default": {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "first_name": "Test",
        "last_name": "User",
    },
    "admin": {
        "email": "<EMAIL>",
        "password": "adminpassword123",
        "first_name": "Admin",
        "last_name": "User",
    },
    "user2": {
        "email": "<EMAIL>",
        "password": "user2password123",
        "first_name": "Second",
        "last_name": "User",
    },
}

# Test data templates
TEST_PERSON_TEMPLATES = {
    "colleague": {
        "first_name": "<PERSON>",
        "last_name": "Colleague",
        "contact_info": {"email": "<EMAIL>", "phone": "+1234567890"},
        "professional_info": {
            "title": "Senior Developer",
            "company": "Tech Corp",
            "department": "Engineering",
        },
        "social_profiles": {"linkedin": "https://linkedin.com/in/johncolleague"},
    },
    "friend": {
        "first_name": "Jane",
        "last_name": "Friend",
        "contact_info": {"email": "<EMAIL>", "phone": "+0987654321"},
        "personal_details": {
            "birthday": "1990-05-15",
            "interests": ["photography", "hiking"],
        },
        "social_profiles": {"instagram": "https://instagram.com/janefriend"},
    },
    "mentor": {
        "first_name": "Dr. Sarah",
        "last_name": "Mentor",
        "contact_info": {"email": "<EMAIL>"},
        "professional_info": {
            "title": "Professor",
            "company": "Tech University",
            "department": "Computer Science",
        },
        "personal_details": {"expertise": ["AI", "Machine Learning", "Data Science"]},
    },
}

TEST_ORGANIZATION_TEMPLATES = {
    "tech_startup": {
        "name": "InnovateTech",
        "description": "A cutting-edge technology startup",
        "details": {
            "industry": "Technology",
            "size": "10-50",
            "founded": "2020",
            "website": "https://innovatetech.com",
            "location": "San Francisco, CA",
        },
    },
    "large_corp": {
        "name": "MegaCorp Industries",
        "description": "A multinational corporation",
        "details": {
            "industry": "Manufacturing",
            "size": "10000+",
            "founded": "1950",
            "website": "https://megacorp.com",
            "location": "New York, NY",
        },
    },
    "nonprofit": {
        "name": "TechForGood Foundation",
        "description": "A nonprofit focused on technology for social impact",
        "details": {
            "industry": "Nonprofit",
            "size": "50-100",
            "founded": "2015",
            "website": "https://techforgood.org",
            "location": "Seattle, WA",
        },
    },
}

TEST_GOAL_TEMPLATES = {
    "career": {
        "title": "Advance to Senior Engineer",
        "description": "Get promoted to senior engineer role within the next year",
        "category": "career",
        "priority": 1,
        "target_date_days": 365,
    },
    "networking": {
        "title": "Expand Professional Network",
        "description": "Connect with 50 new professionals in my industry",
        "category": "networking",
        "priority": 2,
        "target_date_days": 180,
    },
    "learning": {
        "title": "Learn Machine Learning",
        "description": "Complete a comprehensive ML course and build 3 projects",
        "category": "learning",
        "priority": 1,
        "target_date_days": 120,
    },
}

TEST_TASK_TEMPLATES = {
    "research": {
        "title": "Research industry trends",
        "description": "Analyze current trends in the technology industry",
        "priority": 2,
        "estimated_hours": 4,
        "due_date_days": 7,
    },
    "outreach": {
        "title": "Reach out to potential mentor",
        "description": "Send introduction message to identified mentor",
        "priority": 1,
        "estimated_hours": 1,
        "due_date_days": 3,
    },
    "follow_up": {
        "title": "Follow up on previous conversation",
        "description": "Send follow-up message after last meeting",
        "priority": 2,
        "estimated_hours": 0.5,
        "due_date_days": 2,
    },
}

# API endpoint configurations
API_ENDPOINTS = {
    "auth": {
        "register": "/api/v1/auth/register",
        "login": "/api/v1/auth/login",
        "logout": "/api/v1/auth/logout",
    },
    "users": {
        "me": "/api/v1/users/me",
        "preferences": "/api/v1/users/me/preferences",
        "calibration_request": "/api/v1/users/me/calibration-request",
        "calibrate": "/api/v1/users/me/calibrate",
    },
    "persons": {
        "base": "/api/v1/persons",
        "by_id": "/api/v1/persons/{id}",
        "apply_archetype": "/api/v1/persons/{id}/apply-archetype",
        "relationship_prism": "/api/v1/persons/{id}/relationship-prism/{other_id}",
    },
    "organizations": {
        "base": "/api/v1/organizations",
        "by_id": "/api/v1/organizations/{id}",
    },
    "goals": {
        "base": "/api/v1/goals",
        "by_id": "/api/v1/goals/{id}",
        "dashboard": "/api/v1/goals/{id}/dashboard",
    },
    "tasks": {"base": "/api/v1/tasks", "by_id": "/api/v1/tasks/{id}"},
    "copilot": {
        "converse": "/api/v1/copilot/converse",
        "suggestions": "/api/v1/copilot/suggestions",
    },
    "ai": {
        "network_diagnosis": "/api/v1/ai/network/diagnosis",
        "referral_path": "/api/v1/ai/network/referral-path",
    },
    "graph": {"full": "/api/v1/graph/full"},
    "search": {
        "persons": "/api/v1/search/persons",
        "organizations": "/api/v1/search/organizations",
    },
    "data": {"export": "/api/v1/data/export", "import": "/api/v1/data/import"},
    "integrations": {"calendar_sync": "/api/v1/integrations/calendar/sync"},
}

# Test response field configurations
RESPONSE_FIELDS = {
    "user": {
        "required": ["user_id", "email", "first_name", "last_name", "is_active"],
        "optional": [
            "is_verified",
            "created_at",
            "updated_at",
            "last_login",
            "settings",
            "full_name",
        ],
    },
    "person": {
        "required": ["person_id", "first_name", "last_name", "user_id"],
        "optional": [
            "profile_picture",
            "contact_info",
            "social_profiles",
            "professional_info",
            "personal_details",
            "is_user",
            "created_at",
            "updated_at",
        ],
    },
    "organization": {
        "required": ["org_id", "name", "user_id"],
        "optional": ["description", "details", "created_at", "updated_at", "employee_count", "connection_count"],
    },
    "goal": {
        "required": ["goal_id", "title", "user_id", "status"],
        "optional": [
            "description",
            "target_date",
            "priority",
            "category",
            "created_at",
            "updated_at",
        ],
    },
    "task": {
        "required": ["task_id", "title", "user_id", "priority", "is_completed"],
        "optional": [
            "description",
            "goal_id",
            "due_date",
            "estimated_hours",
            "created_at",
            "updated_at",
        ],
    },
    "token": {
        "required": ["access_token", "token_type"],
        "optional": ["expires_in", "refresh_token"],
    },
}

# Test error messages
ERROR_MESSAGES = {
    "unauthorized": "Could not validate credentials",
    "forbidden": "Not enough permissions",
    "not_found": "Not found",
    "validation_error": "Validation error",
    "duplicate_email": "Email already registered",
    "invalid_credentials": "Incorrect email or password",
    "weak_password": "Password must be at least 6 characters",
}

# Test pagination defaults
PAGINATION_DEFAULTS = {"default_limit": 100, "max_limit": 1000, "default_skip": 0}

# Mock AI responses
MOCK_AI_RESPONSES = {
    "copilot_intents": [
        "create_task",
        "create_goal",
        "find_person",
        "schedule_meeting",
        "get_suggestions",
        "analyze_network",
        "general_query",
    ],
    "suggestion_types": [
        "reconnect",
        "introduction",
        "follow_up",
        "goal_related",
        "network_expansion",
        "skill_development",
    ],
    "relationship_archetypes": [
        "colleague",
        "friend",
        "mentor",
        "mentee",
        "client",
        "vendor",
        "family",
        "acquaintance",
        "collaborator",
        "competitor",
    ],
}

# Test file configurations
TEST_FILES = {
    "csv_contacts": {
        "filename": "test_contacts.csv",
        "headers": ["first_name", "last_name", "email", "phone", "company"],
        "sample_rows": [
            ["John", "Doe", "<EMAIL>", "************", "Tech Corp"],
            ["Jane", "Smith", "<EMAIL>", "************", "Design Co"],
        ],
    },
    "json_export": {
        "filename": "test_export.json",
        "structure": {
            "persons": [],
            "organizations": [],
            "goals": [],
            "tasks": [],
            "relationships": [],
        },
    },
}
