"""
Test configuration and fixtures
Enhanced testing architecture with comprehensive fixtures and utilities
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Any, Async<PERSON>enerator, Dict, Optional
from unittest.mock import AsyncMock, Mock

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import (AsyncSession, async_sessionmaker,
                                    create_async_engine)
from sqlalchemy.pool import StaticPool
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.config import settings
from app.core.database import Base, get_db, get_supabase
from app.main import app
from app.models.goal import Goal
from app.models.organization import Organization
from app.models.person import Person
from app.models.task import Task
from app.models.user import User

# Test database URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test.db"
SYNC_TEST_DATABASE_URL = "sqlite:///./test.db"

# Create test engines
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

sync_test_engine = create_engine(
    SYNC_TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = async_sessionmaker(
    test_engine, class_=AsyncSession, expire_on_commit=False
)

SyncTestingSessionLocal = sessionmaker(
    autocommit=False, autoflush=False, bind=sync_test_engine
)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session."""
    async with test_engine.begin() as connection:
        await connection.run_sync(Base.metadata.create_all)

    async with TestingSessionLocal() as session:
        yield session

    async with test_engine.begin() as connection:
        await connection.run_sync(Base.metadata.drop_all)


@pytest.fixture
def db():
    """Create a synchronous test database session."""
    Base.metadata.create_all(bind=sync_test_engine)
    session = SyncTestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        Base.metadata.drop_all(bind=sync_test_engine)


@pytest.fixture
def mock_supabase():
    """Mock Supabase client for testing."""
    mock_client = Mock()
    mock_auth = Mock()
    mock_client.auth = mock_auth

    # Mock successful registration
    mock_auth.sign_up.return_value = Mock(
        user=Mock(id=str(uuid.uuid4()), email="<EMAIL>")
    )

    # Mock successful login
    mock_auth.sign_in_with_password.return_value = Mock(
        session=Mock(
            access_token="test_access_token",
            expires_in=3600,
            refresh_token="test_refresh_token",
        )
    )

    # Mock get_user to return a user with real string email
    mock_user = Mock()
    mock_user.id = str(uuid.uuid4())
    mock_user.email = "<EMAIL>"
    mock_auth.get_user.return_value = Mock(user=mock_user)

    return mock_client


@pytest.fixture
def client(db, mock_supabase):
    """Create a test client with database dependency override."""

    def override_get_db():
        yield db

    def override_get_supabase():
        return mock_supabase

    app.dependency_overrides[get_db] = override_get_db
    app.dependency_overrides[get_supabase] = override_get_supabase

    with TestClient(app) as test_client:
        yield test_client

    app.dependency_overrides.clear()


# Test data factories
@pytest.fixture
def test_user_data():
    """Test user data fixture."""
    return {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "first_name": "Test",
        "last_name": "User",
    }


@pytest.fixture
def test_person_data():
    """Test person data fixture."""
    return {
        "first_name": "John",
        "last_name": "Doe",
        "contact_info": {"email": "<EMAIL>", "phone": "+1234567890"},
        "social_profiles": {"linkedin": "https://linkedin.com/in/johndoe"},
        "professional_info": {"title": "Software Engineer", "company": "Tech Corp"},
        "personal_details": {"birthday": "1990-01-01"},
    }


@pytest.fixture
def test_organization_data():
    """Test organization data fixture."""
    return {
        "name": "Tech Corporation",
        "description": "A leading technology company",
        "details": {
            "industry": "Technology",
            "size": "1000-5000",
            "website": "https://techcorp.com",
            "address": "123 Tech Street, Silicon Valley, CA",
        },
    }


@pytest.fixture
def test_goal_data():
    """Test goal data fixture."""
    return {
        "title": "Find a mentor in AI/ML",
        "description": "Connect with experienced professionals in artificial intelligence and machine learning",
        "status": "active",
        "target_date": (datetime.now() + timedelta(days=90)).isoformat(),
        "priority": 1,
    }


@pytest.fixture
def test_task_data():
    """Test task data fixture."""
    return {
        "title": "Research potential mentors",
        "description": "Create a list of potential AI/ML mentors to reach out to",
        "priority": 1,
        "is_completed": False,
        "due_date": (datetime.now() + timedelta(days=7)).isoformat(),
    }
