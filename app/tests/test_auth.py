"""
Comprehensive Authentication and User Management Tests
"""

import pytest
from fastapi.testclient import Test<PERSON>lient

from app.tests.base import BaseAPITestCase
from app.tests.test_config import ERROR_MESSAGES, RESPONSE_FIELDS, TEST_USERS


class TestAuthentication(BaseAPITestCase):
    """Test authentication endpoints"""

    def test_register_user_success(self, client: TestClient, test_user_data):
        """Test successful user registration."""
        response = client.post("/api/v1/auth/register", json=test_user_data)
        data = self.assert_successful_response(response, 200)

        # Verify response structure
        self.assertions.assert_response_structure(
            data,
            RESPONSE_FIELDS["user"]["required"],
            RESPONSE_FIELDS["user"]["optional"],
        )

        # Verify data correctness
        assert data["email"] == test_user_data["email"]
        assert data["first_name"] == test_user_data["first_name"]
        assert data["last_name"] == test_user_data["last_name"]
        assert data["is_active"] is True
        self.assertions.assert_valid_uuid(data["user_id"])

    def test_register_user_duplicate_email(self, client: TestClient, test_user_data):
        """Test registration with duplicate email."""
        # Register first user
        client.post("/api/v1/auth/register", json=test_user_data)

        # Try to register again with same email
        response = client.post("/api/v1/auth/register", json=test_user_data)
        self.assert_error_response(response, 400, "already")

    def test_register_user_invalid_email(self, client: TestClient):
        """Test registration with invalid email format."""
        invalid_data = {
            "email": "invalid-email",
            "password": "testpassword123",
            "first_name": "Test",
            "last_name": "User",
        }
        response = client.post("/api/v1/auth/register", json=invalid_data)
        self.assert_validation_error_response(response)

    def test_register_user_weak_password(self, client: TestClient):
        """Test registration with weak password."""
        weak_password_data = {
            "email": "<EMAIL>",
            "password": "123",  # Too short
            "first_name": "Test",
            "last_name": "User",
        }
        response = client.post("/api/v1/auth/register", json=weak_password_data)
        self.assert_validation_error_response(response)

    def test_register_user_missing_fields(self, client: TestClient):
        """Test registration with missing required fields."""
        incomplete_data = {
            "email": "<EMAIL>"
            # Missing password, first_name, last_name
        }
        response = client.post("/api/v1/auth/register", json=incomplete_data)
        self.assert_validation_error_response(response)

    def test_login_success(self, client: TestClient, test_user_data):
        """Test successful login."""
        # Register user first
        client.post("/api/v1/auth/register", json=test_user_data)

        # Login
        login_data = {
            "email": test_user_data["email"],
            "password": test_user_data["password"],
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        data = self.assert_successful_response(response, 200)

        # Verify token response structure
        self.assertions.assert_response_structure(
            data,
            RESPONSE_FIELDS["token"]["required"],
            RESPONSE_FIELDS["token"]["optional"],
        )

        assert data["token_type"] == "bearer"
        assert len(data["access_token"]) > 0

    def test_login_invalid_email(self, client: TestClient):
        """Test login with non-existent email."""
        login_data = {"email": "<EMAIL>", "password": "somepassword"}
        response = client.post("/api/v1/auth/login", json=login_data)
        self.assert_unauthorized_response(response)

    def test_login_wrong_password(self, client: TestClient, test_user_data):
        """Test login with wrong password."""
        # Register user first
        client.post("/api/v1/auth/register", json=test_user_data)

        # Try login with wrong password
        login_data = {"email": test_user_data["email"], "password": "wrongpassword"}
        response = client.post("/api/v1/auth/login", json=login_data)
        self.assert_unauthorized_response(response)

    def test_login_invalid_format(self, client: TestClient):
        """Test login with invalid request format."""
        invalid_data = {"email": "invalid-email-format", "password": "somepassword"}
        response = client.post("/api/v1/auth/login", json=invalid_data)
        self.assert_validation_error_response(response)

    def test_login_missing_credentials(self, client: TestClient):
        """Test login with missing credentials."""
        response = client.post("/api/v1/auth/login", json={})
        self.assert_validation_error_response(response)


class TestTokenValidation(BaseAPITestCase):
    """Test token validation and protected endpoints"""

    def test_protected_endpoint_without_token(self, client: TestClient):
        """Test accessing protected endpoint without token."""
        response = client.get("/api/v1/users/me")
        self.assert_unauthorized_response(response)

    def test_protected_endpoint_with_valid_token(
        self, client: TestClient, test_user_data
    ):
        """Test accessing protected endpoint with valid token."""
        auth_result = self.authenticate_user(client, test_user_data)

        response = client.get("/api/v1/users/me", headers=self.auth_headers)
        data = self.assert_successful_response(response)

        assert data["email"] == test_user_data["email"]
        assert data["user_id"] == auth_result["user"]["user_id"]

    def test_protected_endpoint_with_invalid_token(self, client: TestClient):
        """Test accessing protected endpoint with invalid token."""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/v1/users/me", headers=headers)
        self.assert_unauthorized_response(response)

    def test_protected_endpoint_with_malformed_header(self, client: TestClient):
        """Test accessing protected endpoint with malformed auth header."""
        headers = {"Authorization": "InvalidFormat token"}
        response = client.get("/api/v1/users/me", headers=headers)
        self.assert_unauthorized_response(response)

    def test_protected_endpoint_with_expired_token(self, client: TestClient):
        """Test accessing protected endpoint with expired token."""
        # This would require mocking token expiration
        # For now, we'll test with an obviously invalid token
        headers = {"Authorization": "Bearer expired.token.here"}
        response = client.get("/api/v1/users/me", headers=headers)
        self.assert_unauthorized_response(response)


class TestUserManagement(BaseAPITestCase):
    """Test user management endpoints"""

    def test_get_current_user_success(self, client: TestClient, test_user_data):
        """Test getting current user information."""
        self.authenticate_user(client, test_user_data)

        response = client.get("/api/v1/users/me", headers=self.auth_headers)
        data = self.assert_successful_response(response)

        # Verify response structure
        self.assertions.assert_response_structure(
            data,
            RESPONSE_FIELDS["user"]["required"],
            RESPONSE_FIELDS["user"]["optional"],
        )

        # Verify data correctness
        assert data["email"] == test_user_data["email"]
        assert data["first_name"] == test_user_data["first_name"]
        assert data["last_name"] == test_user_data["last_name"]
        self.assertions.assert_valid_uuid(data["user_id"])

    def test_get_current_user_unauthorized(self, client: TestClient):
        """Test getting current user without authentication."""
        response = client.get("/api/v1/users/me")
        self.assert_unauthorized_response(response)

    def test_update_user_preferences_success(self, client: TestClient, test_user_data):
        """Test updating user preferences."""
        self.authenticate_user(client, test_user_data)

        preferences_data = {
            "stated_decision_factors": {
                "relationship_importance": 0.8,
                "professional_focus": 0.9,
                "personal_connection": 0.7,
            },
            "notification_settings": {
                "email_notifications": True,
                "push_notifications": False,
            },
        }

        response = client.put(
            "/api/v1/users/me/preferences",
            json=preferences_data,
            headers=self.auth_headers,
        )

        # This endpoint might not be implemented yet, so we'll handle both cases
        if response.status_code == 404:
            pytest.skip("User preferences endpoint not implemented yet")
        else:
            data = self.assert_successful_response(response)
            # Verify preferences were updated
            assert "stated_decision_factors" in data

    def test_get_calibration_request(self, client: TestClient, test_user_data):
        """Test getting calibration request."""
        self.authenticate_user(client, test_user_data)

        response = client.get(
            "/api/v1/users/me/calibration-request", headers=self.auth_headers
        )

        # This endpoint might not be implemented yet
        if response.status_code == 404:
            pytest.skip("Calibration request endpoint not implemented yet")
        else:
            data = self.assert_successful_response(response)
            # Verify calibration request structure
            assert isinstance(data, dict)

    def test_submit_calibration_response(self, client: TestClient, test_user_data):
        """Test submitting calibration response."""
        self.authenticate_user(client, test_user_data)

        calibration_data = {
            "responses": [
                {
                    "question_id": "relationship_priority",
                    "answer": "professional_growth",
                },
                {"question_id": "networking_style", "answer": "quality_over_quantity"},
            ]
        }

        response = client.post(
            "/api/v1/users/me/calibrate",
            json=calibration_data,
            headers=self.auth_headers,
        )

        # This endpoint might not be implemented yet
        if response.status_code == 404:
            pytest.skip("Calibration submission endpoint not implemented yet")
        else:
            data = self.assert_successful_response(response)
            assert "learned_decision_factors" in data or "status" in data


class TestAuthenticationEdgeCases(BaseAPITestCase):
    """Test edge cases and boundary conditions for authentication"""

    def test_register_with_very_long_email(self, client: TestClient):
        """Test registration with very long email."""
        long_email = "a" * 250 + "@example.com"  # Very long email
        user_data = {
            "email": long_email,
            "password": "testpassword123",
            "first_name": "Test",
            "last_name": "User",
        }
        response = client.post("/api/v1/auth/register", json=user_data)
        # Should either succeed or fail with validation error
        assert response.status_code in [200, 422]

    def test_register_with_unicode_names(self, client: TestClient):
        """Test registration with unicode characters in names."""
        unicode_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "first_name": "José",
            "last_name": "李明",
        }
        response = client.post("/api/v1/auth/register", json=unicode_data)
        data = self.assert_successful_response(response, 200)

        assert data["first_name"] == "José"
        assert data["last_name"] == "李明"

    def test_case_insensitive_email_login(self, client: TestClient, test_user_data):
        """Test that email login is case insensitive."""
        # Register with lowercase email
        client.post("/api/v1/auth/register", json=test_user_data)

        # Try login with uppercase email
        login_data = {
            "email": test_user_data["email"].upper(),
            "password": test_user_data["password"],
        }
        response = client.post("/api/v1/auth/login", json=login_data)

        # Should succeed (case insensitive) or fail consistently
        # Implementation dependent
        assert response.status_code in [200, 401]

    def test_concurrent_registrations(self, client: TestClient):
        """Test handling of concurrent registration attempts."""
        user_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "first_name": "Concurrent",
            "last_name": "User",
        }

        # Simulate concurrent registrations
        response1 = client.post("/api/v1/auth/register", json=user_data)
        response2 = client.post("/api/v1/auth/register", json=user_data)

        # One should succeed, one should fail
        status_codes = [response1.status_code, response2.status_code]
        assert 200 in status_codes
        assert 400 in status_codes
