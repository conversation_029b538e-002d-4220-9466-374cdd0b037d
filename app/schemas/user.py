"""
User schemas
"""

from datetime import datetime
from typing import Any, Dict, Optional
from uuid import UUID

from pydantic import BaseModel, EmailStr


class UserBase(BaseModel):
    """Base user schema"""

    email: EmailStr
    first_name: str
    last_name: str
    is_active: bool = True
    is_verified: bool = False


class UserCreate(UserBase):
    """User creation schema"""

    password: str


class UserUpdate(BaseModel):
    """User update schema"""

    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
    settings: Optional[Dict[str, Any]] = None


class UserInDBBase(UserBase):
    """Base user schema with database fields"""

    user_id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    settings: Dict[str, Any] = {}

    class Config:
        from_attributes = True


class User(UserInDBBase):
    """User schema for API responses"""

    pass


class UserInDB(UserInDBBase):
    """User schema with hashed password (for internal use)"""

    hashed_password: str


class UserProfile(BaseModel):
    """User profile schema"""

    user_id: UUID
    email: EmailStr
    first_name: str
    last_name: str
    full_name: str
    is_active: bool
    is_verified: bool
    created_at: datetime
    last_login: Optional[datetime] = None
    settings: Dict[str, Any] = {}

    class Config:
        from_attributes = True
