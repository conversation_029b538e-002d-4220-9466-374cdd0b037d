"""
Organization-related Pydantic schemas for API validation
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field, validator


class OrganizationBase(BaseModel):
    """Base organization schema with common fields"""
    name: str = Field(..., min_length=1, max_length=255, description="Organization name")
    description: Optional[str] = Field(None, max_length=2000, description="Organization description")
    details: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional organization details")


class OrganizationCreate(OrganizationBase):
    """Schema for creating a new organization"""
    
    @validator('details')
    def validate_details(cls, v):
        """Validate organization details structure"""
        if v is None:
            return {}
        
        # Define allowed detail fields
        allowed_fields = {
            'industry', 'size', 'website', 'address', 'phone', 'email',
            'founded_year', 'headquarters', 'employee_count', 'revenue',
            'description', 'mission', 'values', 'culture', 'benefits',
            'technologies', 'markets', 'competitors', 'parent_company',
            'subsidiaries', 'stock_symbol', 'linkedin', 'twitter'
        }
        
        # Remove any fields not in allowed list
        filtered_details = {k: v for k, v in v.items() if k in allowed_fields}
        
        return filtered_details


class OrganizationUpdate(BaseModel):
    """Schema for updating an organization"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=2000)
    details: Optional[Dict[str, Any]] = None
    
    @validator('details')
    def validate_details(cls, v):
        """Validate organization details structure"""
        if v is None:
            return v
        
        allowed_fields = {
            'industry', 'size', 'website', 'address', 'phone', 'email',
            'founded_year', 'headquarters', 'employee_count', 'revenue',
            'description', 'mission', 'values', 'culture', 'benefits',
            'technologies', 'markets', 'competitors', 'parent_company',
            'subsidiaries', 'stock_symbol', 'linkedin', 'twitter'
        }
        
        filtered_details = {k: v for k, v in v.items() if k in allowed_fields}
        return filtered_details


class OrganizationResponse(OrganizationBase):
    """Schema for organization responses"""
    org_id: uuid.UUID
    user_id: uuid.UUID
    created_at: datetime
    updated_at: Optional[datetime]
    
    # Additional computed fields
    employee_count: Optional[int] = Field(None, description="Number of associated employees")
    connection_count: Optional[int] = Field(None, description="Number of user's connections at this organization")
    
    class Config:
        from_attributes = True


class OrganizationSummary(BaseModel):
    """Simplified organization schema for lists and references"""
    org_id: uuid.UUID
    name: str
    description: Optional[str]
    industry: Optional[str] = None
    employee_count: Optional[int] = None
    connection_count: Optional[int] = None
    
    class Config:
        from_attributes = True


class WorksAtBase(BaseModel):
    """Base schema for work relationships"""
    role: Optional[str] = Field(None, max_length=255, description="Job title or position")
    start_date: Optional[datetime] = Field(None, description="Employment start date")
    end_date: Optional[datetime] = Field(None, description="Employment end date (null if current)")
    is_current: bool = Field(True, description="Whether this is a current position")
    details: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional work details")
    
    @validator('end_date')
    def validate_end_date(cls, v, values):
        """Validate end_date logic"""
        if v is not None and values.get('is_current'):
            raise ValueError("end_date should be null for current positions")
        return v
    
    @validator('details')
    def validate_work_details(cls, v):
        """Validate work details structure"""
        if v is None:
            return {}
        
        allowed_fields = {
            'department', 'responsibilities', 'achievements', 'skills',
            'reporting_to', 'team_size', 'salary_range', 'employment_type',
            'location', 'remote_work', 'benefits', 'notes'
        }
        
        filtered_details = {k: v for k, v in v.items() if k in allowed_fields}
        return filtered_details


class WorksAtCreate(WorksAtBase):
    """Schema for creating work relationships"""
    person_id: uuid.UUID = Field(..., description="Person ID")
    org_id: uuid.UUID = Field(..., description="Organization ID")


class WorksAtUpdate(BaseModel):
    """Schema for updating work relationships"""
    role: Optional[str] = Field(None, max_length=255)
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_current: Optional[bool] = None
    details: Optional[Dict[str, Any]] = None
    
    @validator('details')
    def validate_work_details(cls, v):
        """Validate work details structure"""
        if v is None:
            return v
        
        allowed_fields = {
            'department', 'responsibilities', 'achievements', 'skills',
            'reporting_to', 'team_size', 'salary_range', 'employment_type',
            'location', 'remote_work', 'benefits', 'notes'
        }
        
        filtered_details = {k: v for k, v in v.items() if k in allowed_fields}
        return filtered_details


class WorksAtResponse(WorksAtBase):
    """Schema for work relationship responses"""
    person_id: uuid.UUID
    org_id: uuid.UUID
    created_at: datetime
    updated_at: Optional[datetime]
    
    # Related object summaries
    person_name: Optional[str] = Field(None, description="Person's full name")
    organization_name: Optional[str] = Field(None, description="Organization name")
    
    class Config:
        from_attributes = True


class OrganizationWithEmployees(OrganizationResponse):
    """Organization schema with employee information"""
    employees: List[WorksAtResponse] = Field(default_factory=list, description="Employees at this organization")
    
    class Config:
        from_attributes = True


class OrganizationSearch(BaseModel):
    """Schema for organization search parameters"""
    query: Optional[str] = Field(None, description="Search query for name or description")
    industry: Optional[str] = Field(None, description="Filter by industry")
    size: Optional[str] = Field(None, description="Filter by organization size")
    location: Optional[str] = Field(None, description="Filter by location")
    has_connections: Optional[bool] = Field(None, description="Filter organizations with user connections")
    
    class Config:
        schema_extra = {
            "example": {
                "query": "Tech startup",
                "industry": "Technology",
                "size": "startup",
                "location": "San Francisco",
                "has_connections": True
            }
        }


class OrganizationStats(BaseModel):
    """Schema for organization statistics"""
    total_organizations: int = Field(..., description="Total organizations in user's network")
    organizations_with_connections: int = Field(..., description="Organizations where user has connections")
    top_industries: List[Dict[str, Any]] = Field(..., description="Top industries by connection count")
    top_organizations: List[Dict[str, Any]] = Field(..., description="Top organizations by connection count")
    connection_distribution: Dict[str, int] = Field(..., description="Distribution of connections across organizations")
    
    class Config:
        schema_extra = {
            "example": {
                "total_organizations": 25,
                "organizations_with_connections": 18,
                "top_industries": [
                    {"industry": "Technology", "count": 12, "percentage": 48},
                    {"industry": "Finance", "count": 6, "percentage": 24}
                ],
                "top_organizations": [
                    {"name": "TechCorp", "count": 8, "percentage": 32},
                    {"name": "StartupX", "count": 4, "percentage": 16}
                ],
                "connection_distribution": {
                    "1-5 connections": 15,
                    "6-10 connections": 5,
                    "11+ connections": 3
                }
            }
        }