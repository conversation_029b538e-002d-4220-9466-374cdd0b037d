# Schemas Directory - Pydantic API Validation

This directory contains comprehensive Pydantic models for API request/response validation in the Nexus relationship management platform.

## Schema Architecture

### Validation Strategy
All schemas follow a consistent validation pattern:
- **Type Safety**: Strict type checking with comprehensive validation rules
- **Field Validation**: Custom validators for complex business logic
- **Sanitization**: Input sanitization and normalization
- **OpenAPI Integration**: Automatic documentation generation
- **Error Handling**: Detailed validation error messages

## Core Entity Schemas

### `auth.py` - Authentication and Security
**Secure authentication flow with JWT token management**

**Schema Types**:
- **UserLogin**: Secure login credentials with validation
- **UserRegister**: User registration with password complexity requirements
- **Token**: JWT token response with expiration handling
- **TokenData**: Token payload validation for authentication

**Security Features**:
- Password complexity validation
- Email format validation
- Secure token handling
- Input sanitization for security

### `user.py` - User Profile Management
**User account and profile management schemas**

**Schema Types**:
- **UserCreate**: New user creation with validation
- **UserUpdate**: Profile update with selective field modification
- **UserResponse**: Safe user data response (no sensitive fields)
- **UserProfile**: Comprehensive profile information

### `person.py` - Contact Management
**Comprehensive contact management with relationship integration**

**Schema Types**:
- **PersonCreate**: New contact creation with validation
- **PersonUpdate**: Contact modification with selective updates
- **PersonResponse**: Complete contact information response
- **PersonSummary**: Simplified contact view for lists
- **PersonSearch**: Contact search parameters and filters

**Validation Features**:
- Email format validation
- Phone number normalization
- Name validation and sanitization
- Custom details field validation

### `organization.py` - Organization and Work Relationships
**Organization management with work relationship tracking**

**Schema Types**:
- **OrganizationCreate**: New organization with validation
- **OrganizationUpdate**: Organization modification
- **OrganizationResponse**: Complete organization data
- **OrganizationWithEmployees**: Organization with employee list
- **WorksAtCreate/Update/Response**: Work relationship management
- **OrganizationStats**: Statistical analysis data

**Advanced Features**:
- Work relationship validation with date logic
- Organization hierarchy support
- Industry and size categorization
- Employee association management

### `relationship.py` - Six-Dimensional Relationship Validation
**Sophisticated relationship management with archetype system**

**Schema Types**:
- **RelationshipCreate**: New relationship with dimension validation
- **RelationshipUpdate**: Relationship modification
- **RelationshipResponse**: Complete relationship data
- **DimensionUpdate**: Individual dimension modification
- **ArchetypeApplication**: Archetype template application
- **RelationshipStats**: Relationship analytics

**Six-Dimensional Validation**:
```python
class RelationshipBase(BaseModel):
    emotional_intimacy: int = Field(..., ge=1, le=5)
    professional_collaboration: int = Field(..., ge=1, le=5)
    trust_level: int = Field(..., ge=1, le=5)
    communication_frequency: int = Field(..., ge=1, le=5)
    shared_experience_value: int = Field(..., ge=1, le=5)
    reciprocity_balance: int = Field(..., ge=1, le=5)
```

### `goal.py` - AI-Enhanced Goal Management
**Goal tracking with AI analysis integration**

**Schema Types**:
- **GoalCreate**: New goal with success criteria
- **GoalUpdate**: Goal modification with AI re-analysis
- **GoalResponse**: Complete goal data with AI insights
- **GoalDashboard**: Comprehensive goal dashboard data
- **AIAnalysis**: AI-generated goal analysis results

**AI Integration Features**:
- Success criteria validation
- Target date validation with timeline checking
- Priority level validation
- AI analysis result schemas

### `task.py` - Task Management
**Action item tracking with goal integration**

**Schema Types**:
- **TaskCreate**: New task with goal association
- **TaskUpdate**: Task modification
- **TaskResponse**: Complete task information
- **TaskSummary**: Simplified task view

## Data Portability Schemas

### `data_portability.py` - Export/Import Validation
**Comprehensive data portability with multi-format support**

**Schema Types**:
- **ExportRequest**: Export configuration with format selection
- **ImportRequest**: Import configuration with validation options
- **ExportJobResponse**: Export job status and progress
- **ImportJobResponse**: Import job results and errors
- **DataPreview**: File preview with suggested mappings

**Format Support**:
```python
class ExportFormat(str, Enum):
    JSON = "json"
    CSV = "csv" 
    VCARD = "vcard"

class ImportFormat(str, Enum):
    JSON = "json"
    CSV = "csv"
    VCARD = "vcard"
```

**Advanced Validation**:
- File format validation
- Custom field mapping validation
- Duplicate handling strategy validation
- Data integrity checking

## Advanced Validation Features

### Custom Validators

#### Email Validation
```python
@validator('email')
def validate_email(cls, v):
    if v and not re.match(r'^[^@]+@[^@]+\.[^@]+$', v):
        raise ValueError('Invalid email format')
    return v.lower() if v else v
```

#### Date Logic Validation
```python
@validator('end_date')
def validate_end_date(cls, v, values):
    if v is not None and values.get('is_current'):
        raise ValueError("end_date should be null for current positions")
    if v and values.get('start_date') and v < values['start_date']:
        raise ValueError("end_date cannot be before start_date")
    return v
```

#### Relationship Dimension Validation
```python
@validator('emotional_intimacy', 'professional_collaboration', 'trust_level', 
          'communication_frequency', 'shared_experience_value', 'reciprocity_balance')
def validate_dimension_range(cls, v):
    if v is not None and not (1 <= v <= 5):
        raise ValueError('Relationship dimensions must be between 1 and 5')
    return v
```

### Field Sanitization

#### Name Normalization
```python
@validator('first_name', 'last_name')
def normalize_name(cls, v):
    if v:
        return ' '.join(word.capitalize() for word in v.strip().split())
    return v
```

#### Phone Number Normalization
```python
@validator('phone')
def normalize_phone(cls, v):
    if v:
        # Remove all non-digit characters except +
        cleaned = re.sub(r'[^\d+]', '', v)
        return cleaned if cleaned else None
    return v
```

### Complex Field Validation

#### Goal Success Criteria
```python
@validator('success_criteria')
def validate_success_criteria(cls, v):
    if v and len(v) > 10:
        raise ValueError('Maximum 10 success criteria allowed')
    if v and any(len(criterion) > 200 for criterion in v):
        raise ValueError('Each success criterion must be under 200 characters')
    return v
```

#### Organization Details Validation
```python
@validator('details')
def validate_details(cls, v):
    if v is None:
        return {}
    
    allowed_fields = {
        'industry', 'size', 'website', 'address', 'phone', 'email',
        'founded_year', 'headquarters', 'employee_count', 'revenue'
    }
    
    return {k: v for k, v in v.items() if k in allowed_fields}
```

## Response Schemas

### Secure Response Handling
All response schemas exclude sensitive information:
- No password fields in user responses
- No internal system fields exposed
- Proper field filtering for different access levels
- Sanitized error messages

### Nested Response Schemas
Complex responses include related entity data:
```python
class PersonResponse(PersonBase):
    person_id: UUID
    user_id: UUID
    created_at: datetime
    
    # Related data
    relationship_count: Optional[int]
    recent_interactions: List[InteractionSummary]
    work_history: List[WorksAtResponse]
```

### Pagination Schemas
Standardized pagination across all list endpoints:
```python
class PaginatedResponse(BaseModel, Generic[T]):
    items: List[T]
    total: int
    page: int
    per_page: int
    pages: int
    has_next: bool
    has_prev: bool
```

## OpenAPI Documentation

### Schema Examples
All schemas include comprehensive examples:
```python
class Config:
    schema_extra = {
        "example": {
            "name": "John Doe",
            "email": "<EMAIL>",
            "phone": "******-123-4567"
        }
    }
```

### Field Documentation
Detailed field descriptions for API documentation:
```python
name: str = Field(..., description="Full name of the contact", max_length=255)
email: Optional[str] = Field(None, description="Primary email address")
```

## Usage Patterns

### Request Validation
```python
@router.post("/", response_model=PersonResponse)
def create_person(
    person_data: PersonCreate,  # Automatic validation
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    # person_data is guaranteed to be valid
```

### Response Serialization
```python
person = service.create_person(person_data, user_id)
return PersonResponse.from_orm(person)  # Safe serialization
```

For complete API implementation and service integration, refer to the `../api/` and `../services/` directories.