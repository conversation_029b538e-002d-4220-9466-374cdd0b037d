"""
Data export/import schemas for API validation
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator


class ExportFormat(str, Enum):
    """Supported export formats"""
    JSON = "json"
    CSV = "csv"
    VCARD = "vcard"


class ImportFormat(str, Enum):
    """Supported import formats"""
    JSON = "json"
    CSV = "csv"
    VCARD = "vcard"


class JobStatus(str, Enum):
    """Job processing status"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ExportRequest(BaseModel):
    """Schema for export request"""
    format: ExportFormat = Field(..., description="Export format")
    include_privacy_data: bool = Field(False, description="Include sensitive data in export")
    entities: List[str] = Field(
        default_factory=lambda: ["persons", "organizations", "relationships", "goals", "tasks"],
        description="Entities to include in export"
    )
    custom_fields: Optional[Dict[str, Any]] = Field(
        None, 
        description="Custom field mappings for CSV export"
    )
    
    @validator('entities')
    def validate_entities(cls, v):
        """Validate entity types"""
        allowed_entities = {
            "persons", "organizations", "relationships", "goals", 
            "tasks", "interactions", "notes", "tags"
        }
        invalid_entities = set(v) - allowed_entities
        if invalid_entities:
            raise ValueError(f"Invalid entities: {invalid_entities}")
        return v


class ImportRequest(BaseModel):
    """Schema for import request"""
    format: ImportFormat = Field(..., description="Import format")
    duplicate_handling: str = Field(
        "skip", 
        description="How to handle duplicates: skip, update, merge"
    )
    validation_strict: bool = Field(
        True, 
        description="Whether to use strict validation"
    )
    custom_mappings: Optional[Dict[str, str]] = Field(
        None,
        description="Custom field mappings for import"
    )
    
    @validator('duplicate_handling')
    def validate_duplicate_handling(cls, v):
        """Validate duplicate handling strategy"""
        allowed_strategies = {"skip", "update", "merge"}
        if v not in allowed_strategies:
            raise ValueError(f"Invalid duplicate handling: {v}")
        return v


class JobResponse(BaseModel):
    """Base job response schema"""
    job_id: uuid.UUID = Field(..., description="Job identifier")
    status: JobStatus = Field(..., description="Job status")
    created_at: datetime = Field(..., description="Job creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    user_id: uuid.UUID = Field(..., description="User who created the job")
    
    class Config:
        from_attributes = True


class ExportJobResponse(JobResponse):
    """Export job response schema"""
    format: ExportFormat = Field(..., description="Export format")
    include_privacy_data: bool = Field(..., description="Whether privacy data is included")
    entities: List[str] = Field(..., description="Entities included in export")
    download_url: Optional[str] = Field(None, description="Download URL when completed")
    file_size: Optional[int] = Field(None, description="File size in bytes")
    expires_at: Optional[datetime] = Field(None, description="Download expiration time")
    progress: int = Field(0, description="Progress percentage (0-100)")
    
    class Config:
        from_attributes = True


class ImportJobResponse(JobResponse):
    """Import job response schema"""
    format: ImportFormat = Field(..., description="Import format")
    filename: str = Field(..., description="Original filename")
    file_size: int = Field(..., description="File size in bytes")
    duplicate_handling: str = Field(..., description="Duplicate handling strategy")
    validation_strict: bool = Field(..., description="Validation strictness")
    
    # Processing results
    records_total: Optional[int] = Field(None, description="Total records in file")
    records_processed: Optional[int] = Field(None, description="Records processed")
    records_imported: Optional[int] = Field(None, description="Records successfully imported")
    records_failed: Optional[int] = Field(None, description="Records that failed")
    records_skipped: Optional[int] = Field(None, description="Records skipped (duplicates)")
    
    # Error details
    errors: List[str] = Field(default_factory=list, description="Import errors")
    warnings: List[str] = Field(default_factory=list, description="Import warnings")
    
    class Config:
        from_attributes = True


class JobSummary(BaseModel):
    """Job summary for listing"""
    job_id: uuid.UUID
    job_type: str  # "export" or "import"
    status: JobStatus
    created_at: datetime
    progress: int = Field(0, description="Progress percentage")
    
    class Config:
        from_attributes = True


class ExportStats(BaseModel):
    """Export statistics"""
    total_persons: int = Field(0, description="Total persons exported")
    total_organizations: int = Field(0, description="Total organizations exported")
    total_relationships: int = Field(0, description="Total relationships exported")
    total_goals: int = Field(0, description="Total goals exported")
    total_tasks: int = Field(0, description="Total tasks exported")
    total_interactions: int = Field(0, description="Total interactions exported")
    total_notes: int = Field(0, description="Total notes exported")
    total_tags: int = Field(0, description="Total tags exported")
    
    export_size_bytes: int = Field(0, description="Total export size in bytes")
    processing_time_seconds: float = Field(0, description="Processing time in seconds")
    
    class Config:
        schema_extra = {
            "example": {
                "total_persons": 150,
                "total_organizations": 25,
                "total_relationships": 300,
                "total_goals": 12,
                "total_tasks": 45,
                "total_interactions": 500,
                "total_notes": 80,
                "total_tags": 20,
                "export_size_bytes": 2621440,
                "processing_time_seconds": 12.5
            }
        }


class ImportStats(BaseModel):
    """Import statistics"""
    file_size_bytes: int = Field(..., description="Original file size")
    processing_time_seconds: float = Field(..., description="Processing time")
    
    persons_imported: int = Field(0, description="Persons imported")
    organizations_imported: int = Field(0, description="Organizations imported")
    relationships_imported: int = Field(0, description="Relationships imported")
    goals_imported: int = Field(0, description="Goals imported")
    tasks_imported: int = Field(0, description="Tasks imported")
    interactions_imported: int = Field(0, description="Interactions imported")
    notes_imported: int = Field(0, description="Notes imported")
    tags_imported: int = Field(0, description="Tags imported")
    
    class Config:
        schema_extra = {
            "example": {
                "file_size_bytes": 1048576,
                "processing_time_seconds": 8.2,
                "persons_imported": 120,
                "organizations_imported": 15,
                "relationships_imported": 200,
                "goals_imported": 8,
                "tasks_imported": 25,
                "interactions_imported": 300,
                "notes_imported": 40,
                "tags_imported": 10
            }
        }


class DataPreview(BaseModel):
    """Preview of data to be imported"""
    sample_records: List[Dict[str, Any]] = Field(
        ..., 
        description="Sample records from the file"
    )
    detected_columns: List[str] = Field(
        ...,
        description="Detected columns/fields in the file"
    )
    suggested_mappings: Dict[str, str] = Field(
        ...,
        description="Suggested field mappings"
    )
    record_count: int = Field(
        ...,
        description="Total number of records in file"
    )
    validation_errors: List[str] = Field(
        default_factory=list,
        description="Validation errors found in preview"
    )
    
    class Config:
        schema_extra = {
            "example": {
                "sample_records": [
                    {
                        "First Name": "John",
                        "Last Name": "Doe",
                        "Email": "<EMAIL>",
                        "Company": "Acme Corp"
                    }
                ],
                "detected_columns": ["First Name", "Last Name", "Email", "Company"],
                "suggested_mappings": {
                    "First Name": "first_name",
                    "Last Name": "last_name",
                    "Email": "email",
                    "Company": "organization"
                },
                "record_count": 150,
                "validation_errors": []
            }
        }