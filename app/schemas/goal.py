"""
Goal schemas
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, validator

from app.models.goal import GoalStatus


class GoalBase(BaseModel):
    """Base goal schema"""

    title: str
    description: Optional[str] = None
    priority: int = 3
    target_date: Optional[datetime] = None
    metadata: Dict[str, Any] = {}


class GoalCreate(GoalBase):
    """Goal creation schema"""

    @validator("title")
    def validate_title(cls, v):
        if not v or not v.strip():
            raise ValueError("Title cannot be empty")
        return v.strip()

    @validator("priority")
    def validate_priority(cls, v):
        if v < 1 or v > 5:
            raise ValueError("Priority must be between 1 and 5")
        return v


class GoalUpdate(BaseModel):
    """Goal update schema"""

    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[GoalStatus] = None
    priority: Optional[int] = None
    target_date: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


class GoalInDBBase(GoalBase):
    """Base goal schema with database fields"""

    goal_id: UUID
    user_id: UUID
    status: GoalStatus
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Goal(GoalInDBBase):
    """Goal schema for API responses"""

    @property
    def is_active(self) -> bool:
        return self.status == GoalStatus.ACTIVE

    @property
    def is_completed(self) -> bool:
        return self.status == GoalStatus.COMPLETED

    @property
    def tags(self) -> List[str]:
        if not self.metadata:
            return []
        return self.metadata.get("tags", [])

    @property
    def related_people(self) -> List[str]:
        if not self.metadata:
            return []
        return self.metadata.get("related_people", [])

    @classmethod
    def model_validate(cls, obj, **kwargs):
        """Custom model_validate to handle SQLAlchemy objects"""
        if hasattr(obj, '__dict__'):
            # This is a SQLAlchemy object
            data = {}
            for key, value in obj.__dict__.items():
                if not key.startswith('_'):
                    data[key] = value
            # Map goal_metadata to metadata
            if 'goal_metadata' in data:
                data['metadata'] = data.pop('goal_metadata')
            return super().model_validate(data, **kwargs)
        return super().model_validate(obj, **kwargs)


class GoalInDB(GoalInDBBase):
    """Goal schema for internal use"""

    pass


class GoalSummary(BaseModel):
    """Simplified goal schema for lists and references"""

    goal_id: UUID
    title: str
    status: GoalStatus
    priority: int
    target_date: Optional[datetime] = None
    created_at: datetime

    class Config:
        from_attributes = True


class GoalDashboard(BaseModel):
    """Goal dashboard data schema"""

    goal: Goal
    progress: Dict[str, Any]
    related_people: List[str]
    ai_analysis: Dict[str, Any]
    recent_activity: List[Dict[str, Any]]
