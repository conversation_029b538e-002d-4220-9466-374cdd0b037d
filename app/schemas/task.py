"""
Task schemas
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, validator


class TaskBase(BaseModel):
    """Base task schema"""

    title: str
    description: Optional[str] = None
    priority: int = 3
    due_date: Optional[datetime] = None
    metadata: Dict[str, Any] = {}


class TaskCreate(TaskBase):
    """Task creation schema"""

    goal_id: Optional[UUID] = None

    @validator("title")
    def validate_title(cls, v):
        if not v or not v.strip():
            raise ValueError("Title cannot be empty")
        return v.strip()

    @validator("priority")
    def validate_priority(cls, v):
        if v < 1 or v > 5:
            raise ValueError("Priority must be between 1 and 5")
        return v


class TaskUpdate(BaseModel):
    """Task update schema"""

    title: Optional[str] = None
    description: Optional[str] = None
    priority: Optional[int] = None
    is_completed: Optional[bool] = None
    due_date: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


class TaskInDBBase(TaskBase):
    """Base task schema with database fields"""

    task_id: UUID
    user_id: UUID
    goal_id: Optional[UUID] = None
    is_completed: bool
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Task(TaskInDBBase):
    """Task schema for API responses"""

    @property
    def is_overdue(self) -> bool:
        if not self.due_date or self.is_completed:
            return False
        return self.due_date < datetime.utcnow()

    @property
    def related_people(self) -> List[str]:
        if not self.metadata:
            return []
        return self.metadata.get("related_people", [])

    @property
    def category(self) -> str:
        if not self.metadata:
            return "general"
        return self.metadata.get("category", "general")

    @property
    def estimated_duration(self) -> int:
        if not self.metadata:
            return 0
        return self.metadata.get("estimated_duration", 0)

    @property
    def is_ai_generated(self) -> bool:
        if not self.metadata:
            return False
        return self.metadata.get("ai_generated", False)

    @classmethod
    def model_validate(cls, obj, **kwargs):
        """Custom model_validate to handle SQLAlchemy objects"""
        if hasattr(obj, '__dict__'):
            # This is a SQLAlchemy object
            data = {}
            for key, value in obj.__dict__.items():
                if not key.startswith('_'):
                    data[key] = value
            # Map task_metadata to metadata
            if 'task_metadata' in data:
                data['metadata'] = data.pop('task_metadata')
            return super().model_validate(data, **kwargs)
        return super().model_validate(obj, **kwargs)


class TaskInDB(TaskInDBBase):
    """Task schema for internal use"""

    pass


class TaskSummary(BaseModel):
    """Simplified task schema for lists and references"""

    task_id: UUID
    title: str
    priority: int
    is_completed: bool
    due_date: Optional[datetime] = None
    created_at: datetime

    class Config:
        from_attributes = True
