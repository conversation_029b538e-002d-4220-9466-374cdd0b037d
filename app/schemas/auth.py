"""
Authentication schemas
"""

from typing import Optional

from pydantic import BaseModel, EmailStr, validator


class Token(BaseModel):
    """Token response schema"""

    access_token: str
    token_type: str
    expires_in: int
    refresh_token: str


class TokenData(BaseModel):
    """Token data schema"""

    user_id: Optional[str] = None


class UserLogin(BaseModel):
    """User login schema"""

    email: EmailStr
    password: str


class UserCreate(BaseModel):
    """User creation schema"""

    email: EmailStr
    password: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None

    @validator("password")
    def validate_password(cls, v):
        if len(v) < 6:
            raise ValueError("Password must be at least 6 characters long")
        return v


class UserRegister(UserCreate):
    """User registration schema (alias for UserCreate)"""

    pass
