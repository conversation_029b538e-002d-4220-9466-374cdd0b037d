"""
Person schemas
"""

from datetime import datetime
from typing import Any, Dict, Optional, List
from uuid import UUID
import re

from pydantic import BaseModel, validator


class PersonBase(BaseModel):
    """Base person schema"""

    first_name: str
    last_name: str
    profile_picture: Optional[str] = None
    contact_info: Dict[str, Any] = {}
    social_profiles: Dict[str, Any] = {}
    professional_info: Dict[str, Any] = {}
    personal_details: Dict[str, Any] = {}


class PersonCreate(PersonBase):
    """Person creation schema"""

    is_user: bool = False

    @validator("first_name", "last_name")
    def validate_names(cls, v):
        if not v or not v.strip():
            raise ValueError("Name cannot be empty")
        return v.strip()

    @validator("contact_info")
    def validate_contact_info(cls, v):
        if v and "email" in v:
            email = v["email"]
            if email and not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                raise ValueError("Invalid email format")
        return v


class PersonUpdate(BaseModel):
    """Person update schema"""

    first_name: Optional[str] = None
    last_name: Optional[str] = None
    profile_picture: Optional[str] = None
    contact_info: Optional[Dict[str, Any]] = None
    social_profiles: Optional[Dict[str, Any]] = None
    professional_info: Optional[Dict[str, Any]] = None
    personal_details: Optional[Dict[str, Any]] = None


class PersonInDBBase(PersonBase):
    """Base person schema with database fields"""

    person_id: UUID
    user_id: UUID
    is_user: bool
    stated_decision_factors: Dict[str, Any] = {}
    learned_decision_factors: Dict[str, Any] = {}
    last_calibration_date: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Person(PersonInDBBase):
    """Person schema for API responses"""

    @property
    def full_name(self) -> str:
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def display_name(self) -> str:
        if self.is_user:
            return "Me"
        return self.full_name


class PersonInDB(PersonInDBBase):
    """Person schema for internal use"""

    pass


class PersonSummary(BaseModel):
    """Simplified person schema for lists and references"""

    person_id: UUID
    first_name: str
    last_name: str
    full_name: str
    profile_picture: Optional[str] = None
    is_user: bool = False

    class Config:
        from_attributes = True


class PersonListResponse(BaseModel):
    """Paginated response for person lists"""

    items: List[PersonSummary]
    total: int
    skip: int
    limit: int
    has_more: Optional[bool] = None
