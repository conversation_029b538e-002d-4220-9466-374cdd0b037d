#!/usr/bin/env python3
"""
Debug authentication for copilot tests
"""

import requests
import json
import time

BASE_URL = "http://localhost:8010"

def test_auth():
    session = requests.Session()
    
    print("🔍 Debugging Authentication")
    print("=" * 40)
    
    # 1. Register user
    user_data = {
        "email": f"authtest.{int(time.time())}@example.com",
        "password": "TestPassword123!",
        "first_name": "Auth",
        "last_name": "Test"
    }
    
    print("1. Registering user...")
    response = session.post(f"{BASE_URL}/api/v1/auth/register-local", json=user_data)
    print(f"   Status: {response.status_code}")
    if response.status_code not in [200, 201]:
        print(f"   Error: {response.text}")
        return
    
    # 2. Login
    login_data = {
        "email": user_data["email"],
        "password": user_data["password"]
    }
    
    print("2. Logging in...")
    response = session.post(f"{BASE_URL}/api/v1/auth/login-local", json=login_data)
    print(f"   Status: {response.status_code}")
    
    if response.status_code != 200:
        print(f"   Error: {response.text}")
        return
    
    token_data = response.json()
    print(f"   Token type: {token_data.get('token_type')}")
    print(f"   Expires in: {token_data.get('expires_in')}")
    
    token = token_data.get("access_token")
    print(f"   Token (first 50 chars): {token[:50]}...")
    
    # 3. Test token with a simple endpoint
    headers = {"Authorization": f"Bearer {token}"}
    
    print("3. Testing token with /api/v1/users/me...")
    response = session.get(f"{BASE_URL}/api/v1/users/me", headers=headers)
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        user_info = response.json()
        print(f"   User ID: {user_info.get('user_id')}")
        print(f"   Email: {user_info.get('email')}")
    else:
        print(f"   Error: {response.text}")
        return
    
    # 4. Test copilot endpoint
    print("4. Testing copilot endpoint...")
    message_data = {
        "message": "Hello, this is a test message"
    }
    
    response = session.post(
        f"{BASE_URL}/api/v1/copilot/converse",
        json=message_data,
        headers=headers
    )
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   Response length: {len(data.get('response', ''))}")
        print(f"   Has conversation_id: {bool(data.get('conversation_id'))}")
        print(f"   Tool calls: {len(data.get('tool_calls', []))}")
    else:
        print(f"   Error: {response.text}")
    
    print("\n✅ Authentication debug complete")

if __name__ == "__main__":
    test_auth()
