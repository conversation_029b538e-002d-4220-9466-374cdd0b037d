#!/usr/bin/env python3
"""
Start the Nexus backend server with SQLite database
"""
import os
import subprocess
import sys

# Set environment variables for SQLite
os.environ["DB_SOURCE"] = "sqlite"
os.environ["DATABASE_URL"] = "sqlite:///./nexus.db"

# Start the server
if __name__ == "__main__":
    try:
        print("Starting Nexus backend server with SQLite database...")
        print(f"DB_SOURCE: {os.environ.get('DB_SOURCE')}")
        print(f"DATABASE_URL: {os.environ.get('DATABASE_URL')}")
        
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "app.main:app", 
            "--host", "127.0.0.1", 
            "--port", "8011",
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)
